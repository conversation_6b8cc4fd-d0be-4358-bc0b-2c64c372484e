package com.yunqu.cc.config.servlet;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictCacheMgr;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.model.Yqlogger;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.LogUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.config.base.AppBaseServlet;
import com.yunqu.cc.config.base.CommLogger;
import com.yunqu.cc.config.base.Constants;
@WebServlet("/servlet/SystemReg/*")
public class SystemRegServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;

	private Logger logger = CommLogger.logger;
	
	/**
	 * 进入到修改界面
	 * @return
	 */
	public String actionForToEdit() {
		return "/pages/basis/systemRegEdit.jsp";
	}

	/**
	 * 进入到列表页面
	 * @return
	 */
	public String actionForSystemReg() {
		return "/pages/basis/systemReg.jsp";
	}
	
	
	public EasyResult actionForReloadAll() {
		DictCacheMgr.reloadAll();
		return EasyResult.ok("", "操作成功！");
	}

	/**
	 * 对记录进行保存或修改
	 * @return
	 */
	public EasyResult actionForSaveOrUpdate() {
		JSONObject paramJson = this.getJSONObject();

		JSONObject json = new JSONObject();
		UserModel user = UserUtil.getUser(this.getRequest());
		
		json.put("SENDER", paramJson.getString("systemReg.SENDER"));
		json.put("PASSWORD", paramJson.getString("systemReg.PASSWORD"));
		json.put("NAME", paramJson.getString("systemReg.NAME"));
		json.put("OUT_SYSTEM", paramJson.getString("systemReg.OUT_SYSTEM"));
		json.put("BAKUP", paramJson.getString("systemReg.BAKUP"));
		json.put("SORT_NUM", paramJson.getString("systemReg.SORT_NUM"));
		json.put("ENABLE_STATUS", paramJson.getString("systemReg.ENABLE_STATUS"));
		
		//根据sender查询接口调用方，判断是否重复
		EasyRow row = null;
		EasySQL sql = new EasySQL();
		sql.append(paramJson.getString("systemReg.SENDER")," SELECT ID FROM C_CF_SYSTEM_REG WHERE SENDER=?");
		try {
			row = getQuery().queryForRow(sql.getSQL(), sql.getParams());
		} catch (SQLException e1) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"根据sender查询接口调用方时失败：" + e1.getMessage(), e1);
			return EasyResult.error(500, "根据sender查询接口调用方时失败：" + e1.getMessage());
		}
		
		
		if (StringUtils.isBlank(paramJson.getString("systemReg.ID"))) {
			if(row!=null){
				return EasyResult.error(500, "sender重复!");
			}
			json.put("ID", RandomKit.randomStr());
			json.put("CREATE_ACC", user.getUserAcc());
			json.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			json.put("EP_CODE", user.getEpCode());
		} else {
			if(row!=null && !paramJson.getString("systemReg.ID").equals(row.getColumnValue("ID"))){
				return EasyResult.error(500, "sender重复!");
			}
			json.put("ID", paramJson.getString("systemReg.ID"));
			json.put("UPDATE_ACC",  user.getUserAcc());
			json.put("UPDATE_TIME", DateUtil.getCurrentDateStr());
		}
		EasyRecord record = new EasyRecord("C_CF_SYSTEM_REG", "ID").setColumns(json);

		System.out.println(record.getEasySQL());
		try {
			if (StringUtils.isBlank(paramJson.getString("systemReg.ID"))) {
				this.getQuery().save(record);
				return EasyResult.ok("", "新增成功！");
			} else {
				this.getQuery().update(record);
				return EasyResult.ok("", "修改成功！");
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"操作失败，原因：" + e.getMessage(), e);
			return EasyResult.error(500, "操作失败，原因：" + e.getMessage());
		}
	}

	/**
	 * 删除
	 * @return
	 */
	public EasyResult actionForDel() {
		JSONObject json = new JSONObject();
		JSONObject obj = this.getJSONObject();
		UserModel user = UserUtil.getUser(this.getRequest());
		String id = obj.getString("ID");
		String sender = obj.getString("SENDER");
		
		try {
			EasyRecord record = null;
			json.put("ID", id);
			record = new EasyRecord("C_CF_SYSTEM_REG", "ID").setColumns(json);
			this.getQuery().deleteById(record);
			
			Yqlogger log=new Yqlogger();
			log.setCreateAcc(user.getUserAcc());
			log.setCreateName(user.getUserName());
			log.setCreateTime(DateUtil.getCurrentDateStr());
			log.setModule(Constants.APP_NAME);
			log.setOperType(Yqlogger.OPER_TYPE_DEL);
			log.setContent(user.getUserName()+"删除了"+sender+"接口调用方");
			log.setBakup("");
			EasyResult easyResult=LogUtil.insertLog(log);
			if(easyResult.getState()!=1){
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 用户"+user.getUserAcc()+"删除接口调用方"+sender+"时失败!");
			}
			return EasyResult.ok("删除成功！");
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"删除失败，原因：" + e.getMessage(), e);
			return EasyResult.error(500, "删除失败，原因：" + e.getMessage());
		}
	}
}
