<?xml version="1.0" encoding="UTF-8"?>
<!-- 
	id:必须全局唯一，命名方式：应用名称+菜单名称
	name：菜单或功能名称
	type：资源类型，1：应用 ，2：菜单，3：功能，9: 其它
	state：状态，0:正常，缺省 ，1：暂停
	portal:所属应用，不填写缺省为通用portal，根据菜单的归属，可以填写具体的所属的portal，例如：my_portal
	index:排序，菜单的显示按照升序进行排序。
	icon：菜单图标
 -->
 
<resources>
   <resource id="configmgr" name="配置管理" url="" type="2" portal="" state="0" order="4" index = "100">
	   <resource id="config" name="基础配置管理" url="" type="2" portal="" state="0" order="4" index = "110">
			<resource id="config_yw_operating" name="操作日志"   url="/config/servlet/Logger?action=logger" type="2" portal="" icon="" state="0" index="1"/>
			<resource id="confign_dictiona" name="业务数据字典"   url="/config/servlet/Diction?action=dictiona" type="2" portal="" icon="" state="0" index="2"/>
			<resource id="confign_sys_dictiona" name="系统数据字典"   url="/config/servlet/Diction?action=sysDictiona" type="2" portal="" icon="" state="0" index="3"/>
			<resource id="config_permissions" name="用户建议-产品建议权限配置(测试)"   url="/config/servlet/Diction?action=Permissions&amp;code=Message_001" type="2" portal="" icon="" state="0" index="4"/>
			<resource id="confign_module_dictiona" name="红黑名单模块字典配置(测试)"   url="/config/servlet/Diction?action=DictionaDetails&amp;code=002" type="2" portal="" icon="" state="0" index="5"/>
			<resource id="confign_module_dictionaDetails" name="模块名称配置(测试)"   url="/config/servlet/Diction?action=GetDictionaDetails&amp;code=SYSTEM_MODULE" type="2" portal="" icon="" state="0" index="5"/>
			<resource id="config_module_systemReg" name="接口调用方管理"   url="/config/servlet/SystemReg?action=systemReg" type="2" portal="" icon="" state="0" index="6"/>
			<resource id="config_cache_memcacheSrh" name="Memcache缓存查询"   url="/config/servlet/MemcacheMgr?action=memcacheKeySrh" type="2" portal="" icon="" state="0" index="7"/>
			<resource id="config_birthday" name="生日提醒管理"   url="/config/pages/birthday/birthday-list.jsp" type="2" portal="" icon="" state="0" index="8"/>
		</resource>
	</resource>
</resources>
