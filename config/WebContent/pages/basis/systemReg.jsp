<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>接口调用系统注册管理</title>
	<style type="text/css">
.labelListDiv {
	height: 32px;
	line-height: 32px;
	padding: 0px 10px;
	font-size: 13px;
}

.labelListDiv.active {
	background-color: #f8f8f8
}

.labelListDiv a {
	text-decoration: none;
	color: #666;
}

.ibox-content table tr td label+label {
	margin-left: 10px
}
</style>
</EasyTag:override>
<EasyTag:override name="content">

	<form name="searchForm" class="form-inline" id="searchForm" data-toggle="render">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		       <h5><span class="glyphicon glyphicon-list"></span>接口调用方系统注册</h5>
						   </div>
						  <hr style="margin: 5px -15px">
						  <div class="form-group">
							    <div class="input-group input-group-sm">
							      <span class="input-group-addon">接口标识</span>	
								  <input type="text" name="sender" class="form-control input-sm" style="width:120px">
							   </div>
							    <div class="input-group input-group-sm">
							      <span class="input-group-addon">接口名称</span>	
								  <input type="text" name="name" class="form-control input-sm" style="width:120px">
							   </div>
							   
							    <div class="input-group input-group-sm">
		           		              <span class="input-group-addon">创建时间</span>	
									  <input type="text" name="startDate" id="startDate" class="form-control input-sm" style="width:150px" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',autoPickDate:true,maxDate:'#F{$dp.$D(\'endDate\')}'})">
									  <span class="input-group-addon">-</span>	
									  <input type="text" name="endDate" id="endDate" class="form-control input-sm" style="width:150px" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',autoPickDate:true,minDate:'#F{$dp.$D(\'startDate\')}'})">									  
							    </div>
							    <div class="input-group input-group-sm">
										<button type="button" class="btn btn-sm btn-default" onclick="SystemRegTemp.loadData()"><span class="glyphicon glyphicon-search"></span>查询</button>
										&nbsp;<button type="button" class="btn btn-sm btn-success" onclick="SystemRegTemp.add('0')">新增</button>
										<!-- <button type="button" class="btn btn-sm btn-default" onclick=""><span class="glyphicon glyphicon-repeat"></span> 重置</button> -->
								</div>
						   </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed"  id="tableHead" data-mars="SystemReg.list">
                             <thead>
	                         	 <tr>
									<th>接口标识</th>
									<th>接口密码</th>
									<th>接口名称</th>
									<th>外部系统</th>
									<th>上次调用时间</th>
									<th>调用次数</th>
									<th>当天调用次数</th>
									<th>启用状态</th>
									<th>序号</th>
									<th>创建人</th>
									<th>创建时间</th>
									<th>修改人</th>
									<th>操作</th>
								</tr>
                             </thead>
                             <tbody id="dataList">
                             
                             </tbody>
		                 </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for  list}}
										<tr>
											<td>{{:SENDER }}</td>
											<td>{{:PASSWORD}}</td>         
                                            <td>{{:NAME}}</td>                                         
											<td>{{dictFUN:OUT_SYSTEM "SF_YN" }}</td>
                                            <td>{{:LAST_INVOKE_TIME}}</td>
                                            <td>{{:TOTAL_INVOKE_TIMES}}</td>
                                            <td>{{:TODAY_INVOKE_TIMES}}</td>
                                            <td>{{dictFUN:ENABLE_STATUS "ENABLE_STATUS"}}</td>
                                            <td>{{:SORT_NUM}}</td>
                                            <td>{{:CREATE_ACC}}</td> 
                                            <td>{{:CREATE_TIME}}</td> 
                                            <td>{{:UPDATE_ACC}}</td>        
											<td> 
												<a  href="javascript:void(0)"  onclick="SystemRegTemp.add('1','{{:ID}}','{{:SENDER}}')">修改</a>&nbsp;
												<a  href="javascript:void(0)"  onclick="SystemRegTemp.del('{{:ID}}','{{:SENDER}}')">删除</a>&nbsp;
											</td>
									    </tr>
								    {{/for}}				         
							 </script>
	                     <div class="row paginate">
	                     	<jsp:include page="/pages/common/pagination.jsp"/>
	                     </div> 
	              	</div> 
                </div>
        </form>

</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="/yq_common/static/js/yq/extends.js"></script>
	<script type="text/javascript">
		jQuery.namespace("SystemRegTemp");
		$(function() {
			$("#searchForm").render({
				success : function() {

				}
			});
		
		})
			
		SystemRegTemp.reloadAll = function() {
			ajax.remoteCall("${ctxPath}/servlet/Diction?action=reloadAll",null,function(result) {
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		SystemRegTemp.loadData = function() {
			$("#searchForm").searchData();
		}
		
		//新增员工信息
		SystemRegTemp.add=function(type,id){
			if(0==type){
				popup.layerShow({type:2,title:'新增',offset:'20px',area:['550px','450px']},"${ctxPath}/servlet/SystemReg?action=toEdit",null);
			}
			if(1==type){
			   if(id==""){
				   var checkedRecord = $("#dataList").find("input[type='checkbox']:checked");
				   if(checkedRecord.length!=1){
					   layer.alert('请选择一行记录修改！',{icon: 7, title:'提示',offset:'20px'});
					   return false;
				   }
				   id = $(checkedRecord[0]).attr("data-id");
			   }
			   var url="${ctxPath}/servlet/SystemReg?action=toEdit&ID="+id;
			   popup.layerShow({type:2,title:['修改',"background-color:#fff"],offset:'20px',area:['550px','450px']},url,null);
			}
		}
		
		$(function() {
			$(".labelListDiv").find("a").click(function() {
				var obj = $(this);
				$(obj).parent().parent().addClass("active");
				$(obj).parent().parent().siblings().removeClass("active");
			});
		});
		SystemRegTemp.del=function (id,sender){
			layer.confirm('删除该接口调用方['+sender+']后,该模块将不能再调用本系统的接口，确认要删除?', {
				btn : [ '确定', '取消' ],
				offset : '40px'
			}, function(index, layero) {
				 ajax.remoteCall("${ctxPath}/servlet/SystemReg?action=del", {ID:id,SENDER:sender}, function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon: 1,offset:'60px',time:1200},function(){
							layer.close(index);
							SystemRegTemp.loadData();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}); 
			}, function(index) {
				layer.close(index);
			});
		}
	</script>


</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>