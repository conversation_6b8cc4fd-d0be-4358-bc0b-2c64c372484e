<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>接口新增</title>
</EasyTag:override>
<EasyTag:override name="content">

	<form id="systemRegForm" data-mars="SystemReg.edit" data-pk="" method="post" class="form-inline"
		autocomplete="off" data-mars-prefix="systemReg.">
		<input name="systemReg.ID" class="form-control input-sm" type="hidden" value="${param.ID }">
		<table class="table table-vzebra">
			<tbody>
				<tr>
					<td class="required">接口标识</td>
					<td><input  data-rules="required"  name="systemReg.SENDER" class="form-control input-sm" type="text" maxlength="30"></td>
				</tr>
				<tr>
					<td class="required">接口密码</td>
					<td><input  data-rules="required"  name="systemReg.PASSWORD" class="form-control input-sm" type="text"  maxlength="30"></td>
				</tr>
				<tr>
					<td class="required">接口名称</td>
					<td><input  data-rules="required"  name="systemReg.NAME" class="form-control input-sm" type="text" value=""  maxlength="30"></td>

				</tr>
				<tr>
					<td class="required" width="60px">外部系统</td>
					<td><select class="form-control input-sm" name="systemReg.OUT_SYSTEM" data-rules="required" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('SF_YN')">
							<option value="">请选择</option>
					</select></td>
				</tr>
				<tr>
					<td class="required">序号</td>
					<td><input  data-rules="required"  name="systemReg.SORT_NUM" class="form-control input-sm" type="number"  maxlength="3"></td>
				</tr>
				<tr>
					<td class="required" width="60px">是否启用</td>
					<td><select class="form-control input-sm" name="systemReg.ENABLE_STATUS" data-rules="required" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('ENABLE_STATUS')">
							<option value="">请选择</option>
					</select></td>
				</tr>
				
				<tr>
					<td>备注</td>
					<td><textarea rows="3" name="systemReg.BAKUP" class="form-control input-sm" maxlength="100"></textarea>
					</td>
				</tr>
			</tbody>
		</table>
		<div class="layer-foot text-c">
			<button class="btn btn-sm btn-primary" type="button" onclick="systemReg.ajaxSubmitForm()">保存</button>
			<button class="btn btn-sm btn-default ml-20" type="button"
				id="backbut" onclick="popup.layerClose(this);">关闭</button>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/yq_common/static/js/yq/extends.js"></script>
	<script type="text/javascript">
		requreLib.setplugs('wdate');
		jQuery.namespace("systemReg");

		$(function(){
			$("#systemRegForm").render();
		});
		systemReg.ajaxSubmitForm = function(){
				if(!form.validate("#systemRegForm")){
					return;
				};
				 var data = form.getJSONObject("systemRegForm");
					ajax.remoteCall("${ctxPath}/servlet/SystemReg?action=saveOrUpdate",data,function(result) { 
							if(result.state == 1){
								popup.layerClose("#systemRegForm");
								layer.msg(result.msg,{icon: 1});
								window.parent.SystemRegTemp.loadData();

							}else{
								layer.alert(result.msg,{icon: 5});
							}
						}
					);
		 }
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>