package com.yunqu.cc.smsgw.job;

import java.util.concurrent.TimeUnit;

import org.apache.log4j.Logger;

import com.yq.busi.common.model.LogUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.smsgw.base.CommonLogger;
import com.yunqu.cc.smsgw.base.Constants;
import com.yunqu.cc.smsgw.job.item.SendSmsMgrThread;


public class JobMgr {
	private Logger logger = CommonLogger.logger;

	private static JobMgr instance = new JobMgr();
	
	private static boolean start = true;
	
	private static boolean start2 = true;
	
	private JobMgr(){}
	
	public static JobMgr getInstance(){
		return instance;
	}
	
	/**
	 * 启动所有线程
	 */
	public void startJob(){
		logger.info(" [JobMgr.startJob] start...");
		
		//移到jobs模块里统一调度
//		ThreadMgr.getInstance().executeRepeat(new SendSmsMgrThread(), 5L, Constants.SEND_SMS_THREAD_INTEVAL, TimeUnit.SECONDS);
//		logger.info(" [JobMgr.startJob] 启动【 系统->短信网关】发送短信管理线程:SendSmsMgrThread...");
//		LogUtil.insertLog(Constants.LOG_TYPE_GW_RUN, "启动【 系统->EMG网关】发送短信管理线程:SendSmsMgrThread", "每隔"+Constants.SEND_SMS_THREAD_INTEVAL+"秒钟执行一次");
	}
	
	/**
	 * 停止所有线程
	 * 运行中的线程，每次循环都要读取该变量，直接设置为false，即可停止所有线程
	 */
	public void stopJob(){
		logger.info(" [JobMgr.stopJob] start...");
		start = false;
		start2 = false;
//		LogUtil.insertLog(Constants.LOG_TYPE_GW_RUN, "网关正在停止", "即将停止所有的线程");
		
//		logger.info(CommonUtil.getClassNameAndMethod(this)+"停止所有的线程");
	}

	public static boolean isStart() {
		return start;
	}
	public static boolean isStartExcel() {
		return start2;
	}
}
