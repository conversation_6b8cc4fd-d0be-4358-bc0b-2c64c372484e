package com.yunqu.cc.smsgw.job.item;

import java.sql.SQLException;
import java.util.List;

import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.db.EasyRow;

import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.model.Yqlogger;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.LogUtil;
import com.yunqu.cc.smsgw.base.CommonSendLogger;
import com.yunqu.cc.smsgw.base.Constants;
import com.yunqu.cc.smsgw.dao.SmsInfoDao;
import com.yunqu.cc.smsgw.job.JobMgr;
import com.yunqu.cc.smsgw.job.ThreadMgr;



/**
 * 针对接口类/iservice 数据
 * 短信发送管理类：
 * 只负责找出需要下发的短信记录，然后更新短信记录状态为(发送中)，最后启动专门的短信发送线程去下发短信
 */
public class SendSmsMgrThread implements Runnable{
	private Logger logger = CommonSendLogger.logger;
	private SmsInfoDao smsInfoDao = new SmsInfoDao();
	private int msgSendNumsOnce = Constants.MSG_SEND_NUMS_ONCE;
	
	private EasyCache cache = CacheManager.getMemcache();
	
	@Override
	public void run() {
		if(JobMgr.isStart()){
//			logger.debug(CommonUtil.getClassNameAndMethod(this)+" SendSmsMgrThread begin...");
			try {
				if(!DictConstants.DICT_SY_YN_Y.equals(Constants.ALLOW_SEND_SMS)){
					logger.info(CommonUtil.getClassNameAndMethod(this)+" 短信发送开关已关,不发送!");
					return;
				}
				
				if(msgSendNumsOnce<1){
					msgSendNumsOnce = 10;
				}
				
				List<EasyRow>  list = smsInfoDao.findNeedSendSms(Constants.SMS_SEND_TYPE_1);
				
				if(list==null||list.size()<1){
//					logger.debug(CommonUtil.getClassNameAndMethod(this)+" 暂时没有需要下发的短信!");
					return;
				}
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 此次需要下发的短信数量:"+list.size());
				
				int i = 0;
				int repeatNum = 0;
				//逐条发送短信
				for(EasyRow sms : list){
					String smsId = sms.getColumnValue("ID");
					
					//避免大批短信同时发送时，线程数量不足，短信积压，导致被重复扫到，出现重复发送
					if(cache.get("SMS_SEND_FLAG_"+smsId)!=null){
						logger.info(CommonUtil.getClassNameAndMethod(this)+" 本条短信重复，暂不发送:"+smsId);
						repeatNum ++ ;
						continue;
					}
					//写入缓存
					cache.put("SMS_SEND_FLAG_"+smsId, smsId,3600*2);
					//开始发送
					ThreadMgr.getInstance().executeOneTimes(new SendSmsThread(sms));
					
					//控制为每次处理10条记录
					i++;
					if(i == msgSendNumsOnce ){
						CommonUtil.sleep(1);
						i = 0;
					}
				}
				
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 此次发送完成,重复量:"+repeatNum+",提交量:"+ (list.size()-repeatNum));
				
				
			} catch (SQLException e1) {
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 短信发送管理线程出现异常:"+e1.getMessage(),e1);
				
				Yqlogger logger = new Yqlogger();
				logger.setCreateAcc("system");
				logger.setCreateName("系统");
				logger.setCreateTime(DateUtil.getCurrentDateStr());
				logger.setModule(Constants.APP_NAME);
				logger.setOperType(Yqlogger.OPER_TYPE_ERROR);
				logger.setContent("系统在执行短信发送调度时出现异常："+e1.getMessage());
				logger.setBakup("系统自动运行出错");
				LogUtil.insertLog(logger);
			}
			
			
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" SendSmsMgrThread end...");
		}
	}
	
}
