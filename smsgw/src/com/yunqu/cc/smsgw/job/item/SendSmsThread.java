package com.yunqu.cc.smsgw.job.item;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyRow;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.smsgw.base.CommonSendLogger;
import com.yunqu.cc.smsgw.base.Constants;
import com.yunqu.cc.smsgw.bean.SmsReceipt;
import com.yunqu.cc.smsgw.bean.xml.XmlSmsResp;
import com.yunqu.cc.smsgw.dao.SmsInfoDao;
import com.yunqu.cc.smsgw.service.SendSmsService;
import com.yunqu.cc.smsgw.service.SuperSmsService;
import com.yunqu.cc.smsgw.service.weixinService;


/**
 * 发送短信线程
 * 1、收到要发送的短信记录
 * 2、下发短信
 * 3、根据状态、规则计算短信最终状态
 */
public class SendSmsThread implements Runnable{
	private Logger logger = CommonSendLogger.logger;
	
	private SendSmsService smsService = new SendSmsService();
	private SmsInfoDao smsDao = new SmsInfoDao();
	
	//短信下发记录id
	private String msgId;
	
	//短信发送记录对象
	private EasyRow smsSendRecord;
	
	public SendSmsThread(String msgId) {
		super();
		this.msgId = msgId;
	}
	
	public SendSmsThread(EasyRow smsSendRecord) {
		super();
		this.smsSendRecord = smsSendRecord;
	}

	@Override
	public void run() {
		logger.info(CommonUtil.getClassNameAndMethod(this)+" SendSmsThread begin...");
		try {
			//准备要发送短信记录
			if(smsSendRecord==null && StringUtils.isBlank(msgId)){
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 必须设置要发送的短信记录");
				return;
			}
			if(smsSendRecord==null){
				smsSendRecord = smsDao.findSmsBySmsId(msgId);
				if(smsSendRecord==null ){
					logger.error(CommonUtil.getClassNameAndMethod(this)+" 无法找到对应的短信发送记录,不能发送,短信id:"+msgId);
					return;
				}
			}
			
			
			//短信相关信息
			String smsId = smsSendRecord.getColumnValue("ID");
			String sender = smsSendRecord.getColumnValue("SENDER");
			String receiver = smsSendRecord.getColumnValue("RECEIVER");
			String content = smsSendRecord.getColumnValue("CONTENT");
			String userType = smsSendRecord.getColumnValue("USER_TYPE");
			String channelId = smsSendRecord.getColumnValue("CHANNEL_ID");
			String sendBatch = smsSendRecord.getColumnValue("SEND_BATCH");
			String sendTimes = smsSendRecord.getColumnValue("SEND_TIMES");//
			String smsInfoId = smsSendRecord.getColumnValue("SMS_INFO_ID");
			//超信参数
			String needSuperSms = smsSendRecord.getColumnValue("NEED_SUPER_SMS");
			String superJsonParam = smsSendRecord.getColumnValue("SUPER_JSON_PARAM");
		    content=weixinService.getWeixinContent(content, receiver);
			msgId = smsId;
			if(StringUtils.isBlank(content)){
				logger.error(CommonUtil.getClassNameAndMethod(this)+"ID:"+smsId+"无发送内容取消发送"+content);
				return;
			}
			//当没有设置渠道，而且系统设置为没有渠道不发送的情况下，直接停止处理
			if(StringUtils.isBlank(channelId) && !DictConstants.DICT_SY_YN_Y.equals(Constants.AUTO_FOUND_CHANNEL)){
				smsDao.updateSmsStatus(smsId, Constants.SMS_SEND_STATUS_FAILED, null,null, 0,"短信记录未设置渠道ID",null,null);
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 无法找到对应的短信发送渠道,不能发送,短信id:"+msgId+",渠道id:"+channelId);
				return;
			}
			
			//找到短信发送渠道，如果没有设置渠道，找到默认的短信渠道；如果找不到，则不发送
			EasyRow channel = null;
			if(StringUtils.isBlank(channelId)){
				channel = smsDao.findAlarmChannel();
			}else{
				channel = smsDao.findAlarmChannelById(channelId);
			}
			
			if(channel==null){
				smsDao.updateSmsStatus(smsId, Constants.SMS_SEND_STATUS_FAILED, null,null, 0,"短信记录未设置渠道ID",null,null);
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 无法找到对应的短信发送渠道,不能发送,短信id:"+msgId+",渠道id:"+channelId);
				return;
			}
			
			//先将短信状态修改为:发送中
			smsDao.updateSmsStatus(smsId, Constants.SMS_SEND_STATUS_SENDING, null,null, 0,"发送中",null,null);
			
			//提交报告
			SmsReceipt sr = new SmsReceipt();
			sr.setType("2"); //1-状态报告 2-提交报告 3-终端回复
			sr.setContent("提交短信");
			sr.setCreateTime(DateUtil.getCurrentDateStr());
			sr.setMsgId(smsId);
			sr.setSmsMsgId(smsId);
			sr.setSmsInfoId(smsInfoId);
			sr.setStatus("2");
			sr.setSender(sender);
			sr.setReceiver(receiver);
			sr.setChannelId(channelId);
			
			
			if(StringUtils.isBlank(sendBatch)){
				sendBatch = "1";
			}
			if(StringUtils.isBlank(sendTimes)){
				sendTimes = "1";
			}
			
			channelId = channel.getColumnValue("ID");
			sr.setChannelId(channelId);
			
			String resendTimes = channel.getColumnValue("RESEND_TIMES");
			if(StringUtils.isBlank(resendTimes)){
				resendTimes = "1";
			}
			//短信当前发送批次,一个批次在发送不成功的情况下，会自动使用所有备用通道自动重发；每单用户在界面上对发送失败的短信点击重发时，该批次加1
			int smsSendBatch = CommonUtil.parseInt(sendBatch);
			if(smsSendBatch<=0){
				smsSendBatch = 1;
			}
			//短信当前重发次数
//			int smsSendTimes = CommonUtil.parseInt(sendTimes);
			//短信当前通道允许重发的最大次数
			int smsChannelResendTimes = CommonUtil.parseInt(resendTimes);
			
			//短信使用当前通道重发次数
			int smsChannelSendTimes = 0;
			
			/****判断发送超信*****/
			String isSendSuperSms = null;
			//判断是否需要发送超信
			if("1".equals(needSuperSms)) {
				//判断手机号码是否支持超信
				boolean queryAimAbility = SuperSmsService.queryAimAbility(receiver);
				if(queryAimAbility) {
					//获取超信短链接
					JSONObject json = JSONObject.parseObject(superJsonParam);
					String applyAimUrl = SuperSmsService.applyAimUrl(receiver, json);
					if(StringUtils.isNotBlank(applyAimUrl)) {
						//拼接附加文本到content
						String superAppendConent = json.getString("superAppendConent");
						//最近超信发送内容
						content=superAppendConent.replace ("[URL]",applyAimUrl);
						isSendSuperSms = "1";
					}
				}
			}
			
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 开始下发短信,短信id:"+smsId+",接收人:"+receiver+",内容:"+content+",超信标识:"+isSendSuperSms);
			
			XmlSmsResp resp  = smsService.sendSms(null,smsId,sender,receiver,content,channel);
			
			try {
				//设置短信发送记录与告警通道的关联记录
				EasyRow smsChannelRecord = smsDao.findSmsChannelRecord(channelId,smsId,smsSendBatch);
				if(smsChannelRecord == null){
					smsDao.insertSmsChannelRecord(channelId,smsId,smsSendBatch,1);
				}else{
					String smsChannelRecordId = smsChannelRecord.getColumnValue("SMS_CHANNEL_RECORDID");
					//重新计算使用该通道已经发送的次数
					smsChannelSendTimes = CommonUtil.parseInt(smsChannelRecord.getColumnValue("SEND_TIMES")) + 1;
					smsDao.updateSmsChannelRecord(smsChannelRecordId,1);
				}
				
				//发送成功：EMG会返回短信中心的smsid，需要进行保存；后续处理回执
				if("ok".equals(resp.getResult())){
					sr.setBakup("提交成功");
					JSONObject obj = resp.getData();
					if(StringUtils.isNotBlank(obj.getString("reqId"))){
						sr.setBakup(sr.getBakup()+":返回请求id:"+obj.getString("reqId"));
					}
					//2020-11-27 smsSendId暂用smsId
					smsDao.updateSmsStatus(smsId, Constants.SMS_SEND_STATUS_SUBMIT,smsId,channelId,1,obj.getString("msg"),isSendSuperSms,content);
					logger.info(CommonUtil.getClassNameAndMethod(this)+" 该短信下发成功,短信id:"+smsId+",EMG返回reqId:"+obj.getString("reqId"));
				}else{//发送失败
					logger.error(CommonUtil.getClassNameAndMethod(this)+" 该短信下发失败,短信id:"+smsId);
					sr.setBakup("提交失败:"+resp.getResult());
					//判断是否需要更换通道
					//如果使用当前通道发送的次数大于该通道允许的最大次数，需要切换通道重发
					if(smsChannelSendTimes > smsChannelResendTimes){
						sr.setBakup("提交失败:"+resp.getResult()+",寻找其他通道继续发送");
						
						//找到还未发送过的其他通道
						EasyRow smsNewChannelRecord = smsDao.findAlarmChannelBySmsBatch(msgId);
						//如果找不到其他通道，则状态设置为 发送失败(此时用户可以在页面上点击重发，批次增加，可以重新轮询所有通道发送一遍)
						if(smsNewChannelRecord==null){
							sr.setBakup("提交失败:"+resp.getResult()+",且无法找到其他发送方式重发");
							smsDao.updateSmsStatus(smsId, Constants.SMS_SEND_STATUS_FAILED,null,channelId,1,resp.getResult(),null,null);
							smsDao.insertSmsReceipt(sr);
							return;
						}
						
						//插入新通道信息
						String newChannelId = smsNewChannelRecord.getColumnValue("ID");
						smsDao.insertSmsChannelRecord(newChannelId,msgId,smsSendBatch,0);
						//短信状态设置为 正在重发，等待下一次重新发送
						smsDao.updateSmsStatus(smsId, Constants.SMS_SEND_STATUS_RESEND,null,newChannelId,1,resp.getResult(),null,null);
					}else{
						sr.setBakup("提交失败:"+resp.getResult()+",等待重发");
						//如果该短信的发送次数未超过了通道所允许的次数，则等到重发
						smsDao.updateSmsStatus(msgId, Constants.SMS_SEND_STATUS_RESEND, null,channelId,1,resp.getResult(),null,null);
					}
				}
				
				smsDao.insertSmsReceipt(sr);
				
			} catch (Exception e) {
				sr.setBakup("提交出现异常:"+resp.getResult());
				smsDao.insertSmsReceipt(sr);
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 短信发送出现异常,短信ID:"+smsId+","+e.getMessage(),e);
			}
		} catch (Exception e) {
			//先将短信状态修改为:正在重发
			try {
				smsDao.updateSmsStatus(msgId, Constants.SMS_SEND_STATUS_RESEND, null,null, 0,"重发中",null,null);
			} catch (Exception e1) {
			}
			
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 短信发送线程出现异常:"+e.getMessage(),e);
		}
		
		
		logger.info(CommonUtil.getClassNameAndMethod(this)+" SendSmsThread end...");
	}
	
	public static void main(String[] args) {
		JSONObject json = JSONObject.parseObject("{\"dyncParams\":{\"URL\":\"https://weixincs.midea.com/ccss-ipms-cis-rpc/uat/Cc/3EX2H8\"},\"smsSigns\":[\"梦网科技\"],\"tplId\":\"600180330\",\"superAppendConent\":\"测试附件文本\"}");
		System.out.println(json.toJSONString());
	}
	
}
