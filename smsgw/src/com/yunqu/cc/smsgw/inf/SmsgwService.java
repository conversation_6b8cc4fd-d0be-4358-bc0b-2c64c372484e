package com.yunqu.cc.smsgw.inf;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.user.UserMgr;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.cc.smsgw.base.CommonLogger;
import com.yunqu.cc.smsgw.base.Constants;
import com.yunqu.cc.smsgw.job.item.SendSmsMgrThread;
import com.yunqu.cc.smsgw.job.item.SendSmsMgrThread2;
import com.yunqu.cc.smsgw.service.weixinService;


/**
 * 提供一些常用的接口
 */
public class SmsgwService  extends IService{
	private Logger logger = CommonLogger.logger;

	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		String command = json.getString("command");
		
		//由定时任务jobs调用，通知网关扫描要发送的短信并进行发送
		if(ServiceCommand.SMS_PUSH.equals(command)){
			pushMsg();
			return null;
		}
		if("smsPush2".equals(command)){//手工
			pushMsg2();
			return null;
		}
		
		//接收其他模块发送短信的请求
		if(ServiceCommand.SENDMESSAGE.equals(command)){
			JSONObject rjson = sendMessage(json);
			return rjson;
		}
		
		//短信回执
		if(ServiceCommand.SMS_RT_REQUEST.equals(command)){ 
			JSONObject rjson = receiveRT(json);
			return rjson;
		}
		
		//上行短信
		if(ServiceCommand.SMS_MO_REQUEST.equals(command)){
			JSONObject rjson = receiveMO(json);
			return rjson;
		}
		//短信发送结果查询
		if(ServiceCommand.SMS_SEND_RESULT_SEARCH.equals(command)){
			JSONObject rjson = sendMessageInfo(json);
			return rjson;
		}
		
		return null;
	}
	
	
	private JSONObject sendMessageInfo(JSONObject json) {		
		// TODO Auto-generated method stub
		EasyQuery query = EasyQuery.getQuery("smsgw", "yw-ds");
		JSONObject msg = new JSONObject();
		String source = json.getString("source");
		String busiId = json.getString("busiId");
		msg.put("serialId", json.getString("serialId"));
		try {
			String infoId = sendInfoId(source,busiId,query);//获取发送短信信息id
			JSONObject jsons=sendMsmRecords(infoId,query);//获取发送的详细信息和总条数
			JSONObject jso=sendMsmYn(infoId,query);//获取发送的成功失败			
			msg.put("success", jso.getString("success"));
			msg.put("fail", jso.getString("fail"));
			msg.put("serialId", json.getString("serialId"));
			msg.put("smsSendResults", jsons.get("SmsSendResult"));
			msg.put("total", jsons.get("total"));
			msg.put("iphoneSucc", jsons.get("iphoneSucc"));
			msg.put("iphoneFile", jsons.get("iphoneFile"));
			msg.put("respCode", GWConstants.RET_CODE_SUCCESS);
			msg.put("respDesc", "获取成功");		
			return msg;
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			msg.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			msg.put("respDesc", "获取失败");
			return msg;
		}
	}


	private JSONObject sendMsmYn(String infoId, EasyQuery query) throws SQLException {
		// TODO Auto-generated method stub
		String sql = "select sum(REASON) a,sum(REASON1) b  from (select "
				+ " case when (SEND_STATUS='3' or SEND_STATUS='5') then '1' ELSE '0' end AS REASON, "
				+ " case when SEND_STATUS='4' then '1' ELSE '0' end AS REASON1 "
				+ " from C_SMS_SEND_RECORD where SMS_INFO_ID=?) ";
			JSONObject obj = new JSONObject();					
			List<EasyRow> list = query.queryForList(sql,new Object[]{infoId});
			if(list !=null && list.size()>0){
				for (EasyRow easyRow : list) {
					obj.put("success",easyRow.toJSONObject().getString("A"));
					obj.put("fail",easyRow.toJSONObject().getString("B"));				
				}
	
			}else{
				obj.put("success","0");
				obj.put("fail","0");		
			}
			
			return obj;
			
	}


	private JSONObject sendMsmRecords(String infoId,EasyQuery query) throws SQLException {
		// TODO Auto-generated method stub
		JSONObject json=new JSONObject();
		String sql = " select T1.*,T2.CONTENT SMS_UPLOAD_CONTENT,T2.CREATE_TIME SMS_UPLOAD_TIME from C_SMS_SEND_RECORD T1 LEFT JOIN C_SMS_UPLOAD T2 ON T1.SMS_UPLOAD_ID = T2.ID where T1.SMS_INFO_ID=? ";
		
		List<JSONObject> lis=new ArrayList<>();
			List<EasyRow> list = query.queryForList(sql,new Object[]{infoId});
			String iphoneSucc="";
			String iphoneFile="";
			if(list !=null && list.size()>0){
				for (EasyRow easyRow : list) {
					JSONObject obj = new JSONObject();					
					obj.put("id",easyRow.getColumnValue("ID"));
					obj.put("receiver",easyRow.getColumnValue("RECEIVER"));
					
					obj.put("sendTime",easyRow.getColumnValue("SEND_TIME"));
					obj.put("sendTimes",Integer.parseInt(easyRow.getColumnValue("SEND_TIMES")));
					obj.put("sendStatus",easyRow.getColumnValue("SEND_STATUS"));
					obj.put("smsUploadId",easyRow.getColumnValue("SMS_UPLOAD_ID"));
					obj.put("smsUploadContent",easyRow.getColumnValue("SMS_UPLOAD_CONTENT"));
					obj.put("smsUploadTime",easyRow.getColumnValue("SMS_UPLOAD_TIME"));
					lis.add(obj);
					String status=easyRow.getColumnValue("SEND_STATUS");
					if("3".equals(status)||"5".equals(status)){
						iphoneSucc+=easyRow.getColumnValue("RECEIVER")+";";
					}else if("4".equals(status)){
						iphoneFile+=easyRow.getColumnValue("RECEIVER")+";";

					}
				}
		
			}
			json.put("iphoneSucc",iphoneSucc.length()>0?iphoneSucc.substring(0,iphoneSucc.length()-1):"");
			json.put("iphoneFile",iphoneFile.length()>0?iphoneFile.substring(0,iphoneFile.length()-1):"");
			json.put("total", list.size());
			json.put("SmsSendResult", lis);
			return json;
	}


	private  String sendInfoId(String source, String busiId,EasyQuery query)throws SQLException {
		String id="";	
	    String sql = "select ID from C_SMS_INFO WHERE SOURCE=?  AND BUSI_ID=?";	
	      List<EasyRow> list = query.queryForList(sql, new Object[] { source, busiId });
	      if ((list != null) && (list.size() > 0))
	      {
	    	  for (EasyRow easyRow : list) {
	    		  id= easyRow.toJSONObject().getString("ID");
	    	  }
	      }	 
	      return id;
	  }


	/**
	 * 启动发送短信的线程
	 */
	private void pushMsg() {
		//定时任务会5秒调用一次该方法，此处不能按线程方式运行，否则第一次还没执行完，第二次调用又开始，会导致短信重复发送
//		ThreadMgr.getInstance().executeOneTimes(new SendSmsMgrThread());
		new SendSmsMgrThread().run();
	}
	
	private void pushMsg2() {
		//定时任务会5秒调用一次该方法，此处不能按线程方式运行，否则第一次还没执行完，第二次调用又开始，会导致短信重复发送
//		ThreadMgr.getInstance().executeOneTimes(new SendSmsMgrThread());
		new SendSmsMgrThread2().run();
	}
	
	/**
	 * 接收其他模块的短信发送请求，先记录到短信发送表里，由调度统一发送
	 * @param json
	 * @return
	 */
	private JSONObject sendMessage(JSONObject json) {
		// TODO Auto-generated method stub
		EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
		JSONObject rjson = new JSONObject();
		rjson.put("serialId", json.getString("serialId"));
		JSONObject msg = new JSONObject();
		String model = json.getString("model");
		String category = json.getString("category");
		String sendTime = json.getString("sendTime");
		JSONArray arr = json.getJSONArray("receivers");
		String source = json.getString("source");
		String userAcc = json.getString("userAcc");
		String BUSI_ID = json.getString("busiId");
		String satisfyReject = json.getString("satisfyReject");
		
		logger.info(CommonUtil.getClassNameAndMethod(this)+" 收到短信发送请求:"+json.toJSONString());
		
		//接口可以传入channelId  或者 model、category
		String chanelld = json.getString("chanelld");//渠道id
		if(StringUtils.isBlank(chanelld)){
			if(StringUtils.isBlank(model) || StringUtils.isBlank(category)){
				rjson.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				rjson.put("respDesc", "请求参数model、category为空!");
				logger.error(CommonUtil.getClassNameAndMethod(this)+"请求参数model、category为空,"+json.toJSONString());
				return rjson;
			}
			//根据事业部、品牌找渠道
			JSONObject js = channelId(model, category);
			if(null==js){
				rjson.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				rjson.put("respDesc", "不存在该渠道:model="+model+",category="+category);
				logger.error(CommonUtil.getClassNameAndMethod(this)+"不存在该渠道:model="+model+",category="+category+","+json.toJSONString());
				return rjson;
			}		
			chanelld = js.getString("ID");
		}
		
		if(StringUtils.isBlank(chanelld)){
			rjson.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			rjson.put("respDesc", "找不到短信发送渠道!");
			logger.error(CommonUtil.getClassNameAndMethod(this)+"找不到短信发送渠道,"+json.toJSONString());
			return rjson;
		}
		
		msg.put("CHANNEL_ID", chanelld);
		
		String ID = RandomKit.smsAuthCode(16);
		String createTime = DateUtil.getCurrentDateStr();
		if (StringUtils.isBlank(sendTime)) {
			sendTime = createTime;
		}
		msg.put("ID", ID);
		msg.put("TYPE", "1");
		msg.put("BUSI_ID", BUSI_ID);
		msg.put("SOURCE", source);
		msg.put("SEND_TIME", sendTime);
		if(StringUtils.isNotBlank(satisfyReject)&& StringUtils.equals(satisfyReject,"1")){
			//满意度短信拒发
			msg.put("STATUS", "99");
		}else{
			msg.put("STATUS", "1");
		}
		msg.put("NEED_SMS_RECEIPT", "1");
		msg.put("CREATE_TIME", createTime);
		if ("system".equals(userAcc)) {
			msg.put("CREATE_USER_NAME", "system");
			msg.put("CREATE_DEPT", "001");
			msg.put("CREATE_ACC", "system");
			msg.put("CREATE_NO", "system");
			msg.put("EP_CODE", "001");
		} else {
			UserModel user = UserMgr.getUserByUserAcc(userAcc, true, false, false);
			msg.put("CREATE_USER_NAME", user.getUserName());
			msg.put("CREATE_DEPT", user.getDept().getDeptCode());
			msg.put("CREATE_ACC", userAcc);
			msg.put("CREATE_NO", user.getUserNo());
			msg.put("EP_CODE", user.getEpCode());
		}
		EasyRecord record = new EasyRecord("C_SMS_INFO", new String[] { "ID" }).setColumns(msg);
		try {
			query.begin();
			query.save(record);
			JSONObject superSmsObject = new JSONObject();
			superSmsObject.put("needSuperSms", json.getString("needSuperSms"));
			superSmsObject.put("superSmsParam", json.getString("superSmsParam"));
			sendMsg(arr, msg, query,superSmsObject,satisfyReject);
			query.commit();
			rjson.put("respCode", GWConstants.RET_CODE_SUCCESS);
			rjson.put("respDesc", "发送成功");
			return rjson;
		} catch (Exception e) {
			try {
				query.roolback();
				rjson.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				rjson.put("respDesc", "发送失败");
			} catch (SQLException e1) {
				e1.printStackTrace();
			}
			logger.error(CommonUtil.getClassNameAndMethod(this)+"发送失败:"+e.getMessage(),e);
		}
		return rjson;
	}
	
	/**
	 * 处理上行短信
	 * @param json
	 * @return
	 */
	private JSONObject receiveMO(JSONObject json) {
		logger.info(CommonUtil.getClassNameAndMethod(this)+"收到上行短信:"+json.toJSONString());
		
		String inPlatform = json.getString("inPlatform");//平台
		String tenantCode = json.getString("tenantCode");//租户/商户编码
		String providerShortName = json.getString("providerShortName");//供应商简称
		String providerCode = json.getString("providerCode");//供应商代码
		String sendAccount = json.getString("sendAccount");//发送账号
		String extendNo = json.getString("extendNo");//上行扩招号
		String content = json.getString("content");//上行短信内容
		String phoneNumber = json.getString("phoneNumber");//手机号
		String sendTime = json.getString("sendTime");//发送时间
		String routeType = json.getString("routeType");//路由类型  1:美的 2：阿里
		String sequenceId = json.getString("sequenceId");//序列号
		
		JSONObject rjson = new JSONObject();
		rjson.put("command", "MO_RESPONSE");
		
		try {
			EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
			String smsUploadId = IDGenerator.getDefaultNUMID();
			//找到对应的下行短信ID
			EasySQL sql = new EasySQL();
			sql.append("SELECT ID,CHANNEL_ID FROM C_SMS_SEND_RECORD WHERE SMS_UPLOAD_ID IS NULL");
			sql.append(phoneNumber,"AND RECEIVER = ?");
			sql.append("ORDER BY SEND_TIME DESC");
			EasyRow row = query.queryForRow(sql.getSQL(), sql.getParams() );
			String smsSendRecordId = "";
			if(row!=null){
				EasySQL sql2 = new EasySQL();
				sql2.append("UPDATE C_SMS_SEND_RECORD SET SMS_UPLOAD_ID = ? WHERE ID = ?");
				smsSendRecordId = row.getColumnValue("ID");
				query.execute(sql2.getSQL(), smsUploadId,smsSendRecordId);
			}
			//插入上行短信
			JSONObject j = new JSONObject();
			j.put("ID",smsUploadId);
			j.put("SENDER",phoneNumber);
			j.put("RECEIVER","");
			j.put("CONTENT",content);
			j.put("SMS_MSG_ID","");
			j.put("SMS_INFO_ID","");
			j.put("STATUS","2");
			j.put("BAKUP","收到一条上行短信:"+sequenceId);
			j.put("SMS_MEMBER_ID","");
			j.put("CHANNEL_ID",row.getColumnValue("CHANNEL_ID"));
			j.put("CREATE_TIME",DateUtil.getCurrentDateStr());
			EasyRecord record = new EasyRecord("C_SMS_UPLOAD", "ID").setColumns(j);
			query.save(record);
			
			
			
			logger.info(CommonUtil.getClassNameAndMethod(this)+"上行短信处理完成，入库成功,ID:"+j.getString("ID")+",关联的下行短信:"+smsSendRecordId);
			
			rjson.put("moerrcode", GWConstants.RET_CODE_SUCCESS);
			return rjson;
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"上行短信处理出现异常:"+e.getMessage(),e);
			rjson.put("moerrcode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			return rjson;
		}		
	}

	/**
	 * 处理短信回执
	 * @param json
	 * @return
	 */
	private JSONObject receiveRT(JSONObject json) {
		logger.info(CommonUtil.getClassNameAndMethod(this)+"收到短信回执:"+json.toJSONString());

		String inPlatform = json.getString("inPlatform");//平台
		String tenantCode = json.getString("tenantCode");//租户/商户编码
		String tenantName = json.getString("tenantName");//租户/商户名
		String providerShortName = json.getString("providerShortName");//供应商简称
		String providerCode = json.getString("providerCode");//供应商代码
		String sendAccount = json.getString("sendAccount");//发送账号
		String phoneNumber = json.getString("phoneNumber");//手机号
		String sendTime = json.getString("sendTime");//发送时间
		String success = json.getBoolean("success") + "";//是否成功
		String errCode = json.getString("errCode");//状态报告编码
		String errMsg = json.getString("errMsg");//状态报告说明
		String batchCode = json.getString("batchCode");//发送批次号（目前为短信ID）
		String feeitem = json.getString("feeitem");//请求计费条数
		String routeType = json.getString("routeType");//路由类型  1:美的 2：阿里
		
		JSONObject rjson = new JSONObject();
		rjson.put("command", "RT_RESPONSE");
		
		try {
			EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
			EasySQL sql = new EasySQL();
			sql.append(batchCode,"SELECT * FROM C_SMS_SEND_RECORD WHERE SMS_SEND_ID=?",false);
			EasyRow row = query.queryForRow(sql.getSQL(), sql.getParams() );
			if(row==null){
				logger.warn(CommonUtil.getClassNameAndMethod(this)+"短信回执无法处理,没有找到对应短信发送id的短信记录:"+batchCode);
				rjson.put("rterrcode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				return rjson;
			}
			String id = row.getColumnValue("ID");
			
			//获取短信状态
			String smsstatus = Constants.SMS_SEND_STATUS_FAILED;
			if("true".equals(success)){
				smsstatus = Constants.SMS_SEND_STATUS_REACHED;
			}
			logger.info(CommonUtil.getClassNameAndMethod(this)+"准备写入回执信息");
			//插入回执
			JSONObject j = new JSONObject();
			j.put("ID",IDGenerator.getDefaultNUMID());
			j.put("TYPE",Constants.RECEIPT_TYPE_STATUS_REPORT);
			j.put("CONTENT","收到平台短信回执,ERRCODE="+errCode+",ERRMSG="+errMsg);
			j.put("BAKUP","");
			j.put("CREATE_TIME",DateUtil.getCurrentDateStr());
			j.put("SMS_MSG_ID",id);
			j.put("SMS_INFO_ID",row.getColumnValue("SMS_INFO_ID"));
			j.put("STATUS","2");
			j.put("ACCOUNT",sendAccount);
			j.put("PASSWD","");
			j.put("SENDER",row.getColumnValue("SENDER"));
			j.put("CHANNEL_ID",row.getColumnValue("CHANNEL_ID"));
			j.put("SPSC","");
			EasyRecord record = new EasyRecord("C_SMS_RECEIPT", "ID").setColumns(j);
			query.save(record);
			logger.info(CommonUtil.getClassNameAndMethod(this)+"回执信息写入完成");
			
			logger.info(CommonUtil.getClassNameAndMethod(this)+"准备更新短信状态");
			//更新短信状态
			EasySQL usql = new EasySQL();
			usql.append("UPDATE C_SMS_SEND_RECORD set SEND_STATUS= ? ,SEND_RESULT_DESC=? where ID=?");
			query.execute(usql.getSQL(), new Object[]{smsstatus,"ERRCODE="+errCode+",ERRMSG="+errMsg,id});
			
			logger.info(CommonUtil.getClassNameAndMethod(this)+"短信回执处理完成，更新短信状态,短信ID:"+id+",短信状态:"+smsstatus);
			
			rjson.put("rterrcode", GWConstants.RET_CODE_SUCCESS);
			return rjson;
			
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"短信回执处理出现异常:"+e.getMessage(),e);
			rjson.put("rterrcode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			return rjson;
		}
	}
	private JSONObject channelId(String model, String category)
	  {
	    EasyQuery query = EasyQuery.getQuery("smsgw", "yw-ds");
	    JSONObject jsonObject = new JSONObject();
	    String sql = "select ACCOUNT,PASSWD,ID from C_SMS_CHANNEL WHERE MODEL_ID=?  AND CATEGORY=?";
	    try
	    {
	      List<EasyRow> list = query.queryForList(sql, new Object[] { model, category });
	      if ((list != null) && (list.size() > 0))
	      {
	        jsonObject.put("ACCOUNT", ((EasyRow)list.get(0)).getColumnValue("ACCOUNT"));
	        jsonObject.put("PASSWD", ((EasyRow)list.get(0)).getColumnValue("PASSWD"));
	        jsonObject.put("ID", ((EasyRow)list.get(0)).getColumnValue("ID"));
	        return jsonObject;
	      }
	    }
	    catch (SQLException e)
	    {
	      this.logger.error("获取渠道信息," + e.getMessage(), e);
	    }
	    return null;
	  }
	  
	  private void sendMsg(JSONArray arr, JSONObject msg, EasyQuery query,JSONObject superSmsObj,String satisfyReject) throws Exception
	  {
		List list=new ArrayList<>();
	    for (int i = 0; i < arr.size(); i++)
	    {
	      String phone= arr.getJSONObject(i).getString("receiver");
	      String content= arr.getJSONObject(i).getString("content");
	      if (list.contains(phone)) {
	    	  continue;
	    	  }else{
	    	  list.add(phone);
	      }
			Pattern p = Pattern.compile("^[1][0-9][0-9]{9}$");  
			Matcher md = p.matcher(phone); 
			if(!md.matches()){				
				continue;
			}
	      String ID = RandomKit.smsAuthCode(16);
	      String CREATE_TIME = EasyCalendar.newInstance().getDateTime("-");
	      JSONObject js = new JSONObject();
	      js.put("ID", ID);
	      js.put("USER_TYPE", "1");
	      js.put("SEND_TIME", msg.getString("SEND_TIME"));
	      content=weixinService.getWeixinContent(content, phone);
	      js.put("CONTENT",content);
	      js.put("SEND_TYPE", "1");
	      js.put("RECEIVER", phone);
			if(StringUtils.isNotBlank(satisfyReject)&& StringUtils.equals(satisfyReject,"1")){
				//满意度短信拒发
				js.put("SEND_STATUS", "99");
			}else{
				js.put("SEND_STATUS", "1");
			}
	      js.put("CREATE_TIME", CREATE_TIME);
	      js.put("SMS_INFO_ID", msg.getString("ID"));
	      js.put("BUSINESS_TYPE", "1");
	      js.put("CHANNEL_ID", msg.getString("CHANNEL_ID"));
	      js.put("EP_CODE", msg.getString("EP_CODE"));
	      String needSuperSms = superSmsObj.getString("needSuperSms");
	      String superSmsParam = superSmsObj.getString("superSmsParam");
	      if(StringUtils.isNotBlank(needSuperSms)) {
	    	  js.put("NEED_SUPER_SMS", needSuperSms);//标识发送超信
			}
			if(StringUtils.isNotBlank(superSmsParam)) {
			  js.put("SUPER_JSON_PARAM", superSmsParam);//发送超信参数
			}
	      EasyRecord record = new EasyRecord("C_SMS_SEND_RECORD", new String[] { "ID" }).setColumns(js);
	      query.save(record);
	      }

	  }
	 
}
