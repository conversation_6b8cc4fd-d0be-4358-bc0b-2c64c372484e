package com.yunqu.cc.smsgw.base;

import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.util.ConfigUtil;

/**
 * 常量
 * <AUTHOR>
 *
 */
public class Constants {

	public final static String DS_NAME = "yw-ds";     //默认数据源名称，设置为nul，会取模块的default-ds数据源，设置成default-ds，则会取平台的default-ds
	
	public final static String MARS_DS_NAME = "mars-ds";     //默认数据源名称
	
	public final static String APP_NAME = "smsgw";     //应用
	
	/**
	 * 短信记录未设置渠道时，是否自动寻找对应的渠道并发送,Y-是 N-否
	 */
	public static final String AUTO_FOUND_CHANNEL = ConfigUtil.getString(APP_NAME, "AUTO_FOUND_CHANNEL",DictConstants.DICT_SY_YN_N);
	/**
	 * 是否允许发送短信,Y-是 N-否
	 */
	public static final String ALLOW_SEND_SMS = ConfigUtil.getString(APP_NAME, "ALLOW_SEND_SMS",DictConstants.DICT_SY_YN_N);
	
	/**
	 * 短信发送状态状态： 1-待发送(准备发送)
	 */
	public static final String SMS_SEND_STATUS_WAIT_SEND = "1";
	
	/**
	 * 短信发送状态状态：2-发送中(开始发送)
	 */
	public static final String SMS_SEND_STATUS_SENDING = "2";
	
	/**
	 * 短信发送状态状态：3-提交成功(到运营商)
	 */
	public static final String SMS_SEND_STATUS_SUBMIT = "3";
	
	/**
	 * 短信发送状态状态：4-发送失败 
	 */
	public static final String SMS_SEND_STATUS_FAILED = "4";
	
	/**
	 * 短信发送状态状态： 5-到用户
	 */
	public static final String SMS_SEND_STATUS_REACHED = "5";
	
	/**
	 * 短信发送状态状态：  6-按键确认 
	 */
	public static final String SMS_SEND_STATUS_CONFIRM = "6";
	
	/**
	 * 短信发送状态状态：  7-正在重发
	 */
	public static final String SMS_SEND_STATUS_RESEND = "7";
	
	
	/** 
	 * 短信发送失败后，允许重发的最大次数
	 */
	public static final int MSG_SEND_MAX_TIMES = 1;
	
	/** 
	 * 短信并发发送短信的数量(同一秒钟,运行同时发送短信的线程数量)
	 */
	public static final int MSG_SEND_NUMS_ONCE = 10;

	/**
	 * 短信发送调度运行间隔(秒)
	 */
	public static final long SEND_SMS_THREAD_INTEVAL = 10;

	/**
	 * 短信回执类型：1-状态报告
	 */
	public static final String RECEIPT_TYPE_STATUS_REPORT = "1";
	
	/**
	 * 短信回执类型：2-提交报告
	 */
	public static final String RECEIPT_TYPE_SUBMIT_REPORT = "2";
	
	public static final String COUNTRY_CODE = "86";
	
	
	/**
	 * 短信请求URL
	 */
	public static final String SMS_URL = ConfigUtil.getString(APP_NAME,"SMS_URL","http://************:9081/spreceiver/sendSms");
	/**
	 * 认证key
	 */
	public static final String APP_KEY = ConfigUtil.getString(APP_NAME, "APP_KEY","243382fe4d834befae1882ae5280f6f4");
	/**
	 * 租户/商户编码
	 */
	public static final String TENANT_CODE = ConfigUtil.getString(APP_NAME, "TENANT_CODE","T202007010001");
	/**
	 * 使用平台
	 */
	public static final String IN_PLATFORM = ConfigUtil.getString(APP_NAME, "IN_PLATFORM","CC");
	/**
	 * 加密等级
	 */
	public static final String ENCRYPT_LEVEL = ConfigUtil.getString(APP_NAME, "ENCRYPT_LEVEL","0");
	/**
	 * 路由类型
	 */
	public static final String ROUTE_TYPE = ConfigUtil.getString(APP_NAME, "ROUTE_TYPE","1");

	/**
	 * 短信发送类型:接口类普通短信
	 */
	public static final String SMS_SEND_TYPE_1 = "1";
	/**
	 * 短信发送类型:手工发送（导入）的普通短信
	 */
	public static final String SMS_SEND_TYPE_2 = "2";
}
