package com.yunqu.cc.smsgw.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.smsgw.base.CommonSendLogger;
import com.yunqu.cc.smsgw.base.Constants;
import com.yunqu.cc.smsgw.utils.SHA1Util;
/**
 * 超信服务-对接mixgw网关
 */
public class SuperSmsService {
	
	private static Logger logger = CommonSendLogger.logger;
	
	/**
	 * 检查手机号码是否支持超信
	 * @return
	 */
	public static boolean queryAimAbility(String mobile) {
		Map<String,Object> param = new HashMap<String,Object>();
		boolean result = false;
		JSONObject obj = new JSONObject();
		try {
			obj.put("command","queryAimAbility");
			ArrayList<JSONObject> mobiles = new ArrayList<>();
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("mobile", SHA1Util.getHexSHA1(Constants.COUNTRY_CODE+mobile).toLowerCase());
			mobiles.add(jsonObject);
			param.put("mobiles",mobiles);
			obj.put("params", param);
			IService service = ServiceContext.getService("MIXGW_AIM_INTEFACE");
			JSONObject resp = service.invoke(obj);
			String respCode = resp.getString("respCode");
			if("000".equals(respCode)) {
				JSONObject respData = resp.getJSONObject("respData");
				String subCode = respData.getString("subCode");
				if("0".equals(subCode)) {//成功
					JSONObject data = respData.getJSONObject("data");
					JSONArray jsonArray = data.getJSONArray("mobiles");
					JSONObject mobilesJson = jsonArray.getJSONObject(0);
					String receiveState = mobilesJson.getString("receiveState");
					if("1".equals(receiveState)) {
						result = true;
					}else {
						logger.info("IService请求失败,请求参数"+JSON.toJSONString(obj)+",手机号码不支持超信");
					}
				}else {
					logger.info("IService请求失败,请求参数"+JSON.toJSONString(obj)+",超信接口【判断是否支持超信接口】返回失败");
				}
			}else {
				logger.info("IService请求失败,请求参数"+JSON.toJSONString(obj));
			}
		} catch (Exception e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return result;
	}
	
	/**
	 * 超信短链接申请
	 * @return
	 */
	public static String applyAimUrl(String mobile,JSONObject json) {
		String result = null;
		JSONObject obj = new JSONObject();
		try {
			String tplId = json.getString("tplId");
			JSONArray smsSigns = json.getJSONArray("smsSigns");
			if(StringUtils.isBlank(mobile)) {
				logger.info("IService请求失败,请求参数"+JSON.toJSONString(json)+",缺失参数tplId");
				return null;
			}
			if(smsSigns==null||smsSigns.size()==0) {
				logger.info("IService请求失败,请求参数"+JSON.toJSONString(json)+",缺失参数smsSigns");
				return null;
			}
			JSONObject dyncParams = json.getJSONObject("dyncParams");
			ArrayList<JSONObject> paramList = new ArrayList<>();
			Map<String,Object> param = new HashMap<String,Object>();
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("custFlag", SHA1Util.getHexSHA1(mobile));
			jsonObject.put("dyncParams", dyncParams);
			paramList.add(jsonObject);
			obj.put("command","applyAimUrl");
			param.put("tplId",tplId);
			param.put("smsSigns",smsSigns);
			param.put("showTimes",2);
			param.put("aimCodeType",2);//个性化（支持动参）
			param.put("generationType",1);
			param.put("paramList",paramList);
			obj.put("params", param);
			IService service = ServiceContext.getService("MIXGW_AIM_INTEFACE");
			JSONObject resp = service.invoke(obj);
			String respCode = resp.getString("respCode");
			if("000".equals(respCode)) {
				JSONObject respData = resp.getJSONObject("respData");
				String subCode = respData.getString("subCode");
				if("0".equals(subCode)) {//成功
					JSONObject data = respData.getJSONObject("data");
					JSONArray jsonArray = data.getJSONArray("paramList");
					JSONObject aimJson = jsonArray.getJSONObject(0);
					String resultCode = aimJson.getString("resultCode");
					if("0".equals(resultCode)) {//短链申请成功
						result = aimJson.getString("aimUrl");
					}else {
						logger.info("IService请求失败,请求参数"+JSON.toJSONString(obj)+",接口返回短链申请失败");
					}
				}else {
					logger.info("IService请求失败,请求参数"+JSON.toJSONString(obj)+",超信接口【申请短链接口】返回失败");
				}
			}else {
				logger.info("IService请求失败,请求参数"+JSON.toJSONString(obj));
			}
		} catch (Exception e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return result;
	}
	
	public static void main(String[] args) {
//		System.out.println(JSONObject.parseObject("{\"smsSigns\":[\"梦网科技\"],\"showTimes\":2,\"aimCodeType\":1,\"generationType\":1,\"paramList\":[{\"custFlag\":\"8A6909809965BA92D573D5EB26538876366F33B2\",\"dyncParams\":\"{\\\"URL\\\":\\\"https://weixincs.midea.com/ccss-ipms-cis-rpc/uat/Cc/9uHWHV\\\"}\"}],\"tplId\":\"600180330\"}"));
		System.out.println(SHA1Util.getHexSHA1(Constants.COUNTRY_CODE+"18033204266").toLowerCase());
	}
}
