package com.yunqu.cc.smsgw.service;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyRow;
import org.easitline.common.utils.calendar.EasyCalendar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CharsetUtil;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;
import com.yunqu.cc.smsgw.base.CommonLogger;
import com.yunqu.cc.smsgw.base.Constants;
import com.yunqu.cc.smsgw.bean.xml.XmlSmsResp;
import com.yunqu.cc.smsgw.utils.SecretUtil;

/**
 * 操作短信发送
 * <AUTHOR>
 *
 */
public class SendSmsService {
	private Logger logger = CommonLogger.logger;
	
	/**
	 * 发送短信
	 * @param smsUrl    短信网关URL
	 * @param smsId    短信id
	 * @param sender   发送号码
	 * @param receiver  接收号码
	 * @param content   短信内容
	 * @return
	 */
	public XmlSmsResp sendSms(String smsUrl,String smsId, String sender, String receiver,
			String content,EasyRow channel) {
		
		XmlSmsResp resp = new XmlSmsResp();
		resp.setResult("false");
		
		logger.info(CommonUtil.getClassNameAndMethod(this)+" 开始发送短信,短信ID:"+smsId);
		
		try {
			
//			//检查短信发送通道：在不传url的情况下，必须传短信记录
//			String feeNumber = sender;
//			String feeType = "01";
//			String feeCode = "000000";
//			
//			if(StringUtils.isBlank(smsUrl)){
//				if(channel==null){
//					logger.error(CommonUtil.getClassNameAndMethod(this)+" 无法找到该短信的告警渠道,短信ID:"+smsId);
//					resp.setResult("没有发送渠道");
//					return resp;
//				}
//				//找到短信的告警渠道
//				smsUrl = Constants.SMS_URL;
//				feeNumber = channel.getColumnValue("FEE_NUMBER");
//				feeType = channel.getColumnValue("FEE_TYPE");
//				feeCode = channel.getColumnValue("FEE_CODE");
//			}
//			if(StringUtils.isBlank(smsUrl)){
//				resp.setResult("没有发送地址");
//				logger.error(CommonUtil.getClassNameAndMethod(this)+" 无法找到该短信的告警渠道,短信ID:"+smsId);
//				return resp;
//			}
//			
//			if(StringUtils.isBlank(feeNumber)){
//				feeNumber = sender;
//			}
//			
//			//获取通道中配置的扩展参数
//			String params = "";
//			if(channel!=null){
//				params  = channel.getColumnValue("PARAMS");
//			}
//			//通过美的网关发送短信 http://SMSHOST/sms-gateway/mt?command=MT_REQUEST&spid=test11&sppassword=123456&da=137********&dc=15&sm=c4e3bac3
//			
//			if(smsUrl.endsWith("spreceiver/sendSms")) {
//				resp = sendMideaSms2(smsUrl,smsId, receiver, content);	
//			} else{  //默认按json推送
//				resp = sendByJson(sender,receiver,smsId,content,smsUrl);
//			}
			
			if(StringUtils.isBlank(smsUrl)){
				smsUrl = channel.getColumnValue("URL");
			}
			if(StringUtils.isBlank(smsUrl)){
				resp.setResult("缺少参数：没有发送地址");
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 无法找到该短信的告警渠道,短信ID:"+smsId);
				return resp;
			}
			String tpCode = channel.getColumnValue("TP_CODE");
			if(StringUtils.isBlank(tpCode)){
				resp.setResult("缺少参数：没有发送模板");
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 无法找到该渠道模板编码,短信ID:"+smsId);
				return resp;
			}
			String signName = channel.getColumnValue("SIGN_NAME");
			if(StringUtils.isBlank(signName)){
				resp.setResult("缺少参数：没有签名");
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 无法找到该渠道的签名,短信ID:"+smsId);
				return resp;
			}
			
			JSONObject obj = channel.toJSONObject();
			logger.info("渠道信息：" + obj + ",模板编码：" +tpCode + ",签名："+signName);
			
			resp = sendMideaSms2(smsUrl,smsId, receiver, content, channel);
			
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 发送短信,短信ID:"+smsId+",接收号码:"+receiver+",content:"+content+",result:"+resp.getResult());

			return resp;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 短信发送失败,短信ID:"+smsId,e);
		}
		return resp;
	}

	/**
	 * midea专用
	 * 通过midea提供的ws接口发送短信
	 * @param smsUrl
	 * @param receiver
	 * @param content
	 * @param params  格式为  a=1&b=2 
	 * @return
	 */
	@SuppressWarnings("unused")
	@Deprecated
	private XmlSmsResp sendMideaSms(EasyRow channel,String smsUrl, String receiver,
			String content, String params) {
			
		XmlSmsResp xmlSmsResp = new XmlSmsResp();
		//拼装请求数据: http://SMSHOST/sms-gateway/mt?command=MT_REQUEST&spid=test11&sppassword=123456&da=137********&dc=15&sm=c4e3bac3
		String req = "&da="+receiver+"&dc=15&sm="+CharsetUtil.strToGbkCode(content, "GBK");
		if(StringUtils.isNotBlank(req)){
			req += "&"+params;
		}
		
		
		smsUrl = smsUrl+"?command=MT_REQUEST&spid="+channel.getColumnValue("ACCOUNT")+"&sppassword="+channel.getColumnValue("PASSWD")+"&moduleId="+channel.getColumnValue("MODEL_ID") + req;
		
		logger.info(CommonUtil.getClassNameAndMethod(this)+" 发送给Midea短信网关的请求信息:"+smsUrl+",内容:"+content);
			
		HttpResp result = HttpUtil.post(smsUrl, req,GWConstants.ENCODE_GBK);
		
		logger.info(CommonUtil.getClassNameAndMethod(this)+" 发送给Midea短信网关的结果:"+result);
		
		//返回格式如：<?xml version="1.0" encoding="utf-8"?> <string xmlns="http://tempuri.org/"><?xml version="1.0" encoding="UTF-8" ?><result><response>-6</response></result></string>
		if(result!=null && StringUtils.isNotBlank(result.getResult())){
			String resp = result.getResult();
			resp = resp.replaceAll("&lt;", "<");
			resp = resp.replaceAll("&gt;", ">");
			//得到返回：command=MT_RESPONSE&spid=1&mtmsgid=7190845625870554970&mtstat=ACCEPTD&mterrcode=000
			JSONObject json = new JSONObject();
			for(String str : resp.split("&")){
				if(StringUtils.isNotBlank(str)){
					String arr[] = str.split("=");
					json.put(arr[0], arr[1]);
				}
			}
			if("000".equals(json.getString("mterrcode"))){
				xmlSmsResp.setResult("ok");
				xmlSmsResp.setMsgId(json.getString("mtmsgid"));
			}else{
				xmlSmsResp.setResult("短信发送失败:错误状态码="+json.getString("mterrcode")+",原因:"+resp);
			}
		}else{
			xmlSmsResp.setResult("短信发送失败:接口未返回信息");
		}
		
		return xmlSmsResp;
	}
	
	/**
	 * midea专用
	 * 通过midea提供的新接口发送短信
	 * 接口返回：JSON
	 * @param smsUrl
	 * @param receiver
	 * @param content
 	 * @param smsId
	 * @return
	 */
	private XmlSmsResp sendMideaSms2(String smsUrl,String smsId,String receiver,String content,EasyRow channel) throws Exception{
		//当前时间戳
		long timestamp = EasyCalendar.newInstance().getTimeInMillis();
		JSONObject obj = new JSONObject();
		JSONObject obj2 = new JSONObject();
		JSONObject conObj = new JSONObject();
		JSONArray array = new JSONArray();
		
		conObj.put("content", content);
		obj2.put("phone",receiver);
		obj2.put("param",conObj.toJSONString());
		array.add(obj2);

		obj.put("appKey",Constants.APP_KEY);
		obj.put("timeStamp",timestamp);
		obj.put("batchNumber",smsId);
		obj.put("tenantCode",Constants.TENANT_CODE);
		obj.put("tpCode",channel.getColumnValue("TP_CODE"));
		//发送账号，必须加密
		obj.put("sendAccount",SecretUtil.encrypt(channel.getColumnValue("ACCOUNT"), timestamp + Constants.IN_PLATFORM));
		//发送密码，必须加密
		obj.put("sendPassword",SecretUtil.encrypt(channel.getColumnValue("PASSWD"), timestamp + Constants.IN_PLATFORM));
		obj.put("signName",channel.getColumnValue("SIGN_NAME"));
		obj.put("inPlatform",Constants.IN_PLATFORM);
		obj.put("encryptLevel",Constants.ENCRYPT_LEVEL);
		obj.put("routeType",Constants.ROUTE_TYPE);
		obj.put("sendList",array);
		
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 发送给Midea短信网关的请求信息:"+obj.toJSONString());
		
		HttpResp result = HttpUtil.sendPost(smsUrl, obj.toJSONString(),HttpUtil.TYPE_JSON);
		
		logger.info(CommonUtil.getClassNameAndMethod(this)+" 发送给Midea短信网关的结果:"+result.getResult());
		
		XmlSmsResp resp = new XmlSmsResp();
		
		if(result!=null && StringUtils.isNotBlank(result.getResult())){
			try {
				JSONObject json = JSONObject.parseObject(result.getResult());
				if("0000".equals(json.getString("code"))) {
					resp.setResult("ok");
				}else {
					resp.setResult("短信发送失败:" + json.getString("msg"));
				}
				resp.setData(json);
				return resp;
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "解析短信发送结果失败:" + e.getMessage(),e);
				resp.setResult("解析短信发送结果失败:"+e.getMessage());
				return resp;
			}
		}else{
			resp.setResult("短信发送失败:接口未返回信息");
			return resp;
		}
	}
	
	/**
	 * 通过普通的Json格式发送短信
	 * @param sender
	 * @param receiver
	 * @param msgId
	 * @param content
	 * @param smsUrl
	 * @return
	 */
	@SuppressWarnings("unused")
	@Deprecated
	private XmlSmsResp sendByJson(String sender, String receiver, String msgId,
			String content, String smsUrl) {
		JSONObject obj = new JSONObject();
		obj.put("msgId", msgId);
		obj.put("sender", sender);
		obj.put("receiver", receiver);
		obj.put("content", content);
		String json = obj.toJSONString();
		
		//发送给指定的url
		HttpResp result = HttpUtil.post(smsUrl, json,GWConstants.ENCODE_UTF8);
		if(result!=null && StringUtils.isNotBlank(result.getResult())){
			try {
				XmlSmsResp resp = (XmlSmsResp)JSON.toJavaObject(JSON.parseObject(result.getResult()), XmlSmsResp.class);
				return resp;
			} catch (Exception e) {
				XmlSmsResp resp = new XmlSmsResp();
				resp.setMsgId(msgId);
				resp.setResult("解析短信发送结果失败:"+resp);
				return resp;
			}
		}else{
			XmlSmsResp resp = new XmlSmsResp();
			resp.setMsgId(msgId);
			resp.setResult("false");
			return resp;
		}
	}

}
