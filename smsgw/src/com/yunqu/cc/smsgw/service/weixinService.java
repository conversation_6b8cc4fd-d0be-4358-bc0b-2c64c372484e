package com.yunqu.cc.smsgw.service;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;

import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.smsgw.base.Constants;

public class weixinService {
	protected static EasyQuery query(){
		 return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
	 }
	public static JSONObject getWeixinUrl(String mobile, String longUrl, Object path, Object appletUrl, Object appid) {
			JSONObject json = new JSONObject();
			longUrl=URLDecoder.decode(longUrl);
			json.put("command", "applet");
			JSONObject data = new JSONObject();
			List<JSONObject> list=new ArrayList<JSONObject>();
			data.put("expireTime", DateUtil.getMillseconds(DateUtil.addDay("yyyy-MM-dd HH:mm:ss",DateUtil.getCurrentDateStr() , 30))/1000);
			data.put("isExpire", "true");
			data.put("path", path);
			data.put("query", longUrl);
			data.put("appid", appid);
			data.put("appletUrl", appletUrl);
			data.put("userType", "mobile");//绑定的用户类型：openId或mobile 两值二选一
			data.put("openId", mobile);//scheme与openId或手机号绑定
			json.put("data", data);
			IService service;
			try {
				service = ServiceContext.getService("MIXGW_WCP_INTEFACE");
				JSONObject result = service.invoke(json);
				if("000".equals(result.getString("respCode"))){
					JSONObject respData = JSON.parseObject(result.getString("respData"));
					if("0".equals(respData.getString("code"))){
						String url=respData.getJSONObject("data").getString("scheme");//中控返回地址
						json = new JSONObject();
						json.put("command", "shortLink");
						data = new JSONObject();
						data.put("url", url);
						json.put("data", data);
						 service = ServiceContext.getService("CSSGW-OTHER");
						 result = service.invoke(json);
						 if("000".equals(result.getString("respCode"))){
							return  EasyResult.ok(result.getString("respData")) ;
						 }
					}
				}
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			return EasyResult.error(500,"获取小程序链接失败") ;
	}
	private static   boolean  setWeixinUrl(String msg){
		if(msg.indexOf("{WEIXIN:")>-1&&msg.indexOf("}")>-1){
			return true;
		}
		return false;
	}
	public static   String  getWeixinContent(String msg,String phone){
		try {
			while (setWeixinUrl(msg)) {
				String weixinUrl="";
				String key=msg.substring(msg.indexOf("{WEIXIN:"), msg.indexOf("}",msg.indexOf("{WEIXIN:"))+1);
				String id=msg.substring(msg.indexOf("{WEIXIN:")+8, msg.indexOf("}",msg.indexOf("{WEIXIN:")));
				if(!StringUtils.isEmpty(phone)){//有手机号时 校验是否存在小程序需要获取
					EasySQL sql = new EasySQL();
					sql.append(id,"select  *  from  C_NO_SMS_WEIXIN where KEY=? ");
					JSONObject weixin = query().queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
					JSONObject weixinData = getWeixinUrl(phone, weixin.getString("URL"), weixin.getString("APPLET_PATH"), weixin.getString("APPLET_URL"), weixin.getString("APPID"));
					if("1".equals(weixinData.getString("state"))){
						weixinUrl=weixinData.getString("data");
					}
				}
				msg=msg.replace(key, weixinUrl);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return msg;
	}
	public static void main(String[] args) {
		JSONObject json=new JSONObject();
		System.out.println(StringUtils.isEmpty(json.getString("phone")));
		json.put("phone", "1");
		System.out.println(StringUtils.isEmpty(json.getString("phone")));
		String msg="地址是{WEIXIN:1234567},地址2是{WEIXIN:12345678} 退订D";
		System.out.println(msg.substring(msg.indexOf("{WEIXIN:"), msg.indexOf("}",msg.indexOf("{WEIXIN:"))+1));
		while (setWeixinUrl(msg)) {
			String key=msg.substring(msg.indexOf("{WEIXIN:"), msg.indexOf("}",msg.indexOf("{WEIXIN:"))+1);
			String id=msg.substring(msg.indexOf("{WEIXIN:")+8, msg.indexOf("}",msg.indexOf("{WEIXIN:")));
			msg=msg.replace(key, "123");
			System.out.println(id);
		}
	}
}
