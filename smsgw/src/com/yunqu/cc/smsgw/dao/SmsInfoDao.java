package com.yunqu.cc.smsgw.dao;


import java.sql.SQLException;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.cc.smsgw.base.AppDaoContext;
import com.yunqu.cc.smsgw.base.CommonLogger;
import com.yunqu.cc.smsgw.base.Constants;
import com.yunqu.cc.smsgw.bean.SmsReceipt;

public class SmsInfoDao extends AppDaoContext {

	private Logger logger = CommonLogger.logger;
	
	public  JSONObject channelMessage(){
		EasySQL sql = this.getEasySQL("select row_number() over(ORDER BY t.ID) numindex , t.* ");
		sql.append("from C_SMS_CHANNEL t ");
		logger.debug("获取短信渠道内容,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		JSONObject obj=this.queryForPageList(sql.getSQL(), sql.getParams(),null);
		return  obj;
	}
	
	

	/**
	 * 插入短信回执
	 * @param sr
	 * @return
	 * @throws Exception 
	 */
	public String insertSmsReceipt(SmsReceipt sr) throws Exception {
		if(StringUtils.isBlank(sr.getBakup())){
			sr.setBakup("");
		}
		if(StringUtils.isBlank(sr.getSmsInfoId())){
			sr.setSmsInfoId("");
		}
		String id = IDGenerator.getDefaultNUMID();
		String currTime = DateUtil.getCurrentDateStr();
		
		StringBuffer  sql = new StringBuffer();
		sql.append(" INSERT INTO  C_SMS_RECEIPT(ID,TYPE,CONTENT,BAKUP,CREATE_TIME,SMS_MSG_ID,SMS_INFO_ID,STATUS,RECEIVER,SENDER,CHANNEL_ID)  ");
		sql.append(" VALUES (?,?,?,?,?,?,?,?,?,?,?)");
		
		Object[] args = new Object[]{id,sr.getType(),sr.getContent(),sr.getBakup(),currTime,sr.getSmsMsgId(),sr.getSmsInfoId(),sr.getStatus(),sr.getReceiver(),sr.getSender(),sr.getChannelId()};
		this.getQuery().execute(sql.toString(), args);
		return id;
	}

	/**
	 * 根据短信id，查询短信记录
	 * @param smsId
	 * @return
	 */
	public EasyRow findSmsBySmsId(String smsId) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" SELECT * FROM C_SMS_SEND_RECORD WHERE   ID = '"+smsId+"'");
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" SQL:"+sql.toString());
			return this.getQuery().queryForRow(sql.toString(), null);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询出错",e);
		}
		return null;
	}
	
	/**
	 * 根据短信id，结合短信发送渠道关联记录表，找到可以自动轮发的下一个通道(短信发送失败的情况下，查询下一个通道时，可以使用该接口)
	 * @param msgId
	 * @return
	 */
	public EasyRow findAlarmChannelBySmsBatch(String msgId ) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" SELECT T1.* FROM C_SMS_CHANNEL T1 WHERE T1.TYPE='1' AND IS_AUTO='1' AND STATUS='1'  ");
			sql.append(" AND NOT EXISTS(  SELECT 1 FROM C_SMS_CHANNEL_RECORD T2 WHERE T2.CHANNEL_ID = T1.ID AND T2.MSG_ID='"+msgId+"' ) ");
			return this.getQuery().queryForRow(sql.toString(), null);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询出错",e);
		}
		return null;
		
	}
	
	/**
	 * 查询短信发送渠道关联记录,必须指定渠道id、短信id、发送批次
	 * @param channelId     渠道id
	 * @param msgId         短信id
	 * @param smsSendBatch  发送批次
	 * @return
	 */
	public EasyRow findSmsChannelRecord(String channelId, String msgId,
			int smsSendBatch) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" SELECT * FROM C_SMS_CHANNEL_RECORD WHERE  CHANNEL_ID = '"+channelId+"' AND MSG_ID='"+msgId+"' AND SEND_BATCH = "+smsSendBatch);
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" SQL:"+sql.toString());
			return this.getQuery().queryForRow(sql.toString(), null);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询出错",e);
		}
		return null;
	}

	/**
	 * 添加短信发送渠道关联记录
	 * @param channelId    渠道id
	 * @param msgId        短信id
	 * @param smsSendBatch 发送批次
	 * @param sendTims     发送次数
	 * @return
	 * @throws Exception
	 */
	public String insertSmsChannelRecord(String channelId, String msgId,
			int smsSendBatch, int sendTims) throws Exception {
		
		try {
			StringBuffer  sql = new StringBuffer();
			sql.append(" INSERT INTO  C_SMS_CHANNEL_RECORD(SMS_CHANNEL_RECORDID,CHANNEL_ID,MSG_ID,SEND_TIMES,SEND_BATCH,CREATE_TIME)  ");
			sql.append(" VALUES (?,?,?,?,?,?)");
			String id = IDGenerator.getDefaultNUMID();
			String currTime = DateUtil.getCurrentDateStr();
			Object[] args = new Object[]{id,channelId,msgId,smsSendBatch,sendTims,currTime};
			this.getQuery().execute(sql.toString(), args);
			return id;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 操作出错",e);
		}
		return null;
	}

	/**
	 * 更新短信发送渠道关联记录的发送次数
	 * @param smsChannelRecordId  短信发送渠道关联记录id
	 * @param sendTimes           要增加的短信发送次数
	 * @throws Exception 
	 */
	public void updateSmsChannelRecord(String smsChannelRecordId, int sendTimes) throws Exception {
		try {
			StringBuffer  sql = new StringBuffer();
			sql.append(" UPDATE C_SMS_CHANNEL_RECORD SET SEND_TIMES = SEND_TIMES + "+sendTimes+"  WHERE SMS_CHANNEL_RECORDID=? ");
			Object[] args = new Object[]{smsChannelRecordId};
			this.getQuery().execute(sql.toString(), args);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 操作出错",e);
		}
	}
	
	/**
	 * 找到默认的发送渠道
	 * @return
	 */
	public EasyRow findAlarmChannel() {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" SELECT * FROM C_SMS_CHANNEL WHERE TYPE='1' AND DEFAULT_CHANEL = '1' AND STATUS='1' ");
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" SQL:"+sql.toString());
			return this.getQuery().queryForRow(sql.toString(), null);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询出错",e);
		}
		return null;
	}
	
	/**
	 * 找到id找到对应的告警渠道
	 * @param smsId
	 * @return
	 */
	public EasyRow findAlarmChannelById(String channelId) {
		
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" SELECT * FROM C_SMS_CHANNEL T1 WHERE  ");
			sql.append(" T1.ID = '"+channelId+"' ");
			return this.getQuery().queryForRow(sql.toString(), null);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询出错",e);
		}
		return null;
	}
	
	/**
	 * 查询出要发送的下行短信
	 * @return
	 * @throws SQLException 
	 */
	public List<EasyRow> findNeedSendSms(String type) throws SQLException {
		try {
			StringBuffer sql = new StringBuffer();
			String date = DateUtil.getCurrentDateStr();
			String beginDate = DateUtil.addDay(DateUtil.TIME_FORMAT, date, -5);//向前减5天
//			sql.append(" SELECT * FROM C_SMS_SEND_RECORD WHERE SEND_TIME<='"+date+"' AND SEND_STATUS in('"+Constants.SMS_SEND_STATUS_WAIT_SEND+"','"+Constants.SMS_SEND_STATUS_RESEND+"')  AND SEND_TIMES<"+Constants.MSG_SEND_MAX_TIMES);
			sql.append(" SELECT * FROM C_SMS_SEND_RECORD WHERE SEND_TIME>='"+beginDate+"' AND SEND_TIME<='"+date+"'  AND SEND_STATUS = '" + Constants.SMS_SEND_STATUS_WAIT_SEND+ "' AND SEND_TIMES<"+Constants.MSG_SEND_MAX_TIMES +" and USER_TYPE="+type);

			//logger.debug(CommonUtil.getClassNameAndMethod(this)+" SQL:"+sql.toString());
			return this.getQuery().queryForList(sql.toString(),null);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询出错",e);
		}
		return null;
		
	}
	
	
	
	/**
	 * 修改下行短信的状态
	 * @param smsId     下行短信ID
	 * @param status    短信播发状态：1-待发送 2-发送中 3-发送成功 4-发送失败
	 * @param smsSendId 短信下发id，由运营商返回，可以为空
	 * @param channelId 短信下发渠道id，为空时不做修改
	 * @param sendTimes 增加的短信发送此处
	 * @param sendResult 发送结果
	 * @throws Exception 
	 */
	public void updateSmsStatus(String smsId, String status,String smsSendId,String channelId, int sendTimes,String sendResult,String isSendSuperSms,String content) throws Exception {
		try {
			StringBuffer  sql = new StringBuffer();
			sql.append(" UPDATE C_SMS_SEND_RECORD SET SEND_STATUS=? ,SEND_RESULT_DESC=?, SEND_TIMES = SEND_TIMES+ "+sendTimes);
			if(StringUtils.isNotBlank(channelId)){
				sql.append(" , CHANNEL_ID = '"+channelId+"' ");
			}
			if(StringUtils.isNotBlank(smsSendId)){
				sql.append(" , SMS_SEND_ID = '"+smsSendId+"' ");
			}
			if("1".equals(isSendSuperSms)){//提交超信内容
				sql.append(" , SEND_SUPER_SMS = '"+isSendSuperSms+"' ");
				sql.append(" , CONTENT = '"+content+"' ");
			}
			sql.append(" WHERE ID=?");
			Object[] args = new Object[]{status,sendResult,smsId};
			
			this.getQuery().execute(sql.toString(), args);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 操作出错",e);
		}
		
	}
	
}