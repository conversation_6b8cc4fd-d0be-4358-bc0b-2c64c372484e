package com.yunqu.cc.PeakEnd.inf;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import com.yunqu.cc.PeakEnd.base.Constants;
import com.yunqu.cc.PeakEnd.enums.SubmitStates;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.util.TextUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.JsonUtil;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import java.sql.SQLException;
import java.util.*;
import java.text.SimpleDateFormat;

public class CommonService extends IService{

	private static Logger logger = CommLogger.getCommLogger("common");
	private static final Logger loggerSql = CommLogger.getCommLogger("common_sql");
	// 字典值常量
	private static final String DICT_QUOTA_NOT_ENOUGH = "额度不足";
	private static final String DICT_EXCEED_AUTHORITY = "超权限范围";

	// 字典组编码
	private static final String DICT_GROUP_MIDDLE = "NON_COMPLIANCE_REASONS";
	private static final String DICT_GROUP_AFTER = "AFTER_NON_COMPLIANCE_REASONS";

	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		logger.debug("CommonService invoke:"+json.toJSONString());
		String command = json.getString("command");
		if("PEAKEND-TIME-TO-DISTRIBUTE".equals(command)){
			return DistributeOrderService.getInstance().invoke();
		} else {
			JSONObject result = JsonUtil.createInfRespJson(json);
			result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
			result.put("respDesc", "error：不存在的command！");
			return result;
		}
	}

	/*
	 * Job模块一分钟一次调用 PeakEnd IService的接口，
	 * 接口然后查询C_NO_PEAK_END_REMIND 判断是否达到通知时间，
	 * 到达时间则获取C_NO_PEAKEND_RECORD表中一个月内的全部数据，
	 * 得到补偿记录集合，遍历处理，
	 * 转化成每个坐席的是否有 超权限范围、额度不足 事后补偿、现金补偿 集合 【超权限范围>额度不足>现金补偿24h待领>事后补偿】
	 * 遍历坐席 发送坐席弹窗信息给Notice模块
	 * 前端每分钟调用Notice模块接口获取最新弹窗消息后进行弹窗，
	 * 点击弹窗提醒可以支持跳转服务补偿页面，且默认筛选项：赠送人账号--本账号；补偿项目--现金补偿；是否补偿--已补未提；是否超时--已超时
	 * */
	public JSONObject getServicesCompensationNoticeList(JSONObject json){
		//查询补偿执行时间配置表是否达到通知时间
		EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.YW_DS);
        try {

			//查询C_NO_PEAK_END_REMIND
			EasySQL  sql = new EasySQL("SELECT * FROM C_NO_PEAK_END_REMIND");
			List<JSONObject> list = query.queryForList(sql.getSQL(),new Object[]{},new JSONMapperImpl(){});
			if (CollectionUtils.isNotEmpty(list)){
				for (JSONObject jsonObject : list){
					//提醒类型
					String remindType = jsonObject.getString("REMIND_TYPE");
					//提醒时间间隔（分钟）
					String remindTime = jsonObject.getString("REMIND_TIME");
					//上次提醒时间
					String lastRemindTime = jsonObject.getString("LAST_REMIND_TIME");
					// 解析提醒时间间隔
					int remindTimeMinutes = Integer.parseInt(remindTime);
					// 解析上次提醒时间
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					long nextRemindMillis = 0;
					if (!TextUtils.isEmpty(lastRemindTime)){
						Date lastRemindDate = sdf.parse(lastRemindTime);
						// 计算下次提醒时间
						 nextRemindMillis = lastRemindDate.getTime() + remindTimeMinutes * 60 * 1000L;
					}


					Date nextRemindDate = new Date(nextRemindMillis);
					logger.info("提醒时间间隔：" + remindTimeMinutes + "分钟" + "，下次提醒时间：" + sdf.format(nextRemindDate));
					// 当前时间
					Date now = new Date();

					if (TextUtils.isEmpty(lastRemindTime) || now.compareTo(nextRemindDate) >= 0) {
						logger.info("已到达提醒时间");
						// 已到达提醒时间
						DateTime oneMonthAgo = DateUtil.offsetMonth(DateUtil.date(), -1);
						DateTime oneDayAgo = DateUtil.offsetDay(DateUtil.date(), -1);

						String baseSqlStr = buildBaseSql();
						List<String> noticeMemberList = new ArrayList<>();
						Map<String, Map<String, List<String>>> noticeMemberMap = new HashMap<>();
						if ("1".equals(remindType)){//超权限范围
							List<JSONObject> objectList = processExceedAuthority(query,baseSqlStr,oneDayAgo);
							filterData(remindType,objectList, noticeMemberList,noticeMemberMap);
						}else if ("2".equals(remindType)){//额度不足
							List<JSONObject> objectList = processQuotaNotEnough(query,baseSqlStr,oneMonthAgo);
							filterData(remindType,objectList, noticeMemberList,noticeMemberMap);
						}else if ("3".equals(remindType)){//现金补偿24h待领
							List<JSONObject> objectList = processOneDayAgo(query,baseSqlStr,oneMonthAgo);
							filterData(remindType,objectList, noticeMemberList,noticeMemberMap);
						}else if ("4".equals(remindType)){//事后补偿
							List<JSONObject> objectList = processNotCompensated(query,baseSqlStr,oneMonthAgo,Constants.PEAKEND_TYPE2);
							filterData(remindType,objectList, noticeMemberList,noticeMemberMap);
						}

						//给每个坐席发送通知
						sendNoticeToAcc(noticeMemberList,noticeMemberMap);
						// 已到达提醒时间，更新提醒时间为当前时间
						String updateSql = "UPDATE C_NO_PEAK_END_REMIND SET LAST_REMIND_TIME = ? WHERE REMIND_TYPE = ?";
						//query.execute(updateSql, new Object[]{sdf.format(now), remindType});
					}
				}
			}

        } catch (Exception e) {
			logger.error(e.getMessage(), e);
        }
		return new EasyResult();
	}

	private void sendNoticeToAcc(List<String> noticeMemberList, Map<String, Map<String, List<String>>> noticeMemberMap) {
		logger.info("发送通知给坐席 " + noticeMemberList.size() );
		for (String noticeMember : noticeMemberList){

			Map<String, List<String>> noticeMap = noticeMemberMap.get(noticeMember);
			//超权限范围
			int exceedCount = 0;
			if (noticeMap.get(noticeMember) != null){
				exceedCount = noticeMap.get("1").size();
			}
			//额度不足
			int insufficientQuotaCount = 0;
			if (noticeMap.get("2") != null){
				insufficientQuotaCount = noticeMap.get("2").size();
			}
			//现金补偿24h待领
			int compensationDayAgoCount = 0;
			if (noticeMap.get("3") != null){
				compensationDayAgoCount = noticeMap.get("3").size();
			}
			//事后补偿
			int compensationCount = 0;
			if (noticeMap.get("4") != null){
				compensationCount = noticeMap.get("4").size();
			}
			if (exceedCount != 0){
				logger.info("发送超权限范围通知给" + noticeMember + "，数量：" + exceedCount);
				peakEndNoticePopup(noticeMember,"超权限范围" + exceedCount +"条待处理");
			}
			if (insufficientQuotaCount != 0){
				logger.info("发送额度不足通知给" + noticeMember + "，数量：" + insufficientQuotaCount);
				peakEndNoticePopup(noticeMember,"额度不足" + insufficientQuotaCount +"条待处理");
			}
			if (compensationDayAgoCount != 0){
				logger.info("发送金补偿24h待领通知给" + noticeMember + "，数量：" + compensationDayAgoCount);
				peakEndNoticePopup(noticeMember,"金补偿24h待领" + compensationDayAgoCount +"条已超时");
			}
			if (compensationCount != 0){
				logger.info("发送事后补偿通知给" + noticeMember + "，数量：" + compensationCount);
				peakEndNoticePopup(noticeMember,"事后补偿" + compensationCount +"条待处理");
			}
		}
	}

	//筛选出以CREATE_ACC为键，remindType数组为值
	private void filterData(String remindType, List<JSONObject> list, List<String> noticeList, Map<String, Map<String, List<String>>> noticeMemberMap){
		for (JSONObject jsonObject : list){
			String id = jsonObject.getString("ID");
			String createAcc = jsonObject.getString("CREATE_ACC");

			// 记录所有需要通知的账号
			if (!noticeList.contains(createAcc)) {
				noticeList.add(createAcc);
			}
			// 组织 noticeMemberMap
			Map<String, List<String>> typeMap = noticeMemberMap.get(createAcc);
			if (typeMap == null) {
				typeMap = new HashMap<>();
				noticeMemberMap.put(createAcc, typeMap);
			}
			List<String> remindList = typeMap.get(remindType);
			if (remindList == null) {
				remindList = new ArrayList<>();
				typeMap.put(remindType, remindList);
			}
			remindList.add(id);
		}
	}

	/**
	 * 处理超权限范围数据
	 */
	private List<JSONObject>  processExceedAuthority(EasyQuery query, String baseSqlStr, DateTime oneMonthAgo) throws Exception {
		EasySQL sql = new EasySQL();
		Map<String, String> dictCodes = getDictCodes(null, DICT_EXCEED_AUTHORITY);
		sql.append(DateUtil.format(oneMonthAgo, DatePattern.NORM_DATETIME_PATTERN), baseSqlStr);
		sql.append(SubmitStates.NOT_SATISFIABLE.getStates(), " AND t1.SUBMIT_STATES = ?");

		sql.append(" AND (");
		sql.append("(");
		sql.append(Constants.PEAKEND_TYPE1, "t1.COMPENSATE_TYPE = ?");
		sql.append(dictCodes.get("middle"), " AND t1.NON_CONFORMANCE_REASON = ?");
		sql.append(") OR (");
		sql.append(Constants.PEAKEND_TYPE2, "t1.COMPENSATE_TYPE = ?");
		sql.append(dictCodes.get("after"), " AND t1.NON_CONFORMANCE_REASON = ?");
		sql.append("))");
		loggerSql.info("超权限范围SQL：" + sql.getSQL() + ", 参数: " + JSON.toJSONString(sql.getParams()));
		 return query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

	}

	/**
	 * 处理额度不足数据
	 */
	private List<JSONObject> processQuotaNotEnough(EasyQuery query, String baseSqlStr, DateTime oneMonthAgo) throws Exception {
		Map<String, String> dictCodes = getDictCodes(null, DICT_QUOTA_NOT_ENOUGH);
		EasySQL sql = new EasySQL();
		sql.append(DateUtil.format(oneMonthAgo, DatePattern.NORM_DATETIME_PATTERN), baseSqlStr);
		sql.append(SubmitStates.NOT_SATISFIABLE.getStates(), " AND t1.SUBMIT_STATES = ?");

		sql.append(" AND (");
		sql.append("(");
		sql.append(Constants.PEAKEND_TYPE1, "t1.COMPENSATE_TYPE = ?");
		sql.append(dictCodes.get("middle"), " AND t1.NON_CONFORMANCE_REASON = ?");
		sql.append(") OR (");
		sql.append(Constants.PEAKEND_TYPE2, "t1.COMPENSATE_TYPE = ?");
		sql.append(dictCodes.get("after"), " AND t1.NON_CONFORMANCE_REASON = ?");
		sql.append("))");
		loggerSql.info("额度不足SQL：" + sql.getSQL() + ", 参数: " + JSON.toJSONString(sql.getParams()));
		return query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

	}

	/**
	 * 现金24小时待领
	 */
	private List<JSONObject> processOneDayAgo(EasyQuery query, String baseSqlStr, DateTime oneMonthAgo) throws Exception {
		// 现金24小时待领
		DateTime oneDayAgo = DateUtil.offsetDay(DateUtil.date(), -1);

		EasySQL sql = new EasySQL();
		sql.append(DateUtil.format(oneMonthAgo, DatePattern.NORM_DATETIME_PATTERN), baseSqlStr);
		sql.append(Constants.COMPENSATE_MODE7, " AND t1.COMPENSATE_MODE = ?");
		sql.append(SubmitStates.SAVE_BUT_NOT_SUBMIT.getStates(), " AND t1.SUBMIT_STATES = ?");
		sql.append(DateUtil.format(oneDayAgo, DatePattern.NORM_DATETIME_PATTERN), " AND t1.CREATE_TIME <= ?");
		loggerSql.info("现金24小时待领SQL：" + sql.getSQL() + ", 参数: " + JSON.toJSONString(sql.getParams()));
		return query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());


	}


	/**
	 * 处理应补未补数据
	 */
	private List<JSONObject> processNotCompensated(EasyQuery query,String baseSqlStr, DateTime oneMonthAgo,
									   String compensateType) throws Exception {
		EasySQL sql = new EasySQL();
		sql.append(DateUtil.format(oneMonthAgo, DatePattern.NORM_DATETIME_PATTERN), baseSqlStr);
		sql.append(compensateType, " AND COMPENSATE_TYPE = ?");
		sql.append(SubmitStates.NOT_SAVE.getStates(), " AND SUBMIT_STATES = ?");
		loggerSql.info("待办处理应补未补SQL：" + sql.getSQL() + ", 参数: " + JSON.toJSONString(sql.getParams()));
		return query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

	}

	private String buildBaseSql() {
		return "SELECT * FROM C_NO_PEAK_END_RECORD t1 " +
				"WHERE 1=1 " +
				"AND t1.CREATE_TIME >= ? " +
				"AND t1.TIME_OUT_STATE = '1' ";
	}


	private void  peakEndNoticePopup(String userAcc,String content){
		if (!TextUtils.isBlank(userAcc) && !TextUtils.isBlank(content)){
			try {
				logger.info("服务补偿通知弹窗 " + userAcc + " " + content);
				IService service = ServiceContext.getService(ServiceID.NOTICE_INTERFACE);
				JSONObject j= new JSONObject();
				j.put("sender", "notice");
				j.put("password", "YQ_85521717");
				j.put("serialId", IDGenerator.getDefaultNUMID());
				j.put("command", ServiceCommand.NOTICE_ADD_USER_NOTICE); // serviceId:  ServiceID.NOTICE_INTERFACE;
				j.put("receiverType", "01");//接收类型 01-个人  02-	部门   03-	所有人
				j.put("userAcc", userAcc);//接收通知的人员，如有多个，用;隔开
				j.put("deptCode", "000");//
				j.put("createUserAcc", "system");//
				j.put("createUserDeptCode", "000");//
				j.put("type", "紧急公告");//固定写这个
				j.put("module", Constants.APP_NAME);
				j.put("title", "服务补偿通知");
				j.put("content", "您有服务补偿单据待跟进:" + content );
				j.put("url", "/"); //返回这条公告的访问链接地址，是该条公告的地址
				j.put("method", "02");
				logger.info("服务补偿通知弹窗信息：" + j.toString());
				JSONObject jsonObject =	service.invoke(j);
				logger.info("服务补偿通知弹窗结果：" + jsonObject.toString());
			} catch (Exception e) {
				logger.error("服务补偿通知弹窗失败，原因：" + e.getMessage(), e);
			}
		}

	}
	/**
	 * 获取字典值编码
	 */
	private Map<String, String> getDictCodes(String deptCode, String dictValue) {
		Map<String, String> result = new HashMap<>();
		if ( dictValue == null) {
			return result;
		}

		Map<String, Object> middleReasonMap = Optional.ofNullable(
						DictCache.getMapEnableDictListByGroupCode(deptCode == null?"001":deptCode, DICT_GROUP_MIDDLE))
				.orElse(new HashMap<>());

		Map<String, Object> afterReasonMap = Optional.ofNullable(
						DictCache.getMapEnableDictListByGroupCode(deptCode == null?"001":deptCode, DICT_GROUP_AFTER))
				.orElse(new HashMap<>());

		for (Map.Entry<String, Object> entry : middleReasonMap.entrySet()) {
			if (dictValue.equals(entry.getValue())) {
				result.put("middle", entry.getKey());
				break;
			}
		}

		for (Map.Entry<String, Object> entry : afterReasonMap.entrySet()) {
			if (dictValue.equals(entry.getValue())) {
				result.put("after", entry.getKey());
				break;
			}
		}

		return result;
	}
}
