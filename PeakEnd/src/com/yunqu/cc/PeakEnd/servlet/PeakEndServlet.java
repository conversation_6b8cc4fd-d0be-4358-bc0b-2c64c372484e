package com.yunqu.cc.PeakEnd.servlet;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.stream.StreamUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.model.RoleModel;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.OrgUtil;
import com.yunqu.cc.PeakEnd.base.AppBaseServlet;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import com.yunqu.cc.PeakEnd.base.Constants;
import com.yunqu.cc.PeakEnd.enums.SubmitStates;
import com.yunqu.cc.PeakEnd.service.CommService;
import com.yunqu.cc.PeakEnd.utils.DateUtil;
import com.yunqu.openapi.utils.OpenApiUserUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("serial")
@MultipartConfig
@WebServlet(urlPatterns = {"/servlet/peakEnd", "/openServlet/peakEnd"})
public class PeakEndServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;
    private Logger logger = CommLogger.logger;
    private Logger loggerSync = CommLogger.getCommLogger("sync");
    private Logger oplogger = CommLogger.getCommLogger("operation");// 操作记录每次操作入参和结果日志（开始结束） 请勿瞎填写
    private Logger compLogger = CommLogger.getCommLogger("compensate");
    private Logger submitLogger = CommLogger.getCommLogger("submit");

    private Logger submitTestLogger = CommLogger.getCommLogger("test");

    // 字典值常量
    private static final String DICT_QUOTA_NOT_ENOUGH = "额度不足";
    private static final String DICT_EXCEED_AUTHORITY = "超权限范围";

    // 字典组编码
    private static final String DICT_GROUP_MIDDLE = "NON_COMPLIANCE_REASONS";
    private static final String DICT_GROUP_AFTER = "AFTER_NON_COMPLIANCE_REASONS";

    public String actionForPeakEndDetail() {
        JSONObject json = this.getJSONObject();
        String id = this.getRequest().getParameter("id");
        String sql = "select CONTACT_ORDER,CUSTOMER_MOBILEPHONE1,ORG_CODE,ARCHIVES_NO,SERVICE_ORDER_NO,CUSTOMER_CODE,PROD_CODE,BRAND_CODE,ORDER_SERV_TYPE_CODE,ORDER_SER_ITEM2_CODE,CONVERSION_TYPE,SERVICE_ORDER_NO from C_NO_PEAK_END_ORDER where ID= ?  ";
        try {
            JSONObject order = this.getQuery().queryForRow(sql, new Object[]{id}, new JSONMapperImpl());
            if (order != null) {
                this.setAttr("CONTACT_ORDER", order.getString("CONTACT_ORDER"));
                this.setAttr("CUSTOMER_MOBILEPHONE1", order.getString("CUSTOMER_MOBILEPHONE1"));
                this.setAttr("ORG_CODE", order.getString("ORG_CODE"));
                this.setAttr("PROD_CODE", order.getString("PROD_CODE"));
                this.setAttr("BRAND_CODE", order.getString("BRAND_CODE"));
                this.setAttr("ORDER_SERV_TYPE_CODE", order.getString("ORDER_SERV_TYPE_CODE"));
                this.setAttr("ORDER_SER_ITEM2_CODE", order.getString("ORDER_SER_ITEM2_CODE"));
                this.setAttr("ARCHIVES_NO", order.getString("ARCHIVES_NO"));
                this.setAttr("SERVICE_ORDER_NO", order.getString("SERVICE_ORDER_NO"));
                this.setAttr("CUSTOMER_CODE", order.getString("CUSTOMER_CODE"));
            }
            if (!"".equals(order.getString("CONVERSION_TYPE"))) {// 折算类型
                sql = "select  COEFFICIENT  from  C_NO_CONVERT  where ID=? ";
                JSONObject json1 = new JSONObject();
                json1.put("ID", order.getString("CONVERSION_TYPE"));
                EasyRecord record1 = new EasyRecord("C_NO_CONVERT", "ID").setColumns(json1);
                Map<String, String> map = new HashMap<String, String>();
                try {
                    map = this.getQuery().findById(record1);
                } catch (SQLException e) {
                    e.printStackTrace();
                }
                String coefficient = map == null ? "1.00" : map.get("COEFFICIENT").toString();
                this.setAttr("coefficient", coefficient);
                this.setAttr("coefficientId", order.getString("CONVERSION_TYPE"));
            } else {
                this.setAttr("coefficientId", "0");
                this.setAttr("coefficient", "1.00");
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }
        this.setAttr("startTime", EasyDate.getCurrentDateString());
        this.setAttr("userAcc", OpenApiUserUtil.getUser(this.getRequest()).getUserAcc());
        this.setAttr("orderId", id);
        return "/pages/peakEnd/compensate-detail.jsp";
    }

    public String actionForPeakEndResult() {
        JSONObject json = this.getJSONObject();
        String id = this.getRequest().getParameter("id");
        String orderId = "";
        try {
            String sql = "select * from C_NO_PEAK_END_RESULT where ID= ?  ";
            JSONObject result = this.getQuery().queryForRow(sql, new Object[]{id}, new JSONMapperImpl());
            if (result != null) {
                orderId = result.getString("ORDER_ID");
                this.setAttr("startTime", result.getString("VISIT_TIME"));
                this.setAttr("userAcc", result.getString("VISIT_ACC"));
                this.setAttr("VISIT_RESULT", result.getString("VISIT_RESULT"));
                this.setAttr("VISIT_CONTENT", result.getString("VISIT_CONTENT"));
                this.setAttr("RESPONSIBLE", result.getString("RESPONSIBLE"));
            }
            sql = "select CONTACT_ORDER,CUSTOMER_MOBILEPHONE1,ORG_CODE,ARCHIVES_NO,SERVICE_ORDER_NO,CUSTOMER_CODE,PROD_CODE,BRAND_CODE,ORDER_SERV_TYPE_CODE,ORDER_SER_ITEM2_CODE,CONVERSION_TYPE,SERVICE_ORDER_NO from C_NO_PEAK_END_ORDER where ID= ?  ";
            JSONObject order = this.getQuery().queryForRow(sql, new Object[]{orderId}, new JSONMapperImpl());
            if (order != null) {
                this.setAttr("CONTACT_ORDER", order.getString("CONTACT_ORDER"));
                this.setAttr("CUSTOMER_MOBILEPHONE1", order.getString("CUSTOMER_MOBILEPHONE1"));
                this.setAttr("ORG_CODE", order.getString("ORG_CODE"));
                this.setAttr("PROD_CODE", order.getString("PROD_CODE"));
                this.setAttr("BRAND_CODE", order.getString("BRAND_CODE"));
                this.setAttr("ORDER_SERV_TYPE_CODE", order.getString("ORDER_SERV_TYPE_CODE"));
                this.setAttr("ORDER_SER_ITEM2_CODE", order.getString("ORDER_SER_ITEM2_CODE"));
                this.setAttr("ARCHIVES_NO", order.getString("ARCHIVES_NO"));
                this.setAttr("SERVICE_ORDER_NO", order.getString("SERVICE_ORDER_NO"));
                this.setAttr("CUSTOMER_CODE", order.getString("CUSTOMER_CODE"));
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }
        this.setAttr("resultId", id);
        this.setAttr("orderId", orderId);
        return "/pages/peakEnd/compensate-detail.jsp";
    }

    public EasyResult actionForGetLevel() {
        JSONObject result = new JSONObject();
        JSONObject pcObj = this.getJSONObject();
        JSONObject request = new JSONObject();
        request.put("command", "getLevel");
        JSONObject data = new JSONObject();
        JSONObject restParams = new JSONObject();
        data.put("sourceSys", "CC");
        data.put("brand", "1");
        data.put("mobile", pcObj.get("phone"));
        restParams.put("restParams", data);
        request.put("params", restParams);
        try {
            IService service = ServiceContext.getService("MIXGW_MCSP_INTEFACE");
            result = service.invoke(request);
        } catch (ServiceException e) {
            logger.error("IService请求失败,请求参数" + JSON.toJSONString(request) + ",原因" + e.getMessage());
        }
        return EasyResult.ok(result.get("respData"));
    }

    /**
     * 商品列表
     *
     * @return
     */
    public EasyResult actionForProductlist() {
        JSONObject result = new JSONObject();
        JSONObject json = this.getJSONObject();
        JSONObject request = new JSONObject();
        request.put("command", "queryproductlistbyscene");
        JSONObject data = new JSONObject();
        JSONObject restParams = new JSONObject();
        data.put("nChannel", 14);
        data.put("nPageNum", 1);
        data.put("nPageSize", 20);
        data.put("nSceneId", 4);
        request.put("params", restParams);
        try {
            IService service = ServiceContext.getService("MIXGW_OTHER_INTEFACE");
            result = service.invoke(request);
        } catch (ServiceException e) {
            logger.error("IService请求失败,请求参数" + JSON.toJSONString(request) + ",原因" + e.getMessage());
        }
        JSONObject respData = result.getJSONObject("respData");
        respData.put("scriptData", GetScript(json));
        return EasyResult.ok(respData);
    }

    public EasyResult actionForProductCompensate() {
        JSONObject reqJson = this.getJSONObject();
        String opId = RandomKit.randomStr();
        compLogger.info("<" + opId + "> 【实物补偿提交】入参 <<< " + reqJson.toJSONString());
        try {
            // 基本参数获取和验证
            UserModel user = OpenApiUserUtil.getUser(this.getRequest());
            user = Optional.ofNullable(user).orElse(new UserModel());
            String sysTime = EasyDate.getCurrentDateString();
            JSONArray typeArray = reqJson.getJSONArray("type");
            String phone = reqJson.getString("phone");
            String customerName = reqJson.getString("customerName");
            String responsible = reqJson.getString("responsible");
            String orgCode = reqJson.getString("orgCode");
            String prodCode = reqJson.getString("prodCode");
            orgCode = OrgUtil.getCcOrgCode(orgCode, prodCode);// cc主体
            String orgIntegralId = reqJson.getString("orgIntegralId");

            // 验证事业部积分
            if (StrUtil.isBlank(orgIntegralId) && typeArray.contains("1")) {
                return EasyResult.error(500, "当前事业部无法进行补偿");
            }

            // 处理contactOrderCode
            String contactOrderCode = reqJson.getString("contactOrderCode");
            reqJson.put("contactOrderCode",
                    StrUtil.blankToDefault(contactOrderCode, reqJson.getString("contactRecordId")));
            if (StringUtils.isNotBlank(reqJson.getString("contactOrderCode"))
                    && StringUtils.isBlank(reqJson.getString("contactOrderNo"))) {
                reqJson.put("contactOrderNo", reqJson.getString("contactOrderCode"));
            }
            EasyQuery query = this.getQuery();
            String outOrderId = RandomKit.randomStr();
            boolean opBoolean = true;// 操作是否都成功
            String failContent = "";// 失败内容
            String peakEndRecordId = "";
            String contactRecordId = reqJson.getString("contactRecordId"); // 暂存单ID
            String contactOrderNo = reqJson.getString("contactOrderNo"); // 接入单号
            String contactOrderId = reqJson.getString("CONTACT_ORDER_ID"); // 补偿记录关联的接入单ID

            // 构建补偿信息数据
            JSONObject peakEndInfo = buildCompensationInfoData(reqJson, sysTime, phone, customerName, user,
                    responsible, orgCode, outOrderId, orgIntegralId);

            // 处理不同类型的补偿
            for (Object type : typeArray) {
                // 实物补偿
                if (Constants.COMPENSATE_MODE1.equals(type.toString())) {
                    peakEndRecordId = handleProductCompensation(reqJson, query, outOrderId, opId, contactRecordId,
                            contactOrderNo, contactOrderId, peakEndInfo, user);
                    compLogger.info("<" + opId + ">【实物补偿】实物补偿完成，补偿记录ID（peakEndRecordId）：" + peakEndRecordId);
                }
                // 现金补偿
                else if (Constants.COMPENSATE_MODE7.equals(type.toString())) {
                    try {
                        boolean success = handleCashCompensation(reqJson, query, outOrderId, opId, contactRecordId,
                                contactOrderNo, peakEndInfo, orgIntegralId, user);
                        if (!success) {
                            opBoolean = false;
                            failContent = failContent + "现金补偿记录失败";
                        } else {
                            failContent = failContent + "现金补偿记录成功";
                        }
                    } catch (Exception e) {
                        opBoolean = false;
                        compLogger.error("<" + opId + ">【现金补偿提交】现金补偿失败，异常信息：" + e.getMessage(), e);
                        failContent = failContent + " 现金补偿失败(" + e.getMessage() + ")";
                    }
                }
            }

            // 处理工单状态更新
            if (opBoolean) {
                EasyResult easyResult = updateOrderStatus(reqJson, user, sysTime, responsible, peakEndRecordId);
                if (easyResult.getState() == 1) {
                    easyResult.put("peakEndRecordId", peakEndRecordId); // 补偿记录ID
                }
                return easyResult;
            } else {
                return EasyResult.error(500, failContent);
            }
        } catch (Exception e) {
            compLogger.error("<" + opId + ">【实物补偿提交】补偿异常", e);
            return EasyResult.error(500, "补偿异常" + e.getMessage());
        }
    }

    /**
     * 服务补偿提交
     */
    public EasyResult actionForCompensateMode1() {
        JSONObject reqJson = this.getJSONObject();
        String opId = RandomKit.randomStr();
        submitLogger.info("<" + opId + ">【提交工单保存实物补偿】入参:" + reqJson.toJSONString());
        try {
            String contactRecordId = reqJson.getString("contactRecordId");
            String contactOrderNo = reqJson.getString("contactOrderNo");
            if (StringUtils.isNotBlank(reqJson.getString("contactOrderCode")) && StringUtils.isBlank(contactOrderNo)) {
                reqJson.put("contactOrderNo", reqJson.getString("contactOrderCode"));
                contactOrderNo = reqJson.getString("contactOrderCode");
                submitLogger.info("<" + opId + ">【提交工单保存实物补偿】contactOrderNo:" + contactOrderNo);
            }
            String orgCode = reqJson.getString("orgCode");
            String prodCode = reqJson.getString("prodCode");
            String phone = reqJson.getString("phone");
            String peakEndRecordId = reqJson.getString("peakEndRecordId");
            // 参数验证
            if (StringUtils.isBlank(phone) && StringUtils.isBlank(contactOrderNo)
                    && StringUtils.isBlank(contactRecordId) && StringUtils.isBlank(peakEndRecordId)) {
                return EasyResult.fail("参数不能为空");
            }
            // 获取数据库连接和用户信息
            EasyQuery query = getQuery();
            UserModel user = OpenApiUserUtil.getUser(this.getRequest());
            user = Optional.ofNullable(user).orElse(new UserModel());
            String sysTime = EasyDate.getCurrentDateString();
            String customerName = reqJson.getString("customerName");
            String responsible = reqJson.getString("responsible");

            // 获取CC主体
            orgCode = OrgUtil.getCcOrgCode(orgCode, prodCode);
            String orgIntegralId = reqJson.getString("orgIntegralId");
            String outOrderId = RandomKit.randomStr();
            JSONObject infoData = buildCompensationInfoData(reqJson, sysTime, phone, customerName, user, responsible, orgCode, outOrderId, orgIntegralId);
            submitLogger.info("<" + opId + ">【提交工单保存实物补偿】构建新对象信息：" + infoData.toJSONString());
            List<JSONObject> peakEndList = queryPeakEndRecords(query, peakEndRecordId, contactRecordId, contactOrderNo, opId);
            if ((StringUtils.isBlank(contactOrderNo) && StringUtils.isBlank(peakEndRecordId)) || CollUtil.isEmpty(peakEndList)) {
                // 没有补偿直接暂存 或者 找不到补偿记录的场景，新增补偿记录
                infoData.put("ID", RandomKit.randomStr());
                infoData.put("COMPENSATE_MODE", reqJson.getString("compensateMode"));
                infoData.put("COMPENSATE_TYPE", reqJson.getString("compensateType"));
                infoData.put("SUBMIT_STATES", SubmitStates.NOT_SAVE.getStates());// 应补未补

                EasyRecord record = new EasyRecord("C_NO_PEAK_END_RECORD", "ID").setColumns(infoData);
                query.save(record);

                submitLogger.info("<" + opId + ">【提交工单保存实物补偿】补偿记录不存在，新增实物补偿记录（应补未补）");
            } else {
                //todo 存在记录的情况
                JSONObject peakEndRecord = peakEndList.stream().findFirst().get();
                if (SubmitStates.SAVE_BUT_NOT_SUBMIT.getStates().equals(peakEndRecord.getString("SUBMIT_STATES"))) {
                    submitLogger.info("<" + opId + ">【提交工单保存实物补偿】补偿记录已存在，状态为已补未提，更新记录");
                    JSONObject exJson = parseExistingJson(peakEndRecord.getString("EX_JSON"));
                    // 如果状态是"已补未提"，则提交补偿
                    exJson.put("contactOrderNo", contactOrderNo);
                    JSONObject submitJson = JSON.parseObject(JSON.toJSONString(exJson), JSONObject.class);
                    this.compensationApply(submitJson);
                    submitLogger.info("<" + opId + ">【提交工单保存实物补偿】补偿记录存在且未提交，提交补偿到售后成功（已补偿）");
                    // 修改补偿记录信息
                    peakEndRecord.put("CREATE_TIME", sysTime);
                    this.updatePeakEndRecord(query, peakEndRecord, reqJson, exJson, SubmitStates.SUBMITTED);
                    updateNonConformanceList(query, peakEndList, opId, user);
                } else if (SubmitStates.NOT_SATISFIABLE.getStates().equals(peakEndRecord.getString("SUBMIT_STATES"))
                        && StrUtil.isNotBlank(peakEndRecordId)) {
                    submitLogger.info("<" + opId + ">【提交工单保存实物补偿】此记录为坐席提交不符合补偿再提交工单，只更新单号");
                    this.updatePeakEndRecord(query, peakEndRecord, reqJson, peakEndRecord.getJSONObject("EX_JSON"), null);
                } else if (SubmitStates.SUBMITTED.getStates().equals(peakEndRecord.getString("SUBMIT_STATES"))
                        && StrUtil.isNotBlank(peakEndRecordId)) {
                    submitLogger.info("<" + opId + ">【提交工单保存实物补偿】此记录为坐席提交已补偿再提交工单，只更新单号");
                    this.updatePeakEndRecord(query, peakEndRecord, reqJson, peakEndRecord.getJSONObject("EX_JSON"), null);
                } else if (SubmitStates.SUBMITTED.getStates().equals(peakEndRecord.getString("SUBMIT_STATES"))
                        && StrUtil.isNotBlank(contactRecordId)) {
                    submitLogger.info("<" + opId + ">【提交工单保存实物补偿】此记录为坐席暂存单带入直接提交工单，只更新单号");
                    this.updatePeakEndRecord(query, peakEndRecord, reqJson, peakEndRecord.getJSONObject("EX_JSON"), null);
                } else {
                    submitLogger.info("<" + opId + ">【提交工单保存实物补偿】补偿记录已存在，状态不为已补未提，新增应补未补");
                    infoData.put("ID", RandomKit.randomStr());
                    infoData.put("COMPENSATE_MODE", reqJson.getString("compensateMode"));
                    infoData.put("COMPENSATE_TYPE", reqJson.getString("compensateType"));
                    infoData.put("SUBMIT_STATES", SubmitStates.NOT_SAVE.getStates());// 应补未补

                    EasyRecord record = new EasyRecord("C_NO_PEAK_END_RECORD", "ID").setColumns(infoData);
                    query.save(record);
                    submitLogger.info("<" + opId + ">【提交工单保存实物补偿】补偿记录已存在，状态不为已补未提，新增应补未补成功");
                }
            }
        } catch (Exception e) {
            submitLogger.info("<" + opId + ">【提交工单保存实物补偿】异常");
            submitLogger.error("<" + opId + ">【提交工单保存实物补偿】异常:" + e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    /**
     * 处理实物补偿
     */
    private String handleProductCompensation(JSONObject reqJson, EasyQuery query, String outOrderId, String opId,
                                             String contactRecordId, String contactOrderNo, String contactOrderId,
                                             JSONObject peakEndInfo, UserModel user) throws Exception {
        try {
            // 直接使用全大写加下划线形式的键名从JSON对象中获取值
            String peakEndRecordId = reqJson.getString("peakEndRecordId"); // 实物补偿记录ID
            if (StringUtils.isNotBlank(contactOrderNo)) {
                // 如果接入单号不为空，更新为真实接入单号
                contactOrderId = contactOrderNo;
            }

            // 查询补偿记录
            List<JSONObject> peakEndList = queryPeakEndRecords(query, peakEndRecordId, contactRecordId, contactOrderNo,
                    opId);

            if (CollUtil.isEmpty(peakEndList)
                    || StrUtil.equals(SubmitStates.SUBMITTED.getStates(),
                    peakEndList.stream().findFirst().get().getString("SUBMIT_STATES"))) {
                // 1.1 号码对应的产品没有过实物补偿记录 --新增实物补偿记录（已补未提）
                JSONObject newPeakEndInfo = JSONObject.parseObject(peakEndInfo.toJSONString());// 重新new
                // 订单信息
                newPeakEndInfo.put("OUT_ORDER_ID", outOrderId);
                newPeakEndInfo.put("COMPENSATE_ID", reqJson.get("productId"));
                newPeakEndInfo.put("COMPENSATE_NO", reqJson.get("productId"));
                newPeakEndInfo.put("COMPENSATE_NAME", reqJson.get("productName"));
                newPeakEndInfo.put("COMPENSATE_AMOUNT", reqJson.getDoubleValue("productAmount"));
                newPeakEndInfo.put("ORG_INTEGRAL_ID", reqJson.getString("orgIntegralId"));
                newPeakEndInfo.put("EX_JSON", reqJson.toJSONString());

                newPeakEndInfo.put("SUBMIT_STATES", SubmitStates.SAVE_BUT_NOT_SUBMIT.getStates());
                newPeakEndInfo.put("CONTACT_ORDER_ID", contactOrderId);
                peakEndRecordId = savePeakEndRecord(query, newPeakEndInfo, outOrderId);
                compLogger.info("<" + opId + ">【实物补偿提交】该单号没有实物补偿记录，生成已补未提记录，暂存单号：" + contactRecordId + " 接入单号:"
                        + contactOrderNo + "补偿单号：" + peakEndRecordId);
            } else {
                List<JSONObject> filterPeakEndList = peakEndList.stream()
                        .filter(item -> StrUtil.equalsAny(item.getString("SUBMIT_STATES"),
                                SubmitStates.NOT_SAVE.getStates(), SubmitStates.NOT_SATISFIABLE.getStates()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(filterPeakEndList)) {
                    // 保存一条已补偿记录
                    JSONObject newPeakEndInfo = JSONObject.parseObject(peakEndInfo.toJSONString());// 重新new
                    newPeakEndInfo.put("SUBMIT_STATES", SubmitStates.SUBMITTED.getStates());
                    newPeakEndInfo.put("CONTACT_ORDER_ID", contactOrderId);
                    peakEndRecordId = savePeakEndRecord(query, newPeakEndInfo, outOrderId);
                    newPeakEndInfo.put("compensateItems", reqJson.getString("compensateItems"));
                    newPeakEndInfo.put("compensateDeatils", reqJson.getString("compensateDeatils"));
                    // 提交售后
                    compensationApply(reqJson);
                }

                // 更新已有记录状态
                updateExistingRecordsStatus(query, filterPeakEndList, opId);
                updateNonConformanceList(query, peakEndList, opId, user);
            }
            return peakEndRecordId;
        } catch (Exception e) {
            compLogger.error("<" + opId + ">【实物补偿】实物补偿失败，异常信息：" + e.getMessage(), e);
            throw e;
        }
    }


    /**
     * 单据变为已补偿时，更新不符合补偿为xxx-已处理
     * @param query
     * @param peakEndList
     * @param opId
     * @param user
     * @throws Exception
     */
    private void updateNonConformanceList(EasyQuery query, List<JSONObject> peakEndList, String opId, UserModel user) throws Exception {
        //更新前面不符合单据的记录
        List<JSONObject> nonConformanceList = peakEndList.stream()
                .filter(item -> {
                    String peakEndState = item.getString("SUBMIT_STATES");
                    return SubmitStates.NOT_SATISFIABLE.getStates().equals(peakEndState);
                })
                .collect(Collectors.toList());
        updateExistingRecordsStatus(query, nonConformanceList, opId);
    }

    /**
     * 处理现金补偿
     */
    private boolean handleCashCompensation(JSONObject reqJson, EasyQuery query, String outOrderId, String opId,
                                           String contactRecordId, String contactOrderNo, JSONObject peakEndInfo,
                                           String orgIntegralId, UserModel user) throws Exception {
        try {
            // 现金补偿
            JSONObject cashCompensationResult = compensationApply(reqJson);
            compLogger.info("<" + opId + ">【现金补偿】现金补偿结果：" + cashCompensationResult);
            String cashCompensationStr = cashCompensationResult.getString("message");
            if (StringUtils.isBlank(cashCompensationStr)) {
                JSONObject jsonObject = JSONObject.parseObject(peakEndInfo.toJSONString());// 重新new
                JSONObject returnObject = cashCompensationResult.getJSONObject("returnObject");
                if (returnObject != null && returnObject.containsKey("compensateOrderNo")) {
                    outOrderId = returnObject.getString("compensateOrderNo");
                }
                // 订单信息
                jsonObject.put("OUT_ORDER_ID", outOrderId);
                jsonObject.put("COMPENSATE_ID", reqJson.get("productId"));
                jsonObject.put("COMPENSATE_NO", reqJson.get("productId"));
                jsonObject.put("COMPENSATE_NAME", reqJson.get("productName"));
                jsonObject.put("COMPENSATE_AMOUNT", reqJson.getDoubleValue("compensateCash"));
                jsonObject.put("ORG_INTEGRAL_ID", orgIntegralId);
                jsonObject.put("EX_JSON", cashCompensationResult.getJSONObject("params").toJSONString());
                // 接入单：
                boolean setSendLog = setSendLog(query, reqJson.getString("compensateType"),
                        Constants.COMPENSATE_MODE7, jsonObject, opId);
                if (setSendLog) {
                    // 处理历史单据
                    List<JSONObject> peakEndList = queryPeakEndRecords(query, "", contactRecordId, contactOrderNo,
                            opId);
                    List<JSONObject> filterPeakEndList = peakEndList.stream()
                            .filter(item -> StrUtil.equalsAny(item.getString("SUBMIT_STATES"),
                                    SubmitStates.NOT_SAVE.getStates(), SubmitStates.NOT_SATISFIABLE.getStates()))
                            .collect(Collectors.toList());

                    // 更新状态
                    updateExistingRecordsStatus(query, filterPeakEndList, opId);
                    updateNonConformanceList(query, peakEndList, opId, user);
                    return true;
                } else {
                    compLogger.info("<" + opId + ">【现金补偿提交】现金补偿记录失败，请检查");
                    return false;
                }
            } else {
                compLogger.info("<" + opId + ">【现金补偿提交】现金补偿失败，请检查");
                return false;
            }
        } catch (Exception e) {
            compLogger.error("<" + opId + ">【现金补偿提交】现金补偿失败，异常信息：" + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 查询补偿记录
     */
    private List<JSONObject> queryPeakEndRecords(EasyQuery query, String peakEndRecordId, String contactRecordId,
                                                 String contactOrderNo, String opId) throws SQLException {
        List<JSONObject> peakEndList = new ArrayList<>();
        EasySQL sql = new EasySQL("SELECT * FROM C_NO_PEAK_END_RECORD WHERE 1 = 1 ");
        if (StringUtils.isNotBlank(peakEndRecordId)) {
            // 如果实物补偿记录ID不为空，则直接用这个查（可能是接入单录入，还没暂存或者提交，直接补偿的场景；或者是这个场景之后暂存或者提交工单回写工单号的场景）
            sql.append(peakEndRecordId, "AND ID = ?");
        } else if (StringUtils.isNotBlank(contactRecordId) && StringUtils.isNotBlank(contactOrderNo)) {
            // 如果暂存单ID和接入单号不为空，则根据这两个字段查询
            sql.append(contactRecordId,
                    "AND CONTACT_ORDER_ID IN ('" + contactRecordId + "','" + contactOrderNo + "') ");
        } else if (StringUtils.isNotBlank(contactRecordId) && StringUtils.isBlank(contactOrderNo)) {
            // 如果暂存单ID不为空，则根据这个ID查询
            sql.append(contactRecordId, "AND CONTACT_ORDER_ID = ? ");
        } else if (StringUtils.isBlank(contactRecordId) && StringUtils.isNotBlank(contactOrderNo)) {
            // 如果接入单号不为空，则根据这个接入单号查询
            sql.append(contactOrderNo, "AND CONTACT_ORDER_ID = ? ");
        }

        if (StringUtils.isAllBlank(peakEndRecordId, contactRecordId, contactOrderNo)) {
            compLogger.info("<" + opId + ">【实物补偿提交】查询实物补偿记录，补偿记录ID、接入单号为空，不执行查询");
            return peakEndList;
        }

        String beforeDateTime = DateUtil.getBeforeDateTime(Constants.PEAK_END_VALIDITY_MONTH);
        sql.append(beforeDateTime, " AND CREATE_TIME > ? ");
        sql.append(" ORDER BY CREATE_TIME DESC ");
        compLogger.info("<" + opId + ">【实物补偿】查询是否已存在记录：sql = " + sql.getSQL() + "; param = "
                + Arrays.toString(sql.getParams()));
        return query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
    }

    /**
     * 更新已有记录状态
     */
    private void updateExistingRecordsStatus(EasyQuery query, List<JSONObject> filterPeakEndList, String opId)
            throws SQLException {
        for (int i = 0; i < filterPeakEndList.size(); i++) {
            // 更新为xxx-已处理
            String stateValue = filterPeakEndList.get(i).getString("SUBMIT_STATES");
            String id = filterPeakEndList.get(i).getString("ID");
            SubmitStates descByStates = SubmitStates.getDescByStates(stateValue);
            if (descByStates == null) {
                compLogger.info("<" + opId + ">【补偿提交】更新状态失败，状态值不存在," + "当前状态为：" + stateValue);
                continue;
            }
            String stateStr = descByStates.getDesc() + "-已处理";
            EasySQL sql = new EasySQL();
            sql.append("UPDATE C_NO_PEAK_END_RECORD SET SUBMIT_STATES = ? WHERE ID = ?");
            SubmitStates updateStatesByDesc = SubmitStates.getStatesByDesc(stateStr);
            if (updateStatesByDesc == null) {
                compLogger.info("<" + opId + ">【补偿提交】更新状态失败，状态值不存在," + "需要更新为状态：" + stateStr);
                continue;
            }
            String updateStateValue = updateStatesByDesc.getStates();
            compLogger.info("<" + opId + ">【补偿提交】更新状态sql:" + sql.getSQL() + "; param:"
                    + JSON.toJSONString(new Object[]{updateStateValue, id}));
            int count = query.executeUpdate(sql.getSQL(), new Object[]{updateStateValue, id});
            compLogger.info("<" + opId + ">【补偿提交】更新状态成功，更新条数：" + count);
        }
    }

    /**
     * 更新工单状态
     */
    private EasyResult updateOrderStatus(JSONObject reqJson, UserModel user, String sysTime, String responsible,
                                         String peakEndRecordId) {
        EasyQuery query = this.getQuery();
        try {
            if (reqJson.get("compensateType").equals("2")) {// 事后补偿
                query.begin();
                // 更新当前峰终工单
                EasyRecord orderRecord = new EasyRecord("C_NO_PEAK_END_ORDER", "ID");
                orderRecord.set("RESULT_CONTENT", reqJson.get("remarks"));
                orderRecord.set("RESULT", reqJson.get("result"));
                orderRecord.set("RESPONSIBLE", reqJson.get("responsible"));
                orderRecord.set("COMPENSATE_TYPE", reqJson.get("compensateType"));
                orderRecord.set("ID", reqJson.get("id"));
                orderRecord.set("RESULT_TIME", sysTime);
                orderRecord.set("RESULT_ACC", user.getUserAcc());
                query.update(orderRecord);

                // 历史的结果改无效
                EasyRecord resultValidlyRecord = new EasyRecord("C_NO_PEAK_END_RESULT", "ORDER_ID");
                resultValidlyRecord.set("ORDER_ID", reqJson.get("id"));
                resultValidlyRecord.set("VALIDLY", "N");
                query.update(resultValidlyRecord);

                // 记录结果
                EasyRecord resultRecord = new EasyRecord("C_NO_PEAK_END_RESULT", "ID");
                resultRecord.set("ID", RandomKit.randomStr());
                resultRecord.set("VISIT_CONTENT", reqJson.get("remarks"));
                resultRecord.set("VISIT_RESULT", reqJson.get("result"));
                resultRecord.set("ORDER_ID", reqJson.get("id"));
                resultRecord.set("VISIT_TIME", sysTime);
                resultRecord.set("VISIT_ACC", user.getUserAcc());
                resultRecord.set("VALIDLY", "Y");
                resultRecord.set("CALL_ID", reqJson.get("uids"));
                resultRecord.set("CONVERSION_VALUE", reqJson.get("coefficient"));
                resultRecord.set("RESPONSIBLE", responsible);
                query.save(resultRecord);

                query.commit();
            }

            return EasyResult.ok(peakEndRecordId);
        } catch (SQLException e) {
            try {
                query.roolback();
            } catch (SQLException e1) {
                logger.error(e1.getMessage(), e1);
            }
            logger.error(e.getMessage(), e);
            return EasyResult.error(500, "赠送成功 工单状态修改失败");
        }
    }

    /**
     * 解析现有JSON数据，处理null值情况
     *
     * @param jsonStr JSON字符串
     * @return 解析后的JSON对象
     */
    private JSONObject parseExistingJson(String jsonStr) {
        if (StringUtils.isBlank(jsonStr)) {
            return new JSONObject();
        }
        try {
            return JSON.parseObject(jsonStr);
        } catch (Exception e) {
            logger.warn("解析JSON异常: " + e.getMessage());
            return new JSONObject();
        }
    }

    /**
     * 构建补偿信息数据
     */
    private JSONObject buildCompensationInfoData(JSONObject json, String sysTime, String phone, String customerName,
                                                 UserModel user, String responsible, String orgCode, String outOrderId, String orgIntegralId) {
        JSONObject infoData = new JSONObject();

        // 基本信息
        infoData.put("CREATE_TIME", sysTime);
        infoData.put("PHONE", phone);
        infoData.put("CUSTOMER_NAME", customerName);
        infoData.put("CREATE_ACC", user.getUserAcc());
        infoData.put("RESPONSIBLE", responsible);

        // 区域信息
        infoData.put("AREA_NAME", json.get("areaName"));
        infoData.put("AREA_CODE", json.get("areaCode"));
        infoData.put("ORG_CODE", orgCode);

        // 产品信息
        infoData.put("PROD_NAME", json.get("prodName"));
        infoData.put("PROD_CODE", json.get("prodCode"));
        infoData.put("BRAND_CODE", json.get("brandCode"));
        infoData.put("BRAND_NAME", json.get("brandName"));

        // 服务类型信息
        infoData.put("ORDER_SERV_TYPE_NAME", json.get("orderServTypeName"));
        infoData.put("ORDER_SERV_TYPE_CODE", json.get("orderServTypeCode"));
        infoData.put("ORDER_SER_ITEM1_NAME", json.get("orderSerItem1Name"));
        infoData.put("ORDER_SER_ITEM1_CODE", json.get("orderSerItem1Code"));
        infoData.put("ORDER_SER_ITEM2_NAME", json.get("orderSerItem2Name"));
        infoData.put("ORDER_SER_ITEM2_CODE", json.get("orderSerItem2Code"));

        // 订单信息
        infoData.put("SERVICE_ORDER_NO", json.get("serviceOrderNo"));
        infoData.put("CONTACT_ORDER_ID", json.getString("contactOrderNo"));
        infoData.put("VISIT_RESULT", json.get("result"));
        infoData.put("REMARKS", json.get("remarks"));
        infoData.put("ADDRESS", json.get("customerAddress"));
        infoData.put("AREA_NUM", json.get("areaNum"));
        infoData.put("SCENE_TYPE", json.get("sceneType"));
        infoData.put("CKIENT_NAME", json.get("ckientName"));
        infoData.put("CKIENT_CODE", json.get("ckientCode"));

        // 补偿信息
        infoData.put("OUT_ORDER_ID", outOrderId);
        infoData.put("COMPENSATE_ID", json.get("productId"));
        infoData.put("COMPENSATE_NO", json.get("productId"));
        infoData.put("COMPENSATE_NAME", json.get("productName"));
        infoData.put("COMPENSATE_AMOUNT", json.getDoubleValue("productAmount"));
        infoData.put("ORG_INTEGRAL_ID", orgIntegralId);
        infoData.put("COMPENSATE_TYPE", json.getString("compensateType"));
        infoData.put("COMPENSATE_MODE", json.getString("compensateMode"));
        return infoData;
    }

    private void updatePeakEndRecord(EasyQuery query, JSONObject peakEndRow, JSONObject json, JSONObject exJson,
                                     SubmitStates submitStates) throws SQLException {
        String serviceOrderNo = json.getString("serviceOrderNo");
        String contactOrderNo = json.getString("contactOrderNo");
        String orgCode = json.getString("orgCode");
        String prodCode = json.getString("prodCode");
        String phone = json.getString("phone");
        String peakEndRecordId = json.getString("peakEndRecordId");
        String userAcc = OpenApiUserUtil.getUser(this.getRequest()).getUserAcc();
        String customerName = json.getString("customerName");
        String responsible = json.getString("responsible");
        orgCode = OrgUtil.getCcOrgCode(orgCode, prodCode);// cc主体
        String orgIntegralId = json.getString("orgIntegralId");
        String outOrderId = RandomKit.randomStr();

        JSONObject infoData = new JSONObject();// 其他信息
        // 基本信息
        infoData.put("CREATE_TIME", peakEndRow.getString("CREATE_TIME"));
        infoData.put("PHONE", phone);
        infoData.put("CUSTOMER_NAME", customerName);
        infoData.put("CREATE_ACC", userAcc);
        infoData.put("RESPONSIBLE", responsible);
        infoData.put("AREA_NAME", json.get("areaName"));
        infoData.put("AREA_CODE", json.get("areaCode"));
        infoData.put("ORG_CODE", orgCode);
        infoData.put("PROD_NAME", json.get("prodName"));
        infoData.put("PROD_CODE", json.get("prodCode"));
        infoData.put("BRAND_CODE", json.get("brandCode"));
        infoData.put("BRAND_NAME", json.get("brandName"));
        infoData.put("ORDER_SERV_TYPE_NAME", json.get("orderServTypeName"));
        infoData.put("ORDER_SERV_TYPE_CODE", json.get("orderServTypeCode"));
        infoData.put("ORDER_SER_ITEM1_NAME", json.get("orderSerItem1Name"));
        infoData.put("ORDER_SER_ITEM1_CODE", json.get("orderSerItem1Code"));
        infoData.put("ORDER_SER_ITEM2_NAME", json.get("orderSerItem2Name"));
        infoData.put("ORDER_SER_ITEM2_CODE", json.get("orderSerItem2Code"));
        infoData.put("SERVICE_ORDER_NO", serviceOrderNo);
        infoData.put("CONTACT_ORDER_ID", contactOrderNo);
        infoData.put("VISIT_RESULT", json.get("result"));
        infoData.put("REMARKS", json.get("remarks"));
        infoData.put("ADDRESS", json.get("customerAddress"));
        infoData.put("AREA_NUM", json.get("areaNum"));
        infoData.put("SCENE_TYPE", json.get("sceneType"));
        infoData.put("CKIENT_NAME", json.get("ckientName"));
        infoData.put("CKIENT_CODE", json.get("ckientCode"));
        infoData.put("OUT_ORDER_ID", outOrderId);
        exJson = Optional.ofNullable(exJson).orElse(new JSONObject());
        JSONArray jsonArray = Optional.ofNullable(exJson.getJSONArray("compensateDeatils")).orElse(new JSONArray());
        if (!jsonArray.isEmpty()) {
            infoData.put("COMPENSATE_ID", jsonArray.getJSONObject(0).getString("disSkuId"));
            infoData.put("COMPENSATE_NO", jsonArray.getJSONObject(0).getString("disSkuId"));
            infoData.put("COMPENSATE_NAME", jsonArray.getJSONObject(0).getString("disSkuTitle"));
            infoData.put("COMPENSATE_AMOUNT", jsonArray.getJSONObject(0).getDoubleValue("salePrice"));
        }
        infoData.put("ORG_INTEGRAL_ID", orgIntegralId);
        EasyRecord record = new EasyRecord("C_NO_PEAK_END_RECORD", "ID").setColumns(infoData);
        record.set("ID", peakEndRow.getString("ID"));
        record.set("CONTACT_ORDER_ID", contactOrderNo);
        if (submitStates != null) {
            record.set("SUBMIT_STATES", submitStates.getStates());
        }
        if (StrUtil.isNotBlank(json.getString("compensateType"))) {
            record.set("COMPENSATE_TYPE", json.getString("compensateType"));
        }
        if (StrUtil.isNotBlank(json.getString("compensateMode"))) {
            record.set("COMPENSATE_MODE", json.getString("compensateMode"));
        }
        query.update(record);
    }

    private List<EasyRow> searchPeakRecordList(EasyQuery query, String orgCode, String prodCode, String phone,
                                               String orderServTypeCode, String orderSerItem2Code, String peakEndRecordId, String contactRecordId,
                                               String contactOrderNo, String serialId) throws SQLException {
        EasySQL sql = new EasySQL("SELECT * FROM C_NO_PEAK_END_RECORD WHERE 1 = 1 ");
        // sql.append(orgCode, "AND ORG_CODE = ?");
        // sql.append(prodCode, "AND PROD_CODE = ?");
        // sql.append(phone, "AND PHONE = ?");
        // sql.append(orderServTypeCode, "AND ORDER_SERV_TYPE_CODE = ?");
        // sql.append(orderSerItem2Code, "AND ORDER_SER_ITEM2_CODE = ?");
        // sql.append(SubmitStates.NOT_SATISFIABLE.getStates(), "AND SUBMIT_STATES !=
        // ?");
        // sql.append(Constants.COMPENSATE_MODE1, "AND COMPENSATE_MODE = ?");
        if (StringUtils.isNotBlank(peakEndRecordId)) {
            // 如果实物补偿记录ID不为空，则直接用这个查（可能是接入单录入，还没暂存或者提交，直接补偿的场景；或者是这个场景之后暂存或者提交工单回写工单号的场景）
            sql.append(peakEndRecordId, "AND ID = ?");
        } else if (StringUtils.isNotBlank(contactRecordId) && StringUtils.isNotBlank(contactOrderNo)) {
            // 如果暂存单ID和接入单号不为空，则根据这两个字段查询
            sql.append(contactRecordId,
                    "AND CONTACT_ORDER_ID IN ('" + contactRecordId + "','" + contactOrderNo + "') ");
        } else if (StringUtils.isNotBlank(contactRecordId) && StringUtils.isBlank(contactOrderNo)) {
            // 如果暂存单ID不为空，则根据这个ID查询
            sql.append(contactRecordId, "AND CONTACT_ORDER_ID = ? ");
        } else if (StringUtils.isBlank(contactRecordId) && StringUtils.isNotBlank(contactOrderNo)) {
            // 如果接入单号不为空，则根据这个接入单号查询
            sql.append(contactOrderNo, "AND CONTACT_ORDER_ID = ? ");
        }
        // else if (StringUtils.isNotBlank(contactRecordId) ||
        // StringUtils.isNotBlank(contactOrderNo)){
        // //如果暂存单ID或者接入单号不为空，则根据这两个字段查询
        // sql.append("AND CONTACT_ORDER_ID IN ('"+ contactRecordId +"','"+
        // contactOrderNo +"')");
        // }
        else {
            logger.info("<" + serialId + ">【实物补偿】查询实物补偿记录失败，补偿记录ID或工单为空");
            return Collections.emptyList();
        }
        String beforeDateTime = DateUtil.getBeforeDateTime(Constants.PEAK_END_VALIDITY_MONTH);
        sql.append(beforeDateTime, " AND CREATE_TIME > ? ");
        logger.info("<" + serialId + ">【实物补偿】查询是否已存在记录：sql = " + sql.getSQL() + "; param = "
                + Arrays.toString(sql.getParams()));
        List<EasyRow> easyRows = query.queryForList(sql.getSQL(), sql.getParams());
        return easyRows;
    }

    private JSONObject buildInfoData(JSONObject json, String sysTime, String phone, String customerName, String userAcc,
                                     String responsible, String orgCode, String outOrderId) {
        JSONObject infoData = new JSONObject();// 其他信息
        // 基本信息
        infoData.put("CREATE_TIME", sysTime);
        infoData.put("PHONE", phone);
        infoData.put("CUSTOMER_NAME", customerName);
        infoData.put("CREATE_ACC", userAcc);
        infoData.put("RESPONSIBLE", responsible);
        infoData.put("AREA_NAME", json.get("AREA_NAME"));
        infoData.put("AREA_CODE", json.get("AREA_CODE"));
        infoData.put("ORG_CODE", orgCode);
        infoData.put("PROD_NAME", json.get("PROD_NAME"));
        infoData.put("PROD_CODE", json.get("PROD_CODE"));
        infoData.put("BRAND_CODE", json.get("BRAND_CODE"));
        infoData.put("BRAND_NAME", json.get("BRAND_NAME"));
        infoData.put("ORDER_SERV_TYPE_NAME", json.get("ORDER_SERV_TYPE_NAME"));
        infoData.put("ORDER_SERV_TYPE_CODE", json.get("ORDER_SERV_TYPE_CODE"));
        infoData.put("ORDER_SER_ITEM1_NAME", json.get("ORDER_SER_ITEM1_NAME"));
        infoData.put("ORDER_SER_ITEM1_CODE", json.get("ORDER_SER_ITEM1_CODE"));
        infoData.put("ORDER_SER_ITEM2_NAME", json.get("ORDER_SER_ITEM2_NAME"));
        infoData.put("ORDER_SER_ITEM2_CODE", json.get("ORDER_SER_ITEM2_CODE"));
        infoData.put("SERVICE_ORDER_NO", json.get("SERVICE_ORDER_NO"));
        infoData.put("CONTACT_ORDER_ID", json.getString("CONTACT_ORDER_ID"));
        infoData.put("VISIT_RESULT", json.get("VISIT_RESULT"));
        infoData.put("REMARKS", json.get("REMARKS"));
        infoData.put("ADDRESS", json.get("ADDRESS"));
        infoData.put("AREA_NUM", json.get("AREA_NUM"));
        infoData.put("SCENE_TYPE", json.get("SCENE_TYPE"));
        infoData.put("CKIENT_NAME", json.get("CKIENT_NAME"));
        infoData.put("CKIENT_CODE", json.get("CKIENT_CODE"));
        infoData.put("OUT_ORDER_ID", outOrderId);
        infoData.put("COMPENSATE_ID", json.get("COMPENSATE_ID"));
        infoData.put("COMPENSATE_NO", json.get("COMPENSATE_NO"));
        infoData.put("COMPENSATE_NAME", json.get("COMPENSATE_NAME"));
        oplogger.info("[9] - COMPENSATE_AMOUNT" + json.getDoubleValue("COMPENSATE_AMOUNT"));
        infoData.put("COMPENSATE_AMOUNT", json.getDoubleValue("COMPENSATE_AMOUNT"));
        oplogger.info("[10] - COMPENSATE_AMOUNT" + infoData.getDoubleValue("COMPENSATE_AMOUNT"));
        infoData.put("ORG_INTEGRAL_ID", json.getString("ORG_INTEGRAL_ID"));
        return infoData;
    }

    private String savePeakEndRecord(EasyQuery query, JSONObject json, String outOrderId) throws SQLException {
        String peakEndRecordId = RandomKit.randomStr();
        String sysTime = EasyDate.getCurrentDateString();
        String orgCode = json.getString("ORG_CODE");
        String prodCode = json.getString("PROD_CODE");
        // 对orgCode进行额外处理
        orgCode = OrgUtil.getCcOrgCode(orgCode, prodCode); // cc主体

        JSONObject infoData = JSONObject.parseObject(json.toJSONString());// 其他信息
        // 基本信息
        infoData.put("CREATE_TIME", sysTime);
        infoData.put("PHONE", json.getString("PHONE"));
        infoData.put("CUSTOMER_NAME", json.getString("CUSTOMER_NAME"));
        infoData.put("CREATE_ACC", json.getString("CREATE_ACC"));
        infoData.put("RESPONSIBLE", json.getString("RESPONSIBLE"));
        infoData.put("AREA_NAME", json.get("AREA_NAME"));
        infoData.put("AREA_CODE", json.get("AREA_CODE"));
        infoData.put("ORG_CODE", orgCode);
        infoData.put("PROD_NAME", json.get("PROD_NAME"));
        infoData.put("PROD_CODE", json.get("PROD_CODE"));
        infoData.put("BRAND_CODE", json.get("BRAND_CODE"));
        infoData.put("BRAND_NAME", json.get("BRAND_NAME"));
        infoData.put("ORDER_SERV_TYPE_NAME", json.get("ORDER_SERV_TYPE_NAME"));
        infoData.put("ORDER_SERV_TYPE_CODE", json.get("ORDER_SERV_TYPE_CODE"));
        infoData.put("ORDER_SER_ITEM1_NAME", json.get("ORDER_SER_ITEM1_NAME"));
        infoData.put("ORDER_SER_ITEM1_CODE", json.get("ORDER_SER_ITEM1_CODE"));
        infoData.put("ORDER_SER_ITEM2_NAME", json.get("ORDER_SER_ITEM2_NAME"));
        infoData.put("ORDER_SER_ITEM2_CODE", json.get("ORDER_SER_ITEM2_CODE"));
        infoData.put("SERVICE_ORDER_NO", json.get("SERVICE_ORDER_NO"));
        infoData.put("CONTACT_ORDER_ID", json.getString("CONTACT_ORDER_ID"));
        infoData.put("VISIT_RESULT", json.get("VISIT_RESULT"));
        infoData.put("REMARKS", json.get("REMARKS"));
        infoData.put("ADDRESS", json.get("ADDRESS"));
        infoData.put("AREA_NUM", json.get("AREA_NUM"));
        infoData.put("SCENE_TYPE", json.get("SCENE_TYPE"));
        infoData.put("CKIENT_NAME", json.get("CKIENT_NAME"));
        infoData.put("CKIENT_CODE", json.get("CKIENT_CODE"));
        infoData.put("OUT_ORDER_ID", outOrderId);
        infoData.put("COMPENSATE_ID", json.get("COMPENSATE_ID"));
        infoData.put("COMPENSATE_NO", json.get("COMPENSATE_NO"));
        infoData.put("COMPENSATE_NAME", json.get("COMPENSATE_NAME"));
        infoData.put("COMPENSATE_AMOUNT", json.getDoubleValue("COMPENSATE_AMOUNT"));
        infoData.put("ORG_INTEGRAL_ID", json.getString("ORG_INTEGRAL_ID"));
        infoData.put("ID", peakEndRecordId);

        EasyRecord record = new EasyRecord("C_NO_PEAK_END_RECORD", "ID").setColumns(infoData);
        query.save(record);
        return peakEndRecordId;
    }

    /**
     * 下单功能
     *
     * @param json
     * @param outOrderId
     */
    public String doexchage(JSONObject json, String outOrderId) {
        JSONObject pcObj = this.getJSONObject();
        JSONObject request = new JSONObject();
        request.put("command", "doexchage");
        JSONObject data = new JSONObject();
        JSONObject Query = new JSONObject();
        String appid = Constants.DOEXCHANGESCENE_APPID;
        String key = Constants.DOEXCHANGESCENE_KEY;
        JSONObject body = new JSONObject();
        // body.put("Channel", 14);//渠道
        // body.put("SceneId", 4); //场景id
        body.put("exchangeCode", json.get("productId")); // 商品兑换码
        body.put("num", 1);
        body.put("deliveryName", json.get("customerName"));
        body.put("deliveryMobile", json.get("phone"));
        body.put("deliveryAddress", json.get("customerAddress"));
        body.put("deliveryArea", json.get("areaName"));

        body.put("userId", "0");
        body.put("platform", "7");
        body.put("BizId", outOrderId); // 订单号
        body.put("deliveryProvinceCode", 0); // 默认值
        body.put("deliveryCityCode", 0);// 默认值
        body.put("deliveryCountyCode", 0);// 默认值
        body.put("deliveryTownCode", 0);// 默认值

        List<JSONObject> list1 = new ArrayList<JSONObject>();
        // 不要问我为什么怎么写 这破接口要我有序；顺序错不行
        JSONArray list = new JSONArray();
        // JSONObject.
        // list.add(new JSONObject(){"FsystemType":0});
        String nonceid = outOrderId;
        data.put("appid", appid);
        data.put("nonceid", nonceid);
        data.put("source", "cc");
        data.put("version", "1.0");
        String bizargs = "{\"FsystemType\":0,\"ExchangeId\":\"" + json.get("productId") + "\",\"Channel\":" + 14
                + ",\"ProvinceCode\":0,\"CityCode\":0,\"CountryCode\":0,\"TownCode\":0,\"MailName\":\""
                + json.get("customerName") + "\",\"MailMobile\":\"" + json.get("phone") + "\",\"MailArea\":\""
                + json.get("areaName") + "\",\"MailAddr\":\"" + json.get("customerAddress") + "\",\"BizId\":\""
                + outOrderId + "\",\"SceneId\":" + 4 + "}";
        String str = "appid=" + appid + "&bizargs=" + body.toJSONString() + "&nonceid=" + nonceid + "&source=" + "cc"
                + "&version=" + "1.0" + "&key=" + key;
        String sign = SecureUtil.md5(str);
        data.put("sign", sign);
        request.put("data", data);
        request.put("body", body);
        try {
            IService service = ServiceContext.getService("MIXGW_OTHER_INTEFACE");
            JSONObject result = service.invoke(request);
            if (result != null && Constants.GATEWAY_SUCCESS_CODE.equals(result.getString("respCode"))) {
                oplogger.info("实物赠送成功" + json.toJSONString());
                return "";
            }
            oplogger.info("实物赠送失败" + json.toJSONString());
            return result.getString("respData");
        } catch (ServiceException e) {
            logger.error("IService请求失败,请求参数" + JSON.toJSONString(request) + ",原因" + e.getMessage());
            return e.getMessage();
        }

    }

    /**
     * 延保卡发放功能
     *
     * @param json
     * @param outOrderId
     */
    public JSONObject goldcardGive(JSONObject json, String outOrderId) {
        JSONObject req = this.getJSONObject();
        req.put("state", false);
        JSONObject userMachineHistory = json.getJSONObject("userMachineHistory");
        JSONObject request = new JSONObject();
        request.put("command", "goldcardGive");
        JSONObject data = new JSONObject();
        data.put("customerName", json.get("customerName")); // 商品兑换码
        data.put("telephone1", json.get("phone"));
        data.put("areaCode", userMachineHistory.get("areaCode"));
        data.put("areaName", userMachineHistory.get("areaName"));
        data.put("customerAddress", userMachineHistory.get("customerAddress"));
        data.put("orgCode", userMachineHistory.get("orgCode"));
        data.put("brandCode", userMachineHistory.get("brandCode"));
        data.put("brandName", userMachineHistory.get("brandName"));
        data.put("prodCode", userMachineHistory.get("prodCode"));
        data.put("prodName", userMachineHistory.get("prodName"));
        data.put("purchaseDate", userMachineHistory.get("purchaseDate"));
        data.put("insideBarcode", userMachineHistory.get("insideBarcode"));
        data.put("outsideBarcode", userMachineHistory.get("outsideBarcode"));
        data.put("purchaseAmount", "999"); // 购机金额 默认填写999
        data.put("haveInvoiceFlag", "N"); // 是否有购机发票 默认填写N
        data.put("sourceSystem", "CC"); // 来源系统 CC
        data.put("optionMan", OpenApiUserUtil.getUser(this.getRequest()).getUserAcc());
        request.put("data", data);
        try {
            IService service = ServiceContext.getService("CSSGW-OTHER");
            JSONObject result = service.invoke(request);
            if (result != null && Constants.GATEWAY_SUCCESS_CODE.equals(result.getString("respCode"))) {
                if ("0".equals(result.getJSONObject("respData").getString("code"))) {
                    oplogger.info("延保卡赠送成功" + result.getJSONObject("respData").toJSONString());
                    req.put("state", true);
                    req.put("data", result.getJSONObject("respData").getString("cardNo"));
                    return req;
                } else {
                    oplogger.info("延保卡赠送失败" + result.toJSONString());
                    req.put("msg", result.getJSONObject("respData").getString("msg"));
                    return req;
                }

            } else {
                oplogger.info("延保卡赠送失败" + Optional.ofNullable(result).orElse(new JSONObject()).toJSONString());
                req.put("msg", result.getString(json.toJSONString()));
            }
            return req;
        } catch (ServiceException e) {
            logger.error("IService请求失败,请求参数" + JSON.toJSONString(request) + ",原因" + e.getMessage());
            req.put("msg", e.getMessage());
            return req;
        }

    }

    /**
     * 会员赠送
     *
     * @param json
     * @param outOrderId
     */
    public boolean checkGrowthRule(JSONObject json, String outOrderId) {
        int addGrowValue = 1000;// 需要增加的积分
        if (json.get("growValue") != null && json.getInteger("growValue") >= 0 && json.getInteger("growValue") < 1001) {
            addGrowValue = 1001 - json.getInteger("growValue");
        }
        // addGrowValue=30;//测试时候每次加20分
        JSONObject data = new JSONObject();
        JSONObject restParams = new JSONObject();
        JSONObject request = new JSONObject();
        request.put("command", "checkGrowthRule");
        data.put("serialNumber", IDGenerator.getDefaultNUMID());
        data.put("mobile", json.get("phone"));
        data.put("type", "1");
        data.put("sourceSys", "CC");
        data.put("ruleType", "2");
        data.put("brand", "1");
        data.put("growValue", addGrowValue);
        data.put("taskId", Constants.TCHECK_GROWTH_RULE_TASKID);
        restParams.put("restParams", data);
        request.put("params", restParams);
        try {
            IService service = ServiceContext.getService("MIXGW_MCSP_INTEFACE");
            JSONObject result = service.invoke(request);
            if (result != null && Constants.GATEWAY_SUCCESS_CODE.equals(result.getString("respCode"))) {
                oplogger.info("会员赠送成功" + json.toJSONString());
                return true;
            } else {
                oplogger.info("会员赠送失败" + result.toJSONString());
            }
        } catch (ServiceException e) {
            logger.error("会员赠送失败IService请求失败,请求参数" + JSON.toJSONString(request) + ",原因" + e.getMessage());
        }
        return false;
    }

    /**
     * 补偿记录
     *
     * @param compensatetype 补偿类型 1事中 2事后
     * @param compensateMode 补偿方式
     * @param phone          手机号
     * @param customerName   用户姓名
     * @param createTime     事件
     * @param createAcc      坐席
     * @param infoData       其他信息
     * @return
     */
    public boolean setSendLog(EasyQuery query, String compensatetype, String compensateMode, JSONObject jsonObject,
                              String opId) {
        jsonObject.put("ID", opId);
        jsonObject.put("COMPENSATE_MODE", compensateMode);
        jsonObject.put("COMPENSATE_TYPE", compensatetype);
        jsonObject.put("SUBMIT_STATES", "1");
        EasyRecord recor = new EasyRecord("C_NO_PEAK_END_RECORD", "ID").setColumns(jsonObject);
        try {
            query.save(recor);
            return true;

        } catch (Exception e) {
            logger.error("补偿记录保存失败" + e.getMessage(), e);
        }
        return false;
    }

    /**
     * 补偿记录
     *
     * @param compensatetype 补偿类型 1事中 2事后
     * @param compensateMode 补偿方式
     * @return
     */
    public boolean setSendLogCompensateMode1(EasyQuery query, String compensatetype, String compensateMode,
                                             JSONObject jsonObject, String opId) {
        jsonObject.put("ID", opId);
        jsonObject.put("COMPENSATE_MODE", compensateMode);
        jsonObject.put("COMPENSATE_TYPE", compensatetype);
        EasyRecord recor = new EasyRecord("C_NO_PEAK_END_RECORD", "ID").setColumns(jsonObject);
        try {
            if (query.findById(recor) == null) {
                query.save(recor);
            } else {
                query.update(recor);
            }
            return true;
        } catch (Exception e) {
            logger.error("补偿记录更新失败" + e.getMessage(), e);
            try {
                query.roolback();
            } catch (SQLException e1) {
                logger.error("补偿记录更新失败" + e1.getMessage(), e1);
                e1.printStackTrace();
            }
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 获取脚本
     *
     * @return
     */
    public JSONObject actionForGetScript() {
        JSONObject json = this.getJSONObject();
        return EasyResult.ok(GetScript(json));
    }

    public JSONObject GetScript(JSONObject json) {
        JSONObject data = new JSONObject();
        if (json.get("prodCode") != null && !json.get("prodCode").toString().equals("")) {
            EasySQL sql = new EasySQL();
            sql.append("select * from C_NO_SCRIPT_CONFIG where 1=1 ");
            if (!StrUtil.equals("0", json.getString("isLargeModel"))) {
                sql.append(json.get("compensateType"), "and  COMPENSATE_TYPE=? ");
            }
            sql.append(json.get("brandCode"), " and BRAND_CODE= ? ");
            sql.append(json.get("prodCode"), " and PROD_CODE= ? ");
            sql.append(json.get("orderServTypeCode"), " and SERVICE_TYPE_CODE= ? ");
            sql.append(json.get("serviceTypeCode"), " and SERVICE_TYPE_CODE= ? ");// 兼容
            sql.append(json.get("orderSerItem2Code"), " and SERVICE_ITEM2_CODE= ? ");
            sql.append(json.get("serviceItem2Code"), " and SERVICE_ITEM2_CODE= ? ");// 兼容
            sql.append(Constants.STATUS_Y, "AND  STATUS=? ");
            try {
                CommLogger.logger.info(sql.getSQL() + " -- " + JSONObject.toJSONString(sql.getParams()));
                data = this.getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
                if (data != null) {
                    data.put("startTime", EasyDate.getCurrentDateString());
                    data.put("userAcc", OpenApiUserUtil.getUser(this.getRequest()).getUserAcc());
                }
            } catch (Exception e) {
                logger.error("获取脚本失败" + e.getMessage(), e);
            }
        }
        return data;
    }

    /**
     * 获取商品列表
     */
    public JSONObject actionForGetProduct() {
        try {
            JSONObject json = this.getJSONObject();
            JSONObject data = Optional.ofNullable(GetScript(json)).orElse(new JSONObject());
            if (data.containsKey("MAX_BY_COMPENSATE")) {
                JSONObject param = new JSONObject();
                param.put("priceCeiling", data.getString("MAX_BY_COMPENSATE"));
                param.put("priceFloor", data.getString("MIN_BY_COMPENSATE"));
                param.put("skuState", 2);
                param.put("skuStock", 1);
                param.put("pageIndex", "1");
                param.put("pageCount", "1");
                param.put("operatePerson", OpenApiUserUtil.getUser(this.getRequest()).getUserName());
                logger.info("获取抱怨商品列表--->" + param);
                JSONObject result = doCompensateService(param, "compensateProductList");
                logger.info("回调接口获取抱怨商品列表--->" + result);
                JSONArray productList = new JSONArray();
                if (result != null && StringUtils.equals(result.getString("respCode"), GWConstants.RET_CODE_SUCCESS)) {
                    JSONObject jsonObject = result.getJSONObject("respData").getJSONArray("data").getJSONObject(0);
                    jsonObject.put("ProductType", "抱怨");
                    productList.add(jsonObject);
                }
                param.put("priceCeiling", data.getString("MAX_TS_COMPENSATE"));
                param.put("priceFloor", data.getString("MIN_TS_COMPENSATE"));
                logger.info("获取投诉商品列表--->" + param);
                result = doCompensateService(param, "compensateProductList");
                logger.info("回调接口获取投诉商品列表--->" + result);
                if (result != null && StringUtils.equals(result.getString("respCode"), GWConstants.RET_CODE_SUCCESS)) {
                    JSONObject jsonObject = result.getJSONObject("respData").getJSONArray("data").getJSONObject(0);
                    jsonObject.put("ProductType", "投诉");
                    productList.add(jsonObject);
                }
                param.put("priceCeiling", data.getString("MAX_YYBG_COMPENSATE"));
                param.put("priceFloor", data.getString("MIN_YYBG_COMPENSATE"));
                logger.info("获取扬言曝光商品列表--->" + param);
                result = doCompensateService(param, "compensateProductList");
                logger.info("回调接口获取扬言曝光商品列表--->" + result);
                if (result != null && StringUtils.equals(result.getString("respCode"), GWConstants.RET_CODE_SUCCESS)) {
                    JSONObject jsonObject = result.getJSONObject("respData").getJSONArray("data").getJSONObject(0);
                    jsonObject.put("ProductType", "扬言");
                    productList.add(jsonObject);
                }
                data.put("data", productList);
            }
            return EasyResult.ok(data);
        } catch (Exception e) {
            logger.info("回调接口获取扬言曝光商品列表异常--->" + e.getMessage());
            return EasyResult.error(500, "系统异常，请稍后再试！");
        }
    }

    /**
     * 校验事业部金额剩余情况
     * 当前用户相同的品牌 、品类、服务请求做了服务补偿，提醒用户
     *
     * @return
     */
    public EasyResult actionForPeakEndIimit() {
        JSONObject pcObj = this.getJSONObject();
        String phone = pcObj.getString("phone");
        String orderServTypeCode = pcObj.getString("orderServTypeCode");
        String orderSerItem1Code = pcObj.getString("orderSerItem1Code");
        String orderSerItem2Code = pcObj.getString("orderSerItem2Code");
        String summitResultFlag = pcObj.getString("summitResultFlag");
        String physicalGifts = pcObj.getString("physicalGifts");
        String orgCode = pcObj.getString("orgCode");
        String prodCode = pcObj.getString("prodCode");
        orgCode = OrgUtil.getCcOrgCode(orgCode, prodCode);// cc主体
        String brandCode = pcObj.getString("brandCode");
        double productAmount = 0;
        if (StringUtils.isNotBlank(pcObj.getString("productAmount"))) {
            productAmount = pcObj.getDoubleValue("productAmount");// 商品价值
        }
        logger.info(orgCode);
        logger.info(pcObj.toJSONString());
        String sysTime = EasyDate.getCurrentDateString();
        String orgIntegralId = "";// 事业部金额id
        try {
            EasySQL sql = new EasySQL();
            if (StringUtils.isBlank(summitResultFlag) || ("1".equals(summitResultFlag) && "1".equals(physicalGifts))) {// 进入页面时或者提交时选择实物赠送才检查事业部金额
                // 校验事业部金额剩余情况
                sql.append("select ID,INTEGRAL_LIMIT from C_NO_ORG_INTEGRAL  ");
                sql.append(orgCode, "where  ORG_CODE=? ");
                sql.append(sysTime, "AND  VALID_BEGIN_TIME < ? ");
                sql.append(sysTime, "AND  VALID_END_TIME > ? ");
                sql.append(Constants.STATUS_Y, "AND  STATUS=? ");
                JSONObject data = this.getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
                if (data == null) {
                    return EasyResult.error(500, "无可用金额！");
                } else {
                    orgIntegralId = data.getString("ID");
                    double integralIimit = data.getDoubleValue("INTEGRAL_LIMIT");// 金额上限
                    if (productAmount != 0) {// 存在商品金额
                        double sumIntegral = getPeakEndIntegral(orgIntegralId);
                        logger.info(
                                "查询可用金额：总金额:" + integralIimit + "；已用金额：" + sumIntegral + "；本次使用金额：" + productAmount);
                        if (integralIimit - sumIntegral - productAmount < 0) {
                            return EasyResult.error(500, "无剩余可用金额！");
                        }
                    }
                }
            }
            // 当前用户相同的品牌 、品类、服务请求做了服务补偿，提醒用户
            sql = new EasySQL();
            String beforeDateTime = DateUtil.getBeforeDateTime(Constants.PEAK_END_VALIDITY_MONTH);// N月前
            logger.info("beforeDateTime" + beforeDateTime);
            sql.append("select * from C_NO_PEAK_END_RECORD  ");
            sql.append(phone, "where  phone=? ", false);
            // 过滤现金补偿的类型
            sql.append(Constants.COMPENSATE_MODE7, " and COMPENSATE_MODE!= ? ");
            sql.append(Constants.COMPENSATE_MODE2, " and COMPENSATE_MODE!= ? ");
            sql.append(Constants.COMPENSATE_MODE3, " and COMPENSATE_MODE!= ? ");
            sql.append(orgCode, " and ORG_CODE= ? ");
            // sql.append(brandCode," and BRAND_CODE= ? ");
            sql.append(prodCode, " and PROD_CODE= ? ");
            // sql.append(orderServTypeCode," and ORDER_SERV_TYPE_CODE= ? ");
            // sql.append(orderSerItem1Code," and ORDER_SER_ITEM1_CODE= ? ");
            // sql.append(orderSerItem2Code," and ORDER_SER_ITEM2_CODE= ? ");
            sql.append(beforeDateTime, " and CREATE_TIME> ? ");
            sql.append(" and (SUBMIT_STATES = '0' OR SUBMIT_STATES = '1') ");
            sql.append(" and rownum < 2 ");
            logger.info("peakEndLimit sql = " + sql.getSQL() + "; param = " + Arrays.toString(sql.getParams()));
            List<JSONObject> queryForList = this.getQuery().queryForList(sql.getSQL(), sql.getParams(),
                    new JSONMapperImpl());
            // Constants.PEAK_END_VALIDITY_MONTH==0 不需要校验间隔时间=重复
            if (Constants.PEAK_END_VALIDITY_MONTH != 0 && queryForList.size() != 0) {// 有重复的 不允许继续操作 ，并提示
                sql = new EasySQL();
                sql.append("select REPEAT_CONTENT from C_NO_SCRIPT_CONFIG  where 1=1 ");// 重复的脚本提醒内容
                sql.append(brandCode, " and BRAND_CODE= ? ");
                sql.append(prodCode, " and PROD_CODE= ? ");
                sql.append(orderServTypeCode, " and SERVICE_TYPE_CODE= ? ");
                sql.append(orderSerItem1Code, " and SERVICE_ITEM1_CODE= ? ");
                sql.append(orderSerItem2Code, " and SERVICE_ITEM2_CODE= ? ");
                sql.append(Constants.STATUS_Y, "AND  STATUS=? ");
                JSONObject data2 = this.getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
                if (data2 != null && data2.get("REPEAT_CONTENT") != null) {
                    return EasyResult.error(500, data2.getString("REPEAT_CONTENT"));
                } else {
                    return EasyResult.error(500, "近期已补偿当前用户");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("验证失败" + e.getMessage(), e);
            return EasyResult.error(500, "验证失败" + e.getMessage());
        }
        JSONObject data = new JSONObject();
        data.put("orgIntegralId", orgIntegralId);
        return EasyResult.ok(data);
    }

    /**
     * 金额配置的使用情况
     *
     * @param id
     * @return
     * @throws SQLException
     */
    public double getPeakEndIntegral(String id) throws SQLException {
        EasySQL sql = new EasySQL();
        sql.append("select nvl(sum(COMPENSATE_AMOUNT),0)as sumIntegral from C_NO_PEAK_END_RECORD  ");
        sql.append(id, " where ORG_INTEGRAL_ID= ? ");
        JSONObject data = this.getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        return data.getDoubleValue("SUMINTEGRAL");

    }

    // 清洗补偿
    public JSONObject actionForCleanCompensate() {
        JSONObject json = this.getJSONObject();
        JSONObject result = new JSONObject();
        JSONObject params = new JSONObject();

        oplogger.info("清洗券补偿记录" + json.toJSONString());// 操作记录
        String getEpCode = OpenApiUserUtil.getUser(this.getRequest()).getEpCode();
        String userAcc = OpenApiUserUtil.getUser(this.getRequest()).getUserAcc();
        String sysTime = EasyDate.getCurrentDateString();
        JSONArray jsonArray = json.getJSONArray("type");
        String phone = json.getString("phone");
        String customerName = json.getString("customerName");
        String responsible = json.getString("responsible");
        String orgCode = json.getString("orgCode");
        String prodCode = json.getString("prodCode");
        orgCode = OrgUtil.getCcOrgCode(orgCode, prodCode);// cc主体

        JSONObject infoData = new JSONObject();// 其他信息
        // 基本信息
        infoData.put("COMPENSATE_MODE", phone);
        infoData.put("CREATE_TIME", sysTime);
        infoData.put("PHONE", phone);
        infoData.put("CUSTOMER_NAME", customerName);
        infoData.put("CREATE_ACC", userAcc);
        infoData.put("RESPONSIBLE", responsible);

        infoData.put("AREA_NAME", json.get("areaName"));
        infoData.put("AREA_CODE", json.get("areaCode"));
        infoData.put("ORG_CODE", orgCode);
        infoData.put("PROD_NAME", json.get("prodName"));
        infoData.put("PROD_CODE", json.get("prodCode"));
        infoData.put("BRAND_CODE", json.get("brandCode"));
        infoData.put("BRAND_NAME", json.get("brandName"));

        infoData.put("ORDER_SERV_TYPE_NAME", json.get("orderServTypeName"));
        infoData.put("ORDER_SERV_TYPE_CODE", json.get("orderServTypeCode"));
        infoData.put("ORDER_SER_ITEM1_NAME", json.get("orderSerItem1Name"));
        infoData.put("ORDER_SER_ITEM1_CODE", json.get("orderSerItem1Code"));
        infoData.put("ORDER_SER_ITEM2_NAME", json.get("orderSerItem2Name"));
        infoData.put("ORDER_SER_ITEM2_CODE", json.get("orderSerItem2Code"));
        infoData.put("SERVICE_ORDER_NO", json.get("serviceOrderNo"));
        infoData.put("VISIT_RESULT", json.get("result"));
        infoData.put("REMARKS", json.get("remarks"));
        infoData.put("ADDRESS", json.get("customerAddress"));
        infoData.put("AREA_NUM", json.get("areaNum"));
        infoData.put("SCENE_TYPE", json.get("sceneType"));

        infoData.put("CKIENT_NAME", json.get("ckientName"));
        infoData.put("CKIENT_CODE", json.get("ckientCode"));
        EasyQuery query = this.getQuery();
        try {
            for (Object type : jsonArray) {
                if (Constants.COMPENSATE_MODE4.equals(type.toString())) {// 清洗

                    JSONObject getCardInfo = GetCardInfo(json, this.getRequest());
                    if ("000".equals(getCardInfo.get("respCode"))) {
                        String cardCode = getCardInfo.getJSONObject("respDate").getString("cardCode");// 返回优惠劵编号
                        String takeStartTime = getCardInfo.getJSONObject("respDate").getString("takeStartTime");// 返回优惠劵编号
                        String takeEndTime = getCardInfo.getJSONObject("respDate").getString("takeEndTime");// 返回优惠劵编号
                        String content = json.getString("smsContent");
                        content = content.replace("[CARD_CODE]", cardCode);
                        content = content.replace("[START_TIME]", takeStartTime);
                        content = content.replace("[END_TIME]", takeEndTime);
                        if (CommService.sendMessage(content, phone, userAcc, "06")) {
                            infoData.put("COMPENSATE_NO", cardCode);// 卡券名称/商品名称
                            infoData.put("COMPENSATE_NAME", json.get("cardName"));// 卡券名称/商品名称
                            infoData.put("COMPENSATE_AMOUNT", json.get("cardFaceMoney"));// 卡券名称/商品名称

                            JSONObject jsonObject = JSONObject.parseObject(infoData.toJSONString());// 重新new
                            query.begin();
                            String opId = RandomKit.randomStr();
                            boolean setSendLog = setSendLog(query, json.getString("compensateType"),
                                    Constants.COMPENSATE_MODE4, jsonObject, opId);
                            if (!setSendLog) {
                                return EasyResult.error(500, "赠送成功 ，清洗券发送记录保存失败");
                            }
                            EasyRecord orderRecord = new EasyRecord("C_NO_PEAK_END_ORDER", "ID");
                            orderRecord.set("RESULT_CONTENT", json.get("remarks"));
                            orderRecord.set("RESULT", json.get("result"));
                            orderRecord.set("RESPONSIBLE", json.get("responsible"));
                            orderRecord.set("COMPENSATE_TYPE", json.get("compensateType"));
                            orderRecord.set("ID", json.get("orderId"));
                            orderRecord.set("RESULT_TIME", sysTime);
                            orderRecord.set("RESULT_ACC", userAcc);

                            EasyRecord resultValidlyRecord = new EasyRecord("C_NO_PEAK_END_RESULT", "ORDER_ID");
                            resultValidlyRecord.set("ORDER_ID", json.get("id"));
                            resultValidlyRecord.set("VALIDLY", "N");

                            EasyRecord resultRecord = new EasyRecord("C_NO_PEAK_END_RESULT", "ID");
                            resultRecord.set("ID", RandomKit.randomStr());
                            resultRecord.set("VISIT_CONTENT", json.get("remarks"));
                            resultRecord.set("VISIT_RESULT", json.get("result"));
                            resultRecord.set("ORDER_ID", json.get("id"));
                            resultRecord.set("VISIT_TIME", sysTime);
                            resultRecord.set("VISIT_ACC", userAcc);
                            resultRecord.set("VALIDLY", "Y");
                            resultRecord.set("CALL_ID", json.get("uids"));
                            resultRecord.set("CONVERSION_VALUE", json.get("coefficient"));
                            resultRecord.set("RESPONSIBLE", responsible);
                            try {
                                query.update(orderRecord);// 更新当前峰终工单
                                query.update(resultValidlyRecord);// 历史的结果改无效
                                query.save(resultRecord);// 记录结果
                                query.commit();
                            } catch (SQLException e) {
                                try {
                                    query.roolback();
                                } catch (SQLException e1) {
                                    e1.printStackTrace();
                                }
                                // TODO Auto-generated catch block
                                e.printStackTrace();
                                return EasyResult.error(500, "赠送成功 工单状态修改失败");
                            }

                            return EasyResult.ok();

                        } else {
                            query.roolback();
                            return EasyResult.error(500, "短信发送失败");
                        }

                    } else {
                        query.roolback();
                        return EasyResult.error(500, "获取优惠劵失败");
                    }
                }
            }
            return EasyResult.error(500, "操作失败");
        } catch (Exception e) {
            try {
                query.roolback();
            } catch (SQLException e1) {
                // TODO Auto-generated catch block
                e1.printStackTrace();
            }
            e.printStackTrace();
            logger.debug(e.getMessage(), e);
            return EasyResult.error(500, e.getMessage());
        }

    }

    // 获取优惠劵
    public JSONObject GetCardInfo(JSONObject json, HttpServletRequest request) {
        JSONObject result = new JSONObject();
        JSONObject obj = new JSONObject();
        obj.put("command", ServiceCommand.MIXGW_WEIXINCS_GETCARD);
        obj.put("cardId", json.get("cardId"));
        // obj.put("expireDay", json.get("msg.expireDay"));
        obj.put("agentCode", OpenApiUserUtil.getUser(request).getUserAcc());
        try {
            IService service = ServiceContext.getService(ServiceID.MIXGW_WEIXINCS_INTEFACE);
            result = service.invoke(obj);
        } catch (ServiceException e) {
            logger.error("IService - 洗悦家 - 获取卡号接口,请求参数" + JSON.toJSONString(obj) + ",原因" + e.getMessage());
        }
        return result;
    }

    /**
     * 保存结果
     *
     * @return
     */
    public EasyResult actionForSaveResult() {
        JSONObject json = this.getJSONObject();
        String userAcc = OpenApiUserUtil.getUser(this.getRequest()).getUserAcc();
        String sysTime = EasyDate.getCurrentDateString();
        EasyRecord orderRecord = new EasyRecord("C_NO_PEAK_END_ORDER", "ID");
        orderRecord.set("RESULT_CONTENT", json.get("remarks"));
        orderRecord.set("RESULT", json.get("result"));
        orderRecord.set("RESPONSIBLE", json.get("responsible"));
        orderRecord.set("COMPENSATE_TYPE", json.get("compensateType"));
        orderRecord.set("ID", json.get("id"));
        orderRecord.set("RESULT_TIME", sysTime);
        orderRecord.set("RESULT_ACC", userAcc);

        EasyRecord resultValidlyRecord = new EasyRecord("C_NO_PEAK_END_RESULT", "ORDER_ID");
        resultValidlyRecord.set("ORDER_ID", json.get("id"));
        resultValidlyRecord.set("VALIDLY", "N");

        EasyRecord resultRecord = new EasyRecord("C_NO_PEAK_END_RESULT", "ID");
        resultRecord.set("ID", RandomKit.randomStr());
        resultRecord.set("VISIT_CONTENT", json.get("remarks"));
        resultRecord.set("VISIT_RESULT", json.get("result"));
        resultRecord.set("ORDER_ID", json.get("id"));
        resultRecord.set("VISIT_TIME", sysTime);
        resultRecord.set("VISIT_ACC", userAcc);
        resultRecord.set("VALIDLY", "Y");
        resultRecord.set("CALL_ID", json.get("uids"));
        resultRecord.set("CONVERSION_VALUE", json.get("coefficient"));
        resultRecord.set("RESPONSIBLE", json.getString("responsible"));
        EasyQuery query = this.getQuery();
        try {
            query.begin();
            query.update(orderRecord);// 更新当前峰终工单
            query.update(resultValidlyRecord);// 历史的结果改无效
            query.save(resultRecord);// 记录结果
            query.commit();
        } catch (SQLException e) {
            try {
                query.roolback();
            } catch (SQLException e1) {
                e1.printStackTrace();
            }
            e.printStackTrace();
            return EasyResult.error(500, "操作失败");
        }

        return EasyResult.ok("操作成功");
    }

    public EasyResult actionForCheckRepeat() {
        try {
            JSONObject pcObj = this.getJSONObject();
            String phone = pcObj.getString("phone");
            String orgCode = pcObj.getString("orgCode");
            String prodCode = pcObj.getString("prodCode");
            orgCode = OrgUtil.getCcOrgCode(orgCode, prodCode);// cc主体
            String brandCode = pcObj.getString("brandCode");
            logger.info(pcObj.toJSONString());
            EasySQL sql = new EasySQL();
            String beforeDateTime = DateUtil.getBeforeDateTime(Constants.PEAK_END_VALIDITY_MONTH);// N月前
            logger.info("beforeDateTime" + beforeDateTime);
            sql.append("select * from C_NO_PEAK_END_RECORD where 1 = 1 ");
            sql.append(orgCode, " and ORG_CODE = ? ");
            sql.append(prodCode, " and PROD_CODE = ? ");
            // sql.append(brandCode," and BRAND_CODE = ? ");
            sql.append(phone, " and phone=? ", false);
            List<JSONObject> sumList = this.getQuery().queryForList(sql.getSQL(), sql.getParams(),
                    new JSONMapperImpl());
            sql.append(beforeDateTime, " and CREATE_TIME> ? ");
            sql.append(" and (SUBMIT_STATES = '0' OR SUBMIT_STATES = '1') ");
            sql.append(" and (COMPENSATE_MODE = '1' OR COMPENSATE_MODE = '7' OR COMPENSATE_MODE = '8') ");
            logger.info("checkRepeat sql = " + sql.getSQL() + "; param = " + Arrays.toString(sql.getParams()));
            List<JSONObject> queryForList = this.getQuery().queryForList(sql.getSQL(), sql.getParams(),
                    new JSONMapperImpl());
            JSONObject data = new JSONObject();
            data.put("num", sumList.size());
            if (!queryForList.isEmpty()) {
                data.put("message", "一个月内有重复赠送记录");
            }
            String opId = pcObj.getString("opId");
            EasyRecord record = new EasyRecord("C_NO_PEAK_END_RECORD", "ID").set("ID", opId);
            Map<String, String> map = this.getQuery().findById(record);
            if (map != null && map.get("SUBMIT_STATES").equals("2")) {
                data.put("message", "不符合赠送单据");
            }
            return EasyResult.ok(data);

        } catch (SQLException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            return EasyResult.error(500, e.getMessage());
        }
    }

    /**
     * 用户机器信息（延保）
     *
     * @return
     */
    public EasyResult actionForUserHistorylist() {
        JSONObject result = new JSONObject();
        JSONObject json = this.getJSONObject();
        JSONObject request = new JSONObject();
        request.put("command", "machineHistory");
        request.put("data", json);
        try {
            IService service = ServiceContext.getService("CSSGW-OTHER");
            result = service.invoke(request);
        } catch (ServiceException e) {
            logger.error("IService请求失败,请求参数" + JSON.toJSONString(request) + ",原因" + e.getMessage());
        }
        JSONObject respData = result.getJSONObject("respData");
        return EasyResult.ok(respData);
    }

    /**
     * 委托方剩余金卡数量
     *
     * @return
     */
    public EasyResult actionForGoldcardRemainder() {
        JSONObject result = new JSONObject();
        JSONObject request = new JSONObject();
        JSONObject data = new JSONObject();
        request.put("command", "goldcardRemainder");
        data.put("cCProactiveCareFlag", "Y");
        data.put("cardStatus", "10");
        request.put("data", data);
        try {
            IService service = ServiceContext.getService("CSSGW-OTHER");
            result = service.invoke(request);
        } catch (ServiceException e) {
            logger.error("IService请求失败,请求参数" + JSON.toJSONString(request) + ",原因" + e.getMessage());
        }
        JSONObject respData = result.getJSONObject("respData");
        return EasyResult.ok(respData);
    }

    /**
     * 获取系统MARS数据库连接
     *
     * @return
     */
    private EasyQuery getmarQuery() {
        return EasyQuery.getQuery(Constants.APP_NAME, Constants.FRAME_DS);
    }

    /**
     * 发起现金补偿申请，封装接口入参
     *
     * @param json
     */
    public JSONObject compensationApply(JSONObject json) throws Exception {
        JSONObject result = new JSONObject();
        JSONObject data = new JSONObject();
        try {
            UserModel user = OpenApiUserUtil.getUser(this.getRequest());
            String deptCode = user.getDeptCode();
            String operatorArea = "";
            String staffName = "";
            if (deptCode.length() >= 15) {
                deptCode = deptCode.substring(0, 15);
            }

            if (deptCode.length() == 15) {
                // 查询班组和运营区域
                EasySQL easySQL = new EasySQL();
                easySQL.append("SELECT D1.DEPT_CODE,D1.DEPT_NAME,D2.DEPT_NAME P_DEPT_NAME");
                easySQL.append("FROM EASI_DEPT D1");
                easySQL.append("LEFT JOIN EASI_DEPT D2 on D1.P_DEPT_CODE=D2.DEPT_CODE");
                easySQL.append(deptCode, "WHERE D1.DEPT_CODE=?");
                try {
                    EasyRow easyRow = getmarQuery().queryForRow(easySQL.getSQL(), easySQL.getParams());
                    if (easyRow != null) {
                        staffName = easyRow.getColumnValue("DEPT_NAME");
                        operatorArea = easyRow.getColumnValue("P_DEPT_NAME");
                    }
                } catch (Exception e) {
                }
            } else {
                // 查询运营区域
                EasySQL easySQL = new EasySQL();
                easySQL.append("SELECT D1.DEPT_NAME FROM EASI_DEPT D1");
                easySQL.append(deptCode, "AND D1.DEPT_CODE=?");
                try {
                    operatorArea = getmarQuery().queryForString(easySQL.getSQL(), easySQL.getParams());
                    staffName = operatorArea;
                } catch (Exception e) {
                }
            }
            String compensateItems = json.getString("compensateItems");

            data.put("compensateItems", compensateItems);
            data.put("operatorArea", operatorArea);
            data.put("staffName", staffName);
            data.put("applyPerson", user.getUserAcc());
            data.put("serviceOrderNo", json.get("serviceOrderNo"));
            data.put("contactOrderNo", json.get("contactOrderCode"));
            if (json.containsKey("contactOrderNo") && !json.getString("contactOrderNo").isEmpty()) {
                data.put("contactOrderNo", json.get("contactOrderNo"));
            }

            data.put("customerName", json.getString("customerName"));
            data.put("customerPhone", json.getString("phone"));

            data.put("contactOrderServTypeName", json.get("orderServTypeName"));
            data.put("contactOrderServTypeCode", json.get("orderServTypeCode"));
            data.put("serviceLargeCategoryName", json.get("orderSerItem1Name"));
            data.put("serviceLargeCategory", json.get("orderSerItem1Code"));
            data.put("serviceSmallCategoryName", json.get("orderSerItem2Name"));
            data.put("serviceSmallCategory", json.get("orderSerItem2Code"));
            data.put("brandCode", json.get("brandCode"));
            data.put("brandName", json.get("brandName"));
            data.put("prodCode", json.get("prodCode"));
            data.put("prodName", json.get("prodName"));
            data.put("clientCode", json.get("ckientCode"));
            data.put("clientName", json.get("ckientName"));

            data.put("branchCode", json.get("branchCode"));
            data.put("branchName", json.get("branchName"));
            data.put("unitCode", json.get("unitCode"));
            data.put("unitName", json.get("unitName"));
            data.put("engineerCode", json.get("engineerCode"));
            data.put("engineerName", json.get("engineerName"));

            data.put("compensateCash", json.get("compensateCash"));
            data.put("compensateCauseTypeName", json.get("compensateCauseTypeName"));
            data.put("compensateCauseType", json.get("compensateCauseType"));
            // 补偿说明
            data.put("compensateExplain", json.getString("compensateExplain"));
            // 用户投诉内容
            data.put("remark", json.getString("remark"));
            if (!compensateItems.equals("CASH")) {
                JSONArray compensateDeatils = json.getJSONArray("compensateDeatils");
                JSONObject jsonObject = compensateDeatils.getJSONObject(0);
                String salePrice = jsonObject.getString("salePrice");
                String nakedPrice = jsonObject.getString("nakedPrice");
                BigDecimal salePriceBigDecimal = new BigDecimal(salePrice);
                BigDecimal nakedPriceBigDecimal = new BigDecimal(nakedPrice);
                jsonObject.put("salePrice", salePriceBigDecimal.multiply(new BigDecimal(100)).intValue());
                jsonObject.put("nakedPrice", nakedPriceBigDecimal.multiply(new BigDecimal(100)).intValue());
                data.put("detailList", compensateDeatils);
                data.put("regionCode", json.getString("areaCode"));
                data.put("customerAddress", json.getString("customerAddress"));
                data.put("compensateCash", "0");
                data.put("compensateExplain",
                        StringUtils.isNotBlank(json.getString("compensateExplain"))
                                ? json.getString("compensateExplain")
                                : json.getString("remarks"));
            }
            result.put("params", data);
            JSONObject serviceResult = doCompensateService(data, "compensateApply");
            if (serviceResult != null && Constants.GATEWAY_SUCCESS_CODE.equals(serviceResult.getString("respCode"))) {
                JSONObject respData = serviceResult.getJSONObject("respData");
                if (respData != null && respData.containsKey("returnObject")) {
                    JSONObject returnObject = respData.getJSONObject("returnObject");
                    result.put("returnObject", returnObject);
                }
                return result;
            }
        } catch (Exception e) {
            logger.error("IService请求失败,请求参数" + JSON.toJSONString(data) + ",原因" + e.getMessage());
            result.put("message", e.getMessage());
            throw e;
        }
        return result;
    }

    /**
     * 重新发起补偿
     */
    public JSONObject actionForReSubmit() {
        JSONObject json = this.getJSONObject().getJSONObject("row");
        logger.info("actionForReSubmit --> " + json.toJSONString());
        JSONObject result = new JSONObject();
        try {
            JSONArray detailVOList = json.getJSONArray("detailVOList");
            for (int i = 0; i < detailVOList.size(); i++) {
                JSONObject jsonObject = detailVOList.getJSONObject(i);
                String salePrice = jsonObject.getString("salePrice");
                String nakedPrice = jsonObject.getString("nakedPrice");
                BigDecimal salePriceBigDecimal = new BigDecimal(salePrice);
                BigDecimal nakedPriceBigDecimal = new BigDecimal(nakedPrice);
                jsonObject.put("salePrice", salePriceBigDecimal.multiply(new BigDecimal(100)).intValue());
                // jsonObject.put("salePrice", (int) (jsonObject.getDouble("salePrice") * 100));
                // jsonObject.put("nakedPrice", (int) (jsonObject.getDouble("nakedPrice") *
                // 100));
                jsonObject.put("nakedPrice", nakedPriceBigDecimal.multiply(new BigDecimal(100)).intValue());
            }
            logger.info("actionForReSubmit --> " + detailVOList.toJSONString());
            json.put("detailList", JSON.parseObject(detailVOList.toJSONString(), List.class));
            JSONObject serviceResult = doCompensateService(json, "compensateApply");
            if (serviceResult != null && Constants.GATEWAY_SUCCESS_CODE.equals(serviceResult.getString("respCode"))) {
                JSONObject respData = serviceResult.getJSONObject("respData");
                if (respData != null && respData.containsKey("returnObject")) {
                    JSONObject returnObject = respData.getJSONObject("returnObject");
                    result.put("returnObject", returnObject);
                }
                return EasyResult.ok(result);
            }
            result.put("state", 0);
            result.put("message",
                    serviceResult != null && StringUtils.isNotBlank(serviceResult.getString("respDesc"))
                            ? serviceResult.getString("respDesc")
                            : "程序异常！");
        } catch (Exception e) {
            logger.error("IService请求失败,请求参数" + JSON.toJSONString(json) + ",原因" + e.getMessage());
            result.put("state", 0);
            result.put("message", e.getMessage());
        }
        return result;
    }

    /**
     * 获取服务单对应的补偿次数
     *
     * @return
     */
    public JSONObject actionForGetServiceNoPeakNum() {
        JSONObject json = this.getJSONObject();
        JSONObject result = new JSONObject();
        boolean existsHistory = false;
        try {
            int day = Constants.CASH_COMPENSATE_CHECK_DAYS;
            if (StringUtils.isNotBlank(json.getString("serviceOrderNo"))) {
                EasySQL easySQL = new EasySQL();
                easySQL.append("select count(1) from C_NO_PEAK_END_RECORD");
                easySQL.append(json.getString("serviceOrderNo"), "where SERVICE_ORDER_NO=?", false);
                easySQL.append(DateUtil.getBeforeDateTime(day, Calendar.DAY_OF_MONTH), "and CREATE_TIME>=?");
                easySQL.append(Constants.COMPENSATE_MODE7, "and COMPENSATE_MODE=?");
                existsHistory = getQuery().queryForExist(easySQL.getSQL(), easySQL.getParams());
                result.put("existsMsg", "当前服务单，" + day + "天内存在非失败的补偿记录......");
            }
        } catch (Exception e) {
            logger.error("获取现金补偿配置失败,原因:" + e.getMessage(), e);
        }

        result.put("existsHistory", existsHistory);
        return EasyResult.ok(result);
    }

    /**
     * 根据委托方和角色获取现金补偿配置
     *
     * @return
     */
    public JSONObject actionForGetCashCompensationConfig() {
        JSONObject json = this.getJSONObject();

        List<String> roleIdList = new ArrayList<>();
        if (json.getJSONArray("roleIdList") != null) {
            roleIdList = json.getJSONArray("roleIdList").toJavaList(String.class);
        }
        JSONObject result = new JSONObject();
        List<JSONObject> config = new ArrayList<>();
        try {
            UserModel user = OpenApiUserUtil.getUser(this.getRequest());
            List<RoleModel> roles = user == null ? null : user.getRoles();
            if (CollectionUtils.isEmpty(roleIdList)) {
                roleIdList = StreamUtil.of(roles).map(RoleModel::getRoleId).collect(Collectors.toList());
            }

            if (CollectionUtils.isNotEmpty(roleIdList)) {
                EasySQL easySQL = new EasySQL();
                easySQL.append(" select * from C_NO_ROLE_CASH_SCRIPT");
                easySQL.append(Constants.STATUS_Y, " where ENABLED=?");
                easySQL.append(json.getString("clientCode"), " and PROD_CODE=?");
                // easySQL.append(" AND ROLE_ID in ('"+String.join("','",roleIdList)+"')");
                appendIn(easySQL, roleIdList.toArray(new String[0]), " AND ROLE_ID ");
                easySQL.append(" order by CREATE_TIME desc");
                config = getQuery().queryForList(easySQL.getSQL(), easySQL.getParams(), new JSONMapperImpl());
            }
        } catch (Exception e) {
            logger.error("获取现金补偿配置失败,原因:" + e.getMessage(), e);
        }
        result.put("config", config);
        return EasyResult.ok(result);
    }

    public EasySQL appendIn(EasySQL easySQL, String[] paramValues, String sql) {
        if (paramValues != null) {
            int count = paramValues.length;
            if (count == 0)
                return easySQL;
            if (count == 1) {
                easySQL.append(paramValues[0], sql + " = ? ");
                return easySQL;
            }
            StringBuilder buffer = new StringBuilder();
            buffer.append(" ").append(sql).append(" in (");
            for (int i = 0; i < count; i++) {
                buffer.append("?,");
            }
            buffer.delete(buffer.length() - 1, buffer.length());
            buffer.append(")");
            for (int i = 0; i < count; i++) {
                easySQL.append(paramValues[i], "", false);
            }
            easySQL.append(" ").append(buffer.toString());
        }
        return easySQL;
    }

    /**
     * 获取本次补偿实际上限
     *
     * @return
     */
    public JSONObject actionForGetNowUpperLimit() {
        JSONObject json = this.getJSONObject();
        try {
            String clientCode = json.getString("clientCode");
            String pubCurrentDate = com.yq.busi.common.util.DateUtil.getCurrMonthBeginDay("yyyy-MM-dd HH:mm:ss");
            String pubCurrentDateEnd = DateUtil.getFormatCurrTime(new Date());
            // 获取坐席自然月补偿数据
            JSONObject params = new JSONObject();
            params.put("operatePerson", OpenApiUserUtil.getUser(getRequest()).getUserAcc());
            params.put("pubCurrentDate", pubCurrentDate);
            params.put("pubCurrentDateEnd", pubCurrentDateEnd);
            params.put("clientCode", clientCode);
            params.put("pageIndex", 1);
            params.put("pageSize", 1000);
            JSONArray list = getUpperLimit(params);
            double agentAmountLimit = 0.0;
            if (list != null && list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    if (!StringUtils.equals("20", list.getJSONObject(i).getString("applyStatus"))) {
                        agentAmountLimit += list.getJSONObject(i).getDoubleValue("compensateCash");
                    }
                }
            }

            // 单用户补偿次数
            JSONObject custParams = new JSONObject();
            int day = Constants.CASH_COMPENSATE_CHECK_DAYS;
            pubCurrentDate = DateUtil.getBeforeDateTime(day, Calendar.DAY_OF_MONTH);
            custParams.put("customerPhone", json.getString("customerPhone"));
            custParams.put("pageIndex", 1);
            custParams.put("pageSize", 1000);
            custParams.put("pubCurrentDate", pubCurrentDate);
            custParams.put("pubCurrentDateEnd", pubCurrentDateEnd);
            custParams.put("clientCode", clientCode);
            list = getUpperLimit(custParams);
            double custAmountLimit = 0.0;
            int custNumLimit = 0;
            if (list != null && list.size() > 0) {
                // custNumLimit = list.size();
                for (int i = 0; i < list.size(); i++) {
                    if (!StringUtils.equals("20", list.getJSONObject(i).getString("applyStatus"))) {
                        custAmountLimit += list.getJSONObject(i).getDoubleValue("compensateCash");
                        custNumLimit += 1;
                    }
                }
            }

            JSONObject result = new JSONObject();
            result.put("agentAmountLimit", agentAmountLimit);
            result.put("custNumLimit", custNumLimit);
            result.put("custAmountLimit", custAmountLimit);
            result.put("operatePerson", params.getString("operatePerson"));
            result.put("day", day);
            return EasyResult.ok(result);
        } catch (Exception e) {
            logger.error("获取现金补偿配置失败,原因:" + e.getMessage(), e);
            return EasyResult.fail("查询状态失败：" + e.getMessage());
        }
    }

    /**
     * 处理列表返回的结果
     *
     * @param param
     * @return
     */
    private JSONArray getUpperLimit(JSONObject param) {
        try {
            JSONObject result = doCompensateService(param, "compensateQueryList");
            if (result != null && Constants.GATEWAY_SUCCESS_CODE.equals(result.getString("respCode"))) {
                JSONObject respData = result.getJSONObject("respData");
                if (respData != null && respData.containsKey("data")) {
                    return respData.getJSONArray("data");
                }
            }
        } catch (Exception e) {
        }
        return null;
    }

    /**
     * 调用CSSGW模块服务接口
     *
     * @param json
     * @param command
     * @return
     * @throws Exception
     */
    private JSONObject doCompensateService(JSONObject json, String command) throws Exception {
        JSONObject param = new JSONObject();
        if (!StringUtils.equals(command, "compensateQueryList") && !StringUtils.equals(command, "compensateApply")
                && !StringUtils.equals(command, "compensateProductList")) {
            json.put("operatePerson", OpenApiUserUtil.getUser(getRequest()).getUserAcc());
        }
        param.put("command", command);
        param.put("params", json);
        IService service = ServiceContext.getService(Constants.COMPENSATE_SERVICE_ID);
        JSONObject result = service.invoke(param);
        if ("compensateApply".equals(command)) {
            if (result != null && !StringUtils.equals(result.getString("respCode"), GWConstants.RET_CODE_SUCCESS)) {
                logger.info("调用CSSGW模块服务接口失败,原因:" + result.getString("respDesc"));
                throw new RuntimeException(result.getString("respDesc"));
            }
        }
        return result;
    }

    /**
     * command
     * 操作类型：compensateApplyModify（修改），compensateApplyCancel（取消），compensateUserLoginStatus（查询状态），compensateApplySecond重新补偿，compensateQueryList，compensateApply
     * compensateOrderNo 操作单号 （修改，取消，重新补偿时传入）
     * customerPhone 修改号码/查询号码（修改，查询时传入）
     * cancelCaus 取消说明（取消传入）
     *
     * @return
     */
    public JSONObject actionForDoCompensateService() {
        JSONObject json = this.getJSONObject();
        try {
            String command = json.getString("command");
            json.remove("command");
            json.put("operatePerson", OpenApiUserUtil.getUser(getRequest()).getUserAcc());
            JSONObject result = doCompensateService(json, command);
            if (result != null && StringUtils.equals(result.getString("respCode"), GWConstants.RET_CODE_SUCCESS)) {
                // if(StringUtils.equals(command,"compensateApplyModify")){
                // //修改号码时，同时修改申请记录里面的号码字段
                // String update = "update C_NO_PEAK_END_RECORD set
                // PHONE='"+json.getString("customerPhone")+"' where OUT_ORDER_ID
                // ='"+json.getString("compensateOrderNo")+"' and COMPENSATE_MODE
                // ='"+Constants.COMPENSATE_MODE7+"'";
                // getQuery().execute(update);
                // }
                if (StringUtils.equals(command, "compensateApplyCancel") && json.containsKey("contactOrderNo")
                        && StringUtils.isNotBlank(json.getString("contactOrderNo"))) {
                    String update = "update C_NO_PEAK_END_RECORD  set SUBMIT_STATES = '3' where CONTACT_ORDER_ID ='"
                            + json.getString("contactOrderNo") + "' and COMPENSATE_MODE ='" + Constants.COMPENSATE_MODE1
                            + "'";
                    getQuery().execute(update);
                } else if (StringUtils.equals(command, "compensateApplyCancel") && json.containsKey("serviceOrderNo")
                        && StringUtils.isNotBlank(json.getString("serviceOrderNo"))) {
                    String update = "update C_NO_PEAK_END_RECORD  set SUBMIT_STATES = '3' where SERVICE_ORDER_NO ='"
                            + json.getString("serviceOrderNo") + "' and COMPENSATE_MODE ='" + Constants.COMPENSATE_MODE1
                            + "'";
                    getQuery().execute(update);
                }

                return EasyResult.ok(result.getJSONObject("respData"));
            }
            return EasyResult.fail(result != null && StringUtils.isNotBlank(result.getString("respDesc"))
                    ? result.getString("respDesc")
                    : "操作失败");
        } catch (Exception e) {
            logger.error("获取现金补偿配置失败,原因:" + e.getMessage(), e);
            return EasyResult.fail("查询状态失败：" + e.getMessage());
        }
    }

    /**
     * 发起二次补偿，同步调用售后接口并且在本地数据库保存一条现金补偿记录
     *
     * @return
     */
    public JSONObject actionForCompensateApplySecond() {
        JSONObject json = this.getJSONObject();
        try {
            json.put("operatePerson", OpenApiUserUtil.getUser(getRequest()).getUserAcc());
            String command = "compensateApplySecond";
            JSONObject params = new JSONObject();
            params.put("compensateOrderNo", json.getString("compensateOrderNo"));
            params.put("cancelCaus", json.getString("cancelCaus"));

            String outOrderId = "";
            String orgIntegralId = "";

            JSONObject infoData = new JSONObject();// 其他信息
            String customerName = "";
            // 基本信息
            infoData.put("CREATE_TIME", EasyDate.getCurrentDateString());
            infoData.put("PHONE", json.getString("customerPhone"));
            infoData.put("CUSTOMER_NAME", json.getString("customerName"));
            infoData.put("CREATE_ACC", OpenApiUserUtil.getUser(this.getRequest()).getUserAcc());
            // 责任方
            infoData.put("RESPONSIBLE", "");

            // 无地区信息
            infoData.put("AREA_NAME", json.get("areaName"));
            infoData.put("AREA_CODE", json.get("areaCode"));

            infoData.put("ORG_CODE", json.getString("orgCode"));
            infoData.put("PROD_NAME", json.get("prodName"));
            infoData.put("PROD_CODE", json.get("prodCode"));
            infoData.put("BRAND_CODE", json.get("brandCode"));
            infoData.put("BRAND_NAME", json.get("brandName"));

            // 无服务请求
            infoData.put("ORDER_SERV_TYPE_NAME", json.get("orderServTypeName"));
            infoData.put("ORDER_SERV_TYPE_CODE", json.get("orderServTypeCode"));

            infoData.put("ORDER_SER_ITEM1_NAME", json.get("serviceLargeCategoryName"));
            infoData.put("ORDER_SER_ITEM1_CODE", json.get("serviceLargeCategory"));
            infoData.put("ORDER_SER_ITEM2_NAME", json.get("serviceSmallCategoryName"));
            infoData.put("ORDER_SER_ITEM2_CODE", json.get("serviceSmallCategory"));
            infoData.put("SERVICE_ORDER_NO", json.get("serviceOrderNo"));

            infoData.put("VISIT_RESULT", json.get("result"));
            infoData.put("REMARKS", json.get("remarks"));
            infoData.put("ADDRESS", json.get("customerAddress"));
            infoData.put("AREA_NUM", json.get("areaNum"));

            infoData.put("CKIENT_NAME", json.get("clientCode"));
            infoData.put("CKIENT_CODE", json.get("clientName"));

            infoData.put("OUT_ORDER_ID", outOrderId);
            infoData.put("COMPENSATE_ID", json.get("productId"));
            infoData.put("COMPENSATE_NAME", json.get("productName"));

            infoData.put("COMPENSATE_AMOUNT", json.get("compensateCash"));
            infoData.put("ORG_INTEGRAL_ID", orgIntegralId);
            infoData.put("EX_JSON", json.toJSONString());

            json.remove("command");
            JSONObject result = doCompensateService(json, command);
            if (result != null && StringUtils.equals(result.getString("respCode"), GWConstants.RET_CODE_SUCCESS)) {
                String opId = RandomKit.randomStr();
                // compensateType 页面固定1
                boolean setSendLog = setSendLog(getQuery(), "1", Constants.COMPENSATE_MODE7, infoData, opId);
                return EasyResult.ok(result.getJSONObject("respData"));
            }
            return EasyResult.fail(result != null && StringUtils.isNotBlank(result.getString("respDesc"))
                    ? result.getString("respDesc")
                    : "操作失败");
        } catch (Exception e) {
            logger.error("获取现金补偿配置失败,原因:" + e.getMessage(), e);
            return EasyResult.fail("查询状态失败：" + e.getMessage());
        }
    }

    /**
     * 坐席反馈异常（退货）
     *
     * @return
     */
    public JSONObject actionForFeedbackAnomaly() {
        JSONObject json = this.getJSONObject();
        try {
            String command = "feedbackAnomaly";
            json.put("operatePerson", OpenApiUserUtil.getUser(getRequest()).getUserAcc());
            JSONObject result = doCompensateService(json, command);
            if (result != null && StringUtils.equals(result.getString("respCode"), GWConstants.RET_CODE_SUCCESS)) {
                return EasyResult.ok(result.getJSONObject("respData"));
            }
            return EasyResult.fail(result != null && StringUtils.isNotBlank(result.getString("respDesc"))
                    ? result.getString("respDesc")
                    : "操作失败");
        } catch (Exception e) {
            logger.error("坐席反馈异常（退货）失败,原因:" + e.getMessage(), e);
            return EasyResult.fail("坐席反馈异常（退货）失败：" + e.getMessage());
        }
    }

    /**
     * 确认收货
     *
     * @return
     */
    public JSONObject actionForConfirmReceipt() {
        JSONObject json = this.getJSONObject();
        try {
            String command = "confirmReceipt";
            json.put("operatePerson", OpenApiUserUtil.getUser(getRequest()).getUserAcc());
            JSONObject result = doCompensateService(json, command);
            if (result != null && StringUtils.equals(result.getString("respCode"), GWConstants.RET_CODE_SUCCESS)) {
                return EasyResult.ok(result.getJSONObject("respData"));
            }
            return EasyResult.fail(result != null && StringUtils.isNotBlank(result.getString("respDesc"))
                    ? result.getString("respDesc")
                    : "操作失败");
        } catch (Exception e) {
            logger.error("确认收货失败,原因:" + e.getMessage(), e);
            return EasyResult.fail("确认收货失败：" + e.getMessage());
        }
    }

    /**
     * 批量确认收货
     *
     * @return
     */
    public JSONObject actionForConfirmReceiptList() {
        JSONObject json = this.getJSONObject();
        try {
            for (int i = 0; i < json.getJSONArray("list").size(); i++) {
                JSONObject item = json.getJSONArray("list").getJSONObject(i);
                JSONObject params = new JSONObject();
                String command = "confirmReceipt";
                params.put("operatePerson", OpenApiUserUtil.getUser(getRequest()).getUserAcc());
                params.put("compensateOrderNo", item.getString("compensateOrderNo"));
                JSONObject result = doCompensateService(params, command);
                if (result == null || !StringUtils.equals(result.getString("respCode"), GWConstants.RET_CODE_SUCCESS)) {
                    return EasyResult.fail(result != null && StringUtils.isNotBlank(result.getString("respDesc"))
                            ? result.getString("respDesc")
                            : "操作失败");
                }
            }
            return EasyResult.ok("确认收货成功");
        } catch (Exception e) {
            logger.error("确认收货失败,原因:" + e.getMessage(), e);
            return EasyResult.fail("确认收货失败：" + e.getMessage());
        }
    }

    /**
     * 不符合补偿单据提交
     */
    public JSONObject actionForNonConformance() {
        JSONObject reqJson = this.getJSONObject();
        String opId = RandomKit.randomStr();
        compLogger.info("<" + opId + "> 【不符合补偿单据提交】入参:" + reqJson.toJSONString());
        UserModel user = OpenApiUserUtil.getUser(this.getRequest());
        String sysTime = EasyDate.getCurrentDateString();
        String phone = reqJson.getString("phone");
        String customerName = reqJson.getString("customerName");
        String responsible = reqJson.getString("responsible");
        String orgCode = reqJson.getString("orgCode");
        String prodCode = reqJson.getString("prodCode");
        orgCode = OrgUtil.getCcOrgCode(orgCode, prodCode);// cc主体
        String orgIntegralId = reqJson.getString("orgIntegralId");
        String outOrderId = RandomKit.randomStr();
        JSONObject peakEndInfo = buildCompensationInfoData(reqJson, sysTime, phone, customerName, user,
                responsible, orgCode, outOrderId, orgIntegralId);
        compLogger.info("<" + opId + "> 构建不符合补偿信息:" + peakEndInfo.toJSONString());
        peakEndInfo.put("COMPENSATE_MODE", reqJson.getString("compensateMode"));
        peakEndInfo.put("COMPENSATE_TYPE", reqJson.getString("compensateType"));
        peakEndInfo.put("SUBMIT_STATES", "2");
        peakEndInfo.put("NON_CONFORMANCE_REASON", reqJson.getString("nonConformanceReason"));

        String contactOrderNo = reqJson.getString("contactOrderNo");
        if (StringUtils.isBlank(contactOrderNo)) {
            contactOrderNo = reqJson.getString("contactOrderCode");
        }
        peakEndInfo.put("CONTACT_ORDER_ID", contactOrderNo);
        String peakEndId = RandomKit.randomStr();
        EasyQuery query = this.getQuery();
        peakEndInfo.put("ID", peakEndId);
        try {
            String contactRecordId = query.queryForString(
                    "SELECT ID FROM C_NO_CONTACT WHERE CONTACT_ORDER_CODE = ?", new Object[]{contactOrderNo});
            List<JSONObject> peakEndList = queryPeakEndRecords(query, reqJson.getString("opId"), contactRecordId,
                    contactOrderNo, opId);
            List<JSONObject> notSaveList = peakEndList.stream()
                    .filter(item -> SubmitStates.NOT_SAVE.getStates().equals(item.getString("SUBMIT_STATES")))
                    .collect(Collectors.toList());
            Map<String, String> dictCodes = getDictCodes(user, DICT_QUOTA_NOT_ENOUGH);
            Map<String, String> exceedAuthorityCodes = getDictCodes(user, DICT_EXCEED_AUTHORITY);
            List<JSONObject> nonConformanceList = peakEndList.stream()
                    .filter(item -> {
                        String peakEndState = item.getString("SUBMIT_STATES");
                        String nonConformanceReason = item.getString("NON_CONFORMANCE_REASON");
                        String compensateType = item.getString("COMPENSATE_TYPE");
                        String type = compensateType.equals(Constants.PEAKEND_TYPE1) ? "middle" : "after";
                        compLogger.info("<" + opId + "> "+"不符合补偿原因 " + nonConformanceReason + "----"+ dictCodes.get(type) + exceedAuthorityCodes.get(type));
                        //不符合补偿原因 并且原因不是额度不足 或者 不是超额权限
                        return SubmitStates.NOT_SATISFIABLE.getStates().equals(peakEndState) && !StrUtil.equals(dictCodes.get(type), nonConformanceReason) && !StrUtil.equals(exceedAuthorityCodes.get(type), nonConformanceReason);
                    })
                    .collect(Collectors.toList());
            updateExistingRecordsStatus(query, notSaveList, opId);
            updateExistingRecordsStatus(query, nonConformanceList, opId);
            EasyRecord record = new EasyRecord("C_NO_PEAK_END_RECORD", "ID")
                    .setColumns(peakEndInfo);
            query.save(record);
            return EasyResult.ok(peakEndId);
        } catch (Exception e) {
            compLogger.error("补偿记录更新失败" + e.getMessage(), e);
            return EasyResult.fail("保存失败");
        }
    }

    /**
     * 商品列表接口
     *
     * @return
     */
    public JSONObject actionForCompensateProductList() {
        try {
            JSONObject json = this.getJSONObject();
            JSONObject data = new JSONObject();
            data.put("priceCeiling", json.get("priceCeiling"));
            data.put("priceFloor", json.get("priceFloor"));
            data.put("skuState", 2);
            data.put("skuStock", 1);
            data.put("pageIndex", json.getString("pageIndex"));
            data.put("pageCount", json.getString("pageSize"));
            data.put("operatePerson", OpenApiUserUtil.getUser(this.getRequest()).getUserName());
            logger.info("获取商品列表--->" + data);
            JSONObject result = doCompensateService(data, "compensateProductList");
            logger.info("回调接口获取商品列表--->" + result);
            if (result != null && StringUtils.equals(result.getString("respCode"), GWConstants.RET_CODE_SUCCESS)) {
                return EasyResult.ok(result.getJSONObject("respData"));
            }
            return EasyResult.fail(result != null && StringUtils.isNotBlank(result.getString("respDesc"))
                    ? result.getString("respDesc")
                    : "操作失败");
        } catch (Exception e) {
            logger.error("获取商品列表失败,原因:" + e.getMessage(), e);
            return EasyResult.fail("获取商品列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取实物补偿列表详情
     *
     * @return
     */
    public JSONObject actionForGetMaterialDetail() {
        try {
            JSONObject json = this.getJSONObject();
            logger.info("获取实物补偿列表详情--->" + json);
            JSONObject data = new JSONObject();
            JSONArray result = new JSONArray();
            data.put("operatePerson", OpenApiUserUtil.getUser(this.getRequest()).getUserName());
            if (!json.getJSONArray("detailVOList").isEmpty()) {
                for (int i = 0; i < json.getJSONArray("detailVOList").size(); i++) {
                    data.put("compensateOrderNo",
                            json.getJSONArray("detailVOList").getJSONObject(i).getString("compensateOrderNo"));
                    data.put("afterSalesId",
                            json.getJSONArray("detailVOList").getJSONObject(i).getString("afterSalesId"));
                    data.put("logisticsNo",
                            json.getJSONArray("detailVOList").getJSONObject(i).getString("logisticsNo"));
                    JSONObject res = doCompensateService(data, "getMaterialDetail");
                    JSONObject respData = new JSONObject();
                    respData.put("disSkuTitle",
                            json.getJSONArray("detailVOList").getJSONObject(i).getString("disSkuTitle"));
                    if (res != null && StringUtils.equals(res.getString("respCode"), GWConstants.RET_CODE_SUCCESS)) {
                        respData.putAll(res.getJSONObject("respData"));
                    }
                    result.add(respData);
                }
            }
            logger.info("获取实物补偿列表详情--->" + result);
            return EasyResult.ok(result);
        } catch (Exception e) {
            logger.error("获取实物补偿列表详情异常,原因:" + e.getMessage(), e);
            return EasyResult.fail("获取实物补偿列表详情失败：" + e.getMessage());
        }
    }

    /**
     * 应补未补提交接口
     */
    public JSONObject actionForSubmitErrorMessage() {
        try {
            JSONObject json = this.getJSONObject();
            submitTestLogger.info("应补未补提交接口--->" + json);
            JSONObject data = new JSONObject();
            JSONArray result = new JSONArray();
            JSONObject jsonObject1 = JSONObject.parseObject(json.getString("EX_JSON"));
            jsonObject1.put("contactOrderNo", json.getString("contactOrderNo"));
            JSONObject jsonObject2 = JSON.parseObject(JSON.toJSONString(jsonObject1), new TypeReference<JSONObject>() {
            });
            compensationApply(jsonObject2);
            EasyQuery query = this.getQuery();
            EasyRecord record = new EasyRecord("C_NO_PEAK_END_RECORD", "ID");
            record.set("ID", json.getString("ID"));
            JSONArray jsonArray = jsonObject1.getJSONArray("compensateDeatils");
            if (jsonArray != null && !jsonArray.isEmpty()) {
                record.set("COMPENSATE_ID", jsonArray.getJSONObject(0).getString("disSkuId"));
                record.set("COMPENSATE_NO", jsonArray.getJSONObject(0).getString("disSkuId"));
                record.set("COMPENSATE_NAME", jsonArray.getJSONObject(0).getString("disSkuTitle"));
                oplogger.info("[13] - COMPENSATE_AMOUNT" + jsonArray.getJSONObject(0).getDoubleValue("salePrice"));
                record.set("COMPENSATE_AMOUNT", jsonArray.getJSONObject(0).getDoubleValue("salePrice"));
            }
            record.set("CONTACT_ORDER_ID", json.getString("contactOrderNo"));
            record.set("SCENE_TYPE", json.getString("SCENE_TYPE"));
            record.set("SUBMIT_STATES", "1");
            //更新补偿赠送时间
            String sysTime = EasyDate.getCurrentDateString();
            record.set("CREATE_TIME", sysTime);
            query.update(record);
            logger.info("应补未补提交接口--->" + result);
            return EasyResult.ok(result);
        } catch (Exception e) {
            logger.error("应补未补提交接口异常,原因:" + e.getMessage(), e);
            return EasyResult.fail("应补未补提交接口失败：" + e.getMessage());
        }
    }

    private String getSerialId(JSONObject json) {
        if (StringUtils.isNotBlank(json.getString("opeSerialId"))) {
            return json.getString("opeSerialId");
        }
        String serialId = RandomKit.randomStr();
        json.put("opeSerialId", serialId);
        return serialId;
    }

    /**
     * 同步反馈结果
     */
    public JSONObject actionForSyncFeedbackResult() {
        String opId = RandomKit.randomStr();
        try {
            JSONObject requestJson = this.getJSONObject();
            loggerSync.info("<" + opId + ">" + "同步反馈结果--->" + JSON.toJSONString(requestJson));
            String contactOrderId = requestJson.getString("contactOrderId");
            String feedbackResult = requestJson.getString("feedbackResult");
            String aiResult = requestJson.getString("aiResult");
            String orgCode = requestJson.getString("orgCode");
            String prodCode = requestJson.getString("prodCode");
            String brandCode = requestJson.getString("brandCode");
            String orderServTypeCode = requestJson.getString("orderServTypeCode");
            String orderSerItem1Code = requestJson.getString("orderSerItem1Code");
            String orderSerItem2Code = requestJson.getString("orderSerItem2Code");
            if (StrUtil.isBlank(contactOrderId)) {
                return EasyResult.fail("接入单号不能为空");
            }
            EasyQuery query = this.getQuery();
            EasySQL sql = new EasySQL();
            int count = 0;
            if (StrUtil.isNotBlank(feedbackResult)) {
                feedbackResult = "1";
                sql.append("UPDATE C_NO_PEAK_END_RECORD SET FEEDBACK_RESULT = ? WHERE CONTACT_ORDER_ID = ?");
                count = query.executeUpdate(sql.getSQL(), new Object[]{feedbackResult, contactOrderId});
                loggerSync.info("<" + opId + ">" + "更新反馈结果sql--->" + sql.getSQL() + "  参数:"
                        + JSON.toJSONString(new Object[]{feedbackResult, contactOrderId}));
                loggerSync.info("<" + opId + ">" + "更新反馈结果成功条数--->" + count);
            }
            if (StrUtil.isAllNotBlank(aiResult, brandCode, orgCode, prodCode, orderServTypeCode, orderSerItem1Code,
                    orderSerItem2Code)) {
                sql.append("UPDATE C_NO_PEAK_END_RECORD SET AI_RESULT = ? WHERE ");
                sql.append("CONTACT_ORDER_ID = ? AND ORG_CODE = ? AND PROD_CODE = ? AND BRAND_CODE = ? ");
                sql.append("AND ORDER_SERV_TYPE_CODE = ? AND ORDER_SER_ITEM1_CODE = ? AND ORDER_SER_ITEM2_CODE = ?");
                count = query.executeUpdate(sql.getSQL(), new Object[]{aiResult, contactOrderId, orgCode, prodCode,
                        brandCode, orderServTypeCode, orderSerItem1Code, orderSerItem2Code});
                loggerSync.info("<" + opId + ">" + "更新AI推荐方案sql--->" + sql.getSQL() + "  参数:"
                        + JSON.toJSONString(new Object[]{aiResult, contactOrderId, orgCode, prodCode, brandCode,
                        orderServTypeCode, orderSerItem1Code, orderSerItem2Code}));
                loggerSync.info("<" + opId + ">" + "更新AI推荐方案成功条数--->" + count);
            } else {
                loggerSync.info("<" + opId + ">" + "更新AI推荐方案必须条件不能为空,参数:"
                        + JSON.toJSONString(new Object[]{aiResult, contactOrderId, orgCode, prodCode, brandCode,
                        orderServTypeCode, orderSerItem1Code, orderSerItem2Code}));
            }
            return EasyResult.ok();
        } catch (Exception e) {
            loggerSync.error("<" + opId + ">" + "同步反馈结果异常,原因:" + e.getMessage(), e);
            return EasyResult.fail("同步反馈结果失败：" + e.getMessage());
        }
    }

    private Map<String, String> getDictCodes(UserModel user, String dictValue) {
        Map<String, String> result = new HashMap<>();
        if (user == null || user.getEpCode() == null || dictValue == null) {
            return result;
        }

        Map<String, Object> middleReasonMap = Optional.ofNullable(
                        DictCache.getMapEnableDictListByGroupCode(user.getEpCode(), DICT_GROUP_MIDDLE))
                .orElse(new HashMap<>());

        Map<String, Object> afterReasonMap = Optional.ofNullable(
                        DictCache.getMapEnableDictListByGroupCode(user.getEpCode(), DICT_GROUP_AFTER))
                .orElse(new HashMap<>());

        for (Map.Entry<String, Object> entry : middleReasonMap.entrySet()) {
            if (dictValue.equals(entry.getValue())) {
                result.put("middle", entry.getKey());
                break;
            }
        }

        for (Map.Entry<String, Object> entry : afterReasonMap.entrySet()) {
            if (dictValue.equals(entry.getValue())) {
                result.put("after", entry.getKey());
                break;
            }
        }

        return result;
    }
}
