<?xml version="1.0" encoding="UTF-8"?>
<!-- 
	id:必须全局唯一，命名方式：应用名称+菜单名称
	name：菜单或功能名称
	type：资源类型，1：应用 ，2：菜单，3：功能，9: 其它
	state：状态，0:正常，缺省 ，1：暂停
	portal:所属应用，不填写缺省为通用portal，根据菜单的归属，可以填写具体的所属的portal，例如：my_portal
	index:排序，菜单的显示按照升序进行排序。
	icon：菜单图标
 -->
<resources>
   <resource id="neworder" name="工单与回访" url="" type="2" portal="" state="0" order="4" index = "5">
	   <resource id="PeakEnd" name="服务补偿" url="" type="2" portal="" state="0" order="4" index = "20">
			<resource id="peak_end_order_list" name="补偿发布" type="2" icon="" portal="" state="0" url="/PeakEnd/pages/order/order-list.jsp" index="1">
		   		<resource id="peak_end_order_publish" name="发布"   url="" type="3" portal="" icon="" state="0" index="7"/>
		   		<resource id="peak_end_order_appoit"  name="指派" url="" type="3" portal="" icon="" state="0" index="8"/>
		   		<resource id="peak_end_order_recycle" name="回收"   url="" type="3" portal="" icon="" state="0" index="9" />
		   		<resource id="peak_end_order_delete" name="删除"   url="" type="3" portal="" icon="" state="0" index="9" />
		   		<resource id="peak_end_order_dataImport" name="数据导入"   url="" type="3" portal="" icon="" state="0" index="9" />
		    	<resource id="peak_end_order_monitor" name="班长权限"   url="" type="3" portal="" icon="" state="0" index="10" />
		    </resource>
		    <resource id="peak_end_handle_list" name="补偿处理"  type="2" portal="" icon="" state="0" url="/PeakEnd/pages/order/order-handle-list.jsp" index="2">
		   		<resource id="peak_end_handle_apply" name="申请权限"   url="" type="3" portal="" icon="" state="0" index="12"/>
		   		<resource id="peak_end_handle_tohandle" name="处理权限"   url="" type="3" portal="" icon="" state="0" index="13"/>
		   		<resource id="peak_end_handle_export" name="导出权限"   url="" type="3" portal="" icon="" state="0" index="15"/>
		    </resource>
		    <resource id="peak_end_publish_list" name="补偿发布记录" type="2" icon="" portal="" state="0" url="/PeakEnd/pages/order/order-publish-list.jsp" index="3"/>
			<resource id="peak_end_revisit_result_list" name="补偿结果查询"  type="2" icon="" portal="" state="0" url="/PeakEnd/pages/order/revisit-result-list.jsp" index="5"/>
			<resource id="peak_end_compensate_record" name="坐席申请补偿明细(CC)"  type="2" icon="" portal="" state="0" url="/PeakEnd/pages/peakEnd/peakEnd-record.jsp" index="6">
				<resource id="peak_end_compensate_reload" name="查看"   url="" type="3" portal="" icon="" state="0" index="1"/>
				<resource id="peak_end_compensate_export" name="导出权限"   url="" type="3" portal="" icon="" state="0" index="2"/>
			</resource>
			<resource id="peak_end_logistics_list" name="物流信息查询"  type="2" icon="" portal="" state="0" url="/PeakEnd/pages/peakEnd/logistics-list.jsp" index="7">
				<resource id="peak_end_logistics_reload" name="查看"   url="" type="3" portal="" icon="" state="0" index="1"/>
				<resource id="peak_end_logistics_delete" name="删除权限"   url="" type="3" portal="" icon="" state="0" index="2"/>
				<resource id="peak_end_logistics_import" name="导入权限"   url="" type="3" portal="" icon="" state="0" index="3"/>
			</resource>
			<resource id="peak_end_script_config" name="补偿脚本配置"  type="2" icon="" portal="" state="0" url="/PeakEnd/pages/script/script-list.jsp" index="8"/>
			<resource id="peak_end_org_intergal_config" name="事业部补偿金额配置"  type="2" icon="" portal="" state="0" url="/PeakEnd/pages/orgIntergal/org-intergal-list.jsp" index="9"/>
		    <resource id="peak_end_cash_script_config" name="坐席补偿上限配置" type="2" icon="" portal="" state="0" url="/PeakEnd/pages/cashScript/cash-script-list.jsp" index="10" />
		    <resource id="peak_end_cash_compensate_list" name="现金补偿执行情况（CSS）" type="2" icon="" portal="" state="0" url="/PeakEnd/pages/peakEnd/cashCompensate/peakEnd-cash-list.jsp" index="11" />
		    <resource id="peak_end_cash_compensate_admin" name="现金补偿执行情况（CSS）-管理员权限" type="3" icon="" portal="" state="0"  index="12" />
		    <resource id="peak_end_gift_compensate_list" name="实物补偿执行情况（CSS）" type="2" icon="" portal="" state="0" url="/PeakEnd/pages/vue/giftRecord.html" index="13" />
		    <resource id="peak_end_admin_gift_compensate_list" name="管理员-实物补偿执行情况（CSS）" type="2" icon="" portal="" state="0" url="/PeakEnd/pages/vue/giftRecordAdmin.html" index="14" />
		</resource>
   </resource>
</resources>
