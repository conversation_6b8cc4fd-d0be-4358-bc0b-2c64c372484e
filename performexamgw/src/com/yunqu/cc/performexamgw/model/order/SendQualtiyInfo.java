package com.yunqu.cc.performexamgw.model.order;

import java.util.List;


/**
 * 
 */
public class SendQualtiyInfo implements java.io.Serializable {
	
/*	//消息类型：sendQualtiyInfoByMidea
	private String messageId;//必填
	//消息流水号
	private String serialId;
	//时间戳（格式：long型的毫秒数）
	private String timestamp;*/
	//通话记录唯一ID(通话ID)
	private String serialId;
	//质检平台任务ID
	private String taskId;
	//数据类型 0 语音 1文本
	private String mediaType;
	//通话记录所属业务ID
	private String busiId;
	//技能组ID。
	private String groupId;
	//技能组名称
	private String groupName;
	//坐席工号
	private String agentId;
	private String agentNo;
	private String agentAcc;
	//坐席姓名
	private String agentName;
	//主叫
	private String caller;
	//被叫
	private String called;
	//呼叫类型 0-呼入 1-呼出
	private String callType;
	//录音访问URL，返回对应RECORD_FILE字段值
	private String recordFileName;
	//通话开始时间
	private String startTime;
	//通话结束时间
	private String endTime;
	//01 客户挂断 02 坐席挂断
	private String hangupType;
	//质检结果回传url
	private String callBackUrl;
	//渠道名称
	private String channelName;
	//渠道编码
	private String channelkey;
	//运营区域
	private String operatingArea;
	private String operatingAreaCode;
	//坐席班组
	private String agentTeam;
	private String agentTeamCode;
	//入职时长/月
	private String lengthInduction;
	//事业部
	private String facilitiesDept;
	//产品品类
	private String productCategory;
	private String productCategoryCode;
	//服务请求
	private String serviceRequest;
	//服务请求细则
	private String serviceRequestDetails;
	//必填)满意度 1：非常满意 2：满意 3：一般 4：不满意 5：对结果不满意 91：未评价 92：无效按键
	
	///昵称
	private String custName;
	
	//会话ID
	private String recordSerialId;
	//客户标识
	private String custSessionId;
	
	private String satistactionDegree;
	//对话明细列表
	private List<Chat> chatList;
	//工单信息  格式：JSON串
	private OrderInfo orderInfo;
	
	public String getSerialId() {
		return serialId;
	}
	public void setSerialId(String serialId) {
		this.serialId = serialId;
	}
	public String getTaskId() {
		return taskId;
	}
	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}
	public String getBusiId() {
		return busiId;
	}
	public void setBusiId(String busiId) {
		this.busiId = busiId;
	}
	public String getGroupId() {
		return groupId;
	}
	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public String getAgentId() {
		return agentId;
	}
	public void setAgentId(String agentId) {
		this.agentId = agentId;
	}
	public String getAgentName() {
		return agentName;
	}
	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}
	public String getCaller() {
		return caller;
	}
	public void setCaller(String caller) {
		this.caller = caller;
	}
	public String getCalled() {
		return called;
	}
	public void setCalled(String called) {
		this.called = called;
	}
	public String getCallType() {
		return callType;
	}
	public void setCallType(String callType) {
		this.callType = callType;
	}
	public String getRecordFileName() {
		return recordFileName;
	}
	public void setRecordFileName(String recordFileName) {
		this.recordFileName = recordFileName;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getCallBackUrl() {
		return callBackUrl;
	}
	public void setCallBackUrl(String callBackUrl) {
		this.callBackUrl = callBackUrl;
	}
	public String getChannelName() {
		return channelName;
	}
	public void setChannelName(String channelName) {
		this.channelName = channelName;
	}
	public String getOperatingArea() {
		return operatingArea;
	}
	public void setOperatingArea(String operatingArea) {
		this.operatingArea = operatingArea;
	}
	public String getAgentTeam() {
		return agentTeam;
	}
	public void setAgentTeam(String agentTeam) {
		this.agentTeam = agentTeam;
	}
	public String getLengthInduction() {
		return lengthInduction;
	}
	public void setLengthInduction(String lengthInduction) {
		this.lengthInduction = lengthInduction;
	}
	public String getFacilitiesDept() {
		return facilitiesDept;
	}
	public void setFacilitiesDept(String facilitiesDept) {
		this.facilitiesDept = facilitiesDept;
	}
	public String getProductCategory() {
		return productCategory;
	}
	public void setProductCategory(String productCategory) {
		this.productCategory = productCategory;
	}
	public String getServiceRequest() {
		return serviceRequest;
	}
	public void setServiceRequest(String serviceRequest) {
		this.serviceRequest = serviceRequest;
	}
	public String getServiceRequestDetails() {
		return serviceRequestDetails;
	}
	public void setServiceRequestDetails(String serviceRequestDetails) {
		this.serviceRequestDetails = serviceRequestDetails;
	}
	public String getSatistactionDegree() {
		return satistactionDegree;
	}
	public void setSatistactionDegree(String satistactionDegree) {
		this.satistactionDegree = satistactionDegree;
	}
	public List<Chat> getChatList() {
		return chatList;
	}
	public void setChatList(List<Chat> chatList) {
		this.chatList = chatList;
	}

	public OrderInfo getOrderInfo() {
		return orderInfo;
	}
	public void setOrderInfo(OrderInfo orderInfo) {
		this.orderInfo = orderInfo;
	}
	public String getHangupType() {
		return hangupType;
	}
	public void setHangupType(String hangupType) {
		this.hangupType = hangupType;
	}
	public String getMediaType() {
		return mediaType;
	}
	public void setMediaType(String mediaType) {
		this.mediaType = mediaType;
	}
	public String getCustName() {
		return custName;
	}
	public void setCustName(String custName) {
		this.custName = custName;
	}
	
	public String getAgentTeamCode() {
		return agentTeamCode;
	}
	public void setAgentTeamCode(String agentTeamCode) {
		this.agentTeamCode = agentTeamCode;
	}
	public String getOperatingAreaCode() {
		return operatingAreaCode;
	}
	public void setOperatingAreaCode(String operatingAreaCode) {
		this.operatingAreaCode = operatingAreaCode;
	}
	public String getProductCategoryCode() {
		return productCategoryCode;
	}
	public void setProductCategoryCode(String productCategoryCode) {
		this.productCategoryCode = productCategoryCode;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public String getAgentAcc() {
		return agentAcc;
	}
	public void setAgentAcc(String agentAcc) {
		this.agentAcc = agentAcc;
	}
	public String getChannelkey() {
		return channelkey;
	}
	public void setChannelkey(String channelkey) {
		this.channelkey = channelkey;
	}

	public String getRecordSerialId() {
		return recordSerialId;
	}
	public void setRecordSerialId(String recordSerialId) {
		this.recordSerialId = recordSerialId;
	}
	public String getCustSessionId() {
		return custSessionId;
	}
	public void setCustSessionId(String custSessionId) {
		this.custSessionId = custSessionId;
	}
	
}
