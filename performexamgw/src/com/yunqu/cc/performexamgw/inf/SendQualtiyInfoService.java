package com.yunqu.cc.performexamgw.inf;

import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import com.yunqu.cc.performexamgw.dao.QcRecordDao;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.OrderCommand;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.performexamgw.base.CommonLogger;
import com.yunqu.cc.performexamgw.base.Constants;
import com.yunqu.cc.performexamgw.model.ThreadPoolKit;
import com.yunqu.cc.performexamgw.model.order.Chat;
import com.yunqu.cc.performexamgw.model.order.OrderInfo;
import com.yunqu.cc.performexamgw.model.order.SendQualtiyInfo;
import com.yunqu.cc.performexamgw.model.order.contactOrderVO;
import com.yunqu.cc.performexamgw.model.order.contactUserRequireVOList;
import com.yunqu.cc.performexamgw.service.impl.CommMap;
import com.yunqu.cc.performexamgw.utils.tempHttp.HttpResp;
import com.yunqu.cc.performexamgw.utils.tempHttp.HttpUtil;

public class SendQualtiyInfoService extends IService {

	public static Logger logger = CommonLogger.getLogger("SendQualtiyInfo");
	public static Logger loggerReSend = CommonLogger.getLogger("SendQualtiyInfoRes");
	public static Logger loggerHis = CommonLogger.getLogger("SendQualtiyInfoHis");
	public static Logger loggerStat = CommonLogger.getLogger("send_stat");
	public static Logger loggerError = CommonLogger.getLogger("send_error");
	public static Logger saveQcResultLogger = CommonLogger.getLogger("saveQcResult");
	public static Logger saveQcResultIdLogger = CommonLogger.getLogger("saveQcResult-id");
	private final EasyCache cache = CacheManager.getMemcache();

	private QcRecordDao qcRecordDao = new QcRecordDao();

	protected static EasyQuery getQuery()
    {
 	  return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
    }

	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		String command = json.getString("command");
		logger.info(CommonUtil.getClassNameAndMethod(this) + "参数：" + command);		
		if("SendQualtiyInfo".equals(command)){
			return SendQualtiyInfo();
		}else if("sendHisQualtiyInfo".equals(command)){//转写失败数据定时重推
			String cacheId = "JOB_QC_AUTO_HIS";
			String value = cache.get(cacheId);
			if(value==null||"".equals(value)){
				cache.put(cacheId,"1",60*60*2);
				logger.info(Thread.currentThread().getName()+"开始推送....");
				sendHisQualtiyInfo();
				logger.info(Thread.currentThread().getName()+"结束推送....");
				logger.info("---------------------------");
				cache.delete(cacheId);
			}
		}else if("resendQualtiyInfoByTaskId".equals(command)){//历史数据手工重推
			resendQualtiyInfoByTaskId(json);
		}else if("saveQcResult".equals(command)){
			return saveQcResult(json.getJSONObject("params"));
		}

		logger.info(CommonUtil.getClassNameAndMethod(this) + "调用成功，参数：" + command);		
		return null;
	}
	
	
	public static JSONObject dict(List<EasyRow> list){
		JSONObject dict=new JSONObject();
		JSONObject data=new JSONObject();
		try {
			dict.put("total", list.size());
			for (int i = 0; i < list.size(); i++) {
				data.put(list.get(i).getColumnValue("CODE"),list.get(i).getColumnValue("NAME"));
			}
		} catch (Exception e) {
			logger.error("出错"+",原因"+e.getMessage(),e);
			e.printStackTrace();
		}
		dict.put("data", data);
		return dict;
	}
	private static JSONObject deptDict;
	private static JSONObject mideaSkillDict;
	private static JSONObject callSkillDict;
	private static JSONObject womCcAppointTimeDict;

	
	public  static JSONObject SendQualtiyInfo() {
		try {
			String sql="select  *  from  C_NO_AUTO_QC_TASK_CONFIG where IS_ENABLE =?  ";
			List<JSONObject> taskList = getQuery().queryForList(sql,new Object[]{"Y"}, new JSONMapperImpl());
			String callSkillSql="select SKILL_CODE as CODE,SKILL_NAME as NAME from C_QUEUE where  SKILL_CODE is not null";
			List<EasyRow> callSkill = getQuery().queryForList(callSkillSql,new Object[]{});
			callSkillDict = dict(callSkill).getJSONObject("data");
			String mideaSkillSql="select code,name from V_CF_SKILLS ";
			List<EasyRow> mideaSkill = getQuery().queryForList(mideaSkillSql,new Object[]{});
			mideaSkillDict = dict(mideaSkill).getJSONObject("data");
			String deptSql="select DEPT_CODE as code,DEPT_NAME as  name from C_YG_V_MARS_DEPT ";
			List<EasyRow> dept = getQuery().queryForList(deptSql,new Object[]{});
			deptDict = dict(dept).getJSONObject("data");
			CommMap commMap=new CommMap();
			Map womCcAppointTime = commMap.getMapSysCode("WOM_CC_APPOINT_TIME","");
			womCcAppointTimeDict = new JSONObject(womCcAppointTime);

			for(JSONObject task:taskList){
				String qcType=task.getString("QC_TYPE");
				if (qcType.equals("1")) {//通用质检 保存记录
					
				}
				String taskData=task.getString("TASK_DATA");
				try {//怕部分任务出现问题
					String dataTime=task.getString("AUTO_QC_DATA_TIME");
					JSONObject timeJson = getTime("",dataTime);//获取当前任务的自动质检时间范围
					String startTime = timeJson.getString("startTime");
					String endTime = timeJson.getString("endTime");
					//专项有配置时间的话需要额外处理
					if(StringUtils.isNotBlank(task.getString("START_TIME"))&&StringUtils.isNotBlank(task.getString("END_TIME"))){
						//无历史时间 当前质检时间大于配置的结束时间 则为质检结束时间到开始时间
						//无历史时间 但是质检时间小于配置的结束时间  则为质检时间内的质检内容 到开始时间
						//有历史时间 但是质检时间小于配置的结束时间  则为质检时间当前质检时间 到历史时间
						//有历史时间 但是质检时间大于配置的结束时间   则为质检配置的结束时间 到历史时间
						//开始时间大于质检时间则跳过
						//历史时间等于配置的结束时间跳过
						if(task.getString("START_TIME").compareTo(endTime)>0){
							logger.info(taskData+"自动质检任务的开始时间大于质检时间，跳过");
							continue;
						}
						if(StringUtils.isBlank(task.getString("AUTO_QC_DATA_TIME"))){//无历史时间
								// 当前质检时间大于配置的结束时间 则为质检结束时间到开始时间
							if(endTime.compareTo(task.getString("END_TIME"))>0){
								endTime=task.getString("END_TIME");
								startTime=task.getString("START_TIME");
								logger.info(taskData+"当前质检时间大于配置的结束时间 则为质检结束时间到开始时间"+startTime+"--》"+endTime);
							}else{
								//无历史时间 但是质检时间小于配置的结束时间  则为质检时间内的质检内容 到开始时间
								//endTime不变
								//endTime=endTime;
								startTime=task.getString("START_TIME");
								logger.info(taskData+"无历史时间 但是质检时间小于配置的结束时间  则为质检时间内的质检内容 到开始时间"+startTime+"--》"+endTime);
							}
						}else {//有历史时间 
							//历史时间等于配置的结束时间跳过
							if(task.getString("END_TIME").equals(task.getString("AUTO_QC_DATA_TIME"))){
								logger.info(taskData+"自动质检任务的历史时间等于配置的结束时间，跳过");
								continue;
							}else if(endTime.compareTo(task.getString("END_TIME"))>0){
								//但是质检时间大于配置的结束时间  则为质检配置的结束时间 到历史时间
								endTime=task.getString("END_TIME");
								startTime=task.getString("AUTO_QC_DATA_TIME");
								logger.info(taskData+"但是质检时间大于配置的结束时间  则为质检配置的结束时间 到历史时间"+startTime+"--》"+endTime);
							}else{
								//有历史时间 但是质检时间小于配置的结束时间  则为质检时间当前质检时间 到历史时间
								//endTime=endTime;	
								startTime=task.getString("AUTO_QC_DATA_TIME");
								logger.info(taskData+"有历史时间 但是质检时间小于配置的结束时间  则为质检时间当前质检时间 到历史时间"+startTime+"--》"+endTime);

							}
						}
					}
					logger.info(task.getString("ID")+"自动质检");

					JSONObject taskConfig = new JSONObject();
					taskConfig.put("AUTO_QC_DATA_TIME", endTime);
					taskConfig.put("ID", task.getString("ID"));
					EasyRecord taskConfigRecord = new EasyRecord("C_NO_AUTO_QC_TASK_CONFIG", "ID").setColumns(taskConfig);
					boolean update = getQuery().update(taskConfigRecord);//更新最新的同步记录
					EasySQL oracleSql=new EasySQL();
					//处理多选 
					String orgCode="";
					if(!StringUtils.isBlank(task.getString("ORG_CODE"))){
						String[] split = task.getString("ORG_CODE").split(",");
						orgCode=getInData(split);
					}
					
					String serviceRequireTypeCode="";
					if(!StringUtils.isBlank(task.getString("SERVICE_REQUIRE_TYPE_CODE"))){
						String[] split = task.getString("SERVICE_REQUIRE_TYPE_CODE").split(",");
						serviceRequireTypeCode=getInData(split);
					}
					String serviceRequireItemCode="";
					if(!StringUtils.isBlank(task.getString("SERVICE_REQUIRE_ITEM_CODE"))){
						String[] split = task.getString("SERVICE_REQUIRE_ITEM_CODE").split(",");
						serviceRequireItemCode=getInData(split);
					}
					String serviceRequireItemCode2="";
					if(!StringUtils.isBlank(task.getString("SERVICE_REQUIRE_ITEM_CODE2"))){
						String[] split = task.getString("SERVICE_REQUIRE_ITEM_CODE2").split(",");
						serviceRequireItemCode2=getInData(split);
					}
					
					String zxserviceRequireTypeCode="";
					if(!StringUtils.isBlank(task.getString("ZX_SERVICE_REQUIRE_TYPE_CODE"))){
						String[] split = task.getString("ZX_SERVICE_REQUIRE_TYPE_CODE").split(",");
						zxserviceRequireTypeCode=getInData(split);
					}
					String zxserviceRequireItemCode="";
					if(!StringUtils.isBlank(task.getString("ZX_SERVICE_REQUIRE_ITEM_CODE"))){
						String[] split = task.getString("ZX_SERVICE_REQUIRE_ITEM_CODE").split(",");
						zxserviceRequireItemCode=getInData(split);
					}
					String zxserviceRequireItemCode2="";
					if(!StringUtils.isBlank(task.getString("ZX_SERVICE_REQUIRE_ITEM_CODE2"))){
						String[] split = task.getString("ZX_SERVICE_REQUIRE_ITEM_CODE2").split(",");
						zxserviceRequireItemCode2=getInData(split);
					}
					String channelCode="";
					if(!StringUtils.isBlank(task.getString("CHANNEL_CODE"))){
						String[] split = task.getString("CHANNEL_CODE").split(",");
						channelCode=getInData(split);
					}
					
					logger.info(taskData+"-->"+task.getString("ORG_CODE")+"转换orgCode："+orgCode);
					if(Constants.ORDER_TYPE_NOT_ORDER_CALL.equals(task.getString("ORDER_TYPE"))){
						//以防万一工单数据前后多查询一天
						String orderStartTime=addDay(startTime,-1);
						String orderEndTime=addDay(endTime,1);
						//无工单纯话务
						oracleSql.append("select emp.ENTRY_MONTHS,emp.WORK_NO  ");
						oracleSql.append(",call.AGENT_ACC as AGENTACC ,call.AGENT_DEPT as AGENTDEPT ,CALL.AGENT_NAME AS AGENTNAME,CALL.SESSION_ID AS SESSION_ID,CALL.SESSION_ID AS SESSIONID,CALL.CALLED,CALL.CALLER,'' as CHANNEL_NAME , '' as CHANNEL_KEY  ");
						oracleSql.append(",DIRECTION,RINGING_TIME,ANSWER_TIME,END_TIME,SKILL_CODE,HANGUP_TYPE,VOICE_URL,SATISFACTION_CODE,call.ID AS CALL_ID  ");
						oracleSql.append("from  C_PF_CALL_RECORD call  ");
						oracleSql.append("LEFT JOIN C_YG_EMPLOYEE emp on call.AGENT_ACC =emp.USER_ACC and emp.STATUS!=4  ");
						oracleSql.append(startTime,"where ANSWER_TIME >=? ");
						oracleSql.append(endTime,"and ANSWER_TIME< ?");
						oracleSql.append("AND NOT EXISTS (SELECT 1 FROM C_NO_APPEALLIST app ");
						oracleSql.append("WHERE call.SESSION_ID=app.SESSION_ID ");
						oracleSql.append(orderStartTime,"AND  app.REQUIRE_CREATE_TIME > ? ");
						oracleSql.append(orderEndTime,"AND  app.REQUIRE_CREATE_TIME < ? ");
						oracleSql.append(")");
						oracleSql.append(task.getString("SKILL_CODE")," and SKILL_CODE =?");
					}else if(Constants.ORDER_TYPE_ZX_ORDER.equals(task.getString("ORDER_TYPE"))){
						//咨询工单
						oracleSql.append("select  ord.*,emp.ENTRY_MONTHS,emp.WORK_NO,media.AGENT_ACC as AGENTACC , media.AGENT_DEPT as AGENTDEPT ,media.AGENT_NAME AS AGENTNAME,media.SESSION_ID AS SESSIONID,'' as CALLED,'' as CALLER,media.CHANNEL_NAME,media.CHANNEL_KEY  ");
						oracleSql.append(",'01' as DIRECTION,BEGIN_TIME as RINGING_TIME,BEGIN_TIME as ANSWER_TIME,END_TIME,GROUP_ID as SKILL_CODE,'' as HANGUP_TYPE,'' as VOICE_URL, SATISF_CODE as SATISFACTION_CODE,media.SERIAL_ID AS CALL_ID  ");
						oracleSql.append(",ord.ID as APP_ID ");
						//oracleSql.append(",con.SOURCE_ORDER_CODE,con.CUSTOMER_ID,con.CUSTOMER_CODE,con.CUSTOMER_PHONE,con.IS_INSTEAD_ORDER ,con.SERVICE_CUSTOMER_MOBILE,con.SERVICE_CUSTOMER_MOBILE2,con.SERVICE_CUSTOMER_MOBILE3,con.SERVICE_CUSTOMER_ADDRESS,con.ECM_ORDER_VO ");
						//oracleSql.append(",con.CUSTOMER_MOBILEPHONE1,con.CUSTOMER_MOBILEPHONE2,con.CUSTOMER_MOBILEPHONE3,con.SERVICE_CUSTOMER_MOBILE,con.CONTACT_ORDER_STATUS,con.CREATE_TIME as ORDER_CREATE_TIME,con.CUSTOMER_TYPE,con.CUSTOMER_ADDRESS ");
						//oracleSql.append(",con.AREA_NAME,con.AREA_CODE,con.AREA_NUM,con.CONTACT_TYPE_CODE,con.CUSTOMER_NAME,con.CUSTOMER_LEVEL,con.IS_INSTEAD_ORDER,con.SOURCE_ORDER_CODE,con.id as CONTACT_ORDER_SERAIL_ID,con.OTHER_MODULE_NAME,con.CUSTOMER_STAR_LEVEL,con.SERVICE_CUSTOMER_NAME");
						oracleSql.append("from  C_OL_CONSULT_ORDER ord  ");
						oracleSql.append("LEFT JOIN YCBUSI.CC_MEDIA_RECORD media on media.SERIAL_ID=ord.SESSION_ID  ");
						oracleSql.append("LEFT JOIN C_YG_EMPLOYEE emp on media.AGENT_ACC =emp.USER_ACC and emp.STATUS!=4 ");
						oracleSql.append(startTime," where ord.create_time >=? ");
						oracleSql.append(endTime,"  and ord.create_time< ?"); 
						if(!StringUtils.isBlank(orgCode)){
							oracleSql.append(" and ord.ORG_CODE in ("+orgCode+")");
						}
						if(!StringUtils.isBlank(zxserviceRequireTypeCode)	){
							oracleSql.append(" and ord.ORDER_TYPE_FIRST in ("+zxserviceRequireTypeCode+")");
						}
						if(!StringUtils.isBlank(zxserviceRequireItemCode)	){
							oracleSql.append(" and ord.ORDER_TYPE_SECOND in ("+zxserviceRequireItemCode+")");
						}
						if(!StringUtils.isBlank(zxserviceRequireItemCode2)	){
							oracleSql.append(" and ord.ORDER_TYPE_THIRD in ("+zxserviceRequireItemCode2+")");
						}
						if(!StringUtils.isBlank(channelCode)){
							oracleSql.append(" and media.channel_key in ("+channelCode+")");
						}
						oracleSql.append(task.getString("SKILL_CODE")," and GROUP_ID =?");
						oracleSql.append("and  media.BEGIN_TIME is not null");
					}else{
						if("1".equals(taskData)){//在线
							oracleSql.append("select  app.*,emp.ENTRY_MONTHS,emp.WORK_NO,media.AGENT_ACC as AGENTACC , media.AGENT_DEPT as AGENTDEPT ,media.AGENT_NAME AS AGENTNAME,media.SESSION_ID AS SESSIONID,'' as CALLED,'' as CALLER,media.CHANNEL_NAME,media.CHANNEL_KEY  ");
							oracleSql.append(",'01' as DIRECTION,BEGIN_TIME as RINGING_TIME,BEGIN_TIME as ANSWER_TIME,END_TIME,GROUP_ID as SKILL_CODE,'' as HANGUP_TYPE,'' as VOICE_URL, SATISF_CODE as SATISFACTION_CODE,media.SERIAL_ID AS CALL_ID  ");
							oracleSql.append(",app.ID as APP_ID,con.SOURCE_ORDER_CODE,con.CUSTOMER_ID,con.CUSTOMER_CODE,con.CUSTOMER_PHONE,con.IS_INSTEAD_ORDER ,con.SERVICE_CUSTOMER_MOBILE,con.SERVICE_CUSTOMER_MOBILE2,con.SERVICE_CUSTOMER_MOBILE3 ");
							oracleSql.append(",con.SERVICE_CUSTOMER_ADDRESS,con.ECM_ORDER_VO ");
							oracleSql.append(",con.CUSTOMER_MOBILEPHONE1,con.CUSTOMER_MOBILEPHONE2,con.CUSTOMER_MOBILEPHONE3,con.SERVICE_CUSTOMER_MOBILE,con.CONTACT_ORDER_STATUS,con.CREATE_TIME as ORDER_CREATE_TIME,con.CUSTOMER_TYPE,con.CUSTOMER_ADDRESS ");
							oracleSql.append(",con.AREA_NAME,con.AREA_CODE,con.AREA_NUM,con.CONTACT_TYPE_CODE,con.CUSTOMER_NAME,con.CUSTOMER_LEVEL,con.IS_INSTEAD_ORDER,con.SOURCE_ORDER_CODE,con.id as CONTACT_ORDER_SERAIL_ID,con.OTHER_MODULE_NAME,con.CUSTOMER_STAR_LEVEL,con.SERVICE_CUSTOMER_NAME");
							oracleSql.append(",appe.MACHINE_ID ");
							oracleSql.append("from  C_NO_APPEALLIST app left join C_NO_CONTACT  con on app.CONTACT_SERAIL_ID=con.ID ");
							oracleSql.append("LEFT JOIN YCBUSI.CC_MEDIA_RECORD media on media.SERIAL_ID=app.SESSION_ID and app.CREATE_ACC=media.AGENT_ACC and  app.MODIFY_ACC is null ");
							oracleSql.append("LEFT JOIN C_YG_EMPLOYEE emp on media.AGENT_ACC =emp.USER_ACC and emp.STATUS!=4 ");
							oracleSql.append("LEFT JOIN C_NO_APPEALLIST_EXTEND appe on appe.APPEAL_ID =app.ID ");
							oracleSql.append(startTime," where REQUIRE_CREATE_TIME >=? ");
							oracleSql.append(endTime,"  and REQUIRE_CREATE_TIME< ?"); 
							if(!StringUtils.isBlank(orgCode)	){
								oracleSql.append(" and app.ORG_CODE in ("+orgCode+")");
							}
							if(!StringUtils.isBlank(serviceRequireTypeCode)	){
								oracleSql.append(" and app.CONTACT_ORDER_SERV_TYPE_CODE in ("+serviceRequireTypeCode+")");
							}
							if(!StringUtils.isBlank(serviceRequireItemCode)	){
								oracleSql.append(" and app.CONTACT_ORDER_SER_ITEM1_CODE in ("+serviceRequireItemCode+")");
							}
							if(!StringUtils.isBlank(serviceRequireItemCode2)	){
								oracleSql.append(" and app.CONTACT_ORDER_SER_ITEM2_CODE in ("+serviceRequireItemCode2+")");
							}
							if(!StringUtils.isBlank(channelCode)){
								oracleSql.append(" and media.channel_key in ("+channelCode+")");
							}
							oracleSql.append(task.getString("SKILL_CODE")," and GROUP_ID =?");
							oracleSql.append("and con.CONTACT_ORDER_STATUS is null");
							oracleSql.append("and  media .BEGIN_TIME is not null");
							oracleSql.append("union");
							oracleSql.append("select  app.*,emp.ENTRY_MONTHS,emp.WORK_NO,media.AGENT_ACC as AGENTACC , media.AGENT_DEPT as AGENTDEPT ,media.AGENT_NAME AS AGENTNAME,media.SESSION_ID AS SESSIONID,'' as CALLED,'' as CALLER ,media.CHANNEL_NAME , media.CHANNEL_KEY ");
							oracleSql.append(",'01' as DIRECTION,BEGIN_TIME as RINGING_TIME,BEGIN_TIME as ANSWER_TIME,END_TIME,GROUP_ID as SKILL_CODE,'' as HANGUP_TYPE,'' as VOICE_URL, SATISF_CODE as SATISFACTION_CODE,media.SERIAL_ID AS CALL_ID  ");
							oracleSql.append(",app.ID as APP_ID,con.SOURCE_ORDER_CODE,con.CUSTOMER_ID,con.CUSTOMER_CODE,con.CUSTOMER_PHONE,con.IS_INSTEAD_ORDER ,con.SERVICE_CUSTOMER_MOBILE,con.SERVICE_CUSTOMER_MOBILE2,con.SERVICE_CUSTOMER_MOBILE3 ");
							oracleSql.append(",con.SERVICE_CUSTOMER_ADDRESS,con.ECM_ORDER_VO ");
							oracleSql.append(",con.CUSTOMER_MOBILEPHONE1,con.CUSTOMER_MOBILEPHONE2,con.CUSTOMER_MOBILEPHONE3,con.SERVICE_CUSTOMER_MOBILE,con.CONTACT_ORDER_STATUS,con.CREATE_TIME as ORDER_CREATE_TIME,con.CUSTOMER_TYPE,con.CUSTOMER_ADDRESS ");
							oracleSql.append(",con.AREA_NAME,con.AREA_CODE,con.AREA_NUM,con.CONTACT_TYPE_CODE,con.CUSTOMER_NAME,con.CUSTOMER_LEVEL,con.IS_INSTEAD_ORDER,con.SOURCE_ORDER_CODE,con.id as CONTACT_ORDER_SERAIL_ID,con.OTHER_MODULE_NAME,con.CUSTOMER_STAR_LEVEL,con.SERVICE_CUSTOMER_NAME");
							oracleSql.append(",appe.MACHINE_ID ");
							oracleSql.append("from  C_NO_APPEALLIST app left join C_NO_CONTACT  con on app.CONTACT_SERAIL_ID=con.ID ");
							oracleSql.append("LEFT JOIN YCBUSI.CC_MEDIA_RECORD media on media.SERIAL_ID=app.SESSION_ID and app.MODIFY_ACC=media.AGENT_ACC  ");
							oracleSql.append("LEFT JOIN C_YG_EMPLOYEE emp on media.AGENT_ACC =emp.USER_ACC and emp.STATUS!=4  ");
							oracleSql.append("LEFT JOIN C_NO_APPEALLIST_EXTEND appe on appe.APPEAL_ID =app.ID ");
							oracleSql.append(startTime," where REQUIRE_CREATE_TIME >=? ");
							oracleSql.append(endTime,"  and REQUIRE_CREATE_TIME< ?");
							if(!StringUtils.isBlank(orgCode)	){
								oracleSql.append(" and app.ORG_CODE in ("+orgCode+")");
							}
							if(!StringUtils.isBlank(serviceRequireTypeCode)	){
								oracleSql.append(" and app.CONTACT_ORDER_SERV_TYPE_CODE in ("+serviceRequireTypeCode+")");
							}
							if(!StringUtils.isBlank(serviceRequireItemCode)	){
								oracleSql.append(" and app.CONTACT_ORDER_SER_ITEM1_CODE in ("+serviceRequireItemCode+")");
							}
							if(!StringUtils.isBlank(serviceRequireItemCode2)	){
								oracleSql.append(" and app.CONTACT_ORDER_SER_ITEM2_CODE in ("+serviceRequireItemCode2+")");
							}
							if(!StringUtils.isBlank(channelCode)	){
								oracleSql.append(" and media.channel_key in ("+channelCode+")");
							}
							oracleSql.append(task.getString("SKILL_CODE")," and GROUP_ID =?");
							oracleSql.append("and con.CONTACT_ORDER_STATUS is null");
							oracleSql.append("and  media .BEGIN_TIME is not null");
						}else{
							oracleSql.append("select  app.*,emp.ENTRY_MONTHS,emp.WORK_NO,call.AGENT_ACC as AGENTACC , call.AGENT_DEPT as AGENTDEPT ,CALL.AGENT_NAME AS AGENTNAME,CALL.SESSION_ID AS SESSIONID,CALL.CALLED,CALL.CALLER,'' as CHANNEL_NAME , '' as CHANNEL_KEY  ");
							oracleSql.append(",DIRECTION,RINGING_TIME,call.ANSWER_TIME as ANSWER_TIME,END_TIME,SKILL_CODE,HANGUP_TYPE,VOICE_URL,SATISFACTION_CODE,call.ID AS CALL_ID  ");
							oracleSql.append(",app.ID as APP_ID,con.SOURCE_ORDER_CODE,con.CUSTOMER_ID,con.CUSTOMER_CODE,con.CUSTOMER_PHONE,con.IS_INSTEAD_ORDER ,con.SERVICE_CUSTOMER_MOBILE,con.SERVICE_CUSTOMER_MOBILE2,con.SERVICE_CUSTOMER_MOBILE3 ");
							oracleSql.append(",con.SERVICE_CUSTOMER_ADDRESS,con.ECM_ORDER_VO ");
							oracleSql.append(",con.CUSTOMER_MOBILEPHONE1,con.CUSTOMER_MOBILEPHONE2,con.CUSTOMER_MOBILEPHONE3,con.SERVICE_CUSTOMER_MOBILE,con.CONTACT_ORDER_STATUS,con.CREATE_TIME as ORDER_CREATE_TIME,con.CUSTOMER_TYPE,con.CUSTOMER_ADDRESS ");
							oracleSql.append(",con.AREA_NAME,con.AREA_CODE,con.AREA_NUM,con.CONTACT_TYPE_CODE,con.CUSTOMER_NAME,con.CUSTOMER_LEVEL,con.IS_INSTEAD_ORDER,con.SOURCE_ORDER_CODE,con.id as CONTACT_ORDER_SERAIL_ID,con.OTHER_MODULE_NAME,con.CUSTOMER_STAR_LEVEL,con.SERVICE_CUSTOMER_NAME");
							oracleSql.append(",appe.MACHINE_ID ");
							oracleSql.append("from  C_NO_APPEALLIST app left join C_NO_CONTACT  con on app.CONTACT_SERAIL_ID=con.ID ");
							oracleSql.append("LEFT JOIN C_PF_CALL_RECORD call on call.SESSION_ID=app.SESSION_ID and app.CREATE_ACC=call.AGENT_ACC and  app.MODIFY_ACC is null ");
							oracleSql.append("LEFT JOIN C_YG_EMPLOYEE emp on call.AGENT_ACC =emp.USER_ACC and emp.STATUS!=4 ");
							oracleSql.append("LEFT JOIN C_NO_APPEALLIST_EXTEND appe on appe.APPEAL_ID =app.ID ");
							oracleSql.append(startTime," where REQUIRE_CREATE_TIME >=? ");
							oracleSql.append(endTime,"  and REQUIRE_CREATE_TIME< ?");
							if(!StringUtils.isBlank(orgCode)	){
								oracleSql.append(" and app.ORG_CODE in ("+orgCode+")");
							}
							if(!StringUtils.isBlank(serviceRequireTypeCode)	){
								oracleSql.append(" and app.CONTACT_ORDER_SERV_TYPE_CODE in ("+serviceRequireTypeCode+")");
							}
							if(!StringUtils.isBlank(serviceRequireItemCode)	){
								oracleSql.append(" and app.CONTACT_ORDER_SER_ITEM1_CODE in ("+serviceRequireItemCode+")");
							}
							if(!StringUtils.isBlank(serviceRequireItemCode2)	){
								oracleSql.append(" and app.CONTACT_ORDER_SER_ITEM2_CODE in ("+serviceRequireItemCode2+")");
							}
							oracleSql.append(task.getString("SKILL_CODE")," and SKILL_CODE =?");
							oracleSql.append("and con.CONTACT_ORDER_STATUS is null");
							oracleSql.append("and call.answer_time is not null");
							oracleSql.append("union");
							oracleSql.append("select  app.*,emp.ENTRY_MONTHS,emp.WORK_NO,call.AGENT_ACC as AGENTACC ,call.AGENT_DEPT as AGENTDEPT ,CALL.AGENT_NAME AS AGENTNAME,CALL.SESSION_ID AS SESSIONID,CALL.CALLED,CALL.CALLER,'' as CHANNEL_NAME , '' as CHANNEL_KEY  ");
							oracleSql.append(",DIRECTION,RINGING_TIME,call.ANSWER_TIME as ANSWER_TIME,END_TIME,SKILL_CODE,HANGUP_TYPE,VOICE_URL,SATISFACTION_CODE,call.ID AS CALL_ID  ");
							oracleSql.append(",app.ID as APP_ID,con.SOURCE_ORDER_CODE,con.CUSTOMER_ID,con.CUSTOMER_CODE,con.CUSTOMER_PHONE,con.IS_INSTEAD_ORDER ,con.SERVICE_CUSTOMER_MOBILE,con.SERVICE_CUSTOMER_MOBILE2,con.SERVICE_CUSTOMER_MOBILE3 ");
							oracleSql.append(",con.SERVICE_CUSTOMER_ADDRESS,con.ECM_ORDER_VO ");
							oracleSql.append(",con.CUSTOMER_MOBILEPHONE1,con.CUSTOMER_MOBILEPHONE2,con.CUSTOMER_MOBILEPHONE3,con.SERVICE_CUSTOMER_MOBILE,con.CONTACT_ORDER_STATUS,con.CREATE_TIME as ORDER_CREATE_TIME,con.CUSTOMER_TYPE,con.CUSTOMER_ADDRESS ");
							oracleSql.append(",con.AREA_NAME,con.AREA_CODE,con.AREA_NUM,con.CONTACT_TYPE_CODE,con.CUSTOMER_NAME,con.CUSTOMER_LEVEL,con.IS_INSTEAD_ORDER,con.SOURCE_ORDER_CODE,con.id as CONTACT_ORDER_SERAIL_ID,con.OTHER_MODULE_NAME,con.CUSTOMER_STAR_LEVEL,con.SERVICE_CUSTOMER_NAME");
							oracleSql.append(",appe.MACHINE_ID ");
							oracleSql.append("from  C_NO_APPEALLIST app left join C_NO_CONTACT  con on app.CONTACT_SERAIL_ID=con.ID ");
							oracleSql.append("LEFT JOIN C_PF_CALL_RECORD call on call.SESSION_ID=app.SESSION_ID and app.MODIFY_ACC=call.AGENT_ACC  ");
							oracleSql.append("LEFT JOIN C_YG_EMPLOYEE emp on call.AGENT_ACC =emp.USER_ACC and emp.STATUS!=4  ");
							oracleSql.append("LEFT JOIN C_NO_APPEALLIST_EXTEND appe on appe.APPEAL_ID =app.ID ");
							oracleSql.append(startTime," where REQUIRE_CREATE_TIME >=? ");
							oracleSql.append(endTime,"  and REQUIRE_CREATE_TIME< ?");
							if(!StringUtils.isBlank(orgCode)){
								oracleSql.append(" and app.ORG_CODE in ("+orgCode+")");
							}
							if(!StringUtils.isBlank(serviceRequireTypeCode)	){
								oracleSql.append(" and app.CONTACT_ORDER_SERV_TYPE_CODE in ("+serviceRequireTypeCode+")");
							}
							if(!StringUtils.isBlank(serviceRequireItemCode)	){
								oracleSql.append(" and app.CONTACT_ORDER_SER_ITEM1_CODE in ("+serviceRequireItemCode+")");
							}
							if(!StringUtils.isBlank(serviceRequireItemCode2)	){
								oracleSql.append(" and app.CONTACT_ORDER_SER_ITEM2_CODE in ("+serviceRequireItemCode2+")");
							}
							oracleSql.append(task.getString("SKILL_CODE")," and SKILL_CODE =?");
							oracleSql.append("and con.CONTACT_ORDER_STATUS is null");
							oracleSql.append("and call.answer_time is not null");
						}
					}
					
					logger.info("自动质检的时间："+startTime+"--"+endTime+"-->"+oracleSql.getSQL()+"-》"+oracleSql.getParams());
					getQuery().setMaxRow(99999);
					List<JSONObject> orderList = getQuery().queryForList(oracleSql.getSQL(),oracleSql.getParams(), new JSONMapperImpl()); 
					logger.info(task.getString("ID")+"自动质检时间："+startTime+"-->"+endTime+","+orderList.size());
					logger.info(task.getString("ID")+";"+oracleSql.getSQL()+";"+JSONObject.toJSONString(oracleSql.getParams()));
					HashMap<String, SendQualtiyInfo> map =new HashMap<String, SendQualtiyInfo>();
					List<CompletableFuture<Void>> futures = new ArrayList<>();
					for(JSONObject order:orderList){

						CompletableFuture<Void> future = CompletableFuture.supplyAsync(() -> {
							String sessionId = order.getString("SESSION_ID");
							try {
								logger.info("自动质检sessionId"+sessionId+"数据开始");
								logger.info("自动质检sessionId"+sessionId+"数据:"+order.toJSONString());
								if(map.get(sessionId)==null){
									SendQualtiyInfo sendQualtiyInfo=getSendQualtiyInfo(order,task.getString("TASK_ID"),task.getString("ID"),taskData,qcType,task.getString("ORDER_TYPE"),true);
									map.put(sessionId,sendQualtiyInfo);
									logger.info("自动质检sessionId"+sessionId+"数据："+JSON.toJSONString(sendQualtiyInfo));
								}
							} catch (Exception e) {
								logger.info("线程处理自动质检sessionId"+sessionId+"数据出错");
							}

						return null;
						}, ThreadPoolKit.getExecutor());
						futures.add(future);

					}
					try {
						logger.info(task.getString("ID")+" 等待所有任务执行完成中，执行数量="+futures.size());
						CompletableFuture<Void> voidCompletableFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
						voidCompletableFuture.join();
						logger.info(task.getString("ID")+" 等待所有任务执行完成中");
					} catch (Exception e) {
						logger.info(task.getString("ID")+"线程error：" + e.getMessage(),e);
					}

					logger.info(task.getString("ID")+"自动质检准备发送数据："+map.size());
					String url=Constants.QC_URL;
					int success=0;
					int error=0;
					for (String key : map.keySet()) {
						try {
							SendQualtiyInfo sendQualtiyInfo = map.get(key);
							//JSONObject json = JSON.parseObject(JSON.toJSONString(sendQualtiyInfo));
							JSONObject json=new JSONObject();
							JSONArray jsonArray=new JSONArray();
							jsonArray.add(JSON.parseObject(JSON.toJSONString(sendQualtiyInfo)));
							json.put("data",jsonArray );
							json.put("messageId", RandomKit.randomStr());
							json.put("serialId", sendQualtiyInfo.getSerialId());
							json.put("timestamp", System.currentTimeMillis());
							logger.info("同步自动质检数据"+url+","+sendQualtiyInfo.getSerialId());
							HttpResp post = HttpUtil.post(url,json.toJSONString(),"UTF-8");
							if(post.getCode()==200){
								String result = post.getResult();
								try {
									JSONObject parseObject = JSON.parseObject(result);
									if(parseObject!=null&&parseObject.get("result")!=null&&"000".equals(parseObject.getString("result"))){
										success=success+1;
									}else{
										error=error+1;
										loggerError.error("同步自动质检数据"+sendQualtiyInfo.getSerialId()+"返回"+result);
									}
								} catch (Exception e) {
									error=error+1;
								}
								logger.info("同步自动质检数据"+sendQualtiyInfo.getSerialId()+"返回"+result);
							}else{
								error=error+1;
								loggerError.error("同步自动质检数据"+sendQualtiyInfo.getSerialId()+"返回"+post.getResult());
							}
						} catch (Exception e) {
							logger.error("同步自动质检失败:"+e.getMessage(),e);
							loggerError.error("同步自动质检失败:"+e.getMessage(),e);

						}
					 }
					loggerStat.info(task.getString("ID")+"自动质检时间："+startTime+"-->"+endTime+"数量："+orderList.size()+"其中成功数量："+success+"失败："+error);

				} catch (Exception e) {
					logger.error("自动质检失败:"+e.getMessage(),e);
					e.printStackTrace();
				}
			}
		} catch (Exception e) {
			logger.error("自动质检失败:"+e.getMessage(),e);
			e.printStackTrace();

		}
		//getTime("2023-01-30 00:00:00","2023-01-29 13:00:00");
		JSONObject result=new JSONObject();
		result.put("respData", result);
		result.put("respCode", GWConstants.RET_CODE_SUCCESS);
		result.put("respDesc", "操作成功");
		return result;
		
	}



	/**
	*@title 语音转写失败，转写成功后，历史数据重推
	 *超过指定时间仍未转写，则推送默认的记录
	 * C_NO_AUTO_QC_OBJ_HIS
	 *{
	 *	STATUS,CREATE_DATE 增加索引
	 * }
	*@date 2024/6/17
	*@version 1.0
	*/
	public static JSONObject sendHisQualtiyInfo() {
		try {
			EasyQuery query = getQuery();
			EasyCalendar cal = EasyCalendar.newInstance();
			int maxKeepQcDays = Constants.MAX_KEEP_QC_DAYS;
			cal.add(EasyCalendar.DAY, -maxKeepQcDays);
			int dateId = cal.getDateInt();
			EasySQL sql= new EasySQL("select  *  from  C_NO_AUTO_QC_OBJ_HIS where STATUS = '0' ");
			sql.append(dateId,"and CREATE_DATE>=?");
			query.setMaxRow(99999);
			List<JSONObject> objList = query.queryForList(sql.getSQL(),sql.getParams(), new JSONMapperImpl());
			String url=Constants.QC_URL;
			int success=0;
			int error=0;
			for(JSONObject obj:objList) {
				String taskId = obj.getString("TASK_ID");
				String taskData = obj.getString("TASK_DATA");
				String qcType = obj.getString("QC_TYPE");
				String taskKey = obj.getString("TASK_KEY");
				String id = obj.getString("ID");
				String sessionId = obj.getString("SESSION_ID");
				String orderType = obj.getString("ORDER_TYPE");
				String callId = obj.getString("CALL_ID");
				String appId = obj.getString("APP_ID");
				int createDate = obj.getInteger("CREATE_DATE");
				loggerHis.info( "自动质检历史重推:"+id);
				String chatSql="select count(1)  from  CC_VOC_CALL_DETAIL where CALL_RECORD_ID =?";
				int chatCount = query.queryForInt(chatSql,new Object[]{callId});
				loggerHis.info("转写文本callRecordId="+callId+",转写记录:"+chatCount);
				//没有转写则不执行推送
				boolean saveHisFlag;
				if(chatCount<=0&&createDate>dateId){
					continue;
				}else{
					saveHisFlag = false;
				}
//				String taksSql="select  *  from  C_NO_AUTO_QC_TASK_CONFIG where IS_ENABLE =? and ID = ? ";
//				JSONObject task = getQuery().queryForRow(taksSql,new Object[]{"Y",taskId}, new JSONMapperImpl());
				try {
					loggerHis.info("自动质检sessionId" + sessionId + "数据开始");
					loggerHis.info("自动质检sessionId" + sessionId + "数据:" + obj.toJSONString());
					if(StringUtils.isBlank(callId)){
						loggerHis.info("自动质检sessionId" + sessionId + "callId为空");
						continue;
					}
					EasySQL oracleSql = new EasySQL();
					if(Constants.ORDER_TYPE_NOT_ORDER_CALL.equals(orderType)){
						//无工单纯话务
						//CALL.SESSION_ID AS SESSION_ID,CALL.SESSION_ID AS SESSIONID
						//因为纯话务没有工单，SESSION_ID 取的是诉求表的SESSION_ID,上面之所以取两个别名，是因为下面的程序都用到了，不好改下面的逻辑，影响正常运行
						oracleSql.append("select emp.ENTRY_MONTHS,emp.WORK_NO  ");
						oracleSql.append(",call.AGENT_ACC as AGENTACC ,call.AGENT_DEPT as AGENTDEPT ,CALL.AGENT_NAME AS AGENTNAME,CALL.SESSION_ID AS SESSION_ID,CALL.SESSION_ID AS SESSIONID,CALL.CALLED,CALL.CALLER,'' as CHANNEL_NAME , '' as CHANNEL_KEY  ");
						oracleSql.append(",DIRECTION,RINGING_TIME,call.ANSWER_TIME as ANSWER_TIME,END_TIME,SKILL_CODE,HANGUP_TYPE,VOICE_URL,SATISFACTION_CODE,call.ID AS CALL_ID  ");
						oracleSql.append("from  C_PF_CALL_RECORD call  ");
						oracleSql.append("LEFT JOIN C_YG_EMPLOYEE emp on call.AGENT_ACC =emp.USER_ACC and emp.STATUS!=4  ");
						oracleSql.append(callId," where call.ID =? ");
					}else{
						oracleSql.append("select  app.*,emp.ENTRY_MONTHS,emp.WORK_NO,call.AGENT_ACC as AGENTACC , call.AGENT_DEPT as AGENTDEPT ,CALL.AGENT_NAME AS AGENTNAME,CALL.SESSION_ID AS SESSIONID,CALL.CALLED,CALL.CALLER,'' as CHANNEL_NAME , '' as CHANNEL_KEY  ");
						oracleSql.append(",DIRECTION,RINGING_TIME,call.ANSWER_TIME as ANSWER_TIME,END_TIME,SKILL_CODE,HANGUP_TYPE,VOICE_URL,SATISFACTION_CODE,call.ID AS CALL_ID  ");
						oracleSql.append(",app.ID as APP_ID,con.SOURCE_ORDER_CODE,con.CUSTOMER_ID,con.CUSTOMER_CODE,con.CUSTOMER_PHONE,con.IS_INSTEAD_ORDER ,con.SERVICE_CUSTOMER_MOBILE,con.SERVICE_CUSTOMER_MOBILE2,con.SERVICE_CUSTOMER_MOBILE3 ");
						oracleSql.append(",con.SERVICE_CUSTOMER_ADDRESS,con.ECM_ORDER_VO ");
						oracleSql.append(",con.CUSTOMER_MOBILEPHONE1,con.CUSTOMER_MOBILEPHONE2,con.CUSTOMER_MOBILEPHONE3,con.SERVICE_CUSTOMER_MOBILE,con.CONTACT_ORDER_STATUS,con.CREATE_TIME as ORDER_CREATE_TIME,con.CUSTOMER_TYPE,con.CUSTOMER_ADDRESS ");
						oracleSql.append(",con.AREA_NAME,con.AREA_CODE,con.AREA_NUM,con.CONTACT_TYPE_CODE,con.CUSTOMER_NAME,con.CUSTOMER_LEVEL,con.IS_INSTEAD_ORDER,con.SOURCE_ORDER_CODE,con.id as CONTACT_ORDER_SERAIL_ID,con.OTHER_MODULE_NAME,con.CUSTOMER_STAR_LEVEL,con.SERVICE_CUSTOMER_NAME");
						oracleSql.append(",appe.MACHINE_ID ");
						oracleSql.append("from  C_NO_APPEALLIST app left join C_NO_CONTACT  con on app.CONTACT_SERAIL_ID=con.ID ");
						oracleSql.append("LEFT JOIN C_PF_CALL_RECORD call on call.SESSION_ID=app.SESSION_ID and app.CREATE_ACC=call.AGENT_ACC and  app.MODIFY_ACC is null ");
						oracleSql.append("LEFT JOIN C_YG_EMPLOYEE emp on call.AGENT_ACC =emp.USER_ACC and emp.STATUS!=4 ");
						oracleSql.append("LEFT JOIN C_NO_APPEALLIST_EXTEND appe on appe.APPEAL_ID =app.ID ");
						oracleSql.append(appId," where app.ID = ? ");
					}
					JSONObject order = getQuery().queryForRow(oracleSql.getSQL(),oracleSql.getParams(), new JSONMapperImpl());
					SendQualtiyInfo sendQualtiyInfo = getSendQualtiyInfo(order, taskId, taskKey, taskData, qcType, orderType,saveHisFlag);
					loggerHis.info("自动质检sessionId" + sessionId + "数据：" + JSON.toJSONString(sendQualtiyInfo));
					//JSONObject json = JSON.parseObject(JSON.toJSONString(sendQualtiyInfo));
					JSONObject json=new JSONObject();
					JSONArray jsonArray=new JSONArray();
					jsonArray.add(JSON.parseObject(JSON.toJSONString(sendQualtiyInfo)));
					json.put("data",jsonArray );
					json.put("messageId", RandomKit.randomStr());
					json.put("serialId", sendQualtiyInfo.getSerialId());
					json.put("timestamp", System.currentTimeMillis());
					loggerHis.info("开始同步自动质检数据"+url+","+sendQualtiyInfo.getSerialId());
					HttpResp post = HttpUtil.post(url,json.toJSONString(),"UTF-8");
					if(post.getCode()==200){
						String result = post.getResult();
						try {
							JSONObject parseObject = JSON.parseObject(result);
							if(parseObject!=null&&parseObject.get("result")!=null&&"000".equals(parseObject.getString("result"))){
								int i = query.executeUpdate("update C_NO_AUTO_QC_OBJ_HIS set STATUS = '1' WHERE ID = ? ", new Object[]{id});
								loggerHis.info("推送成功，更新数据已推送，ID="+id);
								success=success+1;
							}else{
								error=error+1;
								loggerHis.error("同步自动质检数据"+sendQualtiyInfo.getSerialId()+"返回"+result);
							}
						} catch (Exception e) {
							error=error+1;
						}
						loggerHis.info("同步自动质检数据"+sendQualtiyInfo.getSerialId()+"返回"+result);
					}else{
						error=error+1;
						loggerHis.error("同步自动质检数据"+sendQualtiyInfo.getSerialId()+"返回"+post.getResult());
					}
				} catch (Exception e) {
					loggerHis.error("同步自动质检失败:"+e.getMessage(),e);
				}
			}
			loggerHis.info("数量："+objList.size()+"其中成功数量："+success+"失败："+error);
		} catch (Exception e) {
			loggerHis.error("自动质检失败:"+e.getMessage(),e);
		}
		JSONObject result=new JSONObject();
		result.put("respData", result);
		result.put("respCode", GWConstants.RET_CODE_SUCCESS);
		result.put("respDesc", "操作成功");
		return result;

	}

	public  static SendQualtiyInfo getSendQualtiyInfo(JSONObject order,String taskId,String id,String taskData,String qcType,String orderType,boolean saveHisFlag) {
		SendQualtiyInfo SendQualtiyInfo=new SendQualtiyInfo();
		SendQualtiyInfo.setTaskId(taskId);
		SendQualtiyInfo.setBusiId("");
		SendQualtiyInfo.setMediaType(taskData);
		SendQualtiyInfo.setAgentId(order.getString("WORK_NO"));
		SendQualtiyInfo.setAgentAcc(order.getString("AGENTACC"));
		SendQualtiyInfo.setAgentNo(order.getString("WORK_NO"));
		SendQualtiyInfo.setAgentName(order.getString("AGENTNAME"));
		deptDict = Optional.ofNullable(deptDict).orElse(new JSONObject());
		SendQualtiyInfo.setAgentTeam(deptDict.getString(order.getString("AGENTDEPT")));
		SendQualtiyInfo.setAgentTeamCode(order.getString("AGENTDEPT"));
		if (StringUtils.isNotBlank(qcType)&&"1".equals(qcType)) {//通用质检 保存记录
			SendQualtiyInfo.setCallBackUrl(Constants.CALL_BACK_URL);
		}else{
			SendQualtiyInfo.setCallBackUrl("");
			
		}
		SendQualtiyInfo.setCalled(order.getString("CALLED"));
		SendQualtiyInfo.setCaller(order.getString("CALLER"));
		if(order.getString("DIRECTION")!=null&&"01".equals(order.getString("DIRECTION"))){//呼入
			SendQualtiyInfo.setCallType("0");
		}else{
			SendQualtiyInfo.setCallType("1");
		}
		if(StringUtils.isNotBlank(order.getString("CHANNEL_NAME"))){
			SendQualtiyInfo.setChannelkey(order.getString("CHANNEL_KEY"));//渠道
			SendQualtiyInfo.setChannelName(order.getString("CHANNEL_NAME"));//渠道
		}else{
			SendQualtiyInfo.setChannelkey("0");//渠道
			SendQualtiyInfo.setChannelName("呼叫中心");//渠道
		}
		
//		SendQualtiyInfo.setStartTime(order.getString("RINGING_TIME"));
		//质检申诉需求修改为接听时间
		SendQualtiyInfo.setStartTime(order.getString("ANSWER_TIME"));
		SendQualtiyInfo.setEndTime(order.getString("END_TIME"));
		SendQualtiyInfo.setFacilitiesDept(order.getString("ORG_CODE"));//事业部
		if(StringUtils.isBlank(order.getString("SKILL_CODE"))){
			SendQualtiyInfo.setGroupId("0");
			SendQualtiyInfo.setGroupName("无");
		}else{
			SendQualtiyInfo.setGroupId(order.getString("SKILL_CODE"));
			if("1".equals(taskData)){
				SendQualtiyInfo.setGroupName(mideaSkillDict.getString(order.getString("SKILL_CODE")));
			}else{
				SendQualtiyInfo.setGroupName(callSkillDict.getString(order.getString("SKILL_CODE")));
			}
		}
		
		SendQualtiyInfo.setHangupType(order.getString("HANGUP_TYPE"));
		SendQualtiyInfo.setLengthInduction(order.getString("ENTRY_MONTHS"));//入职时长/月
		if(!StringUtils.isBlank(order.getString("AGENTDEPT"))&&order.getString("AGENTDEPT").length()>12){
			String OperatingArea=order.getString("AGENTDEPT").substring(0,12);
			SendQualtiyInfo.setOperatingArea(deptDict.getString(OperatingArea));//运营区域
			SendQualtiyInfo.setOperatingAreaCode(OperatingArea);//运营区域
		}
		SendQualtiyInfo.setProductCategory(order.getString("PROD_NAME"));//名称
		if(StringUtils.isBlank(order.getString("PROD_NAME"))){
			SendQualtiyInfo.setProductCategory(order.getString("PROD_CODE_NAME"));//名称
		}
		SendQualtiyInfo.setProductCategoryCode(order.getString("PROD_CODE"));//名称
		SendQualtiyInfo.setRecordFileName(order.getString("VOICE_URL"));//录音访问URL，返回对应RECORD_FILE字段值
		SendQualtiyInfo.setSatistactionDegree(order.getString("SATISFACTION_CODE"));//必填)满意度 1：非常满意 2：满意 3：一般 4：不满意 5：对结果不满意 91：未评价 92：无效按键
		SendQualtiyInfo.setSerialId(id+"_"+order.getString("CALL_ID"));//任务id+通话ID
		SendQualtiyInfo.setServiceRequest(order.getString("CONTACT_ORDER_SERV_TYPE_CODE"));//服务请求
		SendQualtiyInfo.setServiceRequestDetails(order.getString("CONTACT_ORDER_SER_ITEM2_CODE"));//服务请求细则
		SendQualtiyInfo.setCustName(order.getString("CUSTOMER_NAME"));
		SendQualtiyInfo.setRecordSerialId(order.getString("CALL_ID"));
		SendQualtiyInfo.setCustSessionId(order.getString("SESSIONID"));
			
		
		//其他数据
		SendQualtiyInfo.setChatList(getChatList(order,taskData,order.getString("RINGING_TIME"), taskId, id, qcType, orderType,saveHisFlag));
		SendQualtiyInfo.setOrderInfo(getOrderInfo(order));

		return SendQualtiyInfo;
	}
	/**
	 * 获取N分前时间
	 * @return
	 */
	public static String getTime(Date time,int minutes ){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");   
	    time.setMinutes(minutes);
	    String preDay = sdf.format(time);
	    return preDay;
	}

    public static String addDay(String dateString,int day) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date date = sdf.parse(dateString);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, day); // 增加一天
            Date newDate = calendar.getTime();
            String newDateString = sdf.format(newDate);
            System.out.println("原始日期：" + dateString);
            System.out.println("增加一天后日期：" + newDateString);
    	    return newDateString;
        } catch (ParseException e) {
            e.printStackTrace();
    	    return dateString;

        }
    }

	public  static JSONObject getTime(String sysTime,String updateTime) {
		Calendar calendar=Calendar.getInstance();
		SimpleDateFormat format=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");     
		Date time = calendar.getTime();   
		String endTime="";
		String startTime="";
		if(!StringUtils.isBlank(sysTime)){
			try {
				time = format.parse(sysTime);
			} catch (ParseException e) {
				e.printStackTrace();
			}
		}
		endTime=getTime(time,-960);//
		if(!StringUtils.isBlank(updateTime)){//有历史数据按最新的获取
			try {
				startTime = format.format(format.parse(updateTime));
			} catch (Exception e) {
				e.printStackTrace();
			}
		}else{//没有时间按半小时前时间数据
			startTime=getTime(time,-30);
		}
		System.out.println(startTime+"--"+endTime);
		JSONObject json =new JSONObject();
		json.put("startTime", startTime);
		json.put("endTime", endTime);
		return json;
	}

	public static void main(String[] args) {
		JSONObject time = getTime("", "2024-05-23 23:00:00");
		System.out.println(time);
	}
	
	public  static List<Chat> getChatList(JSONObject order,String taskData,String startTime,String taskId,String id,String qcType,String orderType,boolean saveHisFlag){
		List<Chat> list=new ArrayList<Chat>();
		if("1".equals(taskData)){
			if(!StringUtils.isBlank(order.getString("CALL_ID"))){
				String sql="select * from ycbusi.CC_MEDIA_CHAT_RECORD where  CHAT_SESSION_ID=? order by MSG_TIME ";
				List<JSONObject> chatList;
				try {
					chatList = getQuery().queryForList(sql,new Object[]{order.getString("CALL_ID")}, new JSONMapperImpl());
					if(chatList.size()==0){
						Chat chat=new Chat();
						chat.setStartTime(startTime);
						chat.setEndTime(startTime);
						chat.setMsgContent("无转写记录");
						chat.setChannel("1");
						list.add(chat);
					}
					for (int i = 0; i < chatList.size(); i++) {
						Chat chat=new Chat();
						if(chatList.get(i).getString("MSG_TIME").length()>19){
							chat.setStartTime(chatList.get(i).getString("MSG_TIME").substring(0,19));
						}else{
							chat.setStartTime(chatList.get(i).getString("MSG_TIME"));
						}
						if(chatList.get(i).getString("MSG_TIME").length()>19){
							chat.setEndTime(chatList.get(i).getString("MSG_TIME").substring(0,19));
						}else{
							chat.setEndTime(chatList.get(i).getString("MSG_TIME"));
						}
						String sender =chatList.get(i).getString("SENDER");
						if("2".equals(sender)){
							sender="1";
						}else if("1".equals(sender)){
							sender="2";
						}else{
							//保留原本类型
						}
						chat.setChannel(sender);
						chat.setMsgContent(chatList.get(i).getString("MSG_CONTENT"));
						list.add(chat);
					}
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
		}else{
			if(!StringUtils.isBlank(order.getString("CALL_ID"))){
				String sql="select  *  from  CC_VOC_CALL_DETAIL where CALL_RECORD_ID =?  order by MSG_TIMESTAMP_BEGIN  ";
				List<JSONObject> chatList;
				try {
					chatList = getQuery().queryForList(sql,new Object[]{order.getString("CALL_ID")}, new JSONMapperImpl());
					if(chatList.size()==0){
						if(saveHisFlag){
							//没有asr转写记录的先保存到临时表，后面再重推
							EasyRecord record = new EasyRecord("C_NO_AUTO_QC_OBJ_HIS", "ID");
							record.set("ID",RandomKit.randomStr());
							record.set("TASK_ID",taskId);
							record.set("TASK_DATA",taskData);
							record.set("QC_TYPE",qcType);
							record.set("TASK_KEY",id);
							record.set("SESSION_ID",order.getString("SESSIONID"));
							record.set("ORDER_TYPE",orderType);
							record.set("CALL_ID",order.getString("CALL_ID"));
							record.set("APP_ID",order.getString("APP_ID"));
							record.set("STATUS","0");
							record.set("CREATE_DATE",EasyDate.getCurrentDateString("yyyyMMdd"));
							record.set("CREATE_TIME",EasyDate.getCurrentTimeStampString());
							getQuery().save(record);
						}else{
							Chat chat=new Chat();
							chat.setStartTime(startTime);
							chat.setEndTime(startTime);
							//20241128 智能质检入库这句话之后，不会再接受重推，导致转写异常时无法补推，所以推送空字符串，智能质检识别空字符串就直接丢弃
//							chat.setMsgContent("无转写记录");
							chat.setMsgContent("");
							chat.setChannel("1");
							list.add(chat);
						}
					}
					for (int i = 0; i < chatList.size(); i++) {
						Chat chat=new Chat();
						if(chatList.get(i).getString("SOUND_BEGIN_TIME").length()>19){
							chat.setStartTime(chatList.get(i).getString("SOUND_BEGIN_TIME").substring(0,19));
						}else{
							chat.setStartTime(chatList.get(i).getString("SOUND_BEGIN_TIME"));
						}
						if(chatList.get(i).getString("SOUND_END_TIME").length()>19){
							chat.setEndTime(chatList.get(i).getString("SOUND_END_TIME").substring(0,19));
						}else{
							chat.setEndTime(chatList.get(i).getString("SOUND_END_TIME"));
						}
						chat.setChannel(chatList.get(i).getString("USER_TYPE"));
						chat.setMsgContent(chatList.get(i).getString("CONTENT"));
						list.add(chat);
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					logger.error("查询或保存语音转写异常："+e.getMessage(),e);
				}
			}
		}
		
		return list;
		
	}
	public  static OrderInfo getOrderInfo(JSONObject order){
		OrderInfo orderInfo=new OrderInfo();
		String phone= StringUtils.isBlank(order.getString("SERVICE_CUSTOMER_MOBILE"))?order.getString("CUSTOMER_PHONE"):order.getString("CUSTOMER_PHONE");
		orderInfo.setCustInfo(getCustInfo(phone));	
		orderInfo.setHangupType(order.getString("HANGUP_TYPE"));//01 客户挂断 02 坐席挂断
		orderInfo.setMachineId(order.getString("MACHINE_ID"));//产品id C_NO_CONTACT_EXTEND
		orderInfo.setContactUserRequireVOList(getContactUserRequireVOList(order));
		JSONObject hisServiceOrder=getHisServiceOrder(phone,order);
		if(hisServiceOrder!=null){
			orderInfo.setHisServiceOrder("Y");
			orderInfo.setHisServiceOrderInfo(hisServiceOrder);
		}else{
			orderInfo.setHisServiceOrder("N");
			orderInfo.setHisServiceOrderInfo(hisServiceOrder);
		}
	
		return orderInfo;
	}
	public  static JSONObject getHisServiceOrder(String phone,JSONObject order){
		JSONObject json=new JSONObject();
		JSONObject obj=new JSONObject();
		JSONObject params = new JSONObject();
		obj.put("pageIndex",1);
		obj.put("customerMobilephone",phone);
		obj.put("prodCode",order.getString("PROD_CODE"));
		obj.put("pubCreateDateStar","");
		obj.put("pubCreateDateEnd",order.getString("CREATE_TIME"));
		params.put("command", OrderCommand.CONTACT_ORDER_LIST);
		params.put("params", obj);
		IService service;
		try {
			service = ServiceContext.getService("CSSGW-CONTACT");
			JSONObject invoke = service.invoke(params);
			if("000".equals(invoke.getString("respCode"))){
				JSONObject respData = invoke.getJSONObject("respData");
				if(respData!=null&&respData.getInteger("total")>0){
					return respData.getJSONArray("list").getJSONObject(0);
				}
			}
		} catch (ServiceException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		
		return json;
	}
	public  static contactUserRequireVOList getContactUserRequireVOList(JSONObject order){
		contactUserRequireVOList contactUserRequireVO=new contactUserRequireVOList();
		contactUserRequireVO.setServiceCustomerMobilephone2(order.getString("SERVICE_CUSTOMER_MOBILE2"));
		contactUserRequireVO.setServiceCustomerMobilephone3(order.getString("SERVICE_CUSTOMER_MOBILE3"));
		contactUserRequireVO.setIsNotifyBranch(order.getString("IS_NOTIFY_BRANCH"));
		contactUserRequireVO.setProductCode(order.getString("PRODUCT_CODE"));
		contactUserRequireVO.setProductModel(order.getString("PRODUCT_MODEL"));
		contactUserRequireVO.setDisposeType(order.getString("DISPOSE_TYPE"));
		contactUserRequireVO.setContactOrderSaleUnitCode(order.getString("CONTACT_ORDER_SALE_UNIT_CODE"));
		contactUserRequireVO.setContactOrderSaleUnitName(order.getString("CONTACT_ORDER_SALE_UNIT_NAME"));
		contactUserRequireVO.setContactRequireSerialId(order.getString("APP_ID"));
		contactUserRequireVO.setContactOrderBuyDate(order.getString("CONTACT_ORDER_BUY_DATE"));
		contactUserRequireVO.setAppointDate(order.getString("APPOINT_DATE"));
		contactUserRequireVO.setAppointTimeDesc(womCcAppointTimeDict.getString(order.getString("APPOINT_TIME_DESC")));
		//ecm订单
		contactUserRequireVO.setEcmOrder(order.getString("ECM_ORDER_VO"));
		//紧急程度
		contactUserRequireVO.setUrgentLevel(order.getString("URGENT_LEVEL"));
		//CS用户诉求唯一Id
		contactUserRequireVO.setContactUserRequireId(order.getString("CONTACT_USER_REQUIRE_ID"));
		//假性故障标志
		contactUserRequireVO.setExcludedFaultFlag(order.getString("EXCLUDED_FAULT_FLAG"));
		//用户指定网点
		contactUserRequireVO.setUnitCode(order.getString("UNIT_CODE"));
		contactUserRequireVO.setUnitName(order.getString("UNIT_NAME"));
		//产品品类
		if(StringUtils.isNotBlank(order.getString("PROD_CODE_NAME"))){
			contactUserRequireVO.setProdName(order.getString("PROD_CODE_NAME"));
		}else{
			contactUserRequireVO.setProdName(order.getString("PROD_NAME"));
		}
		//产品编码
		contactUserRequireVO.setProdCode(order.getString("PROD_CODE"));
		//购买渠道  字典 WOM_PURCHASING_CHANNELS   10 线下（实体店）
		contactUserRequireVO.setContactOrderBuyChannel(order.getString("CONTACT_ORDER_BUY_CHANNEL"));
		//是否VIP专线热线  Y N
//		contactUserRequireVO.setHotLineFlag(order.getString("AAAA"));
		//品牌编码
		contactUserRequireVO.setBrandCode(order.getString("BRAND_CODE"));
		//品牌
		contactUserRequireVO.setBrandName(order.getString("BRAND_NAME"));
		//服务请求 BZ 报装
		contactUserRequireVO.setContactOrderServTypeCode(order.getString("CONTACT_ORDER_SERV_TYPE_CODE"));
		//服务请求 报装 
		contactUserRequireVO.setContactOrderServTypeName(order.getString("CONTACT_ORDER_SERV_TYPE_NAME"));
		//一级业务类型编码
		contactUserRequireVO.setServiceMainTypeCode(order.getString("SERVICE_MAIN_TYPE_CODE"));
		//一级业务类型名称
		contactUserRequireVO.setServiceMainTypeName(order.getString("SERVICE_MAIN_TYPE_NAME"));
		//二级业务类型编码
		contactUserRequireVO.setServiceSubTypeCode(order.getString("SERVICE_SUB_TYPE_CODE"));
		//二级业务类型名称   安装
		contactUserRequireVO.setServiceSubTypeName(order.getString("SERVICE_SUB_TYPE_NAME"));
		//服务请求一级
		contactUserRequireVO.setContactOrderSerItem1Name(order.getString("CONTACT_ORDER_SER_ITEM1_NAME"));
		//服务请求二级
		contactUserRequireVO.setContactOrderSerItem2Name(order.getString("CONTACT_ORDER_SER_ITEM2_NAME"));
		//服务请求一级编号
		contactUserRequireVO.setContactOrderSerItem1Code(order.getString("CONTACT_ORDER_SER_ITEM1_CODE"));
		//服务请求二级编号
		contactUserRequireVO.setContactOrderSerItem2Code(order.getString("CONTACT_ORDER_SER_ITEM2_CODE"));
		String desc=order.getString("CONTACT_ORDER_SERVICE_DESCRIBE");
//		if(StringUtils.isNotBlank(order.getString("CONTACT_ORDER_SERVICE_DESCRIBE"))){
//			try {
//				desc=URLEncoder.encode(order.getString("CONTACT_ORDER_SERVICE_DESCRIBE"), "UTF-8");
//			} catch (UnsupportedEncodingException e) {
//				// TODO Auto-generated catch block
//				e.printStackTrace();
//			}
//		}
		
		//服务描述
		contactUserRequireVO.setContactOrderServiceDescribe(desc);
		//服务描述
		contactUserRequireVO.setContactOrderServiceDesc(desc);
		//未知
		contactUserRequireVO.setContactOrderSerItemName("");
		//分中心
		contactUserRequireVO.setBranchCode(order.getString("BRANCH_CODE"));
		//分中心
		contactUserRequireVO.setBranchName(order.getString("BRANCH_NAME"));
		//产品数量
		contactUserRequireVO.setProductAmount(order.getString("PRODUCT_AMOUNT"));
		//来源单据号
		contactUserRequireVO.setSourceOrderCode(order.getString("SOURCE_ORDER_CODE"));
		//CC接入单唯一id
		contactUserRequireVO.setContactOrderSerailId(order.getString(""));
		//线下催单次数
		contactUserRequireVO.setContactCallNum(order.getString("IS_HURRY"));
		//操作人
		contactUserRequireVO.setPubModiPerson(order.getString("CREATE_ACC"));
		//是否手工升级  N  Y
		contactUserRequireVO.setManualUpgradeFlag(order.getString("IS_UPGRADE"));
		//接入单号  空
		contactUserRequireVO.setContactOrderCode(order.getString("CONTACT_ORDER_CODE"));
		//用户档案  
		contactUserRequireVO.setCustomerCode(order.getString("CUSTOMER_CODE"));
		//投诉等级  WOM_COMPLAINT_LEVEL  13 无
		contactUserRequireVO.setComplaintLevel(order.getString("COMPLAINT_LEVEL"));
		//单据状态 WOM_SERVICE_ORDER_STATUS
//		contactUserRequireVO.setContactOrderStatus(order.getString("AAAA"));
		//创建时间 时间戳
		contactUserRequireVO.setPubCreateDate(EasyDate.getLong(order.getString("CREATE_TIME"), null)+"");
		//创建时间  时间戳--无
		contactUserRequireVO.setOrderCreateDate(EasyDate.getLong(order.getString("CREATE_TIME"), null)+"");
		//主体  
		contactUserRequireVO.setOrgCode(order.getString("ORG_CODE"));
		//投诉单来源所属地  
		contactUserRequireVO.setComplaintSource(order.getString("COMPLAINT_SOURCE"));//必填 999999999999
		//归属模块唯一ID
		contactUserRequireVO.setOtherModuleId(order.getString("OTHER_MODULE_ID"));
		//
		contactUserRequireVO.setContactProductInfoId("");
		//预约场景 S001  常规
		contactUserRequireVO.setAppointSceneCode(order.getString("APPOINT_SCENE_CODE"));
		
		contactUserRequireVO.setAppointTimeDescText(order.getString("APPOINT_TIME_DESC"));
		//线下催单 N Y
		contactUserRequireVO.setIsHurry(order.getString("IS_HURRY"));
		//全网处理结果 
//		contactUserRequireVO.setTurnWebDepict(order.getString("AAA"));
		//创建人 
		contactUserRequireVO.setPubCreatePerson(order.getString("CREATE_ACC"));
		//
//		contactUserRequireVO.setContactOrderId(order.getString("AAA"));
		//随路数据
		contactUserRequireVO.setSessionId(order.getString("SESSIONID"));
		//诉求创建日期 时间戳
		contactUserRequireVO.setRequireCreateDate(order.getString("REQUIRE_CREATE_TIME"));
		//是否商场代报单 空
		contactUserRequireVO.setIsInsteadOrder(order.getString("IS_INSTEAD_ORDER"));
		//备注
		contactUserRequireVO.setPubRemark(order.getString("PUB_REMARK"));
		//产品用途  WOM_PRODUCT_USE  10 家用 11 商用 12 通讯基站
		contactUserRequireVO.setContactOrderProductUse(order.getString("CONTACT_ORDER_PRODUCT_USE")); //必填
		//模块归属（配置项）,默认01，表示手工录单
		contactUserRequireVO.setOtherModuleName(order.getString("OTHER_MODULE_NAME"));
		//机显代码
		contactUserRequireVO.setMachineErrorDisplay(order.getString("MACHINE_ERROR_DISPLAY"));
		
		contactUserRequireVO.setContactOrderVO(getContactOrderVO(order));
		return contactUserRequireVO;
		
	}
	public  static contactOrderVO getContactOrderVO( JSONObject order){
		contactOrderVO contactOrder=new contactOrderVO();
		//电话号码1 
		contactOrder.setCustomerMobilephone1(order.getString("CUSTOMER_MOBILEPHONE1"));;//必填
		//电话号码2
		contactOrder.setCustomerMobilephone2(order.getString("CUSTOMER_MOBILEPHONE2"));
		//电话号码3
		contactOrder.setCustomerMobilephone3(order.getString("CUSTOMER_MOBILEPHONE3"));
		//是否手工手升级 N
		contactOrder.setManualUpgradeFlag(order.getString("IS_UPGRADE"));//必填
		
		//联系号码1
		contactOrder.setServiceCustomerMobilephone1(order.getString("SERVICE_CUSTOMER_MOBILE"));//必填
		//联系号码2
		contactOrder.setServiceCustomerMobilephone2(order.getString("SERVICE_CUSTOMER_MOBILE2"));
		//联系号码3
		contactOrder.setServiceCustomerMobilephone3(order.getString("SERVICE_CUSTOMER_MOBIL3"));
		//CC接入单诉求唯一id
		contactOrder.setContactRequireSerialId(order.getString("APP_ID"));
		//接入单状态
		contactOrder.setContactOrderStatus(order.getString("CONTACT_ORDER_STATUS"));// 
		//接入单创建日期
		contactOrder.setOrderCreateDate(order.getString("ORDER_CREATE_TIME"));//
		//接入单号
		contactOrder.setContactOrderCode(order.getString("CONTACT_ORDER_CODE"));
		//用户档案号
		contactOrder.setCustomerCode(order.getString("CUSTOMER_CODE"));
		//来电号码
		contactOrder.setCustomerPhone(order.getString("CUSTOMER_PHONE"));//必填
		
		//用户类型 必填 10 普通用户  11 VIP用户 12 经销商
		contactOrder.setCustomerType(order.getString("CUSTOMER_TYPE"));//必填
		//CS用户诉求唯一Id 没存
		contactOrder.setContactUserRequireId("");
		//主体
		contactOrder.setOrgCode(order.getString("ORG_CODE"));//必填
		//归属模块唯一ID
		contactOrder.setOtherModuleId(order.getString("OTHER_MODULE_ID"));
		//
		contactOrder.setContactProductInfoId(order.getString(""));
		//
		contactOrder.setAppointTimeDescText(order.getString("APPOINT_TIME_DESC"));
		//是否热线 N Y
		contactOrder.setHotLineFlag("");//必填
		//详细地址
		contactOrder.setCustomerAddress(order.getString("CUSTOMER_ADDRESS"));//必填
		//区域
		contactOrder.setAreaName(order.getString("AREA_NAME"));//必填
		//区域编码
		contactOrder.setAreaCode(order.getString("AREA_CODE"));//必填
		//电话区号
		contactOrder.setAreaNum(order.getString("AREA_NUM"));//必填
		//接入方式：  10 服务热线  
		contactOrder.setContactTypeCode(order.getString("CONTACT_TYPE_CODE"));//必填
		//
		contactOrder.setContactOrderId(order.getString("CONTACT_ORDER_ID"));//
		//sessionId
		contactOrder.setSessionId(order.getString("SESSION_ID"));// 
		////诉求创建日期
		contactOrder.setRequireCreateDate(order.getString("CREATE_TIME"));// 
		//用户姓名
		if(StringUtils.isNotBlank(order.getString("CUST_NAME"))){
			contactOrder.setCustomerName(order.getString("CUST_NAME"));
		}else {
			contactOrder.setCustomerName(order.getString("CUSTOMER_NAME")); //必填
		}
		//用户等级  10 无   、11 vip1 、12 vip2。。。 
		contactOrder.setCustomerLevel(order.getString("CUSTOMER_LEVEL")); //必填
		//商场代报单
		contactOrder.setIsInsteadOrder(order.getString("IS_INSTEAD_ORDER"));
		//来源单号
		contactOrder.setSourceOrderCode(order.getString("SOURCE_ORDER_CODE"));
		//CC接入单唯一id
		contactOrder.setContactOrderSerailId(order.getString("CONTACT_ORDER_SERAIL_ID"));
		//模块归属（配置项）,默认01，表示手工录单
		contactOrder.setOtherModuleName(order.getString("OTHER_MODULE_NAME"));
		//用户星级   1-5 默认3
		contactOrder.setCustomerStarLevel(order.getString("CUSTOMER_STAR_LEVEL"));//必填
		//服务联系人姓名
		contactOrder.setServiceCustomerName(order.getString("SERVICE_CUSTOMER_NAME"));//必填
		return contactOrder;
	}
		public  static JSONObject getCustInfo(String phoneNum){
		JSONObject json = new JSONObject();
		IService service;
		try {
			service = ServiceContext.getService(ServiceID.CUSTMGR_INTERFACE);
			json.put("add", true);
			json.put("needLevel", "Y");
			json.put("phoneNum", phoneNum);
			json.put("dyylable", "y");
			json.put("add",false);//客户资料界面传来的标识
			json.put("command", ServiceCommand.CUSTMGR_SRH_CUST);
			//Map<String,String> custInfo = new HashMap<String,String>();
			JSONObject result = service.invoke(json);
			if("000".equals(result.get("respCode"))){//客户资料存在数据
				return result.getJSONObject("custInfo");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return json;
	}
		public Map<String, String> loadAllQcKpiId() {
			StringBuffer sql = new StringBuffer(" SELECT T.CHANNEL_ID,T.SESSION_TYPE,T.DIRECTION,T.QC_TYPE,T.ID FROM C_PF_QC_KPI T WHERE T.ENABLE_STATUS='01' AND IS_DEFAULT='Y'  ");
			Map<String, String> map = new HashMap<String, String>();
			try {
				logger.debug(CommonUtil.getClassNameAndMethod(this)+" 查询所有的质检指标:"+sql.toString());
				List<EasyRow> list = getQuery().queryForList(sql.toString(),null);
				if(CommonUtil.listIsNotNull(list)){
					for(EasyRow row :list){
						String channelId = row.getColumnValue("CHANNEL_ID");
						String sessionType = row.getColumnValue("SESSION_TYPE");
						String direction = row.getColumnValue("DIRECTION");
						String qcType = row.getColumnValue("QC_TYPE");
						String id = row.getColumnValue("ID");
						map.put(channelId+"_"+sessionType+"_"+direction+"_"+qcType, id);
					}
				}
				return map;
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询所有的质检指标失败.",e);
			}
			return map;
		}
	
	public   JSONObject saveQcResult(JSONObject json) {
		JSONObject result=new JSONObject();
		try {
			saveQcResultLogger.info("质检结果同步:"+JSON.toJSONString(json));
			JSONObject data = json.getJSONObject("data");
			JSONObject qcResult = data.getJSONObject("qcResult");
			String serialId = data.getString("serialId");//第三方平台音频数据唯一ID
			String taskId = data.getString("taskId");//任务ID
			saveQcResultIdLogger.info("taskId:"+taskId+";serialId:"+serialId);
			String taskConfigId =serialId.split("_")[0];//CC自动质检任务ID
			String callId =serialId.split("_")[1];//通话记录表id
			EasySQL sql=new EasySQL();
			sql.append(taskConfigId,"select  QC_TYPE,TASK_DATA from  C_NO_AUTO_QC_TASK_CONFIG where ID= ?");
			JSONObject queryForRow = getQuery().queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			String qcType=queryForRow.getString("QC_TYPE");
			String taskData=queryForRow.getString("TASK_DATA");
			if (qcType.equals("1")) {//通用质检 保存记录
				String type= DictConstants.SESSION_TYPE_VOICE;
				if(taskData.equals("1")){//在线
					 type= DictConstants.SESSION_TYPE_MEDIA;
					 sql=new EasySQL();
					 sql.append(callId,"select  SERIAL_ID AS CALL_ID,AGENT_ACC,QC_RECORD_ID,(CASE CREATE_CAUSE WHEN 1 THEN '01' WHEN 2 THEN '02' WHEN 3 THEN '01' END) as DIRECTION,CHANNEL_KEY from  YCBUSI.CC_MEDIA_RECORD where SERIAL_ID= ?");
					 
				}else{//语音
					sql=new EasySQL();
					sql.append(callId,"select  * from  C_PF_CALL_RECORD where id= ?");
				}
				
				JSONObject callInfo = getQuery().queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
				
				JSONObject qcRecord=new JSONObject();
				String acc=callInfo.getString("AGENT_ACC");

				//预设人工质检的考评指标
				Map<String,String> kpiMap = qcRecordDao.loadQcKpiIdByQcType(Constants.QC_TYPE_MANUAL);
				if(taskData.equals("1")){//在线
					if(StringUtils.isBlank(callInfo.getString("QC_RECORD_ID"))){
						String channelId = callInfo.getString("CHANNEL_KEY");
						String sessionType = callInfo.getString("02");
						String direction = callInfo.getString("DIRECTION");
						String key = channelId+"_"+sessionType+"_"+direction;

						String kpiId = kpiMap.get(key);
						if (StringUtils.isNotBlank(kpiId))
							qcRecord.put("MANUAL_QC_KPI_ID",kpiId);
					}
				}else{//语音
					String channelId = "99";
					String sessionType = "01";
					String direction = callInfo.getString("DIRECTION");
					String key = channelId+"_"+sessionType+"_"+direction;
					String kpiId = kpiMap.get(key);
					if (StringUtils.isNotBlank(kpiId))
						qcRecord.put("MANUAL_QC_KPI_ID",kpiId);
				}
				
				String qcId=RandomKit.randomStr();
				boolean save=true;//是否保存
				String status="";
				if(qcResult==null){//为空的是失败记录
					status=DictConstants.QC_RECORD_STATUS_QC_FAIL;
				}
				
				String qcRecordId = callInfo.getString("QC_RECORD_ID");
				if(StringUtils.isNotBlank(qcRecordId)){//有质检数据 不新增
					sql=new EasySQL();
					sql.append(qcRecordId,"select  * from  C_PF_QC_RECORD where ID= ?");
					JSONObject qcInfo = getQuery().queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
					qcId=qcRecordId;
					if(!DictConstants.QC_RECORD_STATUS_WAIT_AUTO_QC.equals(qcInfo.getString("STATUS"))){//有其他状态 ，且不是待自动质检，不修改原始的状态
						status=qcInfo.getString("STATUS");
					}
					save=false;

					String qcScoreId = qcInfo.getString("QC_SCORE_ID");
					if (StringUtils.isNotBlank(qcScoreId)){
						//如果已经有人工质检记录，不修改人工质检考评，防止修改后考评标准对不上了
						qcRecord.remove("MANUAL_QC_KPI_ID");
					}
				}
				
				qcRecord.put("ID", qcId);
				qcRecord.put("SESSION_RECORD_ID",callId );
				if(StringUtils.isBlank(status)){
					qcRecord.put("STATUS", DictConstants.QC_RECORD_STATUS_QC_SUCC);
				}
				qcRecord.put("AUTO_QC_KPI_ID", taskId);
				qcRecord.put("AUTO_QC_FULL_SOCRE_NUM", "");
				qcRecord.put("CREATE_TIME", DateUtil.getCurrentDateStr());
				qcRecord.put("SESSION_TYPE",type);
				qcRecord.put("AGENT_ACC",acc);
				if(qcResult!=null){
					qcRecord.put("AUTO_QC_SCORE", qcResult.getString("qcScore"));
					qcRecord.put("AUTO_QC_TIME", qcResult.getString("qcTime"));
					qcRecord.put("AUTO_QC_SCORE_ID", qcResult.getString("resultId"));
				}
			
				EasyRecord record = new EasyRecord("C_PF_QC_RECORD", "ID").setColumns(qcRecord);
//				JSONObject call = new JSONObject();
//				call.put("QC_RECORD_ID", qcId);
//				call.put("ID", callId);
//				EasyRecord callRecord = new EasyRecord("C_PF_CALL_RECORD", "ID").setColumns(call);
				EasySQL updateCallRecord=new EasySQL();
				updateCallRecord.append(qcId,"update C_PF_CALL_RECORD set QC_RECORD_ID=? ");
				updateCallRecord.append(callId,"WHERE id=? ");
				updateCallRecord.append("and QC_RECORD_ID is null ");//有自动质检的记录不修改
				if(save){
					getQuery().save(record);
//					getQuery().update(callRecord);
					getQuery().execute(updateCallRecord.getSQL(), updateCallRecord.getParams());
					saveQcResultLogger.info(callId+"同步结果 ：新增");
				}else{
					getQuery().update(record);
//					getQuery().update(callRecord);
					getQuery().execute(updateCallRecord.getSQL(), updateCallRecord.getParams());

					saveQcResultLogger.info(callId+"同步结果 ：修改");
				}
				saveQcResultItemResultList(data, callId,qcId);
				saveQcResultLogger.info("质检结果同步质检明细saveQcResultItemResultList-》"+serialId+"");
				saveQcResultChatList(data, callId, acc);
				saveQcResultLogger.info("质检结果同步质检明细saveQcResultChatList——》"+serialId+"");

				saveQcResultLogger.info("质检结果同步结果"+serialId+"成功");
				saveQcResultIdLogger.info("质检结果同步结果"+serialId+"成功");
			}else{
				saveQcResultLogger.info("质检结果同步结果"+serialId+"非通用模型");
				saveQcResultIdLogger.info("质检结果同步结果"+serialId+"非通用模型");
			}
			result.put("respData", "1");
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			result.put("respDesc", "操作成功");
			return result;
		} catch (Exception e) {
			saveQcResultLogger.info(e.getMessage(),e);
			saveQcResultIdLogger.info(e.getMessage(),e);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "操作失败："+e.getMessage());
			e.printStackTrace();
			return result;
		}
	}

	public   void saveQcResultItemResultList(JSONObject json,String callId,String qcId ) {
		JSONObject qcResult = json.getJSONObject("qcResult");
		if(qcResult!=null){
			JSONArray itemResultList = qcResult.getJSONArray("itemResultList");
			if(itemResultList!=null){
				JSONObject delqcRecord=new JSONObject();
				delqcRecord.put("CALL_RECORD_ID",qcId );
				EasyRecord delRecord = new EasyRecord("C_NO_AUTO_QC_SCORE_DETAIL", "CALL_RECORD_ID").setColumns(delqcRecord);
				try {
					getQuery().deleteById(delRecord);
				} catch (SQLException e1) {
					e1.printStackTrace();
				}
				for (int i = 0; i < itemResultList.size(); i++) {
					JSONObject itemResult = itemResultList.getJSONObject(i);
					JSONObject qcRecord=new JSONObject();
					qcRecord.put("ID", RandomKit.randomStr());
					qcRecord.put("QC_SCORE_ID",qcId );
					qcRecord.put("CALL_RECORD_ID",callId );
					qcRecord.put("SCORE_METHOD", "01");
					qcRecord.put("KPI_CATEGORY_NAME", itemResult.getString("phaseName"));
					qcRecord.put("KPI_CATEGORY_ID", itemResult.getString("phaseId"));
					qcRecord.put("KPI_ITEM_NAME", itemResult.getString("itemName"));
					qcRecord.put("KPI_ITEM_ID",itemResult.getString("itemId"));
					qcRecord.put("SCORE", itemResult.getString("itemScore"));
	//			qcRecord.put("FULL_SCORE", itemResult.getString("qcTime"));//是否为满分
					qcRecord.put("BAKUP", "");//评分备注
					qcRecord.put("RULE_ID", "");//关联规则ID
					EasyRecord record = new EasyRecord("C_NO_AUTO_QC_SCORE_DETAIL", "ID").setColumns(qcRecord);
					try {
						getQuery().save(record);
					} catch (SQLException e) {
						saveQcResultLogger.error(callId+"自动质检项同步结果失败"+e.getMessage(),e);
						e.printStackTrace();
					}
				}
			}else{
				saveQcResultLogger.info(callId+"自动质检项无内容！");
			}
		}else{
			saveQcResultLogger.info(callId+"自动质检项无内容！！");

		}
	}
	public   JSONObject saveQcResultChatList(JSONObject json,String callId,String acc) {
		JSONArray chatList = json.getJSONArray("chatList");
		if(chatList!=null){
			
			JSONObject delQcChat=new JSONObject();
			delQcChat.put("CALL_RECORD_ID",callId );
			EasyRecord delQCRecord = new EasyRecord("C_NO_AUTO_QC_RECORD_DETAIL", "CALL_RECORD_ID").setColumns(delQcChat);
			
			JSONObject delKey=new JSONObject();
			delKey.put("CALL_DETAIL_ID",callId );
			EasyRecord delKeyRecord = new EasyRecord("C_NO_AUTO_QC_KEYWORD", "CALL_ID").setColumns(delKey);
			try {
				getQuery().deleteById(delQCRecord);
				getQuery().deleteById(delKeyRecord);
			} catch (SQLException e1) {
				e1.printStackTrace();
			}			
			for (int i = 0; i < chatList.size(); i++) {
				JSONObject chat = chatList.getJSONObject(i);
				JSONObject qcRecord=new JSONObject();
				String qcChatId = RandomKit.randomStr();
				qcRecord.put("ID",qcChatId );
				qcRecord.put("CALL_RECORD_ID",callId );
				qcRecord.put("BEGIN_TIME", chat.getString("callStartTime"));
				qcRecord.put("END_TIME", chat.getString("callEndTime"));
				qcRecord.put("CALL_LEN", chat.getInteger("end")-chat.getInteger("start"));
				qcRecord.put("CONTENT", chat.getString("text"));
				//chat.getString("channel");//0为用户，1为客服
				String channel="01";
				if("0".equals(chat.getString("channel"))){
					channel="02";
				}
				qcRecord.put("USER_TYPE",channel);//01-客服 02-客户 03-机器人 04-系统
				qcRecord.put("AGENT_ACC", acc);
				try {//怕返回数据有问题
					Integer robTime = chat.getInteger("robTime");
					qcRecord.put("IS_CROSS", robTime>0?"Y":"N");//是否抢话
					qcRecord.put("CROSS_SECONDS", robTime/1000);//抢话时长(秒)
				} catch (Exception e) {
					saveQcResultLogger.error(callId+"自动质检明抢话判断失败"+e.getMessage(),e);
					qcRecord.put("IS_CROSS", "N");//是否抢话
					qcRecord.put("CROSS_SECONDS", 0);//抢话时长(秒)
				}
				try {//怕返回数据有问题
					Integer silenceDuration = chat.getInteger("silenceDuration");
					qcRecord.put("IS_NO_VOICE", silenceDuration>0?"Y":"N");//是否静音
					qcRecord.put("NO_VOICE_SECONDS", silenceDuration/1000);//静音时长(秒)
				} catch (Exception e) {
					saveQcResultLogger.error(callId+"自动质检明静音判断失败"+e.getMessage(),e);
					qcRecord.put("IS_NO_VOICE", "N");
					qcRecord.put("NO_VOICE_SECONDS", 0);
				}
				qcRecord.put("START_SECONDS", chat.getInteger("start"));//相对开始时间(第几毫秒)
				qcRecord.put("END_SECONDS", chat.getInteger("end"));//相对结束时间(第几毫秒)
				qcRecord.put("SPEED", chat.getInteger("speechRate"));//语速(字/每分钟)
				EasyRecord record = new EasyRecord("C_NO_AUTO_QC_RECORD_DETAIL", "ID").setColumns(qcRecord);
				JSONObject qcKeyRecord=new JSONObject();
				qcKeyRecord.put("ID", RandomKit.randomStr());
				qcKeyRecord.put("CALL_ID",callId );
				qcKeyRecord.put("CALL_DETAIL_ID", qcChatId);
				qcKeyRecord.put("KEYWORD", chat.getString("speechRate"));
				qcKeyRecord.put("USER_TYPE", channel);
				EasyRecord keyRecord = new EasyRecord("C_NO_AUTO_QC_KEYWORD", "ID").setColumns(qcKeyRecord);
				
				try {
					getQuery().save(record);
					getQuery().save(keyRecord);
				} catch (SQLException e) {
					saveQcResultLogger.error(callId+"自动质检明细同步结果失败"+e.getMessage(),e);
					e.printStackTrace();
				}
			}
		}else{
			saveQcResultLogger.info(callId+"自动质检明细无内容！");
		}
		return json;
	}
	private static String getInData(String[] split) {
		String value="";
		for(String str:split ){
			value=value.equals("")?"'"+str+"'":value+",'"+str+"'";
		}
		return value;
	}
	private static HttpResp sendPost(String url){
		return HttpUtil.post(url,"","UTF-8");
	}


	/**
	*@title 历史数据重推智能质检（手工修复数据）
	*@date 2024/10/24
	*/
	public  static JSONObject resendQualtiyInfoByTaskId(JSONObject jsonObject) {
		try {
			String startTime = jsonObject.getString("startTime");
			String endTime = jsonObject.getString("endTime");
			String taskId = jsonObject.getString("taskId");

			String sql="select  *  from  C_NO_AUTO_QC_TASK_CONFIG where ID =?  ";
			JSONObject task = getQuery().queryForRow(sql,new Object[]{taskId}, new JSONMapperImpl());
			String callSkillSql="select SKILL_CODE as CODE,SKILL_NAME as NAME from C_QUEUE where  SKILL_CODE is not null";
			List<EasyRow> callSkill = getQuery().queryForList(callSkillSql,new Object[]{});
			callSkillDict = dict(callSkill).getJSONObject("data");
			String mideaSkillSql="select code,name from V_CF_SKILLS ";
			List<EasyRow> mideaSkill = getQuery().queryForList(mideaSkillSql,new Object[]{});
			mideaSkillDict = dict(mideaSkill).getJSONObject("data");
			String deptSql="select DEPT_CODE as code,DEPT_NAME as  name from C_YG_V_MARS_DEPT ";
			List<EasyRow> dept = getQuery().queryForList(deptSql,new Object[]{});
			deptDict = dict(dept).getJSONObject("data");
			CommMap commMap=new CommMap();
			Map womCcAppointTime = commMap.getMapSysCode("WOM_CC_APPOINT_TIME","");
			womCcAppointTimeDict = new JSONObject(womCcAppointTime);

			String qcType=task.getString("QC_TYPE");
			if (qcType.equals("1")) {//通用质检 保存记录

			}
			String taskData=task.getString("TASK_DATA");
			loggerReSend.info(task.getString("ID")+"[手工推送]自动质检");
			EasySQL oracleSql=new EasySQL();
			//处理多选
			String orgCode="";
			if(!StringUtils.isBlank(task.getString("ORG_CODE"))){
				String[] split = task.getString("ORG_CODE").split(",");
				orgCode=getInData(split);
			}

			String serviceRequireTypeCode="";
			if(!StringUtils.isBlank(task.getString("SERVICE_REQUIRE_TYPE_CODE"))){
				String[] split = task.getString("SERVICE_REQUIRE_TYPE_CODE").split(",");
				serviceRequireTypeCode=getInData(split);
			}
			String serviceRequireItemCode="";
			if(!StringUtils.isBlank(task.getString("SERVICE_REQUIRE_ITEM_CODE"))){
				String[] split = task.getString("SERVICE_REQUIRE_ITEM_CODE").split(",");
				serviceRequireItemCode=getInData(split);
			}
			String serviceRequireItemCode2="";
			if(!StringUtils.isBlank(task.getString("SERVICE_REQUIRE_ITEM_CODE2"))){
				String[] split = task.getString("SERVICE_REQUIRE_ITEM_CODE2").split(",");
				serviceRequireItemCode2=getInData(split);
			}

			String zxserviceRequireTypeCode="";
			if(!StringUtils.isBlank(task.getString("ZX_SERVICE_REQUIRE_TYPE_CODE"))){
				String[] split = task.getString("ZX_SERVICE_REQUIRE_TYPE_CODE").split(",");
				zxserviceRequireTypeCode=getInData(split);
			}
			String zxserviceRequireItemCode="";
			if(!StringUtils.isBlank(task.getString("ZX_SERVICE_REQUIRE_ITEM_CODE"))){
				String[] split = task.getString("ZX_SERVICE_REQUIRE_ITEM_CODE").split(",");
				zxserviceRequireItemCode=getInData(split);
			}
			String zxserviceRequireItemCode2="";
			if(!StringUtils.isBlank(task.getString("ZX_SERVICE_REQUIRE_ITEM_CODE2"))){
				String[] split = task.getString("ZX_SERVICE_REQUIRE_ITEM_CODE2").split(",");
				zxserviceRequireItemCode2=getInData(split);
			}
			String channelCode="";
			if(!StringUtils.isBlank(task.getString("CHANNEL_CODE"))){
				String[] split = task.getString("CHANNEL_CODE").split(",");
				channelCode=getInData(split);
			}

			loggerReSend.info(taskData+"-->"+task.getString("ORG_CODE")+"手工推送转换orgCode："+orgCode);
			if(Constants.ORDER_TYPE_NOT_ORDER_CALL.equals(task.getString("ORDER_TYPE"))){
				//以防万一工单数据前后多查询一天
				String orderStartTime=addDay(startTime,-1);
				String orderEndTime=addDay(endTime,1);
				//无工单纯话务
				oracleSql.append("select emp.ENTRY_MONTHS,emp.WORK_NO  ");
				oracleSql.append(",call.AGENT_ACC as AGENTACC ,call.AGENT_DEPT as AGENTDEPT ,CALL.AGENT_NAME AS AGENTNAME,CALL.SESSION_ID AS SESSION_ID,CALL.SESSION_ID AS SESSIONID,CALL.CALLED,CALL.CALLER,'' as CHANNEL_NAME , '' as CHANNEL_KEY  ");
				oracleSql.append(",DIRECTION,RINGING_TIME,ANSWER_TIME,END_TIME,SKILL_CODE,HANGUP_TYPE,VOICE_URL,SATISFACTION_CODE,call.ID AS CALL_ID  ");
				oracleSql.append("from  C_PF_CALL_RECORD call  ");
				oracleSql.append("LEFT JOIN C_YG_EMPLOYEE emp on call.AGENT_ACC =emp.USER_ACC and emp.STATUS!=4  ");
				oracleSql.append(startTime,"where ANSWER_TIME >=? ");
				oracleSql.append(endTime,"and ANSWER_TIME< ?");
				oracleSql.append("AND NOT EXISTS (SELECT 1 FROM C_NO_APPEALLIST app ");
				oracleSql.append("WHERE call.SESSION_ID=app.SESSION_ID ");
				oracleSql.append(orderStartTime,"AND  app.REQUIRE_CREATE_TIME > ? ");
				oracleSql.append(orderEndTime,"AND  app.REQUIRE_CREATE_TIME < ? ");
				oracleSql.append(")");
				oracleSql.append(task.getString("SKILL_CODE")," and SKILL_CODE =?");
			}else if(Constants.ORDER_TYPE_ZX_ORDER.equals(task.getString("ORDER_TYPE"))){
				//咨询工单
				oracleSql.append("select  ord.*,emp.ENTRY_MONTHS,emp.WORK_NO,media.AGENT_ACC as AGENTACC , media.AGENT_DEPT as AGENTDEPT ,media.AGENT_NAME AS AGENTNAME,media.SESSION_ID AS SESSIONID,'' as CALLED,'' as CALLER,media.CHANNEL_NAME,media.CHANNEL_KEY  ");
				oracleSql.append(",'01' as DIRECTION,BEGIN_TIME as RINGING_TIME,BEGIN_TIME as ANSWER_TIME,END_TIME,GROUP_ID as SKILL_CODE,'' as HANGUP_TYPE,'' as VOICE_URL, SATISF_CODE as SATISFACTION_CODE,media.SERIAL_ID AS CALL_ID  ");
				oracleSql.append(",ord.ID as APP_ID ");
				//oracleSql.append(",con.SOURCE_ORDER_CODE,con.CUSTOMER_ID,con.CUSTOMER_CODE,con.CUSTOMER_PHONE,con.IS_INSTEAD_ORDER ,con.SERVICE_CUSTOMER_MOBILE,con.SERVICE_CUSTOMER_MOBILE2,con.SERVICE_CUSTOMER_MOBILE3,con.SERVICE_CUSTOMER_ADDRESS,con.ECM_ORDER_VO ");
				//oracleSql.append(",con.CUSTOMER_MOBILEPHONE1,con.CUSTOMER_MOBILEPHONE2,con.CUSTOMER_MOBILEPHONE3,con.SERVICE_CUSTOMER_MOBILE,con.CONTACT_ORDER_STATUS,con.CREATE_TIME as ORDER_CREATE_TIME,con.CUSTOMER_TYPE,con.CUSTOMER_ADDRESS ");
				//oracleSql.append(",con.AREA_NAME,con.AREA_CODE,con.AREA_NUM,con.CONTACT_TYPE_CODE,con.CUSTOMER_NAME,con.CUSTOMER_LEVEL,con.IS_INSTEAD_ORDER,con.SOURCE_ORDER_CODE,con.id as CONTACT_ORDER_SERAIL_ID,con.OTHER_MODULE_NAME,con.CUSTOMER_STAR_LEVEL,con.SERVICE_CUSTOMER_NAME");
				oracleSql.append("from  C_OL_CONSULT_ORDER ord  ");
				oracleSql.append("LEFT JOIN YCBUSI.CC_MEDIA_RECORD media on media.SERIAL_ID=ord.SESSION_ID  ");
				oracleSql.append("LEFT JOIN C_YG_EMPLOYEE emp on media.AGENT_ACC =emp.USER_ACC and emp.STATUS!=4 ");
				oracleSql.append(startTime," where ord.create_time >=? ");
				oracleSql.append(endTime,"  and ord.create_time< ?");
				if(!StringUtils.isBlank(orgCode)){
					oracleSql.append(" and ord.ORG_CODE in ("+orgCode+")");
				}
				if(!StringUtils.isBlank(zxserviceRequireTypeCode)	){
					oracleSql.append(" and ord.ORDER_TYPE_FIRST in ("+zxserviceRequireTypeCode+")");
				}
				if(!StringUtils.isBlank(zxserviceRequireItemCode)	){
					oracleSql.append(" and ord.ORDER_TYPE_SECOND in ("+zxserviceRequireItemCode+")");
				}
				if(!StringUtils.isBlank(zxserviceRequireItemCode2)	){
					oracleSql.append(" and ord.ORDER_TYPE_THIRD in ("+zxserviceRequireItemCode2+")");
				}
				if(!StringUtils.isBlank(channelCode)){
					oracleSql.append(" and media.channel_key in ("+channelCode+")");
				}
				oracleSql.append(task.getString("SKILL_CODE")," and GROUP_ID =?");
				oracleSql.append("and  media.BEGIN_TIME is not null");
			}else{
				if("1".equals(taskData)){//在线
					oracleSql.append("select  app.*,emp.ENTRY_MONTHS,emp.WORK_NO,media.AGENT_ACC as AGENTACC , media.AGENT_DEPT as AGENTDEPT ,media.AGENT_NAME AS AGENTNAME,media.SESSION_ID AS SESSIONID,'' as CALLED,'' as CALLER,media.CHANNEL_NAME,media.CHANNEL_KEY  ");
					oracleSql.append(",'01' as DIRECTION,BEGIN_TIME as RINGING_TIME,BEGIN_TIME as ANSWER_TIME,END_TIME,GROUP_ID as SKILL_CODE,'' as HANGUP_TYPE,'' as VOICE_URL, SATISF_CODE as SATISFACTION_CODE,media.SERIAL_ID AS CALL_ID  ");
					oracleSql.append(",app.ID as APP_ID,con.SOURCE_ORDER_CODE,con.CUSTOMER_ID,con.CUSTOMER_CODE,con.CUSTOMER_PHONE,con.IS_INSTEAD_ORDER ,con.SERVICE_CUSTOMER_MOBILE,con.SERVICE_CUSTOMER_MOBILE2,con.SERVICE_CUSTOMER_MOBILE3 ");
					oracleSql.append(",con.SERVICE_CUSTOMER_ADDRESS,con.ECM_ORDER_VO ");
					oracleSql.append(",con.CUSTOMER_MOBILEPHONE1,con.CUSTOMER_MOBILEPHONE2,con.CUSTOMER_MOBILEPHONE3,con.SERVICE_CUSTOMER_MOBILE,con.CONTACT_ORDER_STATUS,con.CREATE_TIME as ORDER_CREATE_TIME,con.CUSTOMER_TYPE,con.CUSTOMER_ADDRESS ");
					oracleSql.append(",con.AREA_NAME,con.AREA_CODE,con.AREA_NUM,con.CONTACT_TYPE_CODE,con.CUSTOMER_NAME,con.CUSTOMER_LEVEL,con.IS_INSTEAD_ORDER,con.SOURCE_ORDER_CODE,con.id as CONTACT_ORDER_SERAIL_ID,con.OTHER_MODULE_NAME,con.CUSTOMER_STAR_LEVEL,con.SERVICE_CUSTOMER_NAME");
					oracleSql.append(",appe.MACHINE_ID ");
					oracleSql.append("from  C_NO_APPEALLIST app left join C_NO_CONTACT  con on app.CONTACT_SERAIL_ID=con.ID ");
					oracleSql.append("LEFT JOIN YCBUSI.CC_MEDIA_RECORD media on media.SERIAL_ID=app.SESSION_ID and app.CREATE_ACC=media.AGENT_ACC and  app.MODIFY_ACC is null ");
					oracleSql.append("LEFT JOIN C_YG_EMPLOYEE emp on media.AGENT_ACC =emp.USER_ACC and emp.STATUS!=4 ");
					oracleSql.append("LEFT JOIN C_NO_APPEALLIST_EXTEND appe on appe.APPEAL_ID =app.ID ");
					oracleSql.append(startTime," where REQUIRE_CREATE_TIME >=? ");
					oracleSql.append(endTime,"  and REQUIRE_CREATE_TIME< ?");
					if(!StringUtils.isBlank(orgCode)	){
						oracleSql.append(" and app.ORG_CODE in ("+orgCode+")");
					}
					if(!StringUtils.isBlank(serviceRequireTypeCode)	){
						oracleSql.append(" and app.CONTACT_ORDER_SERV_TYPE_CODE in ("+serviceRequireTypeCode+")");
					}
					if(!StringUtils.isBlank(serviceRequireItemCode)	){
						oracleSql.append(" and app.CONTACT_ORDER_SER_ITEM1_CODE in ("+serviceRequireItemCode+")");
					}
					if(!StringUtils.isBlank(serviceRequireItemCode2)	){
						oracleSql.append(" and app.CONTACT_ORDER_SER_ITEM2_CODE in ("+serviceRequireItemCode2+")");
					}
					if(!StringUtils.isBlank(channelCode)){
						oracleSql.append(" and media.channel_key in ("+channelCode+")");
					}
					oracleSql.append(task.getString("SKILL_CODE")," and GROUP_ID =?");
					oracleSql.append("and con.CONTACT_ORDER_STATUS is null");
					oracleSql.append("and  media .BEGIN_TIME is not null");
					oracleSql.append("union");
					oracleSql.append("select  app.*,emp.ENTRY_MONTHS,emp.WORK_NO,media.AGENT_ACC as AGENTACC , media.AGENT_DEPT as AGENTDEPT ,media.AGENT_NAME AS AGENTNAME,media.SESSION_ID AS SESSIONID,'' as CALLED,'' as CALLER ,media.CHANNEL_NAME , media.CHANNEL_KEY ");
					oracleSql.append(",'01' as DIRECTION,BEGIN_TIME as RINGING_TIME,BEGIN_TIME as ANSWER_TIME,END_TIME,GROUP_ID as SKILL_CODE,'' as HANGUP_TYPE,'' as VOICE_URL, SATISF_CODE as SATISFACTION_CODE,media.SERIAL_ID AS CALL_ID  ");
					oracleSql.append(",app.ID as APP_ID,con.SOURCE_ORDER_CODE,con.CUSTOMER_ID,con.CUSTOMER_CODE,con.CUSTOMER_PHONE,con.IS_INSTEAD_ORDER ,con.SERVICE_CUSTOMER_MOBILE,con.SERVICE_CUSTOMER_MOBILE2,con.SERVICE_CUSTOMER_MOBILE3 ");
					oracleSql.append(",con.SERVICE_CUSTOMER_ADDRESS,con.ECM_ORDER_VO ");
					oracleSql.append(",con.CUSTOMER_MOBILEPHONE1,con.CUSTOMER_MOBILEPHONE2,con.CUSTOMER_MOBILEPHONE3,con.SERVICE_CUSTOMER_MOBILE,con.CONTACT_ORDER_STATUS,con.CREATE_TIME as ORDER_CREATE_TIME,con.CUSTOMER_TYPE,con.CUSTOMER_ADDRESS ");
					oracleSql.append(",con.AREA_NAME,con.AREA_CODE,con.AREA_NUM,con.CONTACT_TYPE_CODE,con.CUSTOMER_NAME,con.CUSTOMER_LEVEL,con.IS_INSTEAD_ORDER,con.SOURCE_ORDER_CODE,con.id as CONTACT_ORDER_SERAIL_ID,con.OTHER_MODULE_NAME,con.CUSTOMER_STAR_LEVEL,con.SERVICE_CUSTOMER_NAME");
					oracleSql.append(",appe.MACHINE_ID ");
					oracleSql.append("from  C_NO_APPEALLIST app left join C_NO_CONTACT  con on app.CONTACT_SERAIL_ID=con.ID ");
					oracleSql.append("LEFT JOIN YCBUSI.CC_MEDIA_RECORD media on media.SERIAL_ID=app.SESSION_ID and app.MODIFY_ACC=media.AGENT_ACC  ");
					oracleSql.append("LEFT JOIN C_YG_EMPLOYEE emp on media.AGENT_ACC =emp.USER_ACC and emp.STATUS!=4  ");
					oracleSql.append("LEFT JOIN C_NO_APPEALLIST_EXTEND appe on appe.APPEAL_ID =app.ID ");
					oracleSql.append(startTime," where REQUIRE_CREATE_TIME >=? ");
					oracleSql.append(endTime,"  and REQUIRE_CREATE_TIME< ?");
					if(!StringUtils.isBlank(orgCode)	){
						oracleSql.append(" and app.ORG_CODE in ("+orgCode+")");
					}
					if(!StringUtils.isBlank(serviceRequireTypeCode)	){
						oracleSql.append(" and app.CONTACT_ORDER_SERV_TYPE_CODE in ("+serviceRequireTypeCode+")");
					}
					if(!StringUtils.isBlank(serviceRequireItemCode)	){
						oracleSql.append(" and app.CONTACT_ORDER_SER_ITEM1_CODE in ("+serviceRequireItemCode+")");
					}
					if(!StringUtils.isBlank(serviceRequireItemCode2)	){
						oracleSql.append(" and app.CONTACT_ORDER_SER_ITEM2_CODE in ("+serviceRequireItemCode2+")");
					}
					if(!StringUtils.isBlank(channelCode)	){
						oracleSql.append(" and media.channel_key in ("+channelCode+")");
					}
					oracleSql.append(task.getString("SKILL_CODE")," and GROUP_ID =?");
					oracleSql.append("and con.CONTACT_ORDER_STATUS is null");
					oracleSql.append("and  media .BEGIN_TIME is not null");
				}else{
					oracleSql.append("select  app.*,emp.ENTRY_MONTHS,emp.WORK_NO,call.AGENT_ACC as AGENTACC , call.AGENT_DEPT as AGENTDEPT ,CALL.AGENT_NAME AS AGENTNAME,CALL.SESSION_ID AS SESSIONID,CALL.CALLED,CALL.CALLER,'' as CHANNEL_NAME , '' as CHANNEL_KEY  ");
					oracleSql.append(",DIRECTION,RINGING_TIME,call.ANSWER_TIME as ANSWER_TIME,END_TIME,SKILL_CODE,HANGUP_TYPE,VOICE_URL,SATISFACTION_CODE,call.ID AS CALL_ID  ");
					oracleSql.append(",app.ID as APP_ID,con.SOURCE_ORDER_CODE,con.CUSTOMER_ID,con.CUSTOMER_CODE,con.CUSTOMER_PHONE,con.IS_INSTEAD_ORDER ,con.SERVICE_CUSTOMER_MOBILE,con.SERVICE_CUSTOMER_MOBILE2,con.SERVICE_CUSTOMER_MOBILE3 ");
					oracleSql.append(",con.SERVICE_CUSTOMER_ADDRESS,con.ECM_ORDER_VO ");
					oracleSql.append(",con.CUSTOMER_MOBILEPHONE1,con.CUSTOMER_MOBILEPHONE2,con.CUSTOMER_MOBILEPHONE3,con.SERVICE_CUSTOMER_MOBILE,con.CONTACT_ORDER_STATUS,con.CREATE_TIME as ORDER_CREATE_TIME,con.CUSTOMER_TYPE,con.CUSTOMER_ADDRESS ");
					oracleSql.append(",con.AREA_NAME,con.AREA_CODE,con.AREA_NUM,con.CONTACT_TYPE_CODE,con.CUSTOMER_NAME,con.CUSTOMER_LEVEL,con.IS_INSTEAD_ORDER,con.SOURCE_ORDER_CODE,con.id as CONTACT_ORDER_SERAIL_ID,con.OTHER_MODULE_NAME,con.CUSTOMER_STAR_LEVEL,con.SERVICE_CUSTOMER_NAME");
					oracleSql.append(",appe.MACHINE_ID ");
					oracleSql.append("from  C_NO_APPEALLIST app left join C_NO_CONTACT  con on app.CONTACT_SERAIL_ID=con.ID ");
					oracleSql.append("LEFT JOIN C_PF_CALL_RECORD call on call.SESSION_ID=app.SESSION_ID and app.CREATE_ACC=call.AGENT_ACC and  app.MODIFY_ACC is null ");
					oracleSql.append("LEFT JOIN C_YG_EMPLOYEE emp on call.AGENT_ACC =emp.USER_ACC and emp.STATUS!=4 ");
					oracleSql.append("LEFT JOIN C_NO_APPEALLIST_EXTEND appe on appe.APPEAL_ID =app.ID ");
					oracleSql.append(startTime," where REQUIRE_CREATE_TIME >=? ");
					oracleSql.append(endTime,"  and REQUIRE_CREATE_TIME< ?");
					if(!StringUtils.isBlank(orgCode)	){
						oracleSql.append(" and app.ORG_CODE in ("+orgCode+")");
					}
					if(!StringUtils.isBlank(serviceRequireTypeCode)	){
						oracleSql.append(" and app.CONTACT_ORDER_SERV_TYPE_CODE in ("+serviceRequireTypeCode+")");
					}
					if(!StringUtils.isBlank(serviceRequireItemCode)	){
						oracleSql.append(" and app.CONTACT_ORDER_SER_ITEM1_CODE in ("+serviceRequireItemCode+")");
					}
					if(!StringUtils.isBlank(serviceRequireItemCode2)	){
						oracleSql.append(" and app.CONTACT_ORDER_SER_ITEM2_CODE in ("+serviceRequireItemCode2+")");
					}
					oracleSql.append(task.getString("SKILL_CODE")," and SKILL_CODE =?");
					oracleSql.append("and con.CONTACT_ORDER_STATUS is null");
					oracleSql.append("and call.answer_time is not null");
					oracleSql.append("union");
					oracleSql.append("select  app.*,emp.ENTRY_MONTHS,emp.WORK_NO,call.AGENT_ACC as AGENTACC ,call.AGENT_DEPT as AGENTDEPT ,CALL.AGENT_NAME AS AGENTNAME,CALL.SESSION_ID AS SESSIONID,CALL.CALLED,CALL.CALLER,'' as CHANNEL_NAME , '' as CHANNEL_KEY  ");
					oracleSql.append(",DIRECTION,RINGING_TIME,call.ANSWER_TIME as ANSWER_TIME,END_TIME,SKILL_CODE,HANGUP_TYPE,VOICE_URL,SATISFACTION_CODE,call.ID AS CALL_ID  ");
					oracleSql.append(",app.ID as APP_ID,con.SOURCE_ORDER_CODE,con.CUSTOMER_ID,con.CUSTOMER_CODE,con.CUSTOMER_PHONE,con.IS_INSTEAD_ORDER ,con.SERVICE_CUSTOMER_MOBILE,con.SERVICE_CUSTOMER_MOBILE2,con.SERVICE_CUSTOMER_MOBILE3 ");
					oracleSql.append(",con.SERVICE_CUSTOMER_ADDRESS,con.ECM_ORDER_VO ");
					oracleSql.append(",con.CUSTOMER_MOBILEPHONE1,con.CUSTOMER_MOBILEPHONE2,con.CUSTOMER_MOBILEPHONE3,con.SERVICE_CUSTOMER_MOBILE,con.CONTACT_ORDER_STATUS,con.CREATE_TIME as ORDER_CREATE_TIME,con.CUSTOMER_TYPE,con.CUSTOMER_ADDRESS ");
					oracleSql.append(",con.AREA_NAME,con.AREA_CODE,con.AREA_NUM,con.CONTACT_TYPE_CODE,con.CUSTOMER_NAME,con.CUSTOMER_LEVEL,con.IS_INSTEAD_ORDER,con.SOURCE_ORDER_CODE,con.id as CONTACT_ORDER_SERAIL_ID,con.OTHER_MODULE_NAME,con.CUSTOMER_STAR_LEVEL,con.SERVICE_CUSTOMER_NAME");
					oracleSql.append(",appe.MACHINE_ID ");
					oracleSql.append("from  C_NO_APPEALLIST app left join C_NO_CONTACT  con on app.CONTACT_SERAIL_ID=con.ID ");
					oracleSql.append("LEFT JOIN C_PF_CALL_RECORD call on call.SESSION_ID=app.SESSION_ID and app.MODIFY_ACC=call.AGENT_ACC  ");
					oracleSql.append("LEFT JOIN C_YG_EMPLOYEE emp on call.AGENT_ACC =emp.USER_ACC and emp.STATUS!=4  ");
					oracleSql.append("LEFT JOIN C_NO_APPEALLIST_EXTEND appe on appe.APPEAL_ID =app.ID ");
					oracleSql.append(startTime," where REQUIRE_CREATE_TIME >=? ");
					oracleSql.append(endTime,"  and REQUIRE_CREATE_TIME< ?");
					if(!StringUtils.isBlank(orgCode)){
						oracleSql.append(" and app.ORG_CODE in ("+orgCode+")");
					}
					if(!StringUtils.isBlank(serviceRequireTypeCode)	){
						oracleSql.append(" and app.CONTACT_ORDER_SERV_TYPE_CODE in ("+serviceRequireTypeCode+")");
					}
					if(!StringUtils.isBlank(serviceRequireItemCode)	){
						oracleSql.append(" and app.CONTACT_ORDER_SER_ITEM1_CODE in ("+serviceRequireItemCode+")");
					}
					if(!StringUtils.isBlank(serviceRequireItemCode2)	){
						oracleSql.append(" and app.CONTACT_ORDER_SER_ITEM2_CODE in ("+serviceRequireItemCode2+")");
					}
					oracleSql.append(task.getString("SKILL_CODE")," and SKILL_CODE =?");
					oracleSql.append("and con.CONTACT_ORDER_STATUS is null");
					oracleSql.append("and call.answer_time is not null");
				}
			}

			loggerReSend.info("手工推送自动质检的时间："+startTime+"--"+endTime+"-->"+oracleSql.getSQL()+"-》"+oracleSql.getParams());
			EasyQuery query = getQuery();
			query.setMaxRow(99999);
			List<JSONObject> orderList = query.queryForList(oracleSql.getSQL(),oracleSql.getParams(), new JSONMapperImpl());
			loggerReSend.info(task.getString("ID")+"手工推送自动质检时间："+startTime+"-->"+endTime+","+orderList.size());
			loggerReSend.info(task.getString("ID")+";"+oracleSql.getSQL()+";"+JSONObject.toJSONString(oracleSql.getParams()));
			HashMap<String, SendQualtiyInfo> map =new HashMap<String, SendQualtiyInfo>();
			List<CompletableFuture<Void>> futures = new ArrayList<>();
			for(JSONObject order:orderList){

				CompletableFuture<Void> future = CompletableFuture.supplyAsync(() -> {
					String sessionId = order.getString("SESSION_ID");
					try {
						loggerReSend.info("[手工推送]自动质检sessionId"+sessionId+"数据开始");
						loggerReSend.info("[手工推送]自动质检sessionId"+sessionId+"数据:"+order.toJSONString());
						if(map.get(sessionId)==null){
							SendQualtiyInfo sendQualtiyInfo=getSendQualtiyInfo(order,task.getString("TASK_ID"),task.getString("ID"),taskData,qcType,task.getString("ORDER_TYPE"),false);
							map.put(sessionId,sendQualtiyInfo);
							loggerReSend.info("[手工推送]自动质检sessionId"+sessionId+"数据："+JSON.toJSONString(sendQualtiyInfo));
						}
					} catch (Exception e) {
						loggerReSend.info("[手工推送]线程处理手工推送自动质检sessionId"+sessionId+"数据出错");
					}

					return null;
				}, ThreadPoolKit.getExecutor());
				futures.add(future);

			}
			try {
				loggerReSend.info(task.getString("ID")+" [手工推送]等待所有任务执行完成中，执行数量="+futures.size());
				CompletableFuture<Void> voidCompletableFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
				voidCompletableFuture.join();
				loggerReSend.info(task.getString("ID")+" [手工推送]等待所有任务执行完成中");
			} catch (Exception e) {
				loggerReSend.info(task.getString("ID")+"[手工推送]线程error：" + e.getMessage(),e);
			}

			loggerReSend.info(task.getString("ID")+"[手工推送]自动质检准备发送数据："+map.size());
			String url=Constants.QC_URL;
			int success=0;
			int error=0;
			for (String key : map.keySet()) {
				try {
					SendQualtiyInfo sendQualtiyInfo = map.get(key);
					//JSONObject json = JSON.parseObject(JSON.toJSONString(sendQualtiyInfo));
					JSONObject json=new JSONObject();
					JSONArray jsonArray=new JSONArray();
					jsonArray.add(JSON.parseObject(JSON.toJSONString(sendQualtiyInfo)));
					json.put("data",jsonArray );
					json.put("messageId", RandomKit.randomStr());
					json.put("serialId", sendQualtiyInfo.getSerialId());
					json.put("timestamp", System.currentTimeMillis());
					loggerReSend.info("[手工推送]同步自动质检数据"+url+","+sendQualtiyInfo.getSerialId());
					HttpResp post = HttpUtil.post(url,json.toJSONString(),"UTF-8");
					if(post.getCode()==200){
						String result = post.getResult();
						try {
							JSONObject parseObject = JSON.parseObject(result);
							if(parseObject!=null&&parseObject.get("result")!=null&&"000".equals(parseObject.getString("result"))){
								success=success+1;
							}else{
								error=error+1;
								loggerError.error("[手工推送]同步自动质检数据"+sendQualtiyInfo.getSerialId()+"返回"+result);
							}
						} catch (Exception e) {
							error=error+1;
						}
						loggerReSend.info("[手工推送]同步自动质检数据"+sendQualtiyInfo.getSerialId()+"返回"+result);
					}else{
						error=error+1;
						loggerReSend.error("[手工推送]同步自动质检数据"+sendQualtiyInfo.getSerialId()+"返回"+post.getResult());
					}
				} catch (Exception e) {
					loggerReSend.error("[手工推送]同步自动质检失败:"+e.getMessage(),e);
					loggerReSend.error("[手工推送]同步自动质检失败:"+e.getMessage(),e);

				}
			}
			loggerReSend.info(task.getString("ID")+"[手工推送]自动质检时间："+startTime+"-->"+endTime+"数量："+orderList.size()+"其中成功数量："+success+"失败："+error);
		} catch (Exception e) {
			loggerReSend.error("[手工推送]自动质检失败:"+e.getMessage(),e);
		}
		//getTime("2023-01-30 00:00:00","2023-01-29 13:00:00");
		JSONObject result=new JSONObject();
		result.put("respData", result);
		result.put("respCode", GWConstants.RET_CODE_SUCCESS);
		result.put("respDesc", "操作成功");
		return result;

	}

}
