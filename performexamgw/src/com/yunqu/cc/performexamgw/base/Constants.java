package com.yunqu.cc.performexamgw.base;

import org.easitline.common.core.context.AppContext;

import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ConfigUtil;

/**
 * 常量
 * <AUTHOR>
 *
 */
public class Constants {

	public final static String DS_NAME = "yw-ds";     //默认数据源名称

	public final static String MARS_DS_NAME = "mars-ds";     //Mars数据源

	public static final String VOICE_CONTENT_DS = "voice_content_ds"; //获取转写文本的数据源

	public static final String VOICE_SATISFY_DS = "voice_satisfy_ds"; //读取语音满意度结果数据源，如genesys 顺德数据源
	public static final String VOICE_SATISFY_DS2 = "voice_satisfy_ds2"; //读取语音满意度结果数据源，如genesys 合肥数据源

	public static final String MEDIA_DS_NAME = "media_ds"; //全媒体话单数据源

	public final static String APP_NAME = "performexamgw";     //应用

	public static final String CSS_BASE_URL = "";

	public static final AppContext context = AppContext.getContext(APP_NAME);

	/**
	 * 语音通话记录表
	 */
	public static final String CALL_RECORD_TABLE_NAME= "C_PF_CALL_RECORD";//context.getProperty("CALL_RECORD_TABLE_NAME", "");
	/**
	 * 语音通话记录质检记录字段名
	 */
	public static final String CALL_RECORD_QC_FIELD="QC_RECORD_ID"; //context.getProperty("CALL_RECORD_QC_FIELD", "");
	/**
	 * 全媒体会话记录表
	 */
	public static final String SESSION_RECORD_TABLE_NAME="CC_MEDIA_RECORD";//context.getProperty("SESSION_RECORD_TABLE_NAME", "");
	/**
	 * 全媒体会话消息记录表
	 */
	public static final String SESSION_RECORD_DETAIL_TABLE_NAME="CC_MEDIA_CHAT_RECORD";//context.getProperty("SESSION_RECORD_DETAIL_TABLE_NAME", "");
	/**
	 * 全媒体会话记录质检记录字段名
	 */
	public static final String SESSION_RECORD_QC_FIELD="QC_RECORD_ID";//context.getProperty("SESSION_RECORD_QC_FIELD", "");

	/**
	 * 超过该时间间隔，则判断为静音，单位：秒
	 */
	public static final int MAX_NO_VOICE_SECONDS = CommonUtil.parseInt(context.getProperty("MAX_NO_VOICE_SECONDS", "5"));
	/**
	 * 超过该时间间隔，则判断为静音，单位：秒
	 */
	public static final int MAX_QH_VOIC_SECONDS = CommonUtil.parseInt(context.getProperty("MAX_QH_VOIC_SECONDS", "0"));
	/**
	 * 呼叫中心平台：genesys、petra、huawei
	 */
	public static final String CALL_CENTER_PLATEFORM = context.getProperty("CALL_CENTER_PLATEFORM", "genesys");
	public static final String CALL_CENTER_PLATEFORM_PETRA = "petra";
	public static final String CALL_CENTER_PLATEFORM_GENESYS = "genesys";
	public static final String CALL_CENTER_PLATEFORM_HUAWEI = "huawei";


	/**
	 * 文本转写平台：AMI
	 */
	public static final String CALL_ZX_PLATEFORM = context.getProperty("CALL_ZX_PLATEFORM", "AMI");
	public static final String CALL_ZX_PLATEFORM_AMI = "AMI";


	/**
	 * 质检月的开始日期，即每月的哪天作为质检月的第一天
	 */
	public static final int QC_DATE_OF_MONTH = CommonUtil.parseInt(context.getProperty("QC_DATE_OF_MONTH", "25"));

	/**
	 * 统计类型：01-按天 02-按月
	 */
	public static final String ST_TYPE_DAY = "01";
	public static final String ST_TYPE_MONTH = "02";
	
	/**
	 * 推送的自动质检 工单类型
	 */
	public static final String ORDER_TYPE_ORDER = "01";//接入单
	public static final String ORDER_TYPE_ZX_ORDER = "02";//咨询工单
	public static final String ORDER_TYPE_NOT_ORDER_CALL = "03";//无工单热线

	/**
	 * 自动质检规则：01-人工质检 02-自动质检
	 */
	public static final String QC_TYPE_AUTO = "01";//自动质检
	public static final String QC_TYPE_MANUAL = "02";//人工质检


	/**
	 * AMI的数据库表的Sechme
	 */
	public static final String AMI_DB_SECHME = ConfigUtil.getString(Constants.APP_NAME, "AMI_DB_SECHME","avcs");


	/**
	 * genesys录音获取服务器ip
	 */
	public static final String GENESYS_VOICE_INTERFACE_IP = ConfigUtil.getString(Constants.APP_NAME, "GENESYS_VOICE_INTERFACE_IP");
	/**
	 * genesys录音获取服务器端口
	 */
	public static final int GENESYS_VOICE_INTERFACE_PORT = CommonUtil.parseInt(ConfigUtil.getString(Constants.APP_NAME, "GENESYS_VOICE_INTERFACE_PORT","8080"));
	/**
	 * genesys录音获取服务器账号
	 */
	public static final String GENESYS_VOICE_INTERFACE_USER = ConfigUtil.getString(Constants.APP_NAME, "GENESYS_VOICE_INTERFACE_USER");
	/**
	 * genesys录音获取服务器密码
	 */
	public static final String GENESYS_VOICE_INTERFACE_PWD = ConfigUtil.getString(Constants.APP_NAME, "GENESYS_VOICE_INTERFACE_PWD");
	/**
	 * genesys录音获取服务器接口地址
	 */
	public static final String GENESYS_VOICE_INTERFACE_URL = ConfigUtil.getString(Constants.APP_NAME, "GENESYS_VOICE_INTERFACE_URL");
	/**
	 * 录音播放的前缀URL
	 */
	public static final String VOICE_HTTP_BASE_URL = ConfigUtil.getString(Constants.APP_NAME, "VOICE_HTTP_BASE_URL");
	/**
	 * 录音根目录
	 */
	public static final String VOICE_ROOT_PATH = ConfigUtil.getString(Constants.APP_NAME, "VOICE_ROOT_PATH");


	/**
	 * genesys录音获取服务器ip -合肥
	 */
	public static final String GENESYS_VOICE_INTERFACE_IP2 = ConfigUtil.getString(Constants.APP_NAME, "GENESYS_VOICE_INTERFACE_IP2");
	/**
	 * genesys录音获取服务器端口2
	 */
	public static final int GENESYS_VOICE_INTERFACE_PORT2 = CommonUtil.parseInt(ConfigUtil.getString(Constants.APP_NAME, "GENESYS_VOICE_INTERFACE_PORT2","8080"));
	/**
	 * genesys录音获取服务器账号
	 */
	public static final String GENESYS_VOICE_INTERFACE_USER2 = ConfigUtil.getString(Constants.APP_NAME, "GENESYS_VOICE_INTERFACE_USER2");
	/**
	 * genesys录音获取服务器密码
	 */
	public static final String GENESYS_VOICE_INTERFACE_PWD2 = ConfigUtil.getString(Constants.APP_NAME, "GENESYS_VOICE_INTERFACE_PWD2");
	/**
	 * genesys录音获取服务器接口地址
	 */
	public static final String GENESYS_VOICE_INTERFACE_URL2 = ConfigUtil.getString(Constants.APP_NAME, "GENESYS_VOICE_INTERFACE_URL2");
	/**
	 * 录音播放的前缀URL
	 */
	public static final String VOICE_HTTP_BASE_URL2 = ConfigUtil.getString(Constants.APP_NAME, "VOICE_HTTP_BASE_URL2");
	/**
	 * 录音根目录
	 */
	public static final String VOICE_ROOT_PATH2 = ConfigUtil.getString(Constants.APP_NAME, "VOICE_ROOT_PATH2");


	/**
	 * 满意度短信来源编号，由短信平台分配
	 */
	public static final String SATISFY_SMS_SOURCE = ConfigUtil.getString(Constants.APP_NAME, "SATISFY_SMS_SOURCE");;

	/**
	 * 满意度短信事业部标识，由短信平台分配
	 */
	public static final String SATISFY_SMS_MODEL_ID = ConfigUtil.getString(Constants.APP_NAME, "SATISFY_SMS_MODEL_ID");;

	/**
	 * 满意度短信品牌标识，由短信平台分配
	 */
	public static final String SATISFY_SMS_CATEGORY = ConfigUtil.getString(Constants.APP_NAME, "SATISFY_SMS_CATEGORY");

	/**
	 * AMI待转写录音存放目录
	 */
	public static final String AMI_VOICE_ROOT_PATH = ConfigUtil.getString(Constants.APP_NAME, "AMI_VOICE_ROOT_PATH");

	/**
	 * AMI 语音mp3文件转wav文件工具目录
	 */
	public static final String AMI_VOICE_CONVERSION_PATH = ConfigUtil.getString(Constants.APP_NAME, "AMI_VOICE_CONVERSION_PATH");

	/**
	 * 获取满意度的时限，超过该时间则不再获取满意度
	 */
	public static final int GET_STATSIFY_LIMIT = CommonUtil.parseInt(ConfigUtil.getString(Constants.APP_NAME, "GET_STATSIFY_LIMIT","30"));

	/**
	 * 获取x分钟之前的录音
	 */
	public static final int SYNC_VOICE_LIMIT_MINUTES = CommonUtil.parseInt(ConfigUtil.getString(Constants.APP_NAME, "SYNC_VOICE_LIMIT_MINUTES","5"));

	/**
	 * 全媒体质检延迟x分钟进行自动质检
	 */
	public static final int MEDIA_AUTOQC_DELAY_MINUTES = CommonUtil.parseInt(ConfigUtil.getString(Constants.APP_NAME, "MEDIA_AUTOQC_DELAY_MINUTES","60"));

	/**
	 * 是否同步语音转写文本
	 */
	public static final String SYNC_VOICE_ZX_CONTENT = ConfigUtil.getString(Constants.APP_NAME, "SYNC_VOICE_ZX_CONTENT","N");

	/**
	 * 是否上传录音文件获取转写文本
	 */
	public static final String SYNC_VOICE_ZX_BY_RECORDFILE = ConfigUtil.getString(Constants.APP_NAME, "SYNC_VOICE_ZX_BY_RECORDFILE","N");

	/**
	 * 只同步最近几天内的录音
	 */
	public static final int SYNC_VOICE_BEFORE_DAYS = CommonUtil.parseInt(ConfigUtil.getString(Constants.APP_NAME, "SYNC_VOICE_BEFORE_DAYS","3"));

	/**
	 * 获取文本转写结果的时限，超过该时限还获取不到结果，将直接跳过不再处理,单位为秒，默认为7200,2小时
	 */
	public static final int SYNC_VOICE_ZX_TIME_LIMIT = CommonUtil.parseInt(ConfigUtil.getString(Constants.APP_NAME, "SYNC_VOICE_ZX_TIME_LIMIT","7200"));

	/**
	 * 获取文本转写结果的线程数量,默认为 10
	 */
	public static final int SYNC_VOICE_ZX_THREAD_NUMS = CommonUtil.parseInt(ConfigUtil.getString(Constants.APP_NAME, "SYNC_VOICE_ZX_THREAD_NUMS","10"));

	/**
	 * 是否外联网
	 */
	public static final String EXTRANET = ConfigUtil.getString(Constants.APP_NAME, "EXTRANET","N");
	/**
	 * 全媒体统计数据有效天数
	 */
	public static final int MEDIA_VALID_DATE = CommonUtil.parseInt(ConfigUtil.getString(Constants.APP_NAME, "MEDIA_VALID_DATE","2"));

	public static final String VOICE_GET_URL_SD = ConfigUtil.getString(Constants.APP_NAME, "VOICE_GET_URL_SD","N");

	public static final String VOICE_GET_URL_HF = ConfigUtil.getString(Constants.APP_NAME, "VOICE_GET_URL_HF","N");
	public static final String VOICE_AGENT_SPACE = ConfigUtil.getString(Constants.APP_NAME, "VOICE_AGENT_SPACE","N");
	
	public static final String QC_URL = ConfigUtil.getString(Constants.APP_NAME, "QC_URL");
	
	public static final String CALL_BACK_URL = ConfigUtil.getString(Constants.APP_NAME, "CALL_BACK_URL","https://ccuat.midea.com/uinterface/Receive.do");

	/**
	 * 语音ASR未转写时，暂不推送智能质检的时长，单位：天
	 */
	public static final int MAX_KEEP_QC_DAYS = CommonUtil.parseInt(context.getProperty("MAX_KEEP_QC_DAYS", "3"));


}
