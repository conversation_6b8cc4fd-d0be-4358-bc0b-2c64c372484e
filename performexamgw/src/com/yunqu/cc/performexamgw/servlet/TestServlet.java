package com.yunqu.cc.performexamgw.servlet;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.performexamgw.base.AppBaseServlet;

@WebServlet({ "/test" })
public class TestServlet extends AppBaseServlet{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		doPost(req, resp);
	}
	
	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		String command = req.getParameter("command");
		try {
			if(ServiceCommand.PERFORMEXAM_STAT_BY_DAY.equals(command)){
				IService service = ServiceContext.getService(ServiceID.PERFORMEXAMGW_INTEFACE);
				JSONObject json = new JSONObject();
				json.put("command", ServiceCommand.PERFORMEXAM_STAT_BY_DAY);
				json.put("startTime", DateUtil.addDay(DateUtil.TIME_FORMAT, DateUtil.getCurrentDateStr(), -1));
				json.put("endTime", DateUtil.getCurrentDateStr());
				JSONObject j = service.invoke(json);
				System.out.println(j);
			}
			if(ServiceCommand.PERFORMEXAM_SYNC_ZX_CONTENT.equals(command)){
				IService service = ServiceContext.getService(ServiceID.PERFORMEXAMGW_INTEFACE);
				JSONObject json = new JSONObject();
				json.put("command", ServiceCommand.PERFORMEXAM_SYNC_ZX_CONTENT);
				JSONObject j = service.invoke(json);
				System.out.println(j);
			}
			
			if(ServiceCommand.PERFORMEXAM_GEN_QC_RECORD.equals(command)){
				IService service = ServiceContext.getService(ServiceID.PERFORMEXAMGW_INTEFACE);
				JSONObject json = new JSONObject();
				json.put("command", ServiceCommand.PERFORMEXAM_GEN_QC_RECORD);
				JSONObject j = service.invoke(json);
				System.out.println(j);
			}
			if(ServiceCommand.PERFORMEXAM_AUTO_QC.equals(command)){
				IService service = ServiceContext.getService(ServiceID.PERFORMEXAMGW_INTEFACE);
				JSONObject json = new JSONObject();
				json.put("command", ServiceCommand.PERFORMEXAM_AUTO_QC);
				JSONObject j = service.invoke(json);
				System.out.println(j);
			}
			
			if(ServiceCommand.PERFORMEXAM_SYNC_VOICE.equals(command)){
				IService service = ServiceContext.getService(ServiceID.PERFORMEXAMGW_INTEFACE);
				JSONObject json = new JSONObject();
				json.put("command", ServiceCommand.PERFORMEXAM_SYNC_VOICE);
				JSONObject j = service.invoke(json);
				System.out.println(j);
			}
			
			if(ServiceCommand.PERFORMEXAM_TAG_SESSION_RECORD.equals(command)){
				IService service = ServiceContext.getService(ServiceID.PERFORMEXAMGW_INTEFACE);
				JSONObject json = new JSONObject();
				json.put("command", ServiceCommand.PERFORMEXAM_TAG_SESSION_RECORD);
				JSONObject j = service.invoke(json);
				System.out.println(j);
			}
			
			if(ServiceCommand.PERFORMEXAM_SEND_SATISFY_SMS.equals(command)){
				IService service = ServiceContext.getService(ServiceID.PERFORMEXAMGW_INTEFACE);
				JSONObject json = new JSONObject();
				json.put("command", ServiceCommand.PERFORMEXAM_SEND_SATISFY_SMS);
				json.put("startTime", DateUtil.addDay(DateUtil.TIME_FORMAT, DateUtil.getCurrentDateStr(), -1));
				json.put("endTime", DateUtil.getCurrentDateStr());
				JSONObject j = service.invoke(json);
				System.out.println(j);
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}
}
