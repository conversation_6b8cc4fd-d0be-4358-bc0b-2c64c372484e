<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>模板列表</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchFormMolde" class="form-inline" id="searchFormMolde">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
             		     <div class="form-group">
			             	<h5><span class="glyphicon glyphicon-list"></span> 短信模板</h5>
			             	</div>
			             <div class="form-group">
							  <div class="input-group ">
									<span class="input-group-addon">模板分类</span>	
									<select class="form-control input-sm" data-mars="dataInfo.getList" name="moldeClass" id="moldeClass">
				                       		<option value="">-全部-</option>
			                      	</select>							 
			                      	 </div>
			             	<div class="input-group ">
									<span class="input-group-addon">模板类型</span>	
										<select class="form-control input-sm" data-mars="dataInfo.type" name="moldeType" id="moldeType">
				                       		<option value="">-全部-</option>
			                      	</select>
									  </div>
							 <div class="input-group">
								 <span class="input-group-addon">关键词</span>
								 <input type="text" class="form-control input-sm" name="keyword" id="keyword" placeholder="请输入关键词">
							 </div>
									  <div class="input-group ">
										   <button type="button" class="btn btn-sm btn-default" onclick="modelEdit.searchData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
									  </div>
								</div>
							</div>
							 <div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed"  data-mars="ChannelMessage.changeMessage" data-container="#dataList" data-template="list-template" id="tableHead">
	                             <thead>
		                         	 <tr>
		                         	 	  <th>模板名称</th>
									      <th>模板类型</th>
										  <th>是否启用短链接</th> <!-- 新增列 -->
									      <th>启用状态</th>
									      <th>创建人</th>
									      <th>创建时间</th>								
			   						 </tr>
	                             </thead>
	                            <tbody id="dataList">
                             
                             </tbody>
		                 </table>
                       	 <script id="list-template" type="text/x-jsrender">
								   {{for  list}}
										<tr>
											<tr onclick="modelEdit.changeContext('{{:ID}}')"> 
											<td>{{:NAME}}</td>   
											<td>{{type:TYPE}}</td>
											<td>{{rest:ENABLE_SHORT_LINK}}</td> <!-- 新增列，使用 rest 转换器 -->
            								<td>{{dictFUN:STATUS "SMS_CHANNEL_STATUS"}}</td>
											<td>{{:CREATE_USER_NAME}}</td>                                                                                            
											<td>{{:CREATE_TIME}}</td>                                                                                            
									    </tr>
								    {{/for}}					         
						</script>
		                     <div class="row paginate" id="page">
	                     	<jsp:include page="/pages/common/pagination.jsp"/>
		                     </div> 
					   </div>
				 </div>
	</form>				
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	jQuery.namespace("modelEdit");
	$(function(){
		$("#searchFormMolde").render();

	});
	modelEdit.searchData=function(){
		$("#searchFormMolde").searchData();
	}
	
	 $.views.converters("rest", function(val) {
			if(val==1){
				return '是';
			}else {
				return '否';
			}
			return first-last;
	});
	$.views.converters("type", function(val) {
		var status = ${type};
		if(typeof(status.data) === "undefined"){
			return val;
		} else {
			return status.data[val];
		}
	});
	modelEdit.changeContext=function(tId){
		var data = {};
		data.id = tId;
		ajax.remoteCall("/SMS/servlet/chMessage?query=ActOne", data, function(result) {
			if (result.state == 1) {
				var ret = result.data;
				var e=ret.CONTENT;
				$("#contents").val(e);

				// 处理短链内容
				var enableShortLink = ret.ENABLE_SHORT_LINK;
				var shortLinkContent = ret.SHORT_LINK_CONTENT || "";

				if (enableShortLink === "1") {
            $("#shortLinkRow").show();
            $("#shortLinkContent").attr("data-rules", "required");
            $("#shortLinkContent").prop("required", true);
            $("#shortLinkContent").val(shortLinkContent);
            $('#shortLinkCount').text(shortLinkContent.length);
        } else {
            $("#shortLinkRow").hide();
            $("#shortLinkContent").removeAttr("data-rules");
            $("#shortLinkContent").prop("required", false);
            $("#shortLinkContent").val("");
            $('#shortLinkCount').text("0");
        }

				layer.closeAll();

			} else {
				layer.alert(result.msg, {icon : 5});

			}
		});
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>