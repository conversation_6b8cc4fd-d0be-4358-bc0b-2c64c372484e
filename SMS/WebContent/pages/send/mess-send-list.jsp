<!DOCTYPE html>
<html lang="zh-CN" class="no-js">
  <head>
    <%@ page language="java" contentType="text/html;charset=UTF-8"%>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="renderer" content="webkit" />
    <link rel="icon" type="image/x-icon" href="favicon.ico" />
    <meta
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
      name="viewport"
    />
    <meta content="IE=EmulateIE8" http-equiv="X-UA-Compatible" />
    <meta content="yes" name="apple-mobile-web-app-capable" />
    <meta content="black" name="apple-mobile-web-app-status-bar-style" />
    <meta name="robots" content="index,follow" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />
    <link
      href="/easitline-static/lib/bootstrap/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link href="/easitline-static/css/easitline.ui.css" rel="stylesheet" />
    <script
      type="text/javascript"
      src="/easitline-static/js/jquery.min.js"
    ></script>
    <title>组织架构</title>
    <style type="text/css">
      <style > .right-menu {
        position: absolute;
        left: 0px;
        top: 0px;
        z-index: 100;
        display: none;
        width: 160px;
        padding: 5px 0;
        margin: 2px 0 0;
        font-size: 14px;
        text-align: left;
        list-style: none;
        background-color: #fff;
        -webkit-background-clip: padding-box;
        background-clip: padding-box;
        border: 1px solid #ccc;
        border-radius: 4px;
        -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
      }
      .right-menu li a {
        display: block;
        padding: 3px 20px;
        font-weight: 400;
        line-height: 1.42857143;
        color: #333;
        text-decoration: none;
        white-space: nowrap;
      }
      .right-menu li a:focus,
      .right-menu li a:hover {
        color: #262626;
        text-decoration: none;
        background-color: #f5f5f5;
      }
    </style>
  </head>
  <body class="gray-bg">
    <form action="" id="searchForm" name="searchForm" class="form-inline">
      <input type="hidden" name="deptId" id="deptId" />
      <div class="row">
        <div
          style="
            background-color: #fff;
            margin-left: 15px;
            width: 24%;
            float: left;
            height: 100%;
          "
        >
          <div
            style="
              height: 52px;
              line-height: 52px;
              padding: 0px 15px;
              border-bottom: 1px solid #eee;
            "
          >
            <span class="glyphicon glyphicon-home"></span>
            群组人员
          </div>
          <div
            style="
              position: relative;
              overflow: hidden;
              width: auto;
              height: 100%;
            "
            id="ztree"
            class="ztree mb-65 ibox-content"
            data-setting="{
						check:{enable:true},callback:{onCheck:zTreeOnClick},
						async: { enable: true, url:'${ctxPath}/SMS/tree',
						autoParam:['id', 'name']},
						}"
            data-mars="sendMassage.getFrameworkTree"
          ></div>
        </div>
        <div style="height: 450px; width: 72%; float: left; margin-left: 15px">
          <div class="ibox">
            <div class="ibox-title clearfix">
              <div class="form-group">
                <h5>
                  <span class="glyphicon glyphicon-list"></span>
                  发送短信内容
                </h5>
              </div>
            </div>
            <div class="ibox-content">
              <table class="table table-edit table-vzebra">
                <tbody>
                  <tr>
                    <td class="required">所属事业部</td>
                    <td style="width: 30px">
                      <select
                        data-rules="required"
                        class="form-control input-sm"
                        style="width: 160px"
                        data-mars="dataInfo.career"
                        name="career"
                      >
                        <option value="">请选择</option>
                      </select>
                    </td>

                    <td class="required">品牌</td>
                    <td>
                      <select
                        data-rules="required"
                        class="form-control input-sm"
                        style="width: 160px"
                        data-mars="dataInfo.brand"
                        name="brand"
                        id="brand"
                      >
                        <option value="">请选择</option>
                      </select>
                    </td>
                  </tr>
                  <tr>
                    <!-- 		<td class="required">渠道类型</td>
										<td ><select data-rules="required" class="form-control input-sm" style="width:160px"  data-mars="dataInfo.channel" name="channel">
											<option value="">请选择</option>
										</select></td> -->

                    <td colspan="1">
                      <input
                        id="check"
                        type="checkbox"
                        value="1"
                        name="saleFlag"
                        onclick="selectTy()"
                      />
                      <span>发送时间</span>
                    </td>
                    <td>
                      <input
                        type="text"
                        name="sendDate"
                        id="sendDate"
                        class="form-control input-sm Wdate"
                        onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})"
                        style="display: none; width: 190px"
                      />
                    </td>
                  </tr>
                  <tr>
                    <td>所选收件人</td>
                    <td colspan="5">
                      <textarea
                        name="changUser"
                        id="changUsers"
                        disabled="disabled"
                        class="form-control input-sm"
                        rows="2"
                        style="width: 640px"
                      ></textarea>
                    </td>
                  </tr>
                  <tr>
                    <td>输入收件人号码</td>
                    <td colspan="5">
                      <textarea
                        name="importUser"
                        id="importUser"
                        class="form-control input-sm"
                        rows="2"
                        placeholder="发送多个号码,需要用英文分号( ; )分隔"
                        style="width: 640px"
                      ></textarea>
                    </td>
                  </tr>
                  <tr>
                    <td>批量上传收件人</td>
                    <td>
                      <input
                        class=""
                        type="file"
                        id="file"
                        name="file"
                        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                      />
                      <!-- <button class="btn btn-xs btn-info" type="button"
											onclick="$('#file').click()">选择文件</button> -->
                      <a
                        class="btn btn-sm btn-link"
                        href="javascript:void(0)"
                        onclick="Framework.download()"
                        target="_blank"
                      >
                        下载导入模板
                      </a>
                    </td>
                  </tr>
                  <tr>
                    <td>短信内容</td>
                    <td colspan="5">
                      <textarea
                        name="content"
                        id="contents"
                        class="form-control input-sm"
                        rows="9"
                        style="width: 640px"
                        maxlength="500"
                        onchange="this.value=this.value.substring(0, 500)"
                        onkeydown="this.value=this.value.substring(0,500)"
                        onkeyup="this.value=this.value.substring(0, 500)"
                      ></textarea>
                      <button
                        type="button"
                        class="btn btn-sm btn-default"
                        onclick="Framework.changeMolde()"
                      >
                        <span class="glyphicon glyphicon-search"></span>
                        选择模板
                      </button>
                    </td>
                  </tr>
                  <tr id="shortLinkRow">
                    <td>短链内容(h5)</td>
                    <td colspan="5">
                      <div id="shortLinkArea" style="margin-top: 10px">
                        <textarea
                          name="shortLinkContent"
                          id="shortLinkContent"
                          class="form-control input-sm"
                          rows="9"
                          style="width: 640px"
                          placeholder="请输入短链接内容"
                          maxlength="200"
                          data-rules="required"
                          <%--readonly--%>
                        ></textarea>
                      </div>
                      <!-- 添加说明文字 -->
                      <div style="margin-top: 5px; font-size: 12px; color: #999;">
                        最多200字
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="input-group input-group-sm" style="margin-left: 400px">
            <button
              type="button"
              class="btn btn-sm btn-success"
              onclick="Framework.ajaxSubmitForm()"
            >
              <span class="glyphicon glyphicon-ok"></span>
              发送
            </button>
          </div>
        </div>
      </div>
    </form>

    <script
      type="text/javascript"
      src="/easitline-static/lib/My97DatePicker/WdatePicker.js"
    ></script>

    <script
      type="text/javascript"
      src="/easitline-static/lib/bootstrap/js/bootstrap.min.js"
    ></script>
    <script
      type="text/javascript"
      src="/easitline-static/js/jsrender.min.js"
    ></script>
    <script
      type="text/javascript"
      src="/easitline-static/lib/layer/layer.js"
    ></script>
    <script
      type="text/javascript"
      src="/easitline-static/js/pubfunction.js"
    ></script>
    <script
      type="text/javascript"
      src="/easitline-static/js/requreLib.js"
    ></script>
    <script type="text/javascript">
      jQuery.namespace('Framework')
      $(function () {
        // 如果元素存在，才绑定事件
        if ($('#shortLinkContent').length > 0) {
          $('#shortLinkContent').on('input', function () {
            var len = $(this).val().length
            $('#shortLinkCount').text(len)
          })
        }

        // 如果元素存在，才隐藏
        if ($('#shortLinkRow').length > 0) {
          $('#shortLinkRow').hide()
        }
        setIntervalDemo()
        new Render({
          el: '#searchForm'
        })
      })
      var zTree, rMenu
      requreLib.setplugs('slimscroll,ztree', function () {
        $('#ztree').slimScroll({
          height: '550px',
          color: '#ddd'
        })
      })

      function zTreeOnClick(event, treeId, treeNode) {
        var treeObj = $.fn.zTree.getZTreeObj('ztree'),
          nodes = treeObj.getCheckedNodes(true),
          v = ''
        id = ''
        for (var i = 0; i < nodes.length; i++) {
          if ('1' == nodes[i].type) {
            v += nodes[i].name + ';'
            id += nodes[i].id + ';'
          }
        }
        $('#changUsers').html(v.length > 0 ? v.substring(0, v.length - 1) : '')
        $('#deptId').val(id)
      }
      Framework.ajaxSubmitForm = function () {
        // 保证短链区域隐藏时，短链内容不参与校验
        if (!$('#shortLinkRow').is(':visible')) {
          $('#shortLinkContent').removeAttr('data-rules')
          $('#shortLinkContent').prop('required', false)
        }
        if (form.validate('searchForm')) {
          Framework.sendMessage()
        }
      }
      Framework.sendMessage = function () {
        var time = $('#sendDate').val()
        var times = getTime()
        if (time <= times && '' != time && null != time) {
          layer.msg('发送时间不能小于当前时间', {
            icon: 2
          })
          return false
        }
        var files = $('#file').val()
        var data = form.getJSONObject('searchForm')
        var ago = $('#changUsers').val()
        var ago1 = $('#importUser').val()
        var ago2 = $('#contents').val()
        if ('' == files || undefined == files) {
          if (ago == '') {
            if (ago1 == '') {
              layer.msg('电话号码为空', {
                icon: 2
              })
              return false
            }
            var lisIph = ago1.split(';')
            for (var j = 0, len = lisIph.length; j < len; j++) {
              var myreg = /^[1][0-9]{10}$/
              if (!myreg.test(lisIph[j])) {
                layer.msg('号码格式不正确', {
                  icon: 2
                })
                return false
              }
            }
          }

          if (ago2 == '') {
            layer.msg('发送内容为空', {
              icon: 2
            })
            return false
          }

          // 短链内容只在显示状态下才校验必填项
          if ($('#shortLinkRow').is(':visible')) {
            if ($('#shortLinkContent').val().trim() === '') {
              layer.msg('短链内容不能为空', { icon: 2 })
              return false
            }
          }

          ajax.remoteCall(
            '/SMS/servlet/sendMeg?action=send',
            data,
            function (result) {
              if (result.state == 1) {
                layer.msg(
                  result.msg,
                  {
                    icon: 1
                  },
                  function () {
                    window.location.reload()
                  }
                )
              } else {
                layer.alert(
                  result.msg,
                  {
                    icon: 5
                  },
                  function () {
                    layer.closeAll()
                  }
                )
              }
            }
          )
        } else {
          if (ago != '' || ago1 != '') {
            if (ago2 == '') {
              layer.msg('发送内容为空', {
                icon: 2
              })
              return false
            }
          }
          var last = JSON.stringify(data) //将JSON对象转化为JSON字符
          $('#searchForm').attr('enctype', 'multipart/form-data')
          var formData = new FormData($('#searchForm')[0])
          $.ajax({
            url:
              '/SMS/servlet/sendMeg?action=Upload&last=' +
              $('#searchForm').serialize(),
            type: 'POST',
            data: formData,
            async: true,
            cache: false,
            contentType: false,
            processData: false,
            success: function (result) {
              $('#searchForm').removeAttr('enctype')
              alert(result.msg)
              window.location.reload()
            }
          })
        }
      }
      Framework.changeMolde = function () {
        popup.layerShow(
          {
            type: 1,
            title: '选择模板',
            offset: '20px',
            area: ['660px', '520px']
          },
          '/SMS/servlet/Diction?action=Send',
          null
        )
      }
      Framework.download = function () {
        url = '/SMS/pages/send/userInfo.xlsx'
        window.open(url)
      }
      function getTime() {
        var date = new Date()
        var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
        var month =
          date.getMonth() + 1 > 9
            ? date.getMonth() + 1
            : '0' + (date.getMonth() + 1)
        var miao = date.getSeconds()
        var hour =
          date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
        var min =
          date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
        var miao =
          date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()

        var now =
          date.getFullYear() +
          '-' +
          month +
          '-' +
          day +
          ' ' +
          hour +
          ':' +
          min +
          ':' +
          miao
        return now
      }
      function selectTy() {
        var isCheck = $("input[type='checkbox']").is(':checked')
        if (isCheck) {
          $('#sendDate').show()
        } else {
          $('#sendDate').hide()
          $('#sendDate').val('')
        }
      }
      var setTimeoutName
      var i = 0
      function setIntervalDemo() {
        setTimeoutName = setInterval(function () {
          var count = $('#file').val()
          if ('' == count || null == count) {
            $('#contents').attr('disabled', false)
            $('#importUser').attr('disabled', false)
            $('#shortLinkContent').attr('disabled', false); // 添加这一行
          } else {
            $('#contents').attr('disabled', true)
            $('#importUser').attr('disabled', true)
            $('#shortLinkContent').attr('disabled', true); // 添加这一行
          }
        }, 1000)
      }
    </script>
  </body>
</html>
