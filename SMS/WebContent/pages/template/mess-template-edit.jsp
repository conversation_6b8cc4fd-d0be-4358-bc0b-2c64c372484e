<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@page import="com.yunqu.cc.sms.base.Constants" %>
<EasyTag:override name="head">
	<title>短信模板</title>
	<style>
		.red{
		color: red;
		}
		.hideSuper{
			display:none;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="easyform" data-mars="" style="margin-top: 10px;"  autocomplete="off" data-mars-prefix="">
			  <input type="hidden"  name="TYPE_ID" id="TYPE_ID" value="${param.tId}">
		  <table class="table table-edit table-vzebra">
                 <tbody>
                     <tr>
                         <td class="required" width="60px">模板编号</td>
                         <td width="220px"><input type="text" name="MSG_CODE" id="MSG_CODE" data-rules="required" class="form-control input-sm" ></td>
                     </tr>
                     <tr>
                         <td class="required">模板名称</td>
                         <td><input type="text" name="NAME"  id="NAME" data-rules="required" class="form-control input-sm"></td>
                     </tr>
                     <tr>
                         <td>是否公开</td>
                         <td>
                             <label class="radio-inline">
				                <input type="radio" value="Y"  name="IS_PUBLIC"> 是
				             </label>
				             <label class="radio-inline">
				                <input type="radio" value="N"   name="IS_PUBLIC" checked="checked" >  否
				             </label>
                         </td>
                     </tr>
                     <tr>
                         <td>启用状态</td>
                         <td>
                             <label class="radio-inline">
				                <input type="radio" value="1"  checked="checked" name="STATUS"> 启用
				             </label>
				             <label class="radio-inline">
				                <input type="radio" value="2"   name="STATUS"> 不启用
				             </label>
                         </td>
                     </tr>
                     <tr>
                         <td>支持发送超信</td>
                         <td>
                             <label class="radio-inline">
				                <input type="radio" value="1"   name="SEND_SUPER_SMS"> 启用
				             </label>
				             <label class="radio-inline">
				                <input type="radio" value="0"  checked="checked"  name="SEND_SUPER_SMS"> 不启用
				             </label>
                         </td>
                     </tr>
                     <tr class="superSms hideSuper">
                         <td class="required">超信模板[<a target="_blank" href="<%=Constants.SUPER_SMS_CONFIG_URL %>">配置</a>]</td>
                         <td>
                         <div id="superSmsTypeSelect"></div>
                         </td>
                     </tr>
                     <tr class="superSms hideSuper">
                         <td class="required">超信模板签名</td>
                         <td><select class="form-control input-sm" name="SUPER_SMS_SIGN" id="SUPER_SMS_SIGN"  data-mars="dataInfo.superSignList">
                         	<option value="">请选择</option>
                         
                         </select></td>
                     </tr>
                     <tr class="superSms hideSuper">
                         <td class="required">超信附加文本</td>
                         <td><input type="text" name="SUPER_SMS_CONTENT"  id="SUPER_SMS_CONTENT"  class="form-control input-sm"></td>
                     </tr>
                     <tr>
                         <td class="required">模板类型</td>
                         <td><select data-rules="required" class="form-control input-sm" name="TYPE" id="TYPE" data-mars="dataInfo.type">
                         	<option value="">请选择</option>
                         
                         </select></td>
                     </tr>
                     <tr>
                         <td class="required">普通文本短信内容</td>
                         <td>
                            <textarea class="form-control input-sm" data-rules="required" name="CONTENT" rows="6" id ="CONTENT"  maxlength="2000" onchange="this.value=this.value.substring(0, 2000)" onkeydown="this.value=this.value.substring(0, 2000)" onkeyup="this.value=this.value.substring(0, 2000)"></textarea>
                            <span class="red">* 小程序链接配置  {WEIXIN:XXX} 其中XXX为识别标识</span>		
                         </td>
                         
                     </tr>
                     <tr>
                         <td>是否启用短链</td>
                         <td>
                             <label class="radio-inline">
                                 <input type="radio" value="1" name="ENABLE_SHORT_LINK"> 是
                             </label>
                             <label class="radio-inline">
                                 <input type="radio" value="0" name="ENABLE_SHORT_LINK" checked="checked"> 否
                             </label>
                         </td>
                     </tr>
                     <tr id="shortLinkRow" style="display: none;">
                         <td class="required">短链内容</td>
                         <td>
                            <textarea class="form-control input-sm" name="SHORT_LINK_CONTENT" id="SHORT_LINK_CONTENT" rows="6" maxlength="200"
                                      onchange="this.value=this.value.substring(0, 200)"
                                      onkeydown="this.value=this.value.substring(0, 200)"
                                      onkeyup="this.value=this.value.substring(0, 200)"></textarea>
                             <span class="help-block">最多200字</span>
                         </td>
                     </tr>
                 </tbody>
	      </table>
		  <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" id="submit-form" onclick="modelEdit.ajaxSubmitForm()">保存</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="layer.closeAll();">关闭</button>
		  </div>
	</form>				
</EasyTag:override>
<script type="text/javascript" src="${ctxPath}/static/js/xm-select.js"></script>
<EasyTag:override name="script">
	<script type="text/javascript">
	jQuery.namespace("modelEdit");
	modelEdit.mdId ='${param.id}';
	
	//超信模板下拉框
	var superSmsTypeSelect = xmSelect.render({
		el: '#superSmsTypeSelect',
		size: 'small',
		name:'SUPER_SMS_TYPE',
		radio:true,
		clickClose:true,
		filterable: true,
		paging: true,
		pageSize: 10,
		pageRemote: true,
    	searchTips: '输入模板名称',
		remoteMethod:function(val, cb, show, pageIndex){
			ajax.remoteCall("${ctxPath}/webcall?action=superSms.templateList",{
	 			pageIndex:pageIndex,pageSize:100,tplName:val},function(result) {
	 			var superSmsTypeList = [];
	 			if(result.data&&result.data.length>0){
	 				var totalPage = 0;
						var json = result.data;
						for(var key in json){
							var imp = {
								name:json[key].tplName,
								value:json[key].tplId,
							}
							superSmsTypeList.push(imp);
						}
	 				if(result.pageInfo&&result.pageInfo.total){
	 					totalPage = Math.ceil(parseInt(result.pageInfo.total)/100);
	 				}
	 				cb(superSmsTypeList, totalPage);
				}else{
	  				cb([], 0);
	  			}
			})
		 },
		model: {
			label: {
				type: 'block',
				block: {
					//最大显示数量, 0:不限制
					showCount: 1,
					//是否显示删除图标
					showIcon: true,
				}
			}
		}
	})
	
	$(function(){
		$("#easyform").render();
		$('input[type=radio][name=SEND_SUPER_SMS]').change(function() {
	        if (this.value == '1') {
	           $(".superSms").removeClass("hideSuper");
	        }else if (this.value == '0') {
	           $(".superSms").addClass("hideSuper");
	        }
	    });
        // 初始化页面加载时检查是否启用短链
        toggleShortLinkRow();

        // 监听"是否启用短链"的单选按钮变化
        $('input[type=radio][name=ENABLE_SHORT_LINK]').change(function () {
            toggleShortLinkRow();
        });
	});
    // 控制 shortLinkRow 显示或隐藏的函数
    function toggleShortLinkRow() {
        var enableShortLink = $("input[name='ENABLE_SHORT_LINK']:checked").val();
        if (enableShortLink === '1') {
            $("#shortLinkRow").show(); // 显示短链内容
        } else {
            $("#shortLinkRow").hide(); // 隐藏短链内容
        }
    }
	modelEdit.ajaxSubmitForm = function(){
    var enableShortLink = $("input[name='ENABLE_SHORT_LINK']:checked").val(); // 补充这一行
    
		if(form.validate("easyform")){
			var ids = superSmsTypeSelect.getValue('valueStr');
			var isSuperSms = $("input[name='SEND_SUPER_SMS']:checked").val();
			var superSmsSign = $("#SUPER_SMS_SIGN").val();
			var superSmsContent = $("#SUPER_SMS_CONTENT").val();
			if(isSuperSms=='1'&&!ids){
				layer.alert("请选择超信模板",{icon: 5});
				return;
			}
			if(isSuperSms=='1'&&!superSmsSign){
				layer.alert("请选择超信签名",{icon: 5});
				return;
			}
			if(isSuperSms=='1'&&!superSmsContent){
				layer.alert("请选择超信附件文本",{icon: 5});
				return;
			}
            if (enableShortLink === '1' && !$("#SHORT_LINK_CONTENT").val().trim()) {
                layer.alert("请输入短链内容", {icon: 5});
                return;
            }

            if(modelEdit.mdId==''){
				modelEdit.insertData(); 
			}else{
				modelEdit.updateData(); 
			}
		};
	}
	modelEdit.insertData = function() {
		var data = form.getJSONObject("easyform");
		ajax.remoteCall("/SMS/servlet/chMessage?action=add",data,function(result) { 
			//debugger;
				if(result.state == 1){
					layer.closeAll();
					messTemp.searchData(); 
					}else{
					layer.alert(result.msg,{icon: 5});
				}
			}
		);
 }
	modelEdit.updateData = function(){
		 var data = form.getJSONObject("easyform");
			data.id=modelEdit.mdId;
			ajax.remoteCall("/SMS/servlet/chMessage?query=upMod",data,function(result) { 
				//debugger;
					if(result.state == 1){
						layer.closeAll();
						messTemp.searchData(); 
						}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
			);
	 }
	function getQuesData(tId) {
		var data = {};
		data.id = tId;
		ajax.remoteCall("/SMS/servlet/chMessage?query=Act", data, function(result) {
			//console.log(result.data);
			if (result.state == 1) {
				var ret = result.data;
				/* console.log(JSON.stringify(result)); */
				$("#MSG_CODE").val(ret.MSG_CODE);
				$("#NAME").val(ret.NAME);
				initradio('STATUS',ret.STATUS);
				initradio('IS_PUBLIC',ret.IS_PUBLIC);
				$("#CONTENT").html(ret.CONTENT);
				$("#TYPE option[value='" + ret.TYPE + "']").attr("selected", "selected");
                // ✅ 初始化短链内容
                $("#SHORT_LINK_CONTENT").val(ret.SHORT_LINK_CONTENT || '');
            } else {
				layer.alert(result.msg, {icon : 5});

			}
		});
	}
	 function initradio(rName,rValue){
         var rObj = document.getElementsByName(rName);

         for(var i = 0;i < rObj.length;i++){
             if(rObj[i].value == rValue){
                 rObj[i].checked =  'checked';
             }
         }
     }
	 
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>