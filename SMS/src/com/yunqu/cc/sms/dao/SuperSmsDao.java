package com.yunqu.cc.sms.dao;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.sms.base.AppDaoContext;
import com.yunqu.cc.sms.log.CommLogger;
import com.yunqu.cc.sms.utils.ResultFormatUtil;

@WebObject(name="superSms")
public class SuperSmsDao extends AppDaoContext{
	
  private Logger logger = CommLogger.logger;

  @WebControl(name="templateList",type=Types.LIST)
	public JSONObject templateList(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		String pageIndex = this.param.getString("pageIndex");
		String pageSize = this.param.getString("pageSize");
		String tplName = this.param.getString("tplName");
		String tplId = this.param.getString("tplId");
		if(StringUtils.isBlank(pageIndex)) {
			pageIndex = "1";
		}
		if(StringUtils.isBlank(pageSize)) {
			pageSize = "100";
		}
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command","listECTemplates");
		param.put("page",pageIndex);
		param.put("size",pageSize);
		param.put("tplName",tplName);
		param.put("tplId",tplId);
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService("MIXGW_AIM_INTEFACE");
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return ResultFormatUtil.getData(result);
	}
}
