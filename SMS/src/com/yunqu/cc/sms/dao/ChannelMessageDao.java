package com.yunqu.cc.sms.dao;


import com.yq.busi.common.model.UserModel;
import com.yunqu.openapi.utils.OpenApiUserUtil;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.sms.base.AppDaoContext;
import com.yunqu.cc.sms.log.CommLogger;
import com.yunqu.cc.sms.utils.StringUtil;

import java.util.HashSet;
import java.util.Set;
import org.apache.log4j.Logger;
import com.yq.busi.common.dict.DictCache;


@WebObject(name="ChannelMessage")
public class ChannelMessageDao extends AppDaoContext {

	private static Logger logger = CommLogger.logger;

	@WebControl(name="getChannelMessage",type=Types.LIST)
	public  JSONObject channelMessage(){
		EasySQL sql = this.getEasySQL("select  t.* ");
		sql.append("from C_SMS_CHANNEL t where 1=1 ");
		sql.appendLike(this.param.getString("NAME")," and t.NAME like ?");
		sql.append(this.param.getString("TYPE")," and t.TYPE=?");
		sql.append(this.param.getString("MODEL_ID")," and t.MODEL_ID=?");
		sql.append(this.param.getString("CATEGORY")," and t.CATEGORY=?");
		sql.append(this.param.getString("STATUS")," and t.STATUS=?");
		sql.append(" order by t.CREATE_TIME");
		CommLogger.logger.debug("获取短信渠道内容,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		JSONObject obj=this.queryForPageList(sql.getSQL(), sql.getParams(),null);
		return  obj;
	}
	
	@WebControl(name="ChannelMegInfo",type=Types.RECORD)
	public  JSONObject channelMegInfo(){
		EasySQL sql = this.getEasySQL("select * ");
		sql.append("from C_SMS_CHANNEL   where 1=1");
		sql.append(this.param.getString("id"),"and ID=?");
		CommLogger.logger.debug("获取一条短信渠道内容,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		JSONObject obj=this.queryForRecord(sql.getSQL(), sql.getParams(),null);
		StringUtil.addPrefix(obj, "chaMeg");
		return  obj;
	}
	@WebControl(name="messageModel",type=Types.LIST)
	public  JSONObject messageModel(){
		EasySQL sql = this.getEasySQL("select * from C_SMS_TEMPLATE where 1=1 ");
		sql.appendLike(this.param.getString("NAME"),"and NAME like ?");
		sql.append(this.param.getString("PARENT_ID"),"and TYPE=?");
		sql.append(this.param.getString("deptId"),"and TYPE_ID=?");
		sql.append(this.param.getString("TYPE"),"and TYPE_ID=?");
		sql.append(this.param.getString("status"),"and STATUS=?");
		sql.append(this.param.getString("isPublic"),"and IS_PUBLIC=?");
		sql.appendLike(this.param.getString("USER"),"and CREATE_USER_NAME like ?");
		sql.append(this.param.getString("beginTime"),"and CREATE_TIME>=?");
		sql.append(this.param.getString("endTime"),"and CREATE_TIME<=?");
		sql.append("ORDER BY CREATE_TIME");
		CommLogger.logger.debug("获取短信模板,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		JSONObject obj=this.queryForPageList(sql.getSQL(), sql.getParams(),null);
		return  obj;
	}
	@WebControl(name="changeMessage",type=Types.LIST)
	public JSONObject changeMessage(){
		CommLogger.logger.info("获取短信模板内容,请求参数: " + this.param.toString());
		UserModel userModel = OpenApiUserUtil.getUser(this.request);
        String userAccount= userModel.getUserAcc();//登陆账号
		String keyword = this.param.getString("keyword");
		// 获取匹配的类型ID和分类ID
		String matchedIds = "";
		if(StringUtils.isNotEmpty(keyword)) {
			try {
				// 直接获取类型数据
				String depCode = userModel.getEpCode();
				JSONObject typeData = DictCache.getJsonEnableDictListByGroupCode(depCode, "SMS_TYPE");
				CommLogger.logger.info("获取模版类型数据: " + (typeData != null ? typeData.toString() : "null"));

//				// 直接获取分类数据
//				JSONObject listData = this.getDictByQuery(
//					"select ID, NAME from C_SMS_TEMPLATE_TYPE where 1=1 ORDER BY create_time2",
//					new Object[]{}
//				);
//				CommLogger.logger.info("获取模版分类数据: " + (listData != null ? listData.toString() : "null"));

				// 存储匹配的TYPE和TYPE_ID
				Set<String> matchedTypes = new HashSet<>();
				Set<String> matchedTypeIds = new HashSet<>();

				// 搜索模板类型
				if(typeData != null && typeData.containsKey("data")) {
					JSONObject types = typeData.getJSONObject("data");
					if(types != null) {
						for(String key : types.keySet()) {
							String value = types.getString(key);
							if(value != null && value.contains(keyword)) {
								matchedTypes.add(key);
								CommLogger.logger.info("匹配到类型: " + key);
							}
						}
					}
				} else {
					CommLogger.logger.info("类型数据格式不正确或为空");
				}

				// 搜索模板分类
//				if(listData != null && listData.containsKey("data")) {
//					JSONObject lists = listData.getJSONObject("data");
//					if(lists != null) {
//						for(String key : lists.keySet()) {
//							String value = lists.getString(key);
//							if(value != null && value.contains(keyword)) {
//								matchedTypeIds.add(key);
//								CommLogger.logger.info("匹配到分类: " + key);
//							}
//						}
//					}
//				} else {
//					CommLogger.logger.info("分类数据格式不正确或为空");
//				}

				// 构建SQL条件 模糊查询模版类型与模版名称
				StringBuilder condition = new StringBuilder();
				condition.append(" and (");
				condition.append(" NAME like '%").append(keyword).append("%'");
				if(!matchedTypes.isEmpty() || !matchedTypeIds.isEmpty()) {
					condition.append(" or ");
					if(!matchedTypes.isEmpty()) {
						condition.append("TYPE in ('").append(String.join("','", matchedTypes)).append("')");
					}
//					if(!matchedTypeIds.isEmpty()) {
//						if(!matchedTypes.isEmpty()) {
//							condition.append(" or ");
//						}
//						condition.append("TYPE_ID in ('").append(String.join("','", matchedTypeIds)).append("')");
//					}
				}
				condition.append(")");
				matchedIds = condition.toString();

			} catch (Exception e) {
				CommLogger.logger.info("获取匹配的类型ID和分类ID失败", e);
				CommLogger.logger.info("获取匹配的类型ID和分类ID失败,mes:" + e.getMessage());
			}
		}
		CommLogger.logger.info("matchedIds=" + matchedIds);
		EasySQL sql = this.getEasySQL("select * from C_SMS_TEMPLATE where 1=1 ");
		sql.append(this.param.getString("moldeClass"),"and TYPE_ID= ?"); //模版分类
		sql.append(this.param.getString("moldeType"),"and TYPE=?"); //模版类型
		if(StringUtils.isNotEmpty(matchedIds)) {
			sql.append(matchedIds);
		}
		sql.append("and STATUS='1'");
		sql.append("and (CREATE_ACC='"+userAccount+"'  or IS_PUBLIC='Y')");
		sql.append("ORDER BY CREATE_TIME desc");

		CommLogger.logger.info("获取短信模板内容,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		JSONObject obj=this.queryForPageList(sql.getSQL(), sql.getParams(),null);
		return obj;
	}
	  @WebControl(name="getFrameworkTree",type=Types.OTHER)
		public JSONObject getFrameworkTree(){
		  EasySQL sql = this.getEasySQL("select ID  ,NAME ,PARENT_ID from C_SMS_TEMPLATE_TYPE where 1=1  ORDER BY NAME");
			CommLogger.logger.debug("获取模板类型树json,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
			JSONObject obj=this.queryForList(sql.getSQL(), sql.getParams(),null);
			//EasyRowMapper<T> ea=new EasyRowMapper<T>();
			return  obj;
	  }
	  @WebControl(name="getModelType",type=Types.RECORD)
		public  JSONObject getModelType(){
			EasySQL sql = this.getEasySQL("select name as PARNAME from C_SMS_TEMPLATE_TYPE where id=( ");
			sql.append("select PARENT_ID from C_SMS_TEMPLATE_TYPE where 1=1");
			sql.append(this.param.getString("id"),"and ID=?");
			sql.append(")");
			CommLogger.logger.debug("获取一条短信模板分类,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
			JSONObject obj=this.queryForRecord(sql.getSQL(), sql.getParams(),null);
			StringUtil.addPrefix(obj, "MODLE");
			return  obj;
			}
	  @WebControl(name="getModelUser",type=Types.RECORD)
	  public  JSONObject getModelUser(){
		  EasySQL sql = this.getEasySQL("SELECT GROUP_NAME FROM C_SMS_GROUP WHERE GROUP_ID=( ");
		  sql.append("SELECT PARENT_ID FROM C_SMS_GROUP WHERE 1=1 ");
		  sql.append(this.param.getString("GROUP_ID"),"and GROUP_ID=? ");
		  sql.append(")");
		  CommLogger.logger.debug("获取一条用户树信息,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		  JSONObject obj=this.queryForRecord(sql.getSQL(), sql.getParams(),null);
		  StringUtil.addPrefix(obj, "USER");
		  return  obj;
	  }
		}