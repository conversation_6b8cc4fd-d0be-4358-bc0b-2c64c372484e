package com.yunqu.cc.sms.servlet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;

import com.yunqu.openapi.utils.OpenApiUserUtil;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;
import org.omg.CORBA.INTERNAL;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictCache;
import com.yunqu.cc.sms.base.AppBaseServlet;
import com.yunqu.cc.sms.log.CommLogger;
import com.yunqu.cc.sms.utils.StringUtil;


@WebServlet(urlPatterns = {"/servlet/chMessage","/openServlet/chMessage"})
@MultipartConfig
public class ChMessageServlet extends AppBaseServlet{
	

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 新增短信渠道
	 * @return
	 */
	public EasyResult queryForAdd(){
		try {
			JSONObject jsonObject = this.getJSONObject();
			int i=getChannel(jsonObject);
			if(0!=i){
				return EasyResult.error(501, "该渠道已经存在");	
			}
			String CREATE_TIME = EasyCalendar.newInstance().getDateTime("-");
			String ID = RandomKit.smsAuthCode(16);
			
			String deptCode= OpenApiUserUtil.getUser(this.getRequest()).getDept().getDeptCode();//所属部门编号
			String userName=OpenApiUserUtil.getUser(this.getRequest()).getUserName();//创建人姓名
			String userAccount=OpenApiUserUtil.getUser(this.getRequest()).getUserAcc();//登陆账号
			String userNo=OpenApiUserUtil.getUser(this.getRequest()).getUserNo();//工号
			String EP_CODE=OpenApiUserUtil.getUser(this.getRequest()).getEpCode();//企业编号
			
			jsonObject.put("ID", ID);
			jsonObject.put("CREATE_USER_NAME", userName);
			jsonObject.put("CREATE_DEPT", deptCode);
			jsonObject.put("CREATE_TIME", CREATE_TIME);
			jsonObject.put("CREATE_ACC", userAccount);
			jsonObject.put("CREATE_NO", userNo);
			jsonObject.put("EP_CODE", EP_CODE);
			EasyRecord record = new EasyRecord("C_SMS_CHANNEL", "ID").setColumns(jsonObject);
			this.getQuery().save(record);
			return EasyResult.ok(record, "添加成功！");
		} catch (SQLException ex) {

			this.error("添加失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(501, "添加失败，原因：" + ex.getMessage());
		}
	}
	/**
	 * 更新短信渠道
	 * @return
	 */
	public EasyResult queryForUpdate(){
		try {
			String id = this.getJsonPara("id");
			JSONObject jsonObject = this.getJSONObject("chaMeg");			
			jsonObject.put("ID", id);
			int j=toCompare(jsonObject);
			if(0==j){
				int i=getChannel(jsonObject);
				if(0<i){
					return EasyResult.error(501, "该渠道已经存在");	
				}
			}
			EasyRecord record = new EasyRecord("C_SMS_CHANNEL", "ID").setColumns(jsonObject);
			this.getQuery().update(record);
			return EasyResult.ok(record, "更新成功！");
		} catch (SQLException ex) {
			this.error("更新失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(501, "更新失败，原因：" + ex.getMessage());
		}
	}
	/**
	 * 根据id删除短信渠道信息
	 * 
	 * @return
	 */
	public EasyResult queryForDele() {
		try {
			String id = this.getJsonPara("id");
			EasyRecord record = new EasyRecord("C_SMS_CHANNEL", "ID").setPrimaryValues(id);
			this.getQuery().deleteById(record);
			return EasyResult.ok(record);
		} catch (SQLException ex) {
			this.error("删除失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(500, "删除失败，原因：" + ex.getMessage());
		}
	}
	
	
	
	public String getMax(String nameType){
		String sql = "select max(order_no) from "+nameType+"";
		try {
			List<EasyRow> list =  this.getQuery().queryForList(sql, new Object[] { });
			if (list != null && list.size() > 0) {
				String num = list.get(0).getColumnValue("ORDER_NO");
				return num;
			}
		} catch (SQLException e) {
			CommLogger.logger.error("[SQLException]任务条数," + e.getMessage(), e);
			
		}
		return "0";
	
	}
	
	/**
	 *新增短信模板分类
	 * 
	 * @return
	 */
	public EasyResult queryForAddType() {
		try {
			JSONObject jsonObject = this.getJSONObject();
			String tId=jsonObject.getString("id");//父id			
			jsonObject.remove("id");
			String code=jsonObject.getString("CODE");
			int count=getCode(code,"C_SMS_TEMPLATE_TYPE");
			if(0!=count){
				return EasyResult.error(501, "类型编号重复" );
			}
			String CREATE_TIME = EasyCalendar.newInstance().getDateTime("-");
			String ID = RandomKit.smsAuthCode(16);			
			String deptCode=OpenApiUserUtil.getUser(this.getRequest()).getDept().getDeptCode();//所属部门编号
			String userName=OpenApiUserUtil.getUser(this.getRequest()).getUserName();//创建人姓名
			String userAccount=OpenApiUserUtil.getUser(this.getRequest()).getUserAcc();//登陆账号
			String userNo=OpenApiUserUtil.getUser(this.getRequest()).getUserNo();//工号
			String EP_CODE=OpenApiUserUtil.getUser(this.getRequest()).getEpCode();//企业编号
			String num=getMax("C_SMS_TEMPLATE_TYPE");
			int order_num=0;
			if(null==num||"".equals(num)){
				order_num=1;
			}else{
				order_num=StringUtil.InitiNum(num)+1;
			}
			if (null==tId||"".equals(tId)) {
				tId="0";
			}
			jsonObject.put("PARENT_ID", tId);
			jsonObject.put("ID", ID);
			jsonObject.put("CREATE_USER_NAME", userName);
			jsonObject.put("CREATE_DEPT", deptCode);
			jsonObject.put("CREATE_TIME2", CREATE_TIME);
			jsonObject.put("CREATE_ACC", userAccount);
			jsonObject.put("CREATE_NO", userNo);
			jsonObject.put("EP_CODE", EP_CODE);
			jsonObject.put("ORDER_NO", order_num);
			EasyRecord record = new EasyRecord("C_SMS_TEMPLATE_TYPE", "ID").setColumns(jsonObject);
			this.getQuery().save(record);
			return EasyResult.ok(record, "添加成功！");
		} catch (SQLException ex) {

			this.error("添加失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(501, "添加失败，原因：" + ex.getMessage());
		}
	}
	/**
	 * 更新短信模板分类
	 * @return
	 */
	public EasyResult queryForUpdateModel(){
		String CREATE_TIME = EasyCalendar.newInstance().getDateTime("-");
		String deptCode=OpenApiUserUtil.getUser(this.getRequest()).getDept().getDeptCode();//所属部门编号
		String userName=OpenApiUserUtil.getUser(this.getRequest()).getUserName();//创建人姓名
		String userAccount=OpenApiUserUtil.getUser(this.getRequest()).getUserAcc();//登陆账号
		String userNo=OpenApiUserUtil.getUser(this.getRequest()).getUserNo();//工号
		try {
			String id = this.getJsonPara("id");
			JSONObject jsonObject = this.getJSONObject("MODLE");
			String code=jsonObject.getString("CODE");
			String codeTw=getCodeById(id);
			if(!code.equals(codeTw)){
				int count=getCode(code,"C_SMS_TEMPLATE_TYPE");
				if(0!=count){
					return EasyResult.error(501, "类型编号重复" );
				}
			}
			jsonObject.put("UPDATE_TIME", CREATE_TIME);
			jsonObject.put("UPDATE_DEPT", deptCode);
			jsonObject.put("UPDATE_NAME", userName);
			jsonObject.put("UPDATE_ACC", userAccount);
			jsonObject.put("UPDATE_NO", userNo);
			jsonObject.put("ID", id);
			
			EasyRecord record = new EasyRecord("C_SMS_TEMPLATE_TYPE", "ID").setColumns(jsonObject);
			this.getQuery().update(record);
			return EasyResult.ok(record, "更新成功！");
		} catch (SQLException ex) {
			this.error("更新失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(501, "更新失败，原因：" + ex.getMessage());
		}
	}
	/**
	 * 新增短信模板
	 * @return
	 */
	public EasyResult actionForAdd(){
		try {
			JSONObject jsonObject = this.getJSONObject();
			String info=jsonObject.getString("TYPE_ID");
			if(null==info||"".equals(info)){
				CommLogger.logger.info("请选择一个模板类型进行添加");
				return EasyResult.error(501, "没有选择模板类型,请选择一个模板类型进行添加！");
			}
			String CREATE_TIME = EasyCalendar.newInstance().getDateTime("-");
			String ID = RandomKit.smsAuthCode(16);			
			String deptCode=OpenApiUserUtil.getUser(this.getRequest()).getDept().getDeptCode();//所属部门编号
			String userName=OpenApiUserUtil.getUser(this.getRequest()).getUserName();//创建人姓名
			String userAccount=OpenApiUserUtil.getUser(this.getRequest()).getUserAcc();//登陆账号
			String userNo=OpenApiUserUtil.getUser(this.getRequest()).getUserNo();//工号
			String EP_CODE=OpenApiUserUtil.getUser(this.getRequest()).getEpCode();//企业编号
			String num=getMax("C_SMS_TEMPLATE");
			int order_num=0;
			if(null==num||"".equals(num)){
				order_num=1;
			}else{
				order_num=StringUtil.InitiNum(num)+1;
			}
			jsonObject.put("ID", ID);
			jsonObject.put("ORDER_NO", order_num);
			jsonObject.put("CREATE_USER_NAME", userName);
			jsonObject.put("CREATE_DEPT", deptCode);
			jsonObject.put("CREATE_TIME", CREATE_TIME);
			jsonObject.put("CREATE_ACC", userAccount);
			jsonObject.put("CREATE_NO", userNo);
			jsonObject.put("EP_CODE", EP_CODE);
			EasyRecord record = new EasyRecord("C_SMS_TEMPLATE", "ID").setColumns(jsonObject);
			this.getQuery().save(record);
			return EasyResult.ok(record, "添加成功！");
		} catch (SQLException ex) {
			this.error("添加失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(501, "添加失败，原因：" + ex.getMessage());
		}
	}
	/**
	 * 获取一条短信模板信息
	 * @return
	 */
	public EasyResult queryForAct(){
		String id = this.getJsonPara("id");
		String sql = "select * from C_SMS_TEMPLATE WHERE id=?";
		JSONObject obj = new JSONObject();
		CommLogger.logger.debug("[获取一条短信模板信息]，sql="+sql.toString()+"");
		try {
			List<EasyRow> list = this.getQuery().queryForList(sql,new Object[]{id});
			if(list !=null && list.size()>0){
				obj.put("MSG_CODE", list.get(0).getColumnValue("MSG_CODE"));//模板编号
				obj.put("NAME", list.get(0).getColumnValue("NAME"));//模板名称
				obj.put("IS_PUBLIC", list.get(0).getColumnValue("IS_PUBLIC"));//是否公开
				obj.put("STATUS", list.get(0).getColumnValue("STATUS"));//启用状态
				obj.put("TYPE", list.get(0).getColumnValue("TYPE"));//模板类型
				obj.put("CONTENT", list.get(0).getColumnValue("CONTENT"));//短信内容
				obj.put("SEND_SUPER_SMS", list.get(0).getColumnValue("SEND_SUPER_SMS"));
				obj.put("SUPER_SMS_TYPE", list.get(0).getColumnValue("SUPER_SMS_TYPE"));
				obj.put("SUPER_SMS_SIGN", list.get(0).getColumnValue("SUPER_SMS_SIGN"));
				obj.put("SUPER_SMS_CONTENT", list.get(0).getColumnValue("SUPER_SMS_CONTENT"));
				obj.put("ENABLE_SHORT_LINK", list.get(0).getColumnValue("ENABLE_SHORT_LINK"));
				obj.put("SHORT_LINK_CONTENT", list.get(0).getColumnValue("SHORT_LINK_CONTENT"));
				return EasyResult.ok(obj, "获取成功!");
			}else{
				return EasyResult.ok("", "获取成功!");
			}
		} catch (SQLException e) {
			return EasyResult.error(501, "获取数据失败!，原因："+e.getMessage());
		}
	}
	/**
	 * 获取一条短信模板的内容
	 * @return
	 */
	public EasyResult queryForActOne(){
		String id = this.getJsonPara("id");
		String msgCode = this.getJsonPara("msgCode");
		if (StringUtils.isBlank(id) && StringUtils.isBlank(msgCode)) {
			return EasyResult.fail("参数错误，id和msgCode必传一个");
		}
		EasySQL sql = new EasySQL("select * from C_SMS_TEMPLATE WHERE 1=1 ");
		sql.append(id, " AND ID = ? ");
		sql.append(msgCode, " AND MSG_CODE = ? ");

		JSONObject obj = new JSONObject();
		CommLogger.logger.debug("[获取一条短信模板的内容]，sql="+sql.toString()+"");
		try {
			List<EasyRow> list = this.getQuery().queryForList(sql.getSQL(),sql.getParams());
			if(list !=null && list.size()>0){
				obj.put("CONTENT", list.get(0).getColumnValue("CONTENT"));//模板内容
				String enableShortLink = list.get(0).getColumnValue("ENABLE_SHORT_LINK");
				if (StringUtils.isBlank(enableShortLink) || "0".equals(enableShortLink)) {
					obj.put("ENABLE_SHORT_LINK", "0");
				} else {
					obj.put("ENABLE_SHORT_LINK", "1");
					obj.put("SHORT_LINK_CONTENT", list.get(0).getColumnValue("SHORT_LINK_CONTENT"));
				}
				return EasyResult.ok(obj, "获取成功!");
			}else{
				return EasyResult.ok("", "获取成功!");
			}
		} catch (SQLException e) {
			return EasyResult.error(501, "获取数据失败!，原因："+e.getMessage());
		}
	}
	/**
	 * 更新短信模板
	 * @return
	 */
	public EasyResult queryForUpMod(){
		try {
			JSONObject jsonObject = this.getJSONObject();
			jsonObject.remove("TYPE_ID");
			String CREATE_TIME = EasyCalendar.newInstance().getDateTime("-");
			String deptCode=OpenApiUserUtil.getUser(this.getRequest()).getDept().getDeptCode();//所属部门编号
			String userName=OpenApiUserUtil.getUser(this.getRequest()).getUserName();//创建人姓名
			String userAccount=OpenApiUserUtil.getUser(this.getRequest()).getUserAcc();//登陆账号
			String userNo=OpenApiUserUtil.getUser(this.getRequest()).getUserNo();//工号
			jsonObject.put("UPDATE_TIME", CREATE_TIME);
			jsonObject.put("UPDATE_ACC", userAccount);
			jsonObject.put("UPDATE_NO", userNo);
			jsonObject.put("UPDATE_NAME", userName);
			jsonObject.put("UPDATE_DEPT", deptCode);
			EasyRecord record = new EasyRecord("C_SMS_TEMPLATE", "ID").setColumns(jsonObject);
			this.getQuery().update(record);
			return EasyResult.ok(record, "更新成功！");
		} catch (SQLException ex) {
			this.error("更新失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(501, "更新失败，原因：" + ex.getMessage());
		}
	}
	/**
	 * 根据id删除短信模板
	 * 
	 * @return
	 */
	public EasyResult queryForDeMod() {
		try {
			String id = this.getJsonPara("id");
			EasyRecord record = new EasyRecord("C_SMS_TEMPLATE", "ID").setPrimaryValues(id);
			this.getQuery().deleteById(record);
			return EasyResult.ok(record);
		} catch (SQLException ex) {
			this.error("删除失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(500, "删除失败，原因：" + ex.getMessage());
		}
	}
	/**
	 * 根据id删除短信模板类型
	 * 
	 * @return
	 */
	public EasyResult queryForDeModType() {
		try {
			String id = this.getJsonPara("id");
			
			String sql="select * from C_SMS_TEMPLATE_TYPE where PARENT_ID=?";
			String sql1="select a.* from    C_SMS_TEMPLATE  a LEFT JOIN "
					+ " C_SMS_TEMPLATE_TYPE b on a.TYPE_ID=b.ID where b.id=?";
			List<EasyRow> list = this.getQuery().queryForList(sql,new Object[]{id});
			List<EasyRow> list1 = this.getQuery().queryForList(sql1,new Object[]{id});
			if(list !=null && list.size()>0){
				return EasyResult.error(501, "该模板类型下还存在模板类型,不能删除！");

			}
			if(list1 !=null && list1.size()>0){
				return EasyResult.error(501, "该模板下还存在短信模板,不能删除！");
				
			}
			EasyRecord record = new EasyRecord("C_SMS_TEMPLATE_TYPE", "ID").setPrimaryValues(id);
			this.getQuery().deleteById(record);
			return EasyResult.ok(record);
		} catch (SQLException ex) {
			this.error("删除失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(500, "删除失败，原因：" + ex.getMessage());
		}
	}
	/**
	 * 获取一条短信模板分类的内容
	 * @return
	 */
	public EasyResult queryForGetInfo(){
		String id = this.getJsonPara("id");
		String sql = "select * from C_SMS_TEMPLATE_TYPE WHERE id=?";
		JSONObject obj = new JSONObject();
		CommLogger.logger.debug("[获取一条短信模板分类的内容]，sql="+sql.toString()+"");
		try {
			List<EasyRow> list = this.getQuery().queryForList(sql,new Object[]{id});
			if(list !=null && list.size()>0){
				obj.put("BAKUP", list.get(0).getColumnValue("BAKUP"));//备注
				obj.put("STATUS", list.get(0).getColumnValue("STATUS"));//状态
				obj.put("IS_PUBLIC", list.get(0).getColumnValue("IS_PUBLIC"));//是否公开
				obj.put("CODE", list.get(0).getColumnValue("CODE"));//编号
				return EasyResult.ok(obj, "获取成功!");
			}else{
				return EasyResult.ok("", "获取成功!");
			}
		} catch (SQLException e) {
			return EasyResult.error(501, "获取数据失败!，原因："+e.getMessage());
		}
	}
	/**
	 * 查询渠道是否存在
	 * @return
	 */
	public int getChannel(JSONObject obj){
		String sql = "select * from C_SMS_CHANNEL WHERE MODEL_ID=? and CATEGORY=?";
		CommLogger.logger.debug("[查询渠道是否存在]，sql="+sql.toString()+"");
		try {
			List<EasyRow> list = this.getQuery().queryForList(sql,new Object[]{obj.getString("MODEL_ID"),obj.getString("CATEGORY")});
			return list.size();
		} catch (SQLException e) {
			this.error("获取失败，原因：" + e.getMessage(), e);
		}
		return 0;
	}
	/**
	 * 判断该渠道是否修改
	 * @return
	 */
	public int toCompare(JSONObject obj){
		String id = obj.getString("ID");
		String sql = "select * from C_SMS_CHANNEL WHERE id=?";
		JSONObject ob = new JSONObject();
		CommLogger.logger.debug("[判断该渠道是否修改]，sql="+sql.toString()+"");
		try {
			List<EasyRow> list = this.getQuery().queryForList(sql,new Object[]{id});
			if(list !=null && list.size()>0){
				ob.put("MODEL_ID", list.get(0).getColumnValue("MODEL_ID"));
				ob.put("CATEGORY", list.get(0).getColumnValue("CATEGORY"));
			}
			if(obj.getString("MODEL_ID").equals(ob.getString("MODEL_ID"))&&obj.getString("CATEGORY").equals(ob.getString("CATEGORY"))){
				return 1;
			}
		} catch (SQLException e) {
			this.error("获取失败，原因：" + e.getMessage(), e);
		}
		return 0;
	}
	/*
	 判断编号是否存在
	 * 
	 */
	public int getCode(String code,String str){
		try {
			List<EasyRow> list = this.getQuery().queryForList("select ID from "+str+" where  CODE=?",new Object[] {code});
			
			return list.size();
		} catch (SQLException e) {

			e.printStackTrace();
		}
		return 1;
	}
	/*
	 获取模板编号
	 * 
	 */
	public String getCodeById(String id){
		String code="";
		try {
			List<EasyRow> list = this.getQuery().queryForList("select CODE from C_SMS_TEMPLATE_TYPE where  ID =?",new Object[] {id});
			if(list !=null && list.size()>0){
			 code= list.get(0).getColumnValue("CODE");
			}
			return code;
		} catch (SQLException e) {
			
			e.printStackTrace();
		}
		return code;
	}
}
