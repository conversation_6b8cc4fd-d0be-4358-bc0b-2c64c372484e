package com.yunqu.cc.sms.servlet;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.Part;

import cn.hutool.core.text.UnicodeUtil;
import com.alibaba.fastjson.JSON;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yunqu.openapi.utils.OpenApiUserUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.excel.utils.Utils;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.sms.base.AppBaseServlet;
import com.yunqu.cc.sms.base.Constants;
import com.yunqu.cc.sms.log.CommLogger;
import sun.nio.cs.UnicodeEncoder;

@WebServlet(urlPatterns = {"/servlet/sendMeg", "/openServlet/sendMeg"})
@MultipartConfig
public class SendMegServlet  extends AppBaseServlet{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;


	/**
	 * 批量发送短信内容
	 * @return
	 */
	public EasyResult actionForUpload() {
		EasyQuery query = this.getQuery();
		//String userGroup = this.getRequest().getParameter("last");
		try {
			//JSONObject jsonObject1 = JSONObject.parseObject(userGroup);
			String content=getRequest().getParameter("content");			
			String userId=getRequest().getParameter("deptId");
			String manyUser=getRequest().getParameter("importUser");
			String deptCode= OpenApiUserUtil.getUser(this.getRequest()).getDept().getDeptCode();//所属部门编号
			String userName=OpenApiUserUtil.getUser(this.getRequest()).getUserName();//创建人姓名
			String userAccount=OpenApiUserUtil.getUser(this.getRequest()).getUserAcc();//登陆账号
			String userNo=OpenApiUserUtil.getUser(this.getRequest()).getUserNo();//工号
			String EP_CODE=OpenApiUserUtil.getUser(this.getRequest()).getEpCode();//企业编号
			String career=getRequest().getParameter("career");
			//String channel=jsonObject1.getString("channel");
			String brand=getRequest().getParameter("brand");
			String sendDate=getRequest().getParameter("sendDate");
			if("".equals(sendDate)||null==sendDate){
				sendDate = EasyCalendar.newInstance().getDateTime("-");
			}
			CommLogger.logger.info("发送短信"+userAccount);

			JSONObject map=getSendUrl(career,brand);//URL;账号;密码;渠道id
			if(map.size()<1||null==map){
				return EasyResult.error(501, "发送失败，原因：渠道不存在");
			}else if("2".equals(map.get("STATUS"))){
				return EasyResult.error(501, "发送失败，原因：渠道已被禁用");
			}
			JSONObject json =new JSONObject();
			String CREATE_TIME = EasyCalendar.newInstance().getDateTime("-");
			String ID = RandomKit.smsAuthCode(16);
			json.put("CREATE_USER_NAME", userName);
			json.put("CREATE_DEPT", deptCode);
			json.put("CREATE_TIME", CREATE_TIME);
			json.put("CREATE_ACC", userAccount);
			json.put("CREATE_NO", userNo);
			json.put("EP_CODE", EP_CODE);
			json.put("ID", ID);
			json.put("TYPE", "2");
			json.put("SOURCE",  Constants.SEND_TYEP);
			json.put("SEND_TIME", sendDate);
			json.put("CHANNEL_ID",map.get("ID"));
			json.put("STATUS","1");
			json.put("NEED_SMS_RECEIPT","1");
			List<String> com = new ArrayList<>();
			EasyRecord record = new EasyRecord("C_SMS_INFO", "ID").setColumns(json);
			query.save(record);

			Part part = getFile("file");
			Workbook workbook = WorkbookFactory.create(part.getInputStream());
			List<List<String>> list = new ArrayList<>();
			Sheet sheet = workbook.getSheetAt(0);
			int maxLine = sheet.getLastRowNum();
			int lastCellNum = 0;
			for (int ii = 0; ii <= maxLine; ii++) {
				List<String> rows = new ArrayList<>();
				Row row = sheet.getRow(ii);
				if (ii == 0) {
					lastCellNum = row.getLastCellNum();
				}
				if (row != null) {
					for (int j = 0; j < lastCellNum; j++) {
						Cell cell = row.getCell(j);
						if(cell!=null){
							String val = Utils.getCellValue(cell);
							if (StringUtils.isBlank(val)) {
								rows.add("");
							} else {
	            				rows.add(val);
							}
						}else{
							rows.add("");
						}
					}
					list.add(rows);
				}
			}
			CommLogger.logger.info("发送短信表格数"+list.size());

			for (int i = 1; i < list.size(); i++) {
						Map<Object, Object> m = new HashMap<Object, Object>();
						for (int j = 0; j < list.get(0).size(); j++) {
							switch (j) {
							case 0:
								m.put("PHONE", list.get(i).get(j));
								break;
							case 1:
								m.put("COUNT", list.get(i).get(j));
								break;
							case 2:
								m.put("shortLinkContent", list.get(i).get(j));
								break;
							default:
								break;
							}
						}
						String str = JSONObject.toJSON(m).toString();
						JSONObject jsonObject = JSONObject.parseObject(str);
						String count1=jsonObject.getString("COUNT");
						String phone=jsonObject.getString("PHONE");
						String shortLinkContent = jsonObject.getString("shortLinkContent");
						Pattern p = Pattern.compile("^[1][0-9]{10}$");
						Matcher md = p.matcher(phone);
						if(StringUtils.isBlank(count1)&&StringUtils.isBlank(phone)){
							break;
						}
						// 短链文本不为空，则将短链拼接进短信内容中
						if (StringUtils.isNotBlank(shortLinkContent)) {
							CommLogger.logger.info("shoreLinkContent=" + shortLinkContent);
							shortLinkContent = splitShorLinkContent(shortLinkContent);
							CommLogger.logger.info("[params]:" + shortLinkContent);
							// 生成原始长短链
							String shortLinkUrl = Constants.getShortLinkUrl() + "?data=" + Base64.getUrlEncoder().encodeToString(shortLinkContent.getBytes(StandardCharsets.UTF_8));
							CommLogger.logger.info("shortLinkUrl1=" + shortLinkUrl);
							// 长链接转换为短链
							shortLinkUrl = getShortUrl(shortLinkUrl);
							CommLogger.logger.info("shortLinkUrl2=" + shortLinkUrl);
							// 拼接短信内容
							count1 = count1 + shortLinkUrl;
							CommLogger.logger.info("content=" + count1);
						}
						if(!md.matches()){
							com.add(phone);
							continue;
						}
						//写表
						if(!"".equals(count1)&&null!=count1){
							int index=sendMesg(phone,ID,map.getString("ID"),count1,sendDate,query,"2");
							CommLogger.logger.info("批量发送短信");
							if(index==0){
								com.add(phone);
								continue;
								//return EasyResult.error(501, "发送失败");	
							}
						}else{
							com.add(phone);
						}
				
			}
	
			String str=getStr(userId);
			List<String> list1=getSendPhone(str);
			if (list1 != null && list1.size() > 0) {
				for (String string : list1) {				
					int index=sendMesg(string,ID,map.getString("ID"),content,sendDate,query);//写表
					if(index==0){
						com.add(string);
						continue;
						//return EasyResult.error(501, "发送失败");	
					}
				}
			}

			if(!"".equals(manyUser)&&null!=manyUser){
				String [] ars=manyUser.split(";");
				for (String string : ars) {
					int index=sendMesg(string,ID,map.getString("ID"),content,sendDate,query);//写表
					if(index==0){
						com.add(string);
						continue;
						//return EasyResult.error(501, "发送失败");	
					}
				}
			}
			if (com != null && com.size() > 0) {
				String ago=com.toString();
				return EasyResult.ok("", "短信号码"+ago+"没有发送内容或者号码格式不正确,请单独发送");
			}
			return EasyResult.ok("", "短信发送信息成功！");
		} catch (Exception e) {		
			this.error("短信发送信息失败，原因：" + e.getMessage(), e);
			return EasyResult.ok("", "短信发送信息失败！");
		}
	}
	/**
	 *单条量发送短信对象
	 * @return
	 */
	public EasyResult actionForSend(){
		EasyQuery easyQuery=this.getQuery();
		try {
			JSONObject jsonObject = this.getJSONObject();
			String deptCode=OpenApiUserUtil.getUser(this.getRequest()).getDept().getDeptCode();//所属部门编号
			String userName=OpenApiUserUtil.getUser(this.getRequest()).getUserName();//创建人姓名
			String userAccount=OpenApiUserUtil.getUser(this.getRequest()).getUserAcc();//登陆账号
			String userNo=OpenApiUserUtil.getUser(this.getRequest()).getUserNo();//工号
			String EP_CODE=OpenApiUserUtil.getUser(this.getRequest()).getEpCode();//企业编号
			String career=jsonObject.getString("career");
			//String channel=jsonObject.getString("channel");
			String brand=jsonObject.getString("brand");
			String content=jsonObject.getString("content");
			String sendDate=jsonObject.getString("sendDate");
			String shortLinkContent = jsonObject.getString("shortLinkContent");
			if("".equals(sendDate)||null==sendDate){
				sendDate = EasyCalendar.newInstance().getDateTime("-");
			}
			JSONObject map=getSendUrl(career,brand);//URL;账号;密码;渠道id
			if(map.size()<1||null==map){
				return EasyResult.error(501, "发送失败，原因：渠道不存在");
			}else if("2".equals(map.get("STATUS"))){
				return EasyResult.error(501, "发送失败，原因：渠道已被禁用");
			}
			// 短链文本不为空，则将短链拼接进短信内容中
			if (StringUtils.isNotBlank(shortLinkContent)) {
				CommLogger.logger.info("shoreLinkContent=" + shortLinkContent);
				shortLinkContent = splitShorLinkContent(shortLinkContent);
				CommLogger.logger.info("[params]:" + shortLinkContent);
				// 生成原始长短链
				String shortLinkUrl = Constants.getShortLinkUrl() + "?data=" + Base64.getUrlEncoder().encodeToString(shortLinkContent.getBytes(StandardCharsets.UTF_8));
				CommLogger.logger.info("shortLinkUrl1=" + shortLinkUrl);
				// 长链接转换为短链
				shortLinkUrl = getShortUrl(shortLinkUrl);
				CommLogger.logger.info("shortLinkUrl2=" + shortLinkUrl);
				// 拼接短信内容
				content = content + shortLinkUrl;
				CommLogger.logger.info("content=" + content);
			}
			String manyUser=jsonObject.getString("importUser");
			String str=getStr(manyUser);
			List<String> list=getSendPhone(str);//短信接收号码
			JSONObject json =new JSONObject();
			String CREATE_TIME = EasyCalendar.newInstance().getDateTime("-");
			String ID = RandomKit.smsAuthCode(16);
			json.put("CREATE_USER_NAME", userName);
			json.put("CREATE_DEPT", deptCode);
			json.put("CREATE_TIME", CREATE_TIME);
			json.put("CREATE_ACC", userAccount);
			json.put("CREATE_NO", userNo);
			json.put("EP_CODE", EP_CODE);
			json.put("ID", ID);
			json.put("TYPE", "1");
			json.put("SOURCE", Constants.SEND_TYEP);
			json.put("SEND_TIME", sendDate);
			json.put("CHANNEL_ID",map.get("ID"));
			json.put("STATUS","1");
			json.put("NEED_SMS_RECEIPT","1");
			EasyRecord record = new EasyRecord("C_SMS_INFO", "ID").setColumns(json);
			easyQuery.save(record);
			if (list != null && list.size() > 0) {
				for (String string : list) {				
					int index=sendMesg(string,ID,map.getString("ID"),content,sendDate,easyQuery);//写表
					if(index==0){
						return EasyResult.error(501, "发送失败");	
					}
					
				}
			}
			if(!"".equals(manyUser)&&null!=manyUser){
				String [] ars=manyUser.split(";");
				for (String string : ars) {
					int index=sendMesg(string,ID,map.getString("ID"),content,sendDate,easyQuery);//写表
					if(index==0){
						return EasyResult.error(501, "发送失败");	
					}
				}
			}
			return EasyResult.ok(record, "发送成功！");
		} catch (Exception ex) {
			this.error("发送失败，原因：" + ex.getMessage(), ex);
			return EasyResult.error(501, "发送失败，原因：" + ex.getMessage());
		}
	}

	public static void main(String[] args) throws UnsupportedEncodingException {
		String c ="https://ccuatgw.midea.com:9061/yc-mediagw/pages/shortLink/index.html?title=" +
				"尊敬的美的用户：&text1=您好！感谢您使用美的电器，我们将竭诚为您服务！&text2=您需要的美的官方中心电话为：&phone2=10920&text3=地址：广东省佛山市&text4=如需帮助，您也可以拨打美的24小时全国服务热线：&phone4=4008899315";
		System.out.println(c.length());
		String a = "https://ccuatgw.midea.com:9061/yc-mediagw/pages/shortLink/mess-link.html?title%3D%E5%B0%8A%E6%95%AC%E7%9A%84%E7%BE%8E%E7%9A%84%E7%94%A8%E6%88%B7%EF%BC%9A%26text1%3D%E6%82%A8%E5%A5%BD%EF%BC%81%E6%84%9F%E8%B0%A2%E6%82%A8%E4%BD%BF%E7%94%A8%E7%BE%8E%E7%9A%84%E7%94%B5%E5%99%A8%EF%BC%8C%E6%88%91%E4%BB%AC%E5%B0%86%E7%AB%AD%E8%AF%9A%E4%B8%BA%E6%82%A8%E6%9C%8D%E5%8A%A1%EF%BC%81%26text2%3D%E6%82%A8%E9%9C%80%E8%A6%81%E7%9A%84%E7%BE%8E%E7%9A%84%E5%AE%98%E6%96%B9%E4%B8%AD%E5%BF%83%E7%94%B5%E8%AF%9D%E4%B8%BA%EF%BC%9A%26phone2%3D10920%26text3%3D%E5%9C%B0%E5%9D%80%EF%BC%9A%E5%B9%BF%E4%B8%9C%E7%9C%81%E4%BD%9B%E5%B1%B1%E5%B8%82%26text4%3D%E5%A6%82%E9%9C%80%E5%B8%AE%E5%8A%A9%EF%BC%8C%E6%82%A8%E4%B9%9F%E5%8F%AF%E4%BB%A5%E6%8B%A8%E6%89%93%E7%BE%8E%E7%9A%8424%E5%B0%8F%E6%97%B6%E5%85%A8%E5%9B%BD%E6%9C%8D%E5%8A%A1%E7%83%AD%E7%BA%BF%EF%BC%9A%26phone4%3D4008899315%26text5%3D%E5%A6%82%E9%9C%80%E5%B8%AE%E5%8A%A9%EF%BC%8C%E6%82%A8%E4%B9%9F%E5%8F%AF%E4%BB%A5%E6%8B%A8%E6%89%93%E7%BE%8E%E7%9A%8424%E5%B0%8F%E6%97%B6%E5%85%A8%E5%9B%BD%E6%9C%8D%E5%8A%A1%E7%83%AD%E7%BA%BF%EF%BC%9A%26phone5%3D4008899315";
		System.out.println(a.length());
		String b = "https://ccuatgw.midea.com:9061/yc-mediagw/pages/shortLink/mess-link.html?title%3D%E5%B0%8A%E6%95%AC%E7%9A%84%E7%BE%8E%E7%9A%84%E7%94%A8%E6%88%B7%EF%BC%9A%26text1%3D%E6%82%A8%E5%A5%BD%EF%BC%81%E6%84%9F%E8%B0%A2%E6%82%A8%E4%BD%BF%E7%94%A8%E7%BE%8E%E7%9A%84%E7%94%B5%E5%99%A8%EF%BC%8C%E6%88%91%E4%BB%AC%E5%B0%86%E7%AB%AD%E8%AF%9A%E4%B8%BA%E6%82%A8%E6%9C%8D%E5%8A%A1%EF%BC%81%26text2%3D%E6%82%A8%E9%9C%80%E8%A6%81%E7%9A%84%E7%BE%8E%E7%9A%84%E5%AE%98%E6%96%B9%E4%B8%AD%E5%BF%83%E7%94%B5%E8%AF%9D%E4%B8%BA%EF%BC%9A%26phone2%3D10920%26text3%3D%E5%9C%B0%E5%9D%80%EF%BC%9A%E5%B9%BF%E4%B8%9C%E7%9C%81%E4%BD%9B%E5%B1%B1%E5%B8%82%26text4%3D%E5%A6%82%E9%9C%80%E5%B8%AE%E5%8A%A9%EF%BC%8C%E6%82%A8%E4%B9%9F%E5%8F%AF%E4%BB%A5%E6%8B%A8%E6%89%93%E7%BE%8E%E7%9A%8424%E5%B0%8F%E6%97%B6%E5%85%A8%E5%9B%BD%E6%9C%8D%E5%8A%A1%E7%83%AD%E7%BA%BF%EF%BC%9A%26phone4%3D4008899315";
		System.out.println(b.length());
		String data = "title=尊敬的美的用户：&text1=您好！感谢您使用美的电器，我们将竭诚为您服务！&text2=您需要的美的官方中心电话为：&phone2=10920&text3=地址：广东省佛山市&text4=如需帮助，您也可以拨打美的24小时全国服务热线：&phone4=4008899315";
		System.out.println(Base64.getEncoder().encodeToString(data.getBytes("UTF-8")));
	}
	private String splitShorLinkContent(String shortLinkContent) throws UnsupportedEncodingException {
		// 分割 shortLinkContent 为行
		String[] lines = shortLinkContent.split("\n");
		if (lines.length == 0) {
			CommLogger.logger.info("shortLinkContent为空");
			return null;
		}

		// 第一行作为标题
		String title = lines[0].trim();
		StringBuilder params = new StringBuilder();
		params.append("title=").append(title);

		// 处理第二行及以后的内容
		Pattern phonePattern = Pattern.compile("^[0-9]+$"); // 判断是否为纯数字（简单号码判断）
		int textIndex = 1; // 用于给每个 text 参数编号
		/*int phoneIndex = 1; // 用于给每个 phone 参数编号*/

		for (int i = 1; i < lines.length; i++) {
			String line = lines[i].trim();
			if (line.isEmpty()) {
				continue;
			}

			if (line.contains("：")) {
				String[] parts = line.split("：", 2);
				if (parts.length == 2) {
					String key = parts[0].trim();
					String value = parts[1].trim();

					if ("电话".equals(key) || "热线".equals(key) || phonePattern.matcher(value).matches()) { // 如果是电话相关或纯数字（假设为电话号码）
						params.append("&text").append(textIndex).append("=").append(parts[0]).append("：");
						params.append("&phone").append(textIndex).append("=").append(value);
					} else { // 普通文本
						params.append("&text").append(textIndex).append("=").append(line);
					}
				} else {
					params.append("&text").append(textIndex).append("=").append(line);
				}
			} else { // 普通文本
				params.append("&text").append(textIndex).append("=").append(line);
			}
			textIndex++;
			/*phoneIndex++;*/
		}
		return params.toString();
	}


	/**
	 * 生成短连接
	 *
	 * @param shortLinkUrl
	 * @return
	 */
	public String getShortUrl(String shortLinkUrl) {
		JSONObject shortResult = new JSONObject();
		//生成短链
		try {
			JSONObject obj = new JSONObject();
			Map<String, Object> param = new HashMap<String, Object>();
			obj.put("command", "longToShort");
			param.put("longLinkUrl", shortLinkUrl);
			obj.put("params", param);
			CommLogger.logger.info(1);
			try {
				IService service = ServiceContext.getService("MIXGW_MCSP_INTEFACE");
				shortResult = service.invoke(obj);
			} catch (ServiceException e) {
				CommLogger.logger.error("IService请求失败,请求参数" + JSON.toJSONString(obj) + ",原因" + e.getMessage());
			}
			CommLogger.logger.info(2);
			String respCode = shortResult.getString("respCode");
			CommLogger.logger.info("respCode=" + respCode);
			if (GWConstants.RET_CODE_SUCCESS.equals(respCode)) {
				CommLogger.logger.info(3);
				JSONObject respData = shortResult.getJSONObject("respData");
				CommLogger.logger.info("respData=" + respData);
				if (respData != null && respData.getJSONObject("data") != null && org.apache.commons.lang3.StringUtils.isNotBlank(respData.getJSONObject("data").getString("shortLinkUrl"))) {
					CommLogger.logger.info(4);
					CommLogger.logger.info("短链接生成成功" + shortLinkUrl + "->" + respData.getJSONObject("data").getString("shortLinkUrl"));
					shortLinkUrl = respData.getJSONObject("data").getString("shortLinkUrl");
				}
			}
			CommLogger.logger.info(5);

		} catch (Exception e) {
			CommLogger.logger.error("短链接生成失败" + e.getMessage(), e);
		}
		return shortLinkUrl;
	}

	/**
	 * 获取发送短信的url
	 * @return
	 */
	public JSONObject getSendUrl(String career,String brand){
		JSONObject jsonObject=new JSONObject();
		String sql = "select URL,ACCOUNT,PASSWD,ID,STATUS from C_SMS_CHANNEL WHERE MODEL_ID=?  AND CATEGORY=?";
		try {
			List<EasyRow> list =  this.getQuery().queryForList(sql, new Object[] {career,brand });
			if (list != null && list.size() > 0) {
				jsonObject.put("URL", list.get(0).getColumnValue("URL"));
				jsonObject.put("ACCOUNT", list.get(0).getColumnValue("ACCOUNT"));
				jsonObject.put("PASSWD", list.get(0).getColumnValue("PASSWD"));
				jsonObject.put("ID", list.get(0).getColumnValue("ID"));
				jsonObject.put("STATUS", list.get(0).getColumnValue("STATUS"));
				return jsonObject;
			}
		} catch (SQLException e) {
			CommLogger.logger.error("获取渠道信息," + e.getMessage(), e);	
		}
		return jsonObject;
		
	}
	/**
	 * 获取发送短信的用户号码
	 * @return
	 */
	public List<String> getSendPhone(String name){
		List<String> lsit = new ArrayList<String>();
		String sql = "select PHONENUM from C_SMS_MEMBER WHERE MEMBER_ID IN ("+name+") ";
		try {
			List<EasyRow> list =  this.getQuery().queryForList(sql, new Object[] {});
			if (list != null && list.size() > 0) {
				for (EasyRow easyRow : list) {
					String type = easyRow.getColumnValue("PHONENUM");
					lsit.add(type);
				}
				return lsit;
			}
			return lsit;

		} catch (SQLException e) {
			CommLogger.logger.error("获取发送短信的用户号码," + e.getMessage(), e);	
		}
		return lsit;
		
	}
	public String getStr(String phone){
		
		String str="";
		String [] arry=phone.split(";");
		for(int i=0;i<arry.length;i++){
			str+="'"+arry[i]+"'"+",";
		}
		str=str.substring(0, str.length()-1);
		return str;	
	}
	public int sendMesg(String iphone,String ID,String chanId,String content,String time,EasyQuery query ) {
		return sendMesg( iphone, ID, chanId, content, time, query ,"1");
	}
	public int sendMesg(String iphone,String ID,String chanId,String content,String time,EasyQuery query,String type ) {
		try {
			String EP_CODE=OpenApiUserUtil.getUser(this.getRequest()).getEpCode();//企业编号

			//短信发送信息
				String IDd = RandomKit.smsAuthCode(16);
				String CREATE_TIME1 = EasyCalendar.newInstance().getDateTime("-");			
				JSONObject js =new JSONObject();
				js.put("ID", IDd);
				js.put("USER_TYPE", type);
				js.put("SEND_TIME", time);
				js.put("CONTENT", content);
				js.put("SEND_TYPE", "1");
				js.put("RECEIVER", iphone);
				js.put("SEND_STATUS", "1");
				js.put("CREATE_TIME", CREATE_TIME1);
				js.put("SMS_INFO_ID", ID);
				js.put("BUSINESS_TYPE", "1");
				js.put("CHANNEL_ID", chanId);
				js.put("EP_CODE", EP_CODE);
				EasyRecord record1 = new EasyRecord("C_SMS_SEND_RECORD", "ID").setColumns(js);
				query.save(record1);
			return 1;
		} catch (SQLException ex) {
			this.error("发送失败，原因：" + ex.getMessage(), ex);
			return 0;
		}
		
		
	}
}

