package com.yunqu.cc.planning.dao;

import com.alibaba.fastjson.JSON;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.planning.base.AppDaoContext;
import com.yunqu.cc.planning.base.CommLogger;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;


/**
 * 6.外呼总体工作量报表
 * 7.客服外呼工作量报表
 */
@WebObject(name = "outboundReport")
public class OutboundReportDao extends AppDaoContext {

	private Logger logger = CommLogger.logger;

	/**
	 * 外呼报表数据
	 */
	@WebControl(name = "getOutboundReportNum",type = Types.RECORD)
	public JSONObject getOutboundReportNum() {
		JSONObject paramObj = this.param;
		logger.info("企划通回访报表--查询参数：paramObj:"+paramObj.toJSONString());


		JSONObject resultObj = new JSONObject();
		String inputWord = "{\"NEWCUST_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":1,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"OUTBOUND_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":1,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"SUC_CUST_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":0,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"SUC_CUST_RATE\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":0,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"EFFECT_CUST_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":1,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"EFFECT_CUST_RATE\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":1,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"CALL_AGAIN_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":0,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"CALL_TIME_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":0,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"CENTRAL_AIR_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":1,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"CENTRAL_AIR_RATE\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":1,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"ARRIVE_CUST_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":0,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"ARRIVE_CUST_RATE\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":0,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"}}";
		resultObj = JSON.parseObject(inputWord);
		/*
		{
			"NEWCUST_NUM":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":1,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},
			"OUTBOUND_NUM":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":1,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},
			"SUC_CUST_NUM":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":0,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},
			"SUC_CUST_RATE":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":0,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},


			"EFFECT_CUST_NUM":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":1,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},
			"EFFECT_CUST_RATE":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":1,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},
			"CALL_AGAIN_NUM":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":0,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},
			"CALL_TIME_NUM":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":0,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},


			"CENTRAL_AIR_NUM":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":1,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},
			"CENTRAL_AIR_RATE":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":1,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},
			"ARRIVE_CUST_NUM":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":0,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},
			"ARRIVE_CUST_RATE":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":0,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			}
		}
		*/
		return resultObj;
	}


	/**
	 * 客服外呼工作量报表数据
	 */
	@WebControl(name = "getOutboundWorkReportData",type = Types.RECORD)
	public JSONObject getOutboundWorkReportData() {
		JSONObject paramObj = this.param;
		logger.info("企划通回访报表--查询参数：paramObj:"+paramObj.toJSONString());


		JSONObject resultObj = new JSONObject();
		String inputWord = "{\"NEWCUST_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":1,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"OUTBOUND_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":1,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"SUC_CUST_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":0,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"SUC_CUST_RATE\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":0,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"EFFECT_CUST_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":1,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"EFFECT_CUST_RATE\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":1,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"CALL_AGAIN_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":0,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"CALL_TIME_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":0,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"CENTRAL_AIR_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":1,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"CENTRAL_AIR_RATE\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":1,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"ARRIVE_CUST_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":0,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"},\"ARRIVE_CUST_RATE\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":0,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"}}";
		resultObj = JSON.parseObject(inputWord);
		/*
		{
			"NEWCUST_NUM":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":1,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},
			"OUTBOUND_NUM":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":1,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},
			"SUC_CUST_NUM":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":0,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},
			"SUC_CUST_RATE":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":0,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},


			"EFFECT_CUST_NUM":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":1,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},
			"EFFECT_CUST_RATE":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":1,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},
			"CALL_AGAIN_NUM":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":0,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},
			"CALL_TIME_NUM":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":0,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},


			"CENTRAL_AIR_NUM":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":1,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},
			"CENTRAL_AIR_RATE":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":1,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},
			"ARRIVE_CUST_NUM":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":0,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			},
			"ARRIVE_CUST_RATE":{
				"MAIN_NUM":100,
				"PERIOD":"06-06~06-07",
				"INCREASE":0,
				"INCREASE_NUM":20,
				"INCREASE_RATE":"4.3%"
			}
		}
		*/
		return resultObj;
	}

}
