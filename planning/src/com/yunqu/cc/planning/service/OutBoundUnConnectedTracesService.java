package com.yunqu.cc.planning.service;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.planning.base.CommLogger;
import com.yunqu.cc.planning.base.Constants;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.kit.RandomKit;

import java.util.ArrayList;
import java.util.List;

/**
 * 从亿讯库表查询外呼-人工外呼：外呼-未接通的记录
 */
public class OutBoundUnConnectedTracesService {

    public Logger logger = CommLogger.logger;

    private static EasyCache cache = CacheManager.getMemcache();

    public EasyQuery getQuery() {
        EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.ORDER_DS);
        query.setMaxRow(100000);
        return query;
    }

    public EasyQuery getSDQuery() {
        EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.GENESYS_ICON_SD_DS);
        query.setMaxRow(100000);
        return query;
    }

    public EasyQuery getHFQuery() {
        EasyQuery query =  EasyQuery.getQuery(Constants.APP_NAME, Constants.GENESYS_ICON_HF_DS);
        query.setMaxRow(100000);
        return query;
    }

    public String channelType() {
        return "OutBoundUnConnected";
    }
    public String channelName() {
        return "外呼-未接通";
    }

    /**
     * 查询坐席userNo在时间范围内的外呼未接通记录
     * @param calledNum 被叫号码
     * @param agentNo 坐席工号
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    public List<JSONObject> queryLatestUnConnectedData(String calledNum, String agentNo, String beginTime, String endTime) {
        List<JSONObject> resultList = new ArrayList<>();
        String callerNum = getCallerByAgentNo(agentNo);
        if(StringUtils.isBlank(callerNum)){
            return resultList;
        }
        try {
            EasySQL sql = new EasySQL();
            // 构建查询SQL
            sql.append("SELECT ");
            sql.append("A.CONNID as SESSION_ID, ");  // 会话ID
            sql.append("A.CALLANI,"); //主叫号码
            sql.append("A.CALLDNIS,"); //被叫号码
            sql.append("TO_CHAR((A.CREATED + 0) + 8 / 24, 'yyyy-mm-dd hh24:mi:ss') AS BEGIN_TIME,");//录音开始时间
            sql.append("TO_CHAR((TERMINATED + 0) + 8 / 24, 'yyyy-mm-dd hh24:mi:ss') AS END_TIME,");//录音结束时间
            sql.append("NVL(B.TT_ALERTING, 0) ALERTING,");//振铃时长
            sql.append("NVL(B.TT_CONNECTED, 0) DURATION");//录音时长

            sql.append("FROM GEN_ICON.G_CALL A ");
            sql.append("LEFT JOIN GEN_ICON.G_CALL_STAT B ON A.CALLID = B.CALLID ");

            sql.append("WHERE A.CALLTYPE = '3' "); //呼叫方向：2-呼入；3-呼出
            sql.append("AND NVL(b.TT_CONNECTED, 0) = 0 "); //录音时长为0 代表外呼未接通
            sql.append(beginTime, "AND A.CREATED >= TO_DATE(?, 'yyyy-mm-dd hh24:mi:ss') - 8/24"); //第三方接口的时间格式需要减去8/24
            sql.append(endTime, "AND A.CREATED <= TO_DATE(?, 'yyyy-mm-dd hh24:mi:ss') - 8/24");

            sql.append(callerNum, "AND A.CALLANI = ? ");
            sql.append("81"+calledNum,"AND (A.CALLDNIS =? ");//亿讯的外呼是有一个81的前缀
            String outsideNum = "0"+calledNum;
            sql.append("81"+outsideNum, "OR A.CALLDNIS =?) ");//呼出外地号码会在前面+0，防止仅用电话号码查询不到。
            sql.append("ORDER BY BEGIN_TIME DESC");

            logger.info("查询未接通通话记录SQL: " + sql.getSQL() + ",params:" + JSONObject.toJSONString(sql.getParams()));
            // 分页查询顺德和合肥区域的数据
            JSONObject sdUnconnectedData = getSDQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
            logger.info("查询顺德数据源未接通通话记录结果: " + JSONObject.toJSONString(sdUnconnectedData));
            List<JSONObject> records = new ArrayList<>();
            records.add(sdUnconnectedData);
            try {
                JSONObject hfUnconnectedData = getHFQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
                logger.info("查询合肥数据源未接通通话记录结果: " + JSONObject.toJSONString(hfUnconnectedData));
                records.add(hfUnconnectedData);
            }catch (Exception e){
                logger.info("测试环境查询合肥数据失败");
            }
            logger.info("查询最新的未接通通话记录结果: " + JSONObject.toJSONString(records));
            // 处理查询结果
            for (JSONObject record : records) {
                String called = record.getString("CALLDNIS");
                // 去除号码前的81 前缀
                called = StringUtils.removeStart(called,"81");
//                called = StringUtils.removeStart(called,"0");
                JSONObject traceRecord = new JSONObject();
                traceRecord.put("ID", RandomKit.randomStr());
                traceRecord.put("SESSION_ID", record.getString("SESSION_ID"));
                traceRecord.put("PHONE", called);
                traceRecord.put("TRACE_TYPE", "3"); //外呼
                traceRecord.put("CHANNEL_TYPE", this.channelType());
                traceRecord.put("CHANNEL_NAME", this.channelName());
                traceRecord.put("BEGIN_TIME", record.getString("BEGIN_TIME"));
                traceRecord.put("CREATE_TIME", DateUtil.getCurrentDateStr());
                String content = "坐席" + agentNo + "工号外呼联系,用户" + record.getIntValue("ALERTING") + "秒未接听";
                traceRecord.put("COMMUNICATION", content);
                traceRecord.put("AGENT_NO", agentNo);
                resultList.add(traceRecord);
            }
            //根据时间排序
            resultList.sort((o1, o2) -> {
                String dateTime1 = o1.getString("BEGIN_TIME");
                String dateTime2 = o2.getString("BEGIN_TIME");
                return dateTime1.compareTo(dateTime2);
            });
            logger.info("坐席工号："+agentNo+" | 查询未接通通话记录结果: " + JSONObject.toJSONString(resultList));
            return resultList;
        } catch (Exception e) {
            logger.error("查询未接通通话记录失败: " + e.getMessage(), e);
        }
        return resultList;
    }

    /*private Map<String,Boolean> sessionIdExists(EasyQuery query, List<String> sessionIdList) throws SQLException {
        if (CollectionUtils.isEmpty(sessionIdList)){
            return new HashMap<>();
        }
        if (CollectionUtils.size(sessionIdList) > 1000){
            Map<String,Boolean> result = new HashMap<>();
            for (List<String> id : ListUtils.partition(sessionIdList, 1000)){
                result.putAll(sessionIdExists(query,id));
            }
            return result;
        }

        EasySQL sql = new EasySQL();
        sql.append("SELECT SESSION_ID,COUNT(1) AS NUM FROM YWDB.C_PF_CALL_RECORD WHERE ");
        sql.append("SESSION_ID IN ('"+String.join("','",sessionIdList)+"') ");
        sql.append("GROUP BY SESSION_ID");
        logger.info("查询会话ID是否存在SQL: " + sql.getSQL() + ",params:" + JSONObject.toJSONString(sql.getParams()));
        List<JSONObject> rows = query.queryForList(sql.getSQL(), new Object[]{},new JSONMapperImpl());
        logger.info("查询会话ID是否存在结果: " + JSONObject.toJSONString(rows));
        return StreamUtil.of(rows).collect(Collectors.toMap(row -> row.getString("SESSION_ID"), row -> row.getIntValue("NUM") > 0));
    }*/

    public String getCallerByAgentNo(String agentNo) {
        String callerNum = "";
        try {
            callerNum = cache.get("AGENTNO_FOR_CALLIN_" + agentNo);
            logger.info("查询坐席工号关联签入号: " + callerNum);
        } catch (Exception e) {
            logger.info("查询坐席工号关联签入号失败: " + e.getMessage(), e);
        }
        return callerNum;
    }
}
