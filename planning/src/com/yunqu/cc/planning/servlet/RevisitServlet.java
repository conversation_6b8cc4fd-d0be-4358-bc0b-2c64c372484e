package com.yunqu.cc.planning.servlet;

import java.io.*;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;

import com.alibaba.fastjson.JSONArray;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.*;
import com.yq.busi.common.util.export.ExcelExport;
import com.yq.busi.common.util.export.ExcelExportQuery;
import com.yq.busi.common.util.export.ExcelExportQueryFixedList;
import com.yunqu.cc.planning.enums.DataSourceEnum;
import com.yunqu.cc.planning.inf.RevisitService;
import com.yunqu.cc.planning.service.OutBoundUnConnectedTracesService;
import com.yunqu.openapi.utils.OpenApiUserUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;
import com.yunqu.cc.planning.base.AppBaseServlet;
import com.yunqu.cc.planning.base.CommLogger;
import com.yunqu.cc.planning.base.Constants;
import com.yunqu.cc.planning.utils.PwdUtils;
import com.yunqu.cc.planning.utils.StringUtil;



@WebServlet("/servlet/revisit")
public class RevisitServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	private Logger logger = CommLogger.logger;
	private Logger apiLogger = CommLogger.getCommLogger("apiLog");
	private EasyCache cache = CacheManager.getMemcache();
	
	
	public String actionForRevisitPublish(){
		return "/pages/planning/revisit-publish.jsp";
	}

	/**
	 * 指派
	 * @return
	 */
	public JSONObject actionForAppoint() {
		JSONObject data = getJSONObject("appoint");
		EasyQuery query = getQuery();
		String synStartTime = data.getString("SYN_TIME_START");
		String synEndTime = data.getString("SYN_TIME_END");
//		String synMsg = checkSynTime(synStartTime,synEndTime);//验证同步时间
//		if(!"".equals(synMsg)) {
//			return EasyResult.error(500, synMsg);
//		}
		if(StringUtils.isBlank((data.getString("USER_ACC")))||StringUtils.isBlank(data.getString("USER_NAME"))) {
			return EasyResult.error(500,"坐席账号数量不正确");
		}
		if(StringUtils.isBlank(data.getString("TASK_NAME"))){
			return EasyResult.error(500,"请输入任务名称");
		}
		if(data.getIntValue("TASK_NUM") <= 0){
			return EasyResult.error(500,"指派数量不正确");
		}
		if(StringUtils.isBlank(data.getString("CONVERSION_TYPE"))){
			return EasyResult.error(500,"请选择折算类型");
		}

		try {
			String[] userAccList = data.getString("USER_ACC").split(",");
			String[] userNameList = data.getString("USER_NAME").split(",");
			String pubId = RandomKit.uniqueStr();//发布流水ID
			String nowTime = EasyCalendar.newInstance().getDateTime("-");//统一时间


			String planningIdStr = data.getString("PLANNING_ID");
			String[] planningIds = null;
			int appintType = 0; //0-随机指派，1-指定指派
			if(StringUtils.isNotBlank(planningIdStr)) {
				appintType = 1;
				planningIds = planningIdStr.split(",");
			}

			int taskNum = data.getIntValue("TASK_NUM");
			int averageNum = taskNum / userAccList.length; // 7 / 3 --> 3 2 2  2 1
			int molNum = taskNum % userAccList.length;
			int successNum = 0;

			List<Integer> ramdonSizeList = new ArrayList<>(userAccList.length);
			List<String[]> appintIdList = new ArrayList<>(userAccList.length);
			int addSize = 0;
			int addCount = 0;

			for (int i = 0; i < taskNum; i += addSize) {  //7
				addSize = averageNum;
				if (addCount < molNum) {
					addSize++;
					addCount++;
				}
				if(appintType==0){
					ramdonSizeList.add(addSize);
				}else{
					int end = Math.min(planningIds.length, i + addSize);
					appintIdList.add(Arrays.copyOfRange(planningIds, i, end));
				}
			}
			logger.debug("ramdonSizeList："+JSONObject.toJSONString(ramdonSizeList)+" | appintIdList:"+JSONObject.toJSONString(appintIdList));
			logger.debug("userAccList:"+userAccList.length+" | addCount:"+addCount);
			//更新CC_PLANNING，同时写入领取人员表
			for(int i = 0;i<userAccList.length;i++) {

				EasySQL updateSql = new EasySQL("UPDATE CC_PLANNING SET")
				.append(data.getString("CONVERSION_TYPE"),"CONVERSION_TYPE = ?,")
				.append(userAccList[i],"RECEIVE_ACC = ?,").append(nowTime,"RECEIVE_TIME = ?,")
				.append(userAccList[i],"APPOINT_ACC = ?,").append(nowTime,"APPOINT_TIME = ?,")
				.append(pubId,"C_PLANNING_PUB_ID = ?,").append(UserUtil.getUser(getRequest()).getUserAcc(),"PUB_ACC = ?,")
				.append(nowTime,"PUB_TIME = ?,").append(Constants.HAS_RECEIVED,"REVISIT_STATUS = ? WHERE 1=1") //已领取
				.append(Constants.NO_PUBLISH,"AND REVISIT_STATUS = ?");//未发布
				if(appintType==0){
					updateSql.append(synStartTime,"AND SYN_TIME >= ?").append(synEndTime,"AND SYN_TIME <= ?")
					.append(data.getString("TASK_NAME"),"AND TASK_NAME = ?")//任务名称
					.append(data.getString("DEMAND_SIDE"),"AND DEMAND_SIDE = ?")//需求方
					.append(ramdonSizeList.get(i),"AND ROWNUM <= ?");
				} else {
					//这里根据平均分配planningIds
					StringUtil.appendIn(appintIdList.get(i), "AND ID", updateSql);
				}

				int updateTaskNum = query.executeUpdate(updateSql.getSQL(),updateSql.getParams());
				if(updateTaskNum==0) {
					break;
				}
				EasyRecord record = new EasyRecord("CC_PLANNING_RECEIVE_USER")
				.set("ID",RandomKit.uniqueStr())
				.set("USER_ACC",userAccList[i])
				.set("USER_NAME",userNameList[i])
				.set("C_PLANNING_PUB_ID",pubId)
				.set("CREATE_TIME",nowTime)
				.set("CYCLE_NUM",updateTaskNum);
				query.save(record);
				successNum += updateTaskNum;
			}

			//写入发布记录
			EasyRecord record = new EasyRecord("CC_PLANNING_PUBLISH")
			.set("ID", pubId)
			.set("PUB_ACC",UserUtil.getUser(getRequest()).getUserAcc())
			.set("PUB_TIME",nowTime)
			.set("PUB_TYPE",Constants.TO_APPOINT)
			.set("TASK_NAME",data.getString("TASK_NAME"))
			.set("TASK_NUM",successNum)
			.set("DEMAND_SIDE",data.getString("DEMAND_SIDE"))
			.set("CONVERSION_TYPE",data.getString("CONVERSION_TYPE"))
			.set("PUB_STATUS",Constants.HAS_RECEIVED);
			query.save(record);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "指派失败：" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok();
	}
	
	
	/**
	 * 发布
	 * @return
	 */
	public JSONObject actionForPublish() {
		JSONObject data = getJSONObject("publish");
		EasyQuery query = getQuery();
		String synStartTime = data.getString("SYN_TIME_START");
		String synEndTime = data.getString("SYN_TIME_END");

		if(StringUtils.isBlank(data.getString("TASK_NAME"))){
			return EasyResult.error(500,"请输入任务名称");
		}
		if(StringUtils.isBlank(data.getString("CONVERSION_TYPE"))){
			return EasyResult.error(500,"请选择折算类型");
		}

		try {

			String planningIdStr = data.getString("PLANNING_ID");
			String[] planningIds = null;
			int publishType = 0; //0-随机指派，1-指定指派
			if(StringUtils.isNotBlank(planningIdStr)) {
				publishType = 1;
				planningIds = planningIdStr.split(",");
			}

			int userNum = 0;
			String nowTime = EasyCalendar.newInstance().getDateTime("-");//统一时间
			String pubId = RandomKit.uniqueStr();//发布流水ID

			if(Constants.PART_OUT.equals(data.getString("PUB_TYPE"))) {//部分发布
				String pubUserNum = data.getString("PUB_USER_NUM");
				if(StringUtil.getInt(pubUserNum)==0) {
					return EasyResult.error(500,"分配人数不正确");
				}
				userNum = StringUtil.getInt(pubUserNum);
			}

			//将表单状态改为已发布
			EasySQL updateSql = new EasySQL("UPDATE CC_PLANNING SET");
			updateSql.append(pubId,"C_PLANNING_PUB_ID = ?,")
			.append(UserUtil.getUser(getRequest()).getUserAcc(),"PUB_ACC = ?,")
			.append(nowTime,"PUB_TIME = ?,")
			.append(data.getString("CONVERSION_TYPE"),"CONVERSION_TYPE = ?,")
			.append(Constants.HAS_PUBLISHED,"REVISIT_STATUS = ? WHERE 1=1")
			.append(data.getString("TASK_NAME"),"AND TASK_NAME = ?")//任务名称
			.append(synStartTime,"AND SYN_TIME >= ?")//同步开始时间
			.append(synEndTime,"AND SYN_TIME <= ?")//同步结束时间
			.append(Constants.NO_PUBLISH,"AND REVISIT_STATUS = ?")//回访状态：未发布
			.append(data.getString("DEMAND_SIDE"),"AND DEMAND_SIDE = ?");//需求方
			if(Constants.PART_OUT.equals(data.getString("PUB_TYPE")) && publishType == 0) {//部分发布
				/**
				 * 部分发布时，可按同步时间升序或降序获取数据
				 * updateSql.append("ORDER BY SYN_TIME");
				 */
				updateSql.append(userNum*50,"AND ROWNUM <= ?");
			}else if(Constants.PART_OUT.equals(data.getString("PUB_TYPE")) && publishType == 1){
				/**
				 * 指定发布，只发布指定的Id数据
				 */
				StringUtil.appendIn(planningIds, "AND ID ", updateSql);
			}
			//任务数量
			int taskNum = query.executeUpdate(updateSql.getSQL(), updateSql.getParams());
			if(taskNum==0) {
				return EasyResult.error(500,"任务数为0，无资料发布");
			}
			int min = ConfigUtil.getInt(Constants.APP_NAME, "DISTRIBUTE_TIME",3);
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
			Date date = new Date();
	        Calendar calendar = Calendar.getInstance();
	        calendar.setTime(date); //设置为当前时间
	        calendar.set(Calendar.MINUTE, calendar.get(Calendar.MINUTE) + min);
	        date = calendar.getTime();
	        String deadline = dateFormat.format(date);
			//写入发布记录
			EasyRecord record = new EasyRecord("CC_PLANNING_PUBLISH");
			record.set("ID", pubId)
			.set("PUB_ACC",UserUtil.getUser(getRequest()).getUserAcc())
			.set("PUB_TIME",nowTime)
			.set("PUB_TYPE",data.getString("PUB_TYPE"))
			.set("TASK_NAME",data.getString("TASK_NAME"))
			.set("TASK_NUM",taskNum)
			.set("DEMAND_SIDE",data.getString("DEMAND_SIDE"))
			.set("DEADLINE",deadline)
			.set("CONVERSION_TYPE",data.getString("CONVERSION_TYPE"))
			.set("PUB_STATUS",Constants.HAS_PUBLISHED);
			query.save(record);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "ERROR:" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok();
	}
	/**
	 * 回收
	 * @return
	 */
	public JSONObject actionForRecycle() {
		JSONObject data = getJSONObject("recycle");
		String synStartTime = data.getString("SYN_TIME_START");
		String synEndTime = data.getString("SYN_TIME_END");
		
		EasySQL sql = new EasySQL("UPDATE CC_PLANNING SET");
		sql.append("RECEIVE_ACC = NULL,RECEIVE_TIME = NULL,APPOINT_ACC = NULL,CONVERSION_TYPE = NULL,APPOINT_TIME = NULL,C_PLANNING_PUB_ID = NULL,PUB_ACC = NULL,PUB_TIME = NULL,");
		sql.append(Constants.NO_PUBLISH,"REVISIT_STATUS = ? WHERE 1=1");
		sql.append(data.getString("CONVERSION_TYPE"),"AND CONVERSION_TYPE = ?");
		sql.append(synStartTime,"AND SYN_TIME >= ?").append(synEndTime,"AND SYN_TIME <= ?");
		sql.append(data.getString("PUB_TIME_START"),"AND PUB_TIME >= ?").append(data.getString("PUB_TIME_END"),"AND PUB_TIME <= ?");
		sql.append(data.getString("REVISIT_RESULT"),"AND REVISIT_RESULT = ?");
		if("".equals(data.getString("REVISIT_STATUS"))) {
			String status = "'2','3','4'";
			sql.append("AND REVISIT_STATUS IN ( " + status + " )");//回访状态
		}else {
			sql.append(data.getString("REVISIT_STATUS"),"AND REVISIT_STATUS = ?");//回访状态
		}
		sql.append(Constants.IS_DONE,"AND IS_DONE != ?");//已办结状态不可回收
		if(!"".equals(data.getString("USER_ACC"))) {
			String[] userAcc = data.getString("USER_ACC").split(",");
			sql.append("AND RECEIVE_ACC IN (" + StringUtil.joinSqlStr(userAcc) + ")");
		}
		sql.append(data.getString("TASK_NUM"),"AND ROWNUM <= ?");
		sql.append(data.getString("TASK_NAME"),"AND TASK_NAME = ?");
		sql.appendLike(data.getString("DEMAND_SIDE"),"AND DEMAND_SIDE LIKE ?");
		try {
			int taskNum = getQuery().executeUpdate(sql.getSQL(), sql.getParams());
			if(taskNum==0) {
				return EasyResult.error(500,"任务数为0，无资料回收");
			}
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "回收出错：" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok();
	}

	/**
	 * 回访处理
	 * @return
	 */
	public JSONObject actionForToHandle() {
		JSONObject data = getJSONObject("toHandle");
		String nowDate = EasyDate.getCurrentDateString();
		EasyQuery query = getQuery();
		try {
			//判断是否办结
			if(query.queryForInt("SELECT COUNT(1) FROM CC_PLANNING WHERE ID = ? AND IS_DONE = ?", new Object[] {data.getString("pId"),Constants.IS_DONE}) > 0) {
				return EasyResult.error(500,"该回访单已办结，无法处理！");
			}
			CommLogger.getCommLogger("dataSource_"+data.getString("DATA_SOURCE")).error("提交参数是"+this.getJSONObject().toJSONString());

			if(StringUtils.equals(DataSourceEnum.DATA_SOURCE2.getCode(), data.getString("DATA_SOURCE"))){//美云销接口
				JSONObject row = query.queryForRow("SELECT * FROM CC_PLANNING WHERE ID = ? ", new Object[] {data.getString("pId")},new JSONMapperImpl());
				JSONObject params = new JSONObject();
				params.put("rtpId",  row.getString("USER_INFO_ID"));
				params.put("sourceType", data.getString("DATA_SOURCE"));
				params.put("visitTime", nowDate);
				params.put("visitorMip", UserUtil.getUser(getRequest()).getUserAcc());
				params.put("visitorName", UserUtil.getUser(getRequest()).getUserName());
				params.put("visitResult", data.getString("REVISIT_RESULT_TEXT"));
				params.put("solution", data.getString("SOLUTION"));
				params.put("relDocument", data.getString("RELATED_ORDER_ID"));
				params.put("remark", data.getString("REMARKS"));
				try {
					IService service = ServiceContext.getService("MIXGW_GVOC_INTEFACE");
					JSONObject queryParams = new JSONObject();
					queryParams.put("command","pushVisitBack");
					queryParams.put("params",params);
					JSONObject resp = service.invoke(queryParams);
					String respCode = resp.getString("respCode");
					if (!"000".equals(respCode)) {// 不成功则返回
						return EasyResult.error(500,"提交回访数据出错，无法处理！原因："+resp.getString("respDesc"));
					}
				} catch (ServiceException e) {
					return EasyResult.error(500,"提交回访数据出错，无法处理！");
				}
				
			}else if(StringUtils.equals(DataSourceEnum.DATA_SOURCE3.getCode(), data.getString("DATA_SOURCE"))){
				if("1".equals(data.getString("IS_DONE"))) {
					//同步美云销结果
					JSONObject row = query.queryForRow("SELECT * FROM CC_PLANNING WHERE ID = ? ", new Object[] {data.getString("pId")},new JSONMapperImpl());
					JSONObject obj = new JSONObject();
					JSONObject params = new JSONObject();
					params.put("boUserId", row.getString("QUESTION_ID"));//商机id
					params.put("mobile", row.getString("CUSTOMER_PHONE"));//手机
					params.put("followType", row.getString("DATA_SOURCE_NOTE"));//返回参数
					params.put("questionPaperUrl", row.getString("QUESTION_URL"));//问卷链接，拼装了商机id的问卷链接
					params.put("followStatus", data.getString("REVISIT_RESULT"));//外呼结果（枚举） integer
					params.put("followMip", UserUtil.getUser(getRequest()).getUserAcc());
					obj.put("params",params);
					obj.put("command","receiveOutbound");
					try {
						IService service = ServiceContext.getService(ServiceID.MIXGW_ECM_INTEFACE);
						JSONObject result = service.invoke(obj);
						logger.info("同步美云销结果,请求参数"+JSON.toJSONString(params)+",结果"+result.toJSONString());
						String respCode = result.getString("respCode");
						if (!"000".equals(respCode)) {// 不成功则返回
							return EasyResult.error(500,"提交回访数据出错，美云销返回失败，无法处理！原因："+result.getString("respDesc"));
						}
					} catch (ServiceException e) {
						logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
					}
				}
			}
			//更新记录
			EasyRecord upRecord = new EasyRecord("CC_PLANNING","ID").setPrimaryValues(data.getString("pId"));
			upRecord.set("REVISIT_STATUS",Constants.HAS_REVISITED);//回访状态：已回访
			upRecord.set("REVISIT_RESULT",  data.getString("REVISIT_RESULT"));//最近回访结果
			
			upRecord.set("RELATED_ORDER_ID", data.getString("RELATED_ORDER_ID"));
			upRecord.set("REMARKS", data.getString("REMARKS"));
			upRecord.set("IS_UPGRADE", data.getString("IS_UPGRADE"));
			upRecord.set("SOLUTION", data.getString("SOLUTION"));
			if("1".equals(data.getString("IS_DONE"))) {
				upRecord.set("DONE_TIME", nowDate);
				upRecord.set("IS_DONE",Constants.IS_DONE);//如果已办结，最近回访状态直接改为已办结
//				upRecord.set("FOLLOW_STATUS", Constants.FOLLOW_UP_COMPLETED);//如果已办结，跟进状态直接改为跟进结束   第一期不上
			}
			UserModel userModel = OpenApiUserUtil.getUser(getRequest());
			String agentNo = userModel.getUserNo();


			// 计算3天前的时间
			String threeDaysAgo = DateUtil.addDay("yyyy-MM-dd HH:mm:ss", nowDate, -3);
			String calledNum = data.getString("CALLED");
			EasySQL sql = new EasySQL("SELECT LENS, ANSWER_TIME FROM (SELECT LENS, ANSWER_TIME FROM C_PF_CALL_RECORD WHERE 1=1 AND DIRECTION = 2"); //只取呼出方向
			sql.append(threeDaysAgo,"AND ANSWER_TIME >= ? ");
			sql.append(nowDate, "AND ANSWER_TIME <= ? ");
			sql.append(agentNo,"AND AGENT_NO =? ");
			if(StringUtils.isBlank(calledNum)){
				sql.append(calledNum,"AND CALLED =? ", false);
			}else{
				sql.append(calledNum,"AND (CALLED =? ");
				String outsideNum = "0"+calledNum;
				sql.append(outsideNum, "OR CALLED =?) ");//呼出外地号码会在前面+0，防止仅用电话号码查询不到。
			}
			sql.append("ORDER BY ANSWER_TIME DESC) WHERE ROWNUM = 1");
			logger.info("查询3天内的最新lens sql：" + sql.getSQL()+ ",参数：" + JSONObject.toJSONString(sql.getParams()));
			JSONObject connectedRow = query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			logger.info("3天内的时间获取最新的connectedRow："+JSONObject.toJSONString(connectedRow));

			//这里查询呼叫未接的情况
			OutBoundUnConnectedTracesService outBoundUnConnectedTracesService = new OutBoundUnConnectedTracesService();
			List<JSONObject> unconnectedList = outBoundUnConnectedTracesService.queryLatestUnConnectedData(calledNum, agentNo, threeDaysAgo, nowDate);

			boolean callFlag = false;
			String callTime = "";
			int latestCallLen = 0;

			//1、查询外呼未接情况
			if(CollectionUtils.isNotEmpty(unconnectedList)) {
				callTime = unconnectedList.get(0).getString("BEGIN_TIME");
				callFlag = true;
				logger.info("外呼存在未接来电的情况：callTime(未接):"+callTime);
			}
			//2、查询外呼接通情况
			if(connectedRow != null) {
				String answerTime = connectedRow.getString("ANSWER_TIME");
				int connectedLen = connectedRow.getIntValue("LENS");
				if(callFlag) {
					logger.info("外呼存在未接来电和已结的情况：callTime(未接):"+callTime+" | answerTime(已接):"+answerTime+" | connectedLen(已接):"+connectedLen);
					//比较拿到最新的时长
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					try {
						Date unConnectedTime = sdf.parse(callTime);
						Date connectedTime = sdf.parse(answerTime);
						//接通时间比未接通时间更大，取接通时长
						if (unConnectedTime.compareTo(connectedTime) < 0) {
							latestCallLen = connectedLen;
						}
					}catch (ParseException e){
						logger.error("时间解析异常："+e.getMessage(),e);
					}
				}else{
					//没有未接情况，取接通时长
					logger.info("外呼仅存在已结的情况：answerTime(已接):"+answerTime+" | connectedLen(已接):"+connectedLen);
					latestCallLen = connectedLen;
				}
				callFlag = true;
			}

			if(callFlag) {
				if(latestCallLen == 0) {
					upRecord.set("CALL_STATUS", Constants.NOT_CONNECTED);//1-未接通
				} else if (latestCallLen < 10) {
					upRecord.set("CALL_STATUS", Constants.CONNECTED);//2-已接通
				} else if (latestCallLen < 30) {
					upRecord.set("CALL_STATUS", Constants.EFFECTIVE_COMMUNICATION);//3-有效沟通
				} else {
					upRecord.set("CALL_STATUS", Constants.DEEP_COMMUNICATION);//4-深度沟通
				}
			}

			query.update(upRecord);
			
			//写入结果记录表
			EasyRecord record = new EasyRecord("CC_PLANNING_RESULT");
			record.set("ID",RandomKit.uniqueStr());
			record.set("C_PLANNING_ID",data.getString("pId"));
			record.set("REVISIT_RESULT", data.getString("REVISIT_RESULT"));
			record.set("RELATED_ORDER_ID",data.getString("RELATED_ORDER_ID"));
			record.set("REMARKS",data.getString("REMARKS"));
			record.set("IS_UPGRADE",data.getString("IS_UPGRADE"));
			record.set("SOLUTION",data.getString("SOLUTION"));
			
			if("1".equals(data.getString("IS_DONE"))) {
				record.set("IS_DONE",Constants.IS_DONE);
			}else {
				record.set("IS_DONE",Constants.NOT_DONE);
			}
			record.set("CREATE_ACC",UserUtil.getUser(getRequest()).getUserAcc());
			record.set("CREATE_TIME",EasyCalendar.newInstance().getDateTime("-"));
			query.save(record);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"处理失败："+e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok();
	}
	/**
	 * 资料申请
	 * @return
	 */
	public JSONObject actionForApply() {
		JSONObject data = getJSONObject();
		String synStartTime = data.getString("SYN_TIME_START");
		String synEndTime = data.getString("SYN_TIME_END");
		String userAcc = UserUtil.getUser(getRequest()).getUserAcc();//当前坐席
		String nowTime = EasyCalendar.newInstance().getDateTime("-");//统一时间
		EasyQuery query = getQuery();
		//缓存校验
		String cacheKey = "cc_planning_apply_" + userAcc;
		String value = cache.get(cacheKey);
		if(StringUtils.isNotBlank(value)) {
			return EasyResult.error(500,"请勿频繁点击!");
		}
		try {
			cache.put(cacheKey, "1", 2);
			EasySQL checkSql = new EasySQL("SELECT COUNT(*) FROM CC_PLANNING WHERE RECEIVE_ACC = ? AND REVISIT_STATUS = ?");
			int waitToRevisitNum = query.queryForInt(checkSql.getSQL(), new Object[] {userAcc,Constants.HAS_RECEIVED,});
			if(waitToRevisitNum>20) {
				return EasyResult.error(500,"待回访数量大于20，无法申请");
			}
			EasySQL selectSql = new EasySQL("SELECT DISTINCT T1.ID FROM CC_PLANNING_PUBLISH T1")
			.append("LEFT JOIN CC_PLANNING T2 ON T1.ID = T2.C_PLANNING_PUB_ID WHERE 1=1")
			.append(synStartTime,"AND T2.SYN_TIME >= ?").append(synEndTime,"AND T2.SYN_TIME <= ?")
			.append(data.getString("CONVERSION_TYPE"),"AND T1.CONVERSION_TYPE = ?")
			.append(nowTime,"AND T1.DEADLINE >= ?").append(Constants.HAS_PUBLISHED,"AND T2.REVISIT_STATUS = ?");
			List<JSONObject> dataList = query.queryForList(selectSql.getSQL(), selectSql.getParams(),new JSONMapperImpl());
			if(dataList!=null&&dataList.size()>0) {
				EasyRecord record = new EasyRecord("CC_PLANNING_APPLY_USER");
				for(JSONObject obj:dataList) {
					if(query.queryForInt("SELECT COUNT(1) FROM CC_PLANNING_APPLY_USER WHERE USER_ACC = ? AND C_PLANNING_PUB_ID = ?",new Object[] {userAcc,obj.getString("ID")})>0) {
						continue;
					}
					record.set("ID",RandomKit.uniqueStr())
					.set("USER_ACC",userAcc)
					.set("USER_NAME",UserUtil.getUser(getRequest()).getUserName())
					.set("C_PLANNING_PUB_ID",obj.getString("ID"))
					.set("CREATE_TIME",nowTime);
					query.save(record);
				}
			}else {
				return EasyResult.error(500,"无资料发布，申请失败");
			}
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "资料申请失败：" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok();
	}

	/**
	 * 同步时间数据验证
	 * @param startTime
	 * @param endTime
	 * @return
	 */
//	private String checkSynTime(String startTime,String endTime) {
//		try {
//			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//			Date settlementStartTime = getSettlementStartTime();
//			if(StringUtils.isBlank(startTime)||StringUtils.isBlank(endTime)) {
//				return "同步时间不能为空";
//			}
//			Date synStartDate = sdf.parse(startTime);
//			Integer i=synStartDate.compareTo(settlementStartTime);
//			if(i<0){
//				return "不允许发布上一周期的单据，请重新选择同步时间";
//			}
//		} catch (ParseException e) {
//			logger.error(CommonUtil.getClassNameAndMethod(this) + "ERROR:" + e.getMessage(),e);
//			return "系统异常";
//		}
//		return "";
//	}
	
	private static Calendar dateToCalendar(final Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		return calendar;
	}
	private static Date setStartDateHMS(final Date date) {
		Calendar cal = dateToCalendar(date);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		return cal.getTime();
	}
	private static Date getSettlementStartTime(){
		// 系统结算的时间，目前写死26号，后面再使用参数配置
		Calendar settlementCal = Calendar.getInstance();
		settlementCal.set(Calendar.DAY_OF_MONTH, 26);
		// 同步时间：默认是本月22号
		Calendar startCal = Calendar.getInstance();
		startCal.set(Calendar.DAY_OF_MONTH, 22);
		if (Calendar.getInstance().before(settlementCal)){
			startCal.add(Calendar.MONTH, -1);;
		}
		return setStartDateHMS(startCal.getTime());
	}
	
	
	/**
	 * 发布获取任务数量
	 */
	public JSONObject actionForGetPublishNum() {
		JSONObject data = getJSONObject("publish");
		EasySQL sql = new EasySQL("SELECT COUNT(1) AS NUM FROM CC_PLANNING WHERE 1=1");
		sql.append(Constants.NO_PUBLISH,"AND REVISIT_STATUS = ?");//未发布状态
		sql.append(data.getString("SYN_TIME_START"),"AND SYN_TIME >= ?");//同步开始时间
		sql.append(data.getString("SYN_TIME_END"),"AND SYN_TIME <= ?");//同步结束时间
		sql.append(data.getString("TASK_NAME"),"AND TASK_NAME = ?");//任务名称
		sql.appendLike(data.getString("DEMAND_SIDE"),"AND DEMAND_SIDE LIKE ?");//需求方
		JSONObject obj = new JSONObject();
		try {
			obj = getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		} catch (SQLException e) {
			logger.error("查询数量出错:" + e.getMessage(),e);
		}
		return EasyResult.ok(obj);
	}
	/**
	 * 指派获取任务数量--根据任务名称和需求方，查出可分配的数量
	 */
	public JSONObject actionForGetAppointNum() {
		JSONObject data = getJSONObject("appoint");
		EasySQL sql = new EasySQL("SELECT COUNT(1) AS NUM FROM CC_PLANNING WHERE 1=1");
		sql.append(Constants.NO_PUBLISH,"AND REVISIT_STATUS = ?");//未发布状态
		sql.append(data.getString("TASK_NAME"),"AND TASK_NAME = ?");//任务名称
		sql.append(data.getString("SYN_TIME_START"),"AND SYN_TIME >= ?");//同步开始时间
		sql.append(data.getString("SYN_TIME_END"),"AND SYN_TIME <= ?");//同步结束时间
		sql.append(data.getString("DEMAND_SIDE"),"AND DEMAND_SIDE = ?");//需求方
		
		JSONObject obj = new JSONObject();
		try {
			obj = getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		} catch (SQLException e) {
			logger.error("查询数量出错:" + e.getMessage(),e);
		}
		return EasyResult.ok(obj);
	}
	/**
	 * 回收获取任务数量
	 * @throws IOException 
	 * @throws UnsupportedEncodingException 
	 */
	public JSONObject actionForGetRecycleNum() {
		JSONObject data = getJSONObject("recycle");
		
		EasySQL sql = new EasySQL("SELECT COUNT(1) AS NUM FROM CC_PLANNING WHERE 1=1");
		sql.append(data.getString("CONVERSION_TYPE"),"AND CONVERSION_TYPE = ?");//折算类型
		sql.append(data.getString("SYN_TIME_START"),"AND SYN_TIME >= ?");//同步开始时间
		sql.append(data.getString("SYN_TIME_END"),"AND SYN_TIME <= ?");//同步结束时间
		sql.append(data.getString("PUB_TIME_START"),"AND PUB_TIME >= ?");//发布开始时间
		sql.append(data.getString("PUB_TIME_END"),"AND PUB_TIME <= ?");//发布结束时间
		sql.append(data.getString("REVISIT_RESULT"),"AND REVISIT_RESULT = ?");//回访结果
		sql.append(data.getString("TASK_NAME"),"AND TASK_NAME = ?");//任务名称
		sql.append(Constants.IS_DONE,"AND IS_DONE != ?");
		if("".equals(data.getString("REVISIT_STATUS"))) {
			String status = "'2','3','4'";
			sql.append("AND REVISIT_STATUS IN ( " + status + " )");//回访状态
		}else {
			sql.append(data.getString("REVISIT_STATUS"),"AND REVISIT_STATUS = ?");//回访状态
		}
		if(!"".equals(data.getString("USER_ACC"))) {//账号
			String[] userAcc = data.getString("USER_ACC").split(",");
			sql.append("AND RECEIVE_ACC IN (" + StringUtil.joinSqlStr(userAcc) + ")");
		}
		sql.appendLike(data.getString("DEMAND_SIDE"),"AND DEMAND_SIDE LIKE ?");//需求方
		
		JSONObject obj = new JSONObject();
		try {
			obj = getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		} catch (SQLException e) {
			logger.error("查询数量出错:" + e.getMessage(),e);
		}
		return EasyResult.ok(obj);
	}
	
	//问卷结果跳转 查看
	public JSONObject actionForJumpResult() {
		String pId = getJsonPara("pId");
		if(StringUtils.isBlank(pId)) {
			return EasyResult.error();
		}
		try {
			JSONObject questionData = getQuery().queryForRow("SELECT * FROM CC_PLANNING WHERE ID = ?", new Object[] {pId}, new JSONMapperImpl());	
			//问卷id
			String questionId = questionData.getString("QUESTION_ID");
			//客户手机号
			String customerPhone = questionData.getString("CUSTOMER_PHONE");
			//拼接param
			String param = Constants.WJX_GET_ANSER_URL +"?appkey=" + Constants.DATA_APP_KEY + "&questionId=" + questionId + "&mobile=" + customerPhone;
			apiLogger.info("请求接口:" + param);
			HttpResp result = HttpUtil.post(param,null,HttpUtil.ENCODE_UTF8);
			apiLogger.info("接口返回：" + result.getResult());
			if(result!=null && StringUtils.isNotBlank(result.getResult())){
				JSONObject resp = JSONObject.parseObject(result.getResult());
				if(resp!=null&&resp.size()>0&&resp.getJSONArray("data").size()>0) {	
					JSONObject data = resp.getJSONArray("data").getJSONObject(0);
					if(data.size()>0&&StringUtils.isNotBlank(data.getString("jid"))) {
						//时间戳,单位秒
						long timestamp = EasyCalendar.newInstance().getTimeInMillis()/1000;
						String joinActivityId = resp.getJSONArray("data").getJSONObject(0).getString("jid");
						//接口签名sign=sha1(appId+appKey+subUser+mobile+email+roleId+ts)
						String sign = PwdUtils.stringToSHA1(Constants.WJX_APP_ID+Constants.WJX_APP_KEY+Constants.WJX_CHECK_ACC+timestamp);
						//跳转的URL
						String jumpUrl = Constants.WJX_QUESTION_RESULT_URL + "?joinactivityid="+joinActivityId+"&activity="+questionId;
						//拼接SSO请求URL
						String url = Constants.WJX_SSO_CORL_URL + "?appid="+Constants.WJX_APP_ID+"&subuser="+Constants.WJX_CHECK_ACC+"&mobile=&email=&roleId=&ts="+timestamp+"&sign="+sign+"&url="+URLEncoder.encode(jumpUrl,"UTF-8");

						return EasyResult.ok(url);
					}else {
						return EasyResult.error(500,"请求问卷答案失败，原因：返回的结果集中joinActivity为空");
					}
				}else {
					return EasyResult.error(500,"请求问卷答案失败，原因：返回的结果集为空");
				}
			}else {
				return EasyResult.error(500,"请求问卷答案失败，原因：请求不到结果");
			}
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "error：" + e.getMessage(),e);
		}
		return EasyResult.error();
	}
	
	//问卷结果操作跳转  编辑
	public JSONObject actionForJumpResultHandle() {
		String pId = getJsonPara("pId");
		String type = getJsonPara("type");
		String resultId = getJsonPara("resultId");
		if(StringUtils.isBlank(pId)) {
			return EasyResult.error();
		}
		try {
			//UserUtil.getUser(getRequest()).getr
			JSONObject questionData = getQuery().queryForRow("SELECT * FROM CC_PLANNING WHERE ID = ?", new Object[] {pId}, new JSONMapperImpl());	
			//问卷id
			String questionId = questionData.getString("QUESTION_ID");
			//客户手机号
			String customerPhone = questionData.getString("CUSTOMER_PHONE");
			
			
			String dataSource=questionData.getString("DATA_SOURCE");
			if("3".equals(dataSource)){
				String url="/planning/pages/planning/revisit-handle-toHandle3.jsp?ID="+pId+"&resultId="+resultId;
				return EasyResult.ok(url);
			}
			if("2".equals(dataSource)){
				String url="/planning/pages/planning/revisit-handle-toHandle2.jsp?ID="+pId+"&resultId="+resultId;
				return EasyResult.ok(url);
			}else{
				//拼接param
				String param = Constants.WJX_GET_ANSER_URL +"?appkey=" + Constants.DATA_APP_KEY + "&questionId=" + questionId + "&mobile=" + customerPhone;
				apiLogger.info("请求接口:" + param);
				HttpResp result = HttpUtil.post(param,null,HttpUtil.ENCODE_UTF8);
				apiLogger.info("接口返回：" + result.getResult());
				if(result!=null && StringUtils.isNotBlank(result.getResult())){
					JSONObject resp = JSONObject.parseObject(result.getResult());
					if(resp!=null&&resp.size()>0&&resp.getJSONArray("data").size()>0) {	
						JSONObject data = resp.getJSONArray("data").getJSONObject(0);
						if(data.size()>0&&StringUtils.isNotBlank(data.getString("jid"))) {
							//时间戳,单位秒
							long timestamp = EasyCalendar.newInstance().getTimeInMillis()/1000;
							String joinActivityId = resp.getJSONArray("data").getJSONObject(0).getString("jid");
							//接口签名sign=sha1(appId+appKey+subUser+mobile+email+roleId+ts)
							String sign = PwdUtils.stringToSHA1(Constants.WJX_APP_ID+Constants.WJX_APP_KEY+Constants.WJX_ADMIN_ACC+timestamp);
							//跳转的URL
							String jumpUrl = Constants.WJX_QUESTION_RESULT_HANDLE_URL + "?activity="+questionId+"&joinactivityid="+joinActivityId;
							//拼接SSO请求URL
							String url = Constants.WJX_SSO_CORL_URL + "?appid="+Constants.WJX_APP_ID+"&subuser="+Constants.WJX_ADMIN_ACC+"&mobile=&email=&roleId=&ts="+timestamp+"&sign="+sign + "&url=" + URLEncoder.encode(jumpUrl, "UTF-8");
							
							return EasyResult.ok(url);
						}else {
							return EasyResult.error(500,"请求问卷答案失败，原因：返回的结果集中joinActivity为空");
						}
					}else {
						return EasyResult.error(500,"请求问卷答案失败，原因：返回的结果集为空");
					}
				}else {
					return EasyResult.error(500,"请求问卷答案失败，原因：请求不到结果");
				}
			}
			
			
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "error：" + e.getMessage(),e);
		}
		return EasyResult.error();
	}

	public EasyResult actionForCheckNumber() {
		Logger strategyLogger = CommLogger.getCommLogger("strategy");
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		JSONObject params = new JSONObject();
		JSONObject param = getJSONObject();
		obj.put("command", "getStrategyIdByPhoneAndChannelId");
		params.put("phone", param.getString("phone"));
		params.put("channelId", "2");
		params.put("type", "0");
		obj.put("params", params);
		try {
			strategyLogger.info("检查号码是否在免打扰人群中，参数：" + obj.toJSONString());
			IService service = ServiceContext.getService("ACTIVE-SERVICE-CROWD-INTEFACE");
			JSONObject results = service.invoke(obj);
			strategyLogger.info("检查号码是否在免打扰人群中，结果：" + results.toJSONString());
			if (results.getString("respCode").equals(GWConstants.RET_CODE_SUCCESS) && results.getJSONObject("respData").getBoolean("isExist")) {
				EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.ORDER_DS);
				JSONArray data = results.getJSONObject("respData").getJSONArray("data");
				EasySQL sql = new EasySQL("SELECT SCRIPT_CONTENT FROM C_NO_AS_DND_STRATEGY WHERE 1 = 1 ");
				sql.append(" AND STRATEGY_ID IN (");
				for (int i = 0; i < data.size(); i++) {
					if (i != 0) {
						sql.append(",");
					}
					sql.append(data.getJSONObject(i).getString("STRATEGY_ID"), "?");
				}
				sql.append(") ");
				strategyLogger.info("检查号码是否在免打扰人群中，sql：" + sql.getSQL() + " 参数：" + Arrays.toString(sql.getParams()));
				List<EasyRow> easyRows = query.queryForList(sql.getSQL(), sql.getParams());
				strategyLogger.info("检查号码是否在免打扰人群中，结果：" + JSON.toJSONString(easyRows));
				JSONArray jsonArray = new JSONArray();
				for (EasyRow easyRow : easyRows) {
					jsonArray.add(easyRow.getColumnValue("SCRIPT_CONTENT"));
				}
				result.put("result", jsonArray);
				obj.put("command", "saveRemindRecord");
				params.put("CHANNEL_NAME", "企划通回访呼出");
				params.put("STRATEGY_ID", data.getJSONObject(0).getString("STRATEGY_ID"));
				params.put("STRATEGY_NAME", data.getJSONObject(0).getString("STRATEGY_NAME"));
				params.put("CROWD_PACK_ID", data.getJSONObject(0).getString("CROWD_PACK_ID"));
				params.put("CROWD_NAME", data.getJSONObject(0).getString("CROWD_NAME"));
				params.put("CREATE_ACC", UserUtil.getUser(getRequest()).getUserAcc());
				obj.put("params", params);
				strategyLogger.info("记录免打扰提醒表，参数：" + obj.toJSONString());
				IService service1 = ServiceContext.getService("ACTIVE-SERVICE-CROWD-INTEFACE");
				JSONObject results1 = service1.invoke(obj);
				strategyLogger.info("记录免打扰提醒表，结果：" + results1.toJSONString());
			} else {
				return EasyResult.fail();
			}
		} catch (Exception e) {
			strategyLogger.error(e);
		}
		return EasyResult.ok(result);
	}

	public JSONObject actionForUpdateFollowState() {
		String pId = getJsonPara("pId");
		if (StringUtils.isEmpty(pId)) {
			return EasyResult.fail("参数不能为空" );
		}
		EasyQuery query = getQuery();
		try {
			EasyRecord upRecord = new EasyRecord("CC_PLANNING","ID").setPrimaryValues(pId);
			upRecord.set("FOLLOW_STATE",Constants.FOLLOWING_UP);

			query.update(upRecord);
		} catch ( Exception e ) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "error：" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok();
	}
	
	/*
	 * public JSONObject actionForTest(){ String url =
	 * "http://localhost:8080/uinterface/Receive.do";
	 * 
	 * JSONObject data = new JSONObject(); data.put("messageId",
	 * "84629417184259862098476");//编号 data.put("mipCode", "test");//mip账号
	 * data.put("cssState", "test");//单据状态 data.put("cssFeedbackTime",
	 * "test");//事业部反馈时间 data.put("csseffectiveness", "test");//有效性
	 * data.put("cssUpdateName", "test");//修改人 data.put("ccUpdateTime",
	 * "test");//修改时间 data.put("cssAccept", "test");//是否接受
	 * data.put("cssResponsibleBody", "test");//责任主体 data.put("cssFlowId",
	 * "test");//CSS内部流程处理单号 data.put("cssDisagreeContent", "test");//不接受原因
	 * data.put("cssPlannedTime", "test");//整改后计划上市时间 data.put("cssImprovement",
	 * "test");//如何改进 data.put("cssOtherContent", "test");//其他说明
	 * data.put("qmsCountersigner", "test"); data.put("qmsQuestionUrl", "test");
	 * data.put("qmsOrderNo", "test"); data.put("qmsRectifyStatus", "test");
	 * 
	 * JSONObject json = new JSONObject(); json.put("sender", "advice");
	 * json.put("password", "YQ_85521717"); json.put("serialId", "test");
	 * json.put("command", "UPDATE_MESSAGE_INFO"); json.put("serviceId",
	 * "CSSGW-ADVICE"); json.put("data", data); json.put("timestamp",
	 * DateUtil.getCurrentDateStr()); String sign =
	 * SecurityUtil.encryptMsgByMD5(json.getString("sender")+json.getString(
	 * "password")+json.getString("timestamp")+json.getString("serialId"));
	 * json.put("signature", sign); HttpResp resp =
	 * HttpUtil.sendPost(url,json.toJSONString());
	 * 
	 * return EasyResult.ok(resp.getResult());
	 * 
	 * // try { // JSONObject obj = new JSONObject(); // //IService service =
	 * ServiceContext.getService("PLANNING-INTEFACE"); // obj =
	 * service.invoke(json); // System.out.println(obj); // } catch
	 * (ServiceException e) { // // } }
	 * 
	 * protected EasyQuery getQuery(){ return EasyQuery.getQuery(Constants.APP_NAME,
	 * Constants.ORDER_DS); }
	 * 
	 * public JSONObject actionForTest2(){ EasyRecord record = new
	 * EasyRecord("CC_MDEIA_WORD_COPY"); record.set("WORD_ID",
	 * "83790937386301586977507"); record.set("CHANNEL_CUST_INFO", "test");
	 * record.set("CUST_CODE", "test2"); record.set("CUST_NAME", "test3");
	 * record.set("CREATE_TIME", "test4"); EasyQuery query = getQuery(); try {
	 * query.save(record); } catch (SQLException e) {
	 * logger.error(CommonUtil.getClassNameAndMethod(this) + e.getMessage(),e);
	 * return EasyResult.error(); } return EasyResult.ok(); }
	 */
	
	//测试类
//	public JSONObject actionForTest() {
//		String url = "http://localhost:8080/uinterface/Receive.do";
//		JSONObject json = new JSONObject();
//		json.put("sender", Constants.APP_NAME);
//		json.put("password", "YQ_85521717");
//		json.put("serialId", "a");
//		json.put("command", "PLANNING_SYN");
//		json.put("serviceId", "PLANNING-INTEFACE");
//		JSONArray data = new JSONArray();
//		for(int i=0;i<3;i++) {
//			JSONObject obj = new JSONObject();
//			obj.put("Id", "TEST" + i);
//			obj.put("taskId", "TEST" + i);
//			obj.put("taskName", "TEST" + i);
//			obj.put("questionId", "TEST" + i);
//			obj.put("questionUrl", "TEST" + i);
//			obj.put("clientName", "TEST" + i);
//			obj.put("clientAcc", "TEST" + i);
//			obj.put("demandSide", "TEST" + i);
//			obj.put("demandSideDept", "TEST" + i);
//			obj.put("orgCode", "TEST" + i);
//			obj.put("customerName", "TEST" + i);
//			obj.put("customePhone", "TEST" + i);
//			obj.put("synTime", EasyCalendar.newInstance().getDateTime("-"));
//			data.add(obj);
//		}
//		json.put("data", data);
//		json.put("timestamp", DateUtil.getCurrentDateStr());
//		String sign = SecurityUtil.encryptMsgByMD5(json.getString("sender")+json.getString("password")+json.getString("timestamp")+json.getString("serialId"));
//		json.put("signature", sign);
//		//HttpResp resp = HttpUtil.post(url,json.toJSONString(),GWConstants.ENCODE_UTF8);
//		
//		
//		try {
//			JSONObject obj = new JSONObject();
//			IService service = ServiceContext.getService("PLANNING-INTEFACE");
//			obj = service.invoke(json);
//			System.out.println(obj);
//		} catch (ServiceException e) {
//			
//		}
//		return null;
//	}


	/**
	 * 导出结果
	 * @throws SQLException
	 */
	public void actionForExportPlanningReport() {
		HttpServletRequest request = this.getRequest();
		JSONObject paramObj = requestToJsonObject(request);
		FileOutputStream fileOutputStream = null;
		try {
			JSONObject conditionJson = RevisitService.getDataFilterConditions(request);
			if(conditionJson != null){
				paramObj.put("DATA_SOURCE", conditionJson.getString("dataSource"));
				paramObj.put("BUSINESS_TYPE", conditionJson.getString("businessType"));
			}
			logger.info("企划通回访报表--导出结果，参数：" + paramObj.toJSONString());
			RevisitService revisitService = new RevisitService();

			EasySQL sql = revisitService.getPlanningReportSql(paramObj);
			getQuery().setMaxRow(99999);
			logger.info("企划通回访报表--导出结果，sql：" + sql.getSQL() + " 参数：" + JSONObject.toJSONString(paramObj));
			List<JSONObject> data = getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

			//固定数据导出
			ExcelExportQuery exportData = new ExcelExportQueryFixedList() {

				@Override
				public List<Object> formatRow(JSONObject row) {
					List<Object> list = new ArrayList<Object>();
					list.add(row.get("TASK_ID"));
					list.add(row.get("TASK_NAME"));
					list.add(row.get("DEMAND_SIDE"));
					list.add(row.get("DEMAND_SIDE_DEPT"));
					list.add(row.get("SYN_NUM"));
					list.add(row.get("SUCCESS_NUM"));
					list.add(row.get("NOBODY_NUM"));
					list.add(row.get("NO_RESPONSE_NUM"));
					list.add(row.get("REFUSE_NUM"));
					list.add(row.get("NUMBER_ERROR_NUM"));
					list.add(row.get("EXIST_NUM"));
					list.add(row.get("BUSI_NUM"));
					list.add(row.get("NOT_CURR_NUM"));
					list.add(row.get("SHUTDWON_NUM"));
					list.add(row.get("OTHER_NUM"));
					list.add(row.get("VISIT_NUM"));
					list.add(row.get("VISIT_RATE"));
					list.add(row.get("ANSWER_RATE"));
					return list;
				}

				@Override
				public int getTotalCount() {
					return fixedList.size();
				}

				@Override
				public List<JSONObject> fixedData() {
					List<JSONObject> list = new ArrayList<>();
					for (JSONObject datum : data) {
						JSONObject map = new JSONObject();
						map.put("TASK_ID", datum.getString("TASK_ID"));
						map.put("TASK_NAME", datum.getString("TASK_NAME"));
						map.put("DEMAND_SIDE", datum.getString("DEMAND_SIDE"));
						map.put("DEMAND_SIDE_DEPT", datum.getString("DEMAND_SIDE_DEPT"));
						map.put("SYN_NUM", datum.getString("SYN_NUM"));
						map.put("SUCCESS_NUM", datum.getString("SUCCESS_NUM"));
						map.put("NOBODY_NUM", datum.getString("NOBODY_NUM"));
						map.put("NO_RESPONSE_NUM", datum.getString("NO_RESPONSE_NUM"));
						map.put("REFUSE_NUM", datum.getString("REFUSE_NUM"));
						map.put("NUMBER_ERROR_NUM", datum.getString("NUMBER_ERROR_NUM"));
						map.put("EXIST_NUM", datum.getString("EXIST_NUM"));
						map.put("BUSI_NUM", datum.getString("BUSI_NUM"));
						map.put("NOT_CURR_NUM", datum.getString("NOT_CURR_NUM"));
						map.put("SHUTDWON_NUM", datum.getString("SHUTDWON_NUM"));
						map.put("OTHER_NUM", datum.getString("OTHER_NUM"));

						revisitService.getPlanningReportRate(datum);
						map.put("VISIT_NUM", datum.getString("VISIT_NUM"));
						map.put("VISIT_RATE", datum.getString("VISIT_RATE"));
						map.put("ANSWER_RATE", datum.getString("ANSWER_RATE"));
						list.add(map);
					}
					return list;
				}
			};

			// 组装表头
			File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
			fileOutputStream = new FileOutputStream(file);

			ExcelExport execlExport = new ExcelExport(exportData , fileOutputStream) {
				@Override
				public List<List<String>> getHeader() {
					List<List<String>> list = new ArrayList<List<String>>();
					String title = "回访结果";

					List<String> head0 = new ArrayList<String>();
					head0.add("人群包ID");
					List<String> head1 = new ArrayList<String>();
					head1.add("任务包名称");
					List<String> head2 = new ArrayList<String>();
					head2.add("需求方");
					List<String> head3 = new ArrayList<String>();
					head3.add("需求方岗位");
					List<String> head4 = new ArrayList<String>();
					head4.add("同步数量");

					List<String> head5_0 = new ArrayList<String>();
					head5_0.add(title);
					head5_0.add("有效回访");
					List<String> head5_1 = new ArrayList<String>();
					head5_1.add(title);
					head5_1.add("无人接听");
					List<String> head5_2 = new ArrayList<String>();
					head5_2.add(title);
					head5_2.add("无法接通");
					List<String> head5_3 = new ArrayList<String>();
					head5_3.add(title);
					head5_3.add("用户拒访");
					List<String> head5_4 = new ArrayList<String>();
					head5_4.add(title);
					head5_4.add("号码错误");
					List<String> head5_5 = new ArrayList<String>();
					head5_5.add(title);
					head5_5.add("电话空号");
					List<String> head5_6 = new ArrayList<String>();
					head5_6.add(title);
					head5_6.add("不方便接听");
					List<String> head5_7 = new ArrayList<String>();
					head5_7.add(title);
					head5_7.add("非当事人、不明情况");
					List<String> head5_8 = new ArrayList<String>();
					head5_8.add(title);
					head5_8.add("停机");
					List<String> head5_9 = new ArrayList<String>();
					head5_9.add(title);
					head5_9.add("其他");

					List<String> head6 = new ArrayList<String>();
					head6.add("已回访");
					List<String> head7 = new ArrayList<String>();
					head7.add("有效率");
					List<String> head8 = new ArrayList<String>();
					head8.add("接通率");

					list.add(head0);
					list.add(head1);
					list.add(head2);
					list.add(head3);
					list.add(head4);
					list.add(head5_0);
					list.add(head5_1);
					list.add(head5_2);
					list.add(head5_3);
					list.add(head5_4);
					list.add(head5_5);
					list.add(head5_6);
					list.add(head5_7);
					list.add(head5_8);
					list.add(head5_9);
					list.add(head6);
					list.add(head7);
					list.add(head8);
					return list;
				}
			};

			execlExport.executeAndClose();
			renderFile(file, "企划通回访报表列表.xlsx");
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}finally {
			if(fileOutputStream!= null) {
                try {
                    fileOutputStream.close();
                } catch (IOException e) {
					logger.error(e.getMessage(),e);
                }
            }
		}
	}

	public void actionForExportRevisitResult() {
		HttpServletRequest request = this.getRequest();
		JSONObject param = requestToJsonObject(request);
		logger.info("导出回访结果参数：" + param.toJSONString());
		FileOutputStream fileOutputStream = null;
		try {
			int checkRole = StringUtil.getInt(param.getString("check"));
			EasySQL sql = new EasySQL("SELECT T1.*,T2.TASK_ID,T2.CUSTOMER_NAME,T2.CUSTOMER_PHONE,T2.TASK_NAME,T2.DEMAND_SIDE,T2.QUESTION_URL,T2.QUESTION_ID,t2.DATA_SOURCE,t2.CALL_STATUS FROM CC_PLANNING_RESULT T1 ");
			sql.append("LEFT JOIN CC_PLANNING T2 ON T1.C_PLANNING_ID = T2.ID WHERE 1=1");
			sql.append(param.getString("CREATE_ACC")," AND T1.CREATE_ACC = ?");
			sql.append(param.getString("CUSTOMER_PHONE")," AND T2.CUSTOMER_PHONE = ?");
			sql.append(param.getString("REVISIT_RESULT")," AND T1.REVISIT_RESULT = ?");
			sql.append(param.getString("REV_TIME_START"),"AND T1.CREATE_TIME >= ?");
			if(checkRole <= 0) {
				sql.append(UserUtil.getUser(request).getUserAcc(),"AND T1.CREATE_ACC = ?");
			}
			sql.append(param.getString("REV_TIME_END"),"AND T1.CREATE_TIME <= ?");

			JSONObject conditionJson = RevisitService.getDataFilterConditions(request);
			if(conditionJson != null){
				sql.append(conditionJson.getString("dataSource"), "AND T2.DATA_SOURCE = ? ");
				sql.append(conditionJson.getString("businessType"), "AND T2.BUSINESS_TYPE = ? ");
			}else{
				sql.append(param.getString("DATA_SOURCE"),"AND T2.DATA_SOURCE = ?");
			}

			sql.appendLike(param.getString("TASK_NAME"), " AND T2.TASK_NAME LIKE ? ");
			sql.append(param.getString("TASK_ID"),"AND T2.TASK_ID = ?");
			sql.append("ORDER BY T1.CREATE_TIME DESC");
			List<JSONObject> data = getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

			//获取回访结果和通话状态字典表
			String epCode = OpenApiUserUtil.getUser(request).getEpCode();
			epCode = StringUtils.isNotBlank(epCode)?epCode: "001";
			Map<String, Object> revisitResultMap =DictCache.getMapAllDictListByGroupCode(epCode, "PLANNING_REVISIT_RESULT");
			Map<String, Object> revisitResultMap1 =DictCache.getMapAllDictListByGroupCode(epCode, "PLANNING_REVISIT_RESULT1");
			revisitResultMap.putAll(revisitResultMap1);
			Map<String, Object> callStatusMap =DictCache.getMapAllDictListByGroupCode(epCode, "PLANNING_CALL_STATUS");

			ExcelExportQuery exportData = new ExcelExportQueryFixedList() {

				@Override
				public List<Object> formatRow(JSONObject row) {
					List<Object> list = new ArrayList<Object>();
					list.add(row.get("REVISIT_RESULT"));
					list.add(row.get("CREATE_ACC"));
					list.add(row.get("CREATE_TIME"));
					list.add(row.get("CUSTOMER_NAME"));
					list.add(row.get("CUSTOMER_PHONE"));
					list.add(row.get("TASK_NAME"));
					list.add(row.get("CALL_STATUS"));
					list.add(row.get("TASK_ID"));
					list.add(row.get("DEMAND_SIDE"));
					return list;
				}

				@Override
				public int getTotalCount() {
					return fixedList.size();
				}

				@Override
				public List<JSONObject> fixedData() {
					List<JSONObject> list = new ArrayList<>();
					for (JSONObject jsonObject : data) {
						JSONObject map = new JSONObject();
						map.put("REVISIT_RESULT", revisitResultMap.get(jsonObject.getString("REVISIT_RESULT")));
						map.put("CREATE_ACC", jsonObject.getString("CREATE_ACC"));
						map.put("CREATE_TIME", jsonObject.getString("CREATE_TIME"));
						map.put("CUSTOMER_NAME", jsonObject.getString("CUSTOMER_NAME"));
						String phone = PrivacyUtil.desensitization(jsonObject.getString("CUSTOMER_PHONE"), PrivacyUtil.phone);
						map.put("CUSTOMER_PHONE", phone);
						map.put("TASK_NAME", jsonObject.getString("TASK_NAME"));
						map.put("CALL_STATUS", callStatusMap.get(jsonObject.getString("CALL_STATUS")));
						map.put("TASK_ID", jsonObject.getString("TASK_ID"));
						map.put("DEMAND_SIDE", jsonObject.getString("DEMAND_SIDE"));
						list.add(map);
					}
					return list;
				}
			};

			// 组装表头
			File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
			fileOutputStream = new FileOutputStream(file);

			ExcelExport execlExport = new ExcelExport(exportData , fileOutputStream) {
				@Override
				public List<List<String>> getHeader() {
					List<List<String>> list = new ArrayList<List<String>>();
					List<String> head0 = new ArrayList<String>();
					head0.add("回访结果");
					List<String> head1 = new ArrayList<String>();
					head1.add("回访人");
					List<String> head2 = new ArrayList<String>();
					head2.add("回访时间");
					List<String> head3 = new ArrayList<String>();
					head3.add("用户姓名");
					List<String> head4 = new ArrayList<String>();
					head4.add("用户号码");
					List<String> head5 = new ArrayList<String>();
					head5.add("任务包");
					List<String> head6 = new ArrayList<String>();
					head6.add("通话状态");
					List<String> head7 = new ArrayList<String>();
					head7.add("人群包ID");
					List<String> head8 = new ArrayList<String>();
					head8.add("需求方");


					list.add(head0);
					list.add(head1);
					list.add(head2);
					list.add(head3);
					list.add(head4);
					list.add(head5);
					list.add(head6);
					list.add(head7);
					list.add(head8);
					return list;
				}
			};

			execlExport.executeAndClose();
			renderFile(file, "企划通回访结果列表.xlsx");


		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		} finally {
			if(fileOutputStream!= null) {
				try {
					fileOutputStream.close();
				} catch (IOException e) {
					logger.error(e.getMessage(),e);
				}
			}
		}
	}
}
