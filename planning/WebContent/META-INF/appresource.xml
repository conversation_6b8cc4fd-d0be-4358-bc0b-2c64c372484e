<?xml version="1.0" encoding="UTF-8"?>
<!-- 
	id:必须全局唯一，命名方式：应用名称+菜单名称
	name：菜单或功能名称
	type：资源类型，1：应用 ，2：菜单，3：功能，9: 其它
	state：状态，0:正常，缺省 ，1：暂停
	portal:所属应用，不填写缺省为通用portal，根据菜单的归属，可以填写具体的所属的portal，例如：my_portal
	index:排序，菜单的显示按照升序进行排序。
	icon：菜单图标
 -->
<resources>
   <resource id="neworder" name="工单与回访" url="" type="2" portal="" state="0" order="4" index = "5">
		<resource id="neworder_planning" name="企划通" type="2" icon="" portal="" state="0" url="" index="1">
			<!--<resource id="planning_list" name="回访发布" type="2" icon="" portal="" state="0" url="/planning/pages/planning/revisit-list.jsp" index="1">-->
			<resource id="planning_list" name="回访发布" type="2" icon="" portal="" state="0" url="/planning/pages/planning/revisit-list.jsp" index="1">
		   		<resource id="planning_publish" name="发布"   url="" type="3" portal="" icon="" state="0" index="11"/>
		   		<resource id="planning_appoit" name="指派" url="" type="3" portal="" icon="" state="0" index="12"/>
		   		<resource id="planning_recycle" name="回收"   url="" type="3" portal="" icon="" state="0" index="13" />
		    	<resource id="planning_revisit_monitor" name="班长权限"   url="" type="3" portal="" icon="" state="0" index="14" />
		    </resource>
		    <resource id="planning_handle-list" name="回访处理"  type="2" portal="" icon="" state="0" url="/planning/pages/planning/revisit-handle-list.jsp" index="2">
		   		<resource id="planning_visit_publish" name="申请权限"   url="" type="3" portal="" icon="" state="0" index="20"/>
		   		<resource id="planning_handle-tohandle" name="处理权限"   url="" type="3" portal="" icon="" state="0" index="21"/>
		   		<resource id="planning_handle_search" name="查询权限"   url="" type="3" portal="" icon="" state="0" index="22"/>
		    </resource>
		    <resource id="planning_publish_list" name="回访发布记录" type="2" icon="" portal="" state="0" url="/planning/pages/planning/revisit-publish-list.jsp" index="3"/>
		    <resource id="planning_result_list" name="回访结果查询" type="2" icon="" portal="" state="0" url="/planning/pages/planning/revisit-result-list.jsp" index="4">
		    	<resource id="planning_result_tohandle" name="操作权限"   url="" type="3" portal="" icon="" state="0" index="41"/>
		    	<resource id="planning_result_search" name="查询权限"   url="" type="3" portal="" icon="" state="0" index="42"/>
		    </resource>
		    <resource id="planning_report_list" name="回访报表"  type="2" icon="" portal="" state="0" url="/planning/pages/report/planning-report.jsp" index="5"/>
		</resource>
	</resource>
</resources>
