	<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>企划通回访处理</title>
			<link href="/easitline-static/lib/layui/css/layui.css" rel="stylesheet">

</EasyTag:override>
<EasyTag:override name="content">
       <form  autocomplete="off" data-mars="revisit.getRevisitInfo" method="post" name="toHandleForm" class="form-inline" id="toHandleForm" onsubmit="return false" data-mars-prefix="toHandle.">
             	<input type="hidden" name="toHandle.pId" value="${param.ID}">
            	<input type="hidden" id="dataSource" name="toHandle.DATA_SOURCE" >
            	<input type="hidden" id="resultId" name="resultId"  value="${param.resultId}" >

        <div class="row">
			<div class="col-xs-4">
					<div class="ibox-title clearfix">
							 <div class="form-group">
							 	<div class="input-group input-group-sm" style="width: 300px;">
							 		<span class="input-group-addon" style="width: 80px;">用户姓名</span>
							 		<input class="form-control input-sm" type="text" id="userName" readonly="readonly">
								</div>
								<div class="input-group input-group-sm" style="width: 300px;">
									<span class="input-group-addon" style="width: 80px;">联系方式</span>
							 		<input class="form-control input-sm" type="text" id="userPhone" readonly="readonly">
							 		<span class="input-group-addon" style="cursor: pointer;" onclick="Call()"><i class="glyphicon glyphicon-earphone"></i></span>
								</div>

						     	<div class="input-group input-group-sm " style="width: 300px;">
							    	<span class="input-group-addon" style="width: 80px;">处理结果</span>
							    	<input class="form-control input-sm hide" type="text" name="toHandle.REVISIT_RESULT_TEXT" id="resultText" >
									<select class="form-control input-sm" name="toHandle.REVISIT_RESULT" id="result" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PLANNING_KF_PROCESSING_RESULT')"  data-rules="required">
		                                 <option value="">请选择</option>
		                            </select>
								</div>

						     	<div class="input-group input-group-sm " style="width: 300px;">
									<span class="input-group-addon" style="width: 80px;">解决方案</span>
							 		<select class="form-control input-sm" name="toHandle.SOLUTION" id="solution" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PLANNING_KF_SOLUTION')" onchange="toHandle.changeResult(this.value)">
										<option value="">请选择</option>
		                            </select>
								</div>

								<div class="input-group input-group-sm" style="width: 300px;">
									<span class="input-group-addon" style="width: 80px;">跟进单号</span>
							 		<input class="form-control input-sm" type="text" id="relatedOrderId"  name="toHandle.RELATED_ORDER_ID"  >
								</div>
								<div class="input-group input-group-sm  " style="width: 200px;">
							    	<span class="input-group-addon" style="width: 80px;">是否办结</span>
									<select class="form-control input-sm" name="toHandle.IS_DONE" id="isDone" data-rules="required">
										<option value="">请选择</option>
		                                <option value="1">是</option>
		                                <option value="2">否</option>
		                            </select>
								</div>
									<div class="input-group input-group-sm" style="width: 300px;">
									<span class="input-group-addon" style="width: 80px;">备注</span>
                                        <textarea class="form-control input-sm" id="remarks" name="toHandle.REMARKS" style="height: 90px"></textarea>
								</div>
								<!-- <div class="input-group input-group-sm" style="width: 300px;">
									<span class="input-group-addon" style="width: 80px;">备注</span>
							 		<input class="form-control input-sm" type="text" id="relatedOrderId"  name="toHandle.RELATED_ORDER_ID"  >
								</div> -->
								 <div class="input-group input-group-sm" style="width: 300px;    text-align: center;">
									<button type="button" class="btn btn-sm btn-success" onclick="toHandle.ajaxSubmit()">确认</button>
								 </div>
		             		 </div>
	             	    </div>
			</div>
			<div class="col-xs-8">
			<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
			<ul class="layui-tab-title" >
				<li class="layui-this"  onclick="showDiv('1')">
					<span >用户信息</span>
				</li>
				<li class="layui-this" onclick="showDiv('2')">
					<span>历史服务单列表</span>
				</li>
				<!-- <li class="layui-this" onclick="showDiv('3')">
					<span>历史回访列表</span>
				</li> -->
			</ul>
			<div class="layui -tab-content ">
				<div class="layui-tab-item layui-show" id="showDiv1"><div><iframe id="ifm" src="" style="height: 580px;width: 100%"></iframe></div></div>
				<div class="layui-tab-item " id="showDiv2"><div><iframe id="hisOrder" src="" style="height: 580px;width: 100%"></iframe></div></div>
				<div class="layui-tab-item " id="showDiv3"><div><iframe id="hisRevisit" src="" style="height: 580px;width: 100%"></iframe></div></div>
			</div>
			</div>


			</div>
		</div>

        </form>

</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/layui/layui.js"></script>
	<script type = "text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	<script type="text/javascript">
		jQuery.namespace("toHandle");
		var _custPhone = "";//客户手机号
		var _custName = "";//客户姓名
		var _coefficient = "";//折算系数
		var _orgCode = "";//事业部
		var _orgCode = "";//事业部
		$(function(){
			layui.use(["element", "jquery"], function () {})

			$("#toHandleForm").render({success : function(result){
				if(result['revisit.getRevisitInfo'].data){
					var data = result['revisit.getRevisitInfo'].data;
					if(data.DATA_SOURCE=="2"){
						$("#ifm").attr('src',data.USER_INFO_URL);
					}else{
						$("#ifm").attr('src',data.QUESTION_URL);
					}
					$("#hisOrder").attr('src',"/neworder/pages/access/service-history-phone.jsp?callback=1&customerTel="+data.CUSTOMER_PHONE);
					$("#hisRevisit").attr('src',"/neworder/pages/revisit/revisit-history.jsp?archivesNo=&customerCode=&customerTel="+data.CUSTOMER_PHONE);

					$("#userName").val(data.CUSTOMER_NAME);
					$("#userPhone").val(data.CUSTOMER_PHONE);
					$("#dataSource").val(data.DATA_SOURCE);
					if(data.IS_DONE!=null&&data.IS_DONE=='Y'){
						$("#isDone").val("1");
					}else if(data.IS_DONE!=null&&data.IS_DONE=='N'){
						$("#isDone").val("2");
					}else{
						$("#isDone").val(data.IS_DONE);
					}
					_custPhone = data.CUSTOMER_PHONE;
					_custName = data.CUSTOMER_NAME;
					_coefficient = data.COEFFICIENT;
					_orgCode = data.ORG_CODE
				}


					checkFiled();
					// 默认办结选择是
					$("#isDone").val("1");
					$('#result').on('change',function () {
						checkFiled();
					})
			}});


		})
		toHandle.ajaxSubmit = function(){
			if(form.validate("#toHandleForm")){

                // 若选择了处理结果且为有效回访，解决方案必填
                if ($('#result').val() === '1') {
                    if (!$('#solution').val()) {
                        layer.alert('请选择解决方案');
                        return;
                    }
                }

				var result=$("#result option:selected").text();
				$("#resultText").val(result);
				$("#isDone").attr("disabled",null);
	    		var data = form.getJSONObject("toHandleForm");
	    		ajax.remoteCall("${ctxPath}/servlet/revisit?action=toHandle",data,function(result) {
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							var index = parent.layer.getFrameIndex(window.name);
							parent.reload()
							parent.layer.close(index)
						});
					}else{
						layer.alert(result.msg,{icon:5});
					}
				})
	    	}
		}
		toHandle.changeResult = function(value){
			if(value == "1"){
				$("#isDone").val("1")
				$("#isDone").attr("disabled","disabled");
			}else{
				$("#isDone").val("")
				$("#isDone").attr("disabled",null);
			}
		}
		function Call(){
			//根据用户手机号和渠道ID判断用户是否为敏感用户
			ajax.remoteCall("${ctxPath}/servlet/revisit?action=checkNumber",{"phone":_custPhone},function(result) {
				if (result.state === 1 && result.data.result.length > 1) {
					layer.confirm(result.data.result[0], {
						title: "提示",
						icon: 0,
						btn: ['继续回访', '取消回访', '更多信息']
						, btn3: function () {
							layer.confirm(result.data.result.join('<br/>'), {
								title: "更多详细信息",
								btn: ['继续回访', '取消回访'],
								btn1: function (index) {
									if(_custPhone.substring(0,1) != "1"&&_custPhone.length>11){
										layer.confirm('该手机号码可能出错，是否继续', {
											btn : [ '确定', '取消' ]//按钮
										}, function(index) {
											layer.closeAll();
											telephoneTemp_callPhone();
										});
									}else{
										telephoneTemp_callPhone();
									}
									layer.close(index);
								}, btn2: function () {
									layer.closeAll();
								}
							});
						}, btn1: function (index) {
							if(_custPhone.substring(0,1) != "1"&&_custPhone.length>11){
								layer.confirm('该手机号码可能出错，是否继续', {
									btn : [ '确定', '取消' ]//按钮
								}, function(index) {
									layer.closeAll();
									telephoneTemp_callPhone();
								});
							}else{
								telephoneTemp_callPhone();
							}
							layer.close(index);
						}, btn2: function () {
							layer.closeAll();
						}
					});
				} else if (result.state === 1 && result.data.result.length === 1) {
					layer.confirm(result.data.result[0], {
						title: "提示",
						icon: 0,
						btn: ['继续回访', '取消回访']
						, btn1: function (index) {
							if(_custPhone.substring(0,1) != "1"&&_custPhone.length>11){
								layer.confirm('该手机号码可能出错，是否继续', {
									btn : [ '确定', '取消' ]//按钮
								}, function(index) {
									layer.closeAll();
									telephoneTemp_callPhone();
								});
							}else{
								telephoneTemp_callPhone();
							}
							layer.close(index);
						}, btn2: function () {
							layer.closeAll();
						}
					});
				} else {
					if(_custPhone.substring(0,1) != "1"&&_custPhone.length>11){
						layer.confirm('该手机号码可能出错，是否继续', {
							btn : [ '确定', '取消' ]//按钮
						}, function(index) {
							layer.closeAll();
							telephoneTemp_callPhone();
						});
					}else{
						telephoneTemp_callPhone();
					}
				}
			});
		}
		function showDiv(val){
			$(".layui-tab-item").removeClass("layui-show")
			$("#showDiv"+val).addClass("layui-show")
		}
		function telephoneTemp_callPhone(){
			layer.prompt({
				formType: 0,
				shade : false ,
				title: ['请确认号码','color:red'],
				value:_custPhone,
				area: ['500px', '300px'] ,
				offset: '150px',btn:['拨打','取消']},
				function(value,userData, index, elem){
					Isexistunitmanagertel(value,userData, index, elem);
			});
		}
		var uids = "";
		function  Isexistunitmanagertel(value,userData, index, elem){
			var uid = guid();
			uids = uids=="" ? uid:uid+";"+uids;
			_orgCode = _orgCode=="" ? "CS006":_orgCode
			var userData={ICC_BUSI_TYPE:'04', ICC_BUSI_ID:uid,ICC_COEFFICIENT:_coefficient,ICC_DIVISION_ID:_orgCode};
			/*  var areaNum=$("#areaNum").val();
	    	if(areaNum!=null&&areaNum!=""&&value.length <= 8){
	    		value=areaNum+value;
	    	} */
			window.parent.call(value,userData, index, elem);
		    layer.closeAll();
		}
		function guid() {//生成uuid
	        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
	            var r = Math.random()*16|0, v = c == 'x' ? r : (r&0x3|0x8);
	            return v.toString(16);
	        });
	    }

		// 处理结果不为“有效回访”时，解决方案（请选择，不可选）、跟进单号（不可填）
		function checkFiled() {
			var $solution = $('#solution');
			var $relatedOrderId = $('#relatedOrderId');
			if ($('#result').val() !== '1') {
				$solution.val('');
				$solution.prop('disabled', true);

				$relatedOrderId.val('');
				$relatedOrderId.prop('disabled', true);

				$("#isDone").val("")
				$("#isDone").attr("disabled",null);
			} else {
				$solution.removeAttr('disabled');
				$relatedOrderId.removeAttr('disabled');

				$("#isDone").val("1")
				$("#isDone").attr("disabled","disabled");
			}
		}

	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>