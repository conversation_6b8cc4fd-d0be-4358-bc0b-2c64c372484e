	<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>企划通回访处理</title>
	<style>
		.red-text {
			color: red;
		}
		.right-align {
			text-align: right;
		}
		.font-size {
			font-size: 17px;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form  autocomplete="off" data-mars="revisit.getRevisitInfo" method="post" name="toHandleForm" class="form-inline" id="toHandleForm" onsubmit="return false" data-mars-prefix="toHandle.">
             	<input type="hidden" name="toHandle.pId" value="${param.ID}">
             	<input type="hidden" id="dataSource" name="toHandle.DATA_SOURCE" >
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="red-text right-align font-size" >请检查确认问卷内容已提交，再点击此处反馈！</div>
						 <div class="form-group">
						 	<div class="input-group input-group-sm">
						 		<span class="input-group-addon" style="width: 80px;">用户姓名</span>	
						 		<input class="form-control input-sm" type="text" id="userName" readonly="readonly">
							</div>
							<div class="input-group input-group-sm">
								<span class="input-group-addon" style="width: 80px;">联系方式</span>
						 		<input class="form-control input-sm" type="text" id="userPhone" readonly="readonly" name="toHandle.CALLED">
						 		<span class="input-group-addon" style="cursor: pointer;" onclick="Call()"><i class="glyphicon glyphicon-earphone"></i></span>
							</div>
							 <div class="input-group input-group-sm pull-right ml-10">
								<button type="button" class="btn btn-sm btn-success" onclick="toHandle.ajaxSubmit()">确认</button>
							 </div>
							 <div class="input-group input-group-sm pull-right ml-10" style="width: 200px;">
						    	<span class="input-group-addon" style="width: 80px;">是否办结</span>	
								<select class="form-control input-sm" name="toHandle.IS_DONE" id="isDone" data-rules="required">
									<option value="">请选择</option>
	                                <option value="1">是</option>
	                                <option value="2">否</option>
	                            </select>
							</div>
						     <div class="input-group input-group-sm pull-right" style="width: 200px;">
						    	<span class="input-group-addon" style="width: 80px;">回访结果</span>
								<select class="form-control input-sm" name="toHandle.REVISIT_RESULT" id="Rresult" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PLANNING_REVISIT_RESULT')" onchange="toHandle.changeResult(this.value)" data-rules="required">
	                                 <option value="">请选择</option>
	                            </select>
							</div>
	             		 </div>
	             		 <div class="form-group">
						 	<div class="input-group input-group-sm" style="width: 650px;">
						 		<span class="input-group-addon" style="width: 80px;">备注</span>	
						 		<textarea class="form-control input-sm" rows="3" cols="" name="toHandle.CONTENT" title=""></textarea>
							</div>
							<div class="input-group input-group-sm pull-right hide" style="padding-right: 8px;">
								<label class="checkbox checkbox-info checkbox-inline remind-checkbox" style="margin-left: 5px;">
									是否同步结果 <input type="checkbox" class="remind-input" id="manualUpgradeFlag" name="toHandle.manualUpgradeFlag"  value="Y" checked>
								 </label>
							</div>
							
							<div class="input-group input-group-sm pull-right" style="margin-right: 265px;">
								<span class="input-group-addon" style="width: 80px;">外呼次数</span>
						 		<textarea class="form-control input-sm" rows="3" name="toHandle.REMARKS" id="remakes" style="width: 120px;" readonly="readonly"></textarea>
							</div>
							
	             		 </div>
             	    </div>  
	            <iframe id="ifm" src="" style="height: 580px;width: 100%"></iframe>
              	</div>
        </form>
        
</EasyTag:override>

<EasyTag:override name="script">
	<script type = "text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	<script type="text/javascript">
		jQuery.namespace("toHandle");
		var _custPhone = "";//客户手机号
		var _custName = "";//客户姓名
		var _coefficient = "";//折算系数
		var _orgCode = "";//事业部
		$(function(){
			$("#toHandleForm").render({success : function(result){
				if(result['revisit.getRevisitInfo'].data){
					$("#Rresult").val("");
					var data = result['revisit.getRevisitInfo'].data;
					$("#ifm").attr('src',data.QUESTION_URL);
					$("#userName").val(data.CUSTOMER_NAME);
					$("#userPhone").val(data.CUSTOMER_PHONE);
					$("#dataSource").val(data.DATA_SOURCE);
					_custPhone = data.CUSTOMER_PHONE;
					_custName = data.CUSTOMER_NAME;
					_coefficient = data.COEFFICIENT;
					_orgCode = data.ORG_CODE
				}
			}})
		})
		toHandle.ajaxSubmit = function(){
			//添加校验逻辑 回访结果 与 是否办结 提交时都不得回空
			if($("#Rresult").val() === ""  && $("#isDone").val() === "" ){
				layer.alert("请选择回访结果与是否办结后,在确认",{icon:5});
			}
			if(form.validate("#toHandleForm")){
				$("#isDone").attr("disabled",null);
	    		var data = form.getJSONObject("toHandleForm");
	    		ajax.remoteCall("${ctxPath}/servlet/revisit?action=toHandle",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							var index = parent.layer.getFrameIndex(window.name);
							parent.reload()
							parent.layer.close(index)
						});
					}else{
						layer.alert(result.msg,{icon:5});
					}
				})
	    	}
		}
		toHandle.changeResult = function(value){
			if(value == "1"){
				$("#isDone").val("1")
				$("#isDone").attr("disabled","disabled");
				document.getElementById("manualUpgradeFlag").checked = true;
			}else{
				$("#isDone").val("")
				$("#isDone").attr("disabled",null);
			}
		}
		function Call(){
			//根据用户手机号和渠道ID判断用户是否为敏感用户
			ajax.remoteCall("${ctxPath}/servlet/revisit?action=checkNumber",{"phone":_custPhone},function(result) {
				if (result.state === 1 && result.data.result.length > 1) {
					layer.confirm(result.data.result[0], {
						title: "提示",
						icon: 0,
						btn: ['继续回访', '取消回访', '更多信息']
						, btn3: function () {
							layer.confirm(result.data.result.join('<br/>'), {
								title: "更多详细信息",
								btn: ['继续回访', '取消回访'],
								btn1: function (index) {
									if(_custPhone.substring(0,1) != "1"&&_custPhone.length>11){
										layer.confirm('该手机号码可能出错，是否继续', {
											btn : [ '确定', '取消' ]//按钮
										}, function(index) {
											layer.closeAll();
											telephoneTemp_callPhone();
										});
									}else{
										telephoneTemp_callPhone();
									}
									layer.close(index);
								}, btn2: function () {
									layer.closeAll();
								}
							});
						}, btn1: function (index) {
							if(_custPhone.substring(0,1) != "1"&&_custPhone.length>11){
								layer.confirm('该手机号码可能出错，是否继续', {
									btn : [ '确定', '取消' ]//按钮
								}, function(index) {
									layer.closeAll();
									telephoneTemp_callPhone();
								});
							}else{
								telephoneTemp_callPhone();
							}
							layer.close(index);
						}, btn2: function () {
							layer.closeAll();
						}
					});
				} else if (result.state === 1 && result.data.result.length === 1) {
					layer.confirm(result.data.result[0], {
						title: "提示",
						icon: 0,
						btn: ['继续回访', '取消回访']
						, btn1: function (index) {
							if(_custPhone.substring(0,1) != "1"&&_custPhone.length>11){
								layer.confirm('该手机号码可能出错，是否继续', {
									btn : [ '确定', '取消' ]//按钮
								}, function(index) {
									layer.closeAll();
									telephoneTemp_callPhone();
								});
							}else{
								telephoneTemp_callPhone();
							}
							layer.close(index);
						}, btn2: function () {
							layer.closeAll();
						}
					});
				} else {
					if(_custPhone.substring(0,1) != "1"&&_custPhone.length>11){
						layer.confirm('该手机号码可能出错，是否继续', {
							btn : [ '确定', '取消' ]//按钮
						}, function(index) {
							layer.closeAll();
							telephoneTemp_callPhone();
						});
					}else{
						telephoneTemp_callPhone();
					}
				}
			});
		}
		function getCallNum(){
			var remakes = $("#remakes").val();
			if ($.isNumeric(remakes)) {
			   return remakes = parseInt(remakes) + 1; // 如果是数字，则将其转换为整数并加1
			} else {
				return  remakes = 1; // 如果不是数字，则设置为1
			}
		}
		function telephoneTemp_callPhone(){
			layer.prompt({
				formType: 0,
				shade : false ,
				title: ['请确认号码','color:red'],
				value:_custPhone,
				area: ['500px', '300px'] ,
				offset: '150px',btn:['拨打','取消']},
				function(value,userData, index, elem){
					Isexistunitmanagertel(value,userData, index, elem);
			});
		}
		var uids = "";
		function  Isexistunitmanagertel(value,userData, index, elem){
			$("#remakes").val(getCallNum());
			var uid = guid();
			uids = uids=="" ? uid:uid+";"+uids;
			_orgCode = _orgCode=="" ? "CS006":_orgCode
			var userData={ICC_BUSI_TYPE:'04', ICC_BUSI_ID:uid,ICC_COEFFICIENT:_coefficient,ICC_DIVISION_ID:_orgCode};
			/*  var areaNum=$("#areaNum").val();
	    	if(areaNum!=null&&areaNum!=""&&value.length <= 8){
	    		value=areaNum+value;
	    	} */
			window.parent.call(value,userData, index, elem);
		    layer.closeAll();
		}
		function guid() {//生成uuid
	        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
	            var r = Math.random()*16|0, v = c == 'x' ? r : (r&0x3|0x8);
	            return v.toString(16);
	        });
	    }

	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>