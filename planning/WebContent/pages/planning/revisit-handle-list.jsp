	<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>企划通回访处理</title>
	<link href="/easitline-static/lib/layui/css/layui.css" rel="stylesheet">
		<style>
			.pagination pagination-sm pageNumV{float:right}
		</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form  autocomplete="off" action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false" data-mars="revisit.checkRole">
             	<input type="hidden" id="check" name="check" value="0">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		       <h5><span class="glyphicon glyphicon-list"></span> 企划通回访处理</h5>
	             		 </div>
	             		 <hr style="margin:5px -15px">
	             			<div class="form-group">
	             				<div class="input-group input-group-sm" style="width: 200px;">
									<span class="input-group-addon" style="width: 80px;">折算类型</span>	
									<select name="CONVERSION_TYPE" id="hConversionType" data-rules="required" class="form-control input-sm" data-mars="common.getUserConvert">
			                        	<option value="">请选择</option>
			                        </select>
								</div>
	             				<div class="input-group input-group-sm" style="width: 200px;">
									<span class="input-group-addon" style="width: 80px;">申请人</span>	
									<input type="text" name="USER_ACC" id="applyUserAcc" class="form-control input-sm" >
								</div>
								<div class="input-group input-group-sm" style="width: 200px;">
									<span class="input-group-addon" style="width: 80px;">用户号码</span>	
									<input type="text" name="CUSTOMER_PHONE" class="form-control input-sm" >
								</div>
								<div class="input-group input-group-sm" style="width: 200px;">
									<span class="input-group-addon" style="width: 80px;">人群包ID</span>
									<input type="text" name="TASK_ID" class="form-control input-sm" >
								</div>
								<div class="input-group input-group-sm" style="width: 200px;">
									<span class="input-group-addon" style="width: 80px;">任务包名称</span>	
									<input type="text" name="TASK_NAME" class="form-control input-sm" >
								</div>
								<div class="input-group input-group-sm" style="width: 200px;">
								      <span class="input-group-addon" style="width: 80px;">回访状态</span>	
									  <select class="form-control input-sm" id="revisit_status" name="REVISIT_STATUS" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PLANNING_REVISIT_STATUS')">
                                          <option value="">请选择</option>
                                      </select>
								</div>
								<div class="input-group input-group-sm">
									<span class="input-group-addon" style="width: 80px;">同步时间</span>	
									<input type="text" name="SYN_TIME_START" id="SynStart" class="form-control input-sm Wdate" value="" onclick="WdatePicker({onpicked:function(){this.blur();maxTime()},dateFmt:'yyyy-MM-dd HH:mm:ss'})">
									<span class="input-group-addon" >-</span>	
									<input type="text" name="SYN_TIME_END" id="SynEnd" class="form-control input-sm Wdate" value=""   onclick="WdatePicker({minDate:'#F{$dp.$D(\'SynStart\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">									  
								</div>   
									<div class="input-group input-group-sm" style="width: 200px;">
								      <span class="input-group-addon" style="width: 80px;">是否办结</span>	
                                      <select class="form-control input-sm" name="IS_DONE" id="IS_DONE" data-rules="required">
										<option value="">请选择</option>
		                                <option value="Y">是</option>
		                                <option value="N">否</option>
		                              </select>
								</div>
								<div class="input-group input-group-sm" style="width: 200px;">
									<span class="input-group-addon" style="width: 80px;">通话状态</span>
									<select class="form-control input-sm" id="call_status" name="CALL_STATUS" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PLANNING_CALL_STATUS')">
										<option value="">请选择</option>
									</select>
								</div>
<%--								<div class="input-group input-group-sm" style="width: 200px;">--%>
<%--									<span class="input-group-addon" style="width: 80px;">回访结果</span>--%>
<%--									<select class="form-control input-sm" id="revisit_result" name="REVISIT_RESULT" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PLANNING_REVISIT_RESULT')">--%>
<%--										<option value="">请选择</option>--%>
<%--									</select>--%>
<%--								</div>--%>
<%--								<div class="input-group input-group-sm" style="width: 200px;">--%>
<%--									<span class="input-group-addon" style="width: 80px;">跟进状态</span>--%>
<%--									<select class="form-control input-sm" id="follow_status" name="FOLLOW_STATUS" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PLANNING_FOLLOW_STATUS')">--%>
<%--										<option value="">请选择</option>--%>
<%--									</select>--%>
<%--								</div>--%>
						  </div>
						 <div class="form-group">
							  <div class="input-group input-group-sm pull-left mr-10">
								  <button type="button" class="btn btn-sm btn-success" onclick="resetting()">重置</button>
						  	  </div>
					 		   <EasyTag:res resId="planning_visit_publish">
			             		       <div class="input-group input-group-sm pull-left mr-10">
											 <button type="button" id="applyBtn" class="btn btn-sm btn-success" onclick="handle.apply()">资料申请</button>
									   </div>
							   </EasyTag:res>
							    <div class="input-group input-group-sm pull-right">
									  <button type="button" class="btn btn-sm btn-default" onclick="reload()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>
	             		 </div>
             	    </div>  
             	  	<div class="ibox-content">
	              		<div class="row table-responsive">
		           	     	<table class="table table-auto table-bordered table-hover table-condensed text-c" id="tableHead" >
                             	<thead>
	                         		 <tr>
		                         	      <th class="text-c">序号</th>
		                         	      <th class="text-c">回访状态</th>
									      <th class="text-c">申请人</th>
									      <th class="text-c">用户姓名</th>
									      <th class="text-c">用户号码</th>
									      <th class="text-c" style="width:50px;max-width:50px;min-width:50px">查看</th>
										  <th class="text-c">人群包ID</th>
										  <th class="text-c">通话状态
											  <i class="layui-icon layui-icon-about tips" style="color: #1E9FFF;"
												 title="待联系：市场推广搜集到线索后，线索的初始通话状态。
未接通：给用户拨打过电话，但是电话未接通。
已接通：给用户拨打过电话，电话已接通但是通话时间小于10s。
有效沟通：给用户拨打过电话，电话已接通目通话时间不小于10s且不大于30s。
深度沟通：给用户拨打过电话，电话已接通且通话时间大于30s。"></i>
										  </th>
<%--										  <th class="text-c">跟进状态</th>--%>
									      <th class="text-c">任务包</th>
									      <th class="text-c">折算类型</th>
									      <th class="text-c">是否办结</th>
		                         	      <th class="text-c">操作</th>
			   						 </tr>
                            	 </thead>
                             <tbody id="dataList">
                             </tbody>
                           </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td>{{:#index+1}}</td>
                                     		<td>{{dictFUN:REVISIT_STATUS 'PLANNING_REVISIT_STATUS'}}</td>
                                      		<td>{{:RECEIVE_ACC}}</td>
                                      		<td>{{:CUSTOMER_NAME}}</td>
                                      		<td>{{call:CUSTOMER_PHONE CUSTOMER_PHONE_data #index+1 'phone' fn='getData2'}}</td>
                                      		<td style="width:50px;max-width:50px;min-width:50px" rowspan="{{:rowspan}} "title="查看">
												<span onclick="showDetail('{{:#index+1}}','{{:CUSTOMER_PHONE_data}}')" class="glyphicon glyphicon-eye-open" id="show{{:#index+1}}" style="color: rgb(255, 140, 60);"></span>
											</td>
											<td>{{:TASK_ID}}</td>
											<td>{{dictFUN:CALL_STATUS 'PLANNING_CALL_STATUS'}}</td>
<%--											<td>{{dictFUN:FOLLOW_STATUS 'PLANNING_FOLLOW_STATUS'}}</td>--%>
                                    		<td>{{:TASK_NAME}}</td>
											<td>{{CONVERSION_TYPE:CONVERSION_TYPE}}</td>
                                     		<td>{{IS_DONE:IS_DONE}}</td>
											<td><a href="javascript:handle.toHandle('{{:ID}}','{{:DATA_SOURCE}}')">处理</a></td>
									    </tr>
								   {{/for}}					         
						 	</script>
	              		 </div> 
	              		 <div class="row paginate" id="page">
	                     		<jsp:include page="/pages/common/pagination.jsp">
	                     			<jsp:param value="10" name="pageSize"/>
	                     		</jsp:include>
	                     </div> 
	                  </div>
              	</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css" />
	<script type = "text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	<script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
	<script type="text/javascript">
		jQuery.namespace("handle");
		requreLib.setplugs('wdate')//加载时间控件
		var conversion_type;
		var acc;
		function reload(){
			$("#tableHead").data("mars","revisit.getHandleList");
			$("#searchForm").searchData();
		}
		$(function(){
			var startDate = getTodayDate(-7);
   	    	var endDate = getTodayDate();
	    	$("#SynStart").val(startDate+" 00:00:00");
	    	$("#SynEnd").val(endDate+" 23:59:59");
	    	$("#searchForm").render({success : function(result){
	    		//折算类型
	    		if(result["common.getUserConvert"]){
    				conversion_type = result["common.getUserConvert"].data;
    			}
	    		if(result["revisit.checkRole"]){
	    			var check = result["revisit.checkRole"].check
	    			acc = result["revisit.checkRole"].userAcc
	    			$("#check").val(check)
	    			$("#applyUserAcc").val(acc)
	    			if(check <= 0){
	    				$("#applyUserAcc").attr("readonly","readonly")
	    			}
	    		}
	    		$("#revisit_status").val("3");//默认已分配
    		}});
	    });
		handle.apply = function(){
			cId = $("#hConversionType").val()//折算类型
			if(!cId){
				layer.msg("请选择折算类型",{icon:0})
				return
			}
			$("#applyBtn").attr("disabled","disabled");
			var data = form.getJSONObject("searchForm");
    		ajax.remoteCall("${ctxPath}/servlet/revisit?action=apply",data,function(result) {
    			$("#applyBtn").removeAttr("disabled");
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						window.location.reload();
					});
				}else{
					layer.alert(result.msg,{icon:5});
				}
			})
		}
		handle.toHandle = function(id,type){
			if(type=='2'){
				popup.layerShow({
					type:2,
					title:'快反处理',
					shadeClose:false,
					scrollbar: false,
					area:['1200px','700px'],
					end:function(){
						reload()
					},
					offset:'20px'},"${ctxPath}/pages/planning/revisit-handle-toHandle2.jsp",{ID:id});
			}else if(type=='3'){
				popup.layerShow({
					type:2,
					title:'美云销处理',
					shadeClose:false,
					scrollbar: false,
					area:['1200px','800px'],
					end:function(){
						reload()
					},
					offset:'20px'},"${ctxPath}/pages/planning/revisit-handle-toHandle3.jsp",{ID:id});
			}else{
				popup.layerShow({
					type:2,
					title:'问卷处理',
					shadeClose:false,
					scrollbar: false,
					area:['1200px','700px'],
					end:function(){
						reload()
					},
					offset:'20px'},"${ctxPath}/pages/planning/revisit-handle-toHandle.jsp",{ID:id});
			}
		
		}
		
		$.views.converters("CONVERSION_TYPE", function(val) {
			var req = conversion_type;
			if (typeof (req) == "undefined") {
				return val;
			} else {
				return req[val];
			}
		});
		$.views.converters("IS_DONE", function(val) {
			if (typeof (val) != "undefined") {
				if(val=="Y"){
					return "是";
				}else if(val=="N"){
					return "否";
				}
				return "";
			} else {
				return "";
			}
		});
		function resetting(){
			document.searchForm.reset(); 
			var startDate = getTodayDate(-7);
   	    	var endDate = getTodayDate();
   	    	$("#applyUserAcc").val(acc)
	    	$("#SynStart").val(startDate+" 00:00:00");
	    	$("#SynEnd").val(endDate+" 23:59:59");
	    	reload();
		}
		function maxTime(e){
			var start=$("#SynStart").val();
			var end=$("#SynEnd").val();
			if(start!=""&&end!=""&&start>end){
				layer.msg("开始时间应小于结束时间",{icon: 5});
				var time =end.substring(0,10)+start.substring(10);
				$("#SynStart").val(time);
			}
		}
		//如果值为空则清空某个id的值
	    function cleanVal(data,id){
			if($(data).val()==""){
				$("#"+id).val('');
			}
		}
	    function call(value,data, index, elem){
			if(value){
	   			//外呼
		  		parent.top.myCCbar.call(value,data,function(){
				    compUpgrade.changeParentStatus('call');
				    layer.msg("正在接通···",{icon:1,time:1000});
				    layer.close(index); 
			  	}
				,function(){
					layer.msg("外呼失败，请重新拨打！",{icon:5,time:3000});
				});
	   		}
		}
	    
	    function  getData(val,valData,index){
			return "<span id='"+index+"-"+valData+"' data-val='"+val+"'>"+val+"</span>"
		}
	    
		var showId = {};
		handle.showDetail = function(id,phone,name,address){
			if($("#show"+id).hasClass("glyphicon-eye-close")){
				showId[id]='';
				var phoneId= document.getElementById(id+"-"+phone)
				phoneId.innerHTML= phoneId.getAttribute("data-val");
				$("#show"+id).addClass("glyphicon-eye-open");
				$("#show"+id).removeClass("glyphicon-eye-close");
			}else{//显示
				var phoneId= document.getElementById(id+"-"+phone)
				decrypt({"value":phone},function(value){ phone=value});
				phoneId.innerHTML= phone;
				$("#show"+id).removeClass("glyphicon-eye-open");
				$("#show"+id).addClass("glyphicon-eye-close");
				showId[id]='show';
				auditLog(JSON.stringify({
					"model":"planning",
					"url":"/planning/pages/planning/revisit-handle-list.jsp",
					"action":"query",
					"describe":"用户查询[企划通回访处理列表]数据，查看客户["+name+"]敏感信息：[用户号码："+phone+"]"}));
			}
		}

		function auditLog(data){
			$.post('/iccportal5/servlet/auditLog', {action: 'Log', data: data}, function (result) {
				console.log(result);
			}, 'json');
		}
		function decrypt(data,returnVal){
			ajax.remoteCall("/iccportal5/servlet/auditLog?action=decrypt",data,function(result) {
	    		if(result.state == 1){
	    			returnVal(result.data)
				}
	    	},{async:false});
		}
		function showDetail(id,customerTel1){
			showDetailCommon({
				"model":"planning",
				"url":"/planning/pages/planning/revisit-handle-list.jsp",
				"action":"query",
				"describe":"用户查询[企划通回访处理列表]数据，査看敏感信息:[用户号码：{{customerTel1}}]"},id,null,null,customerTel1);
		}
	</script>
	<script type="text/javascript" src="/iccportal5/static/js/privacyUtil.js"></script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>