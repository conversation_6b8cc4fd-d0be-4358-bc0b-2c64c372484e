package com.yunqu.uinterface.in.servlet;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.service.BaseService;
import com.yq.busi.common.servlet.BaseInterfaceServlet;
import com.yq.busi.common.servlet.model.BaseResp;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.JsonUtil;
import com.yq.busi.common.util.security.SecurityUtil;
import com.yunqu.uinterface.common.CommonLogger;
import com.yunqu.uinterface.common.Constants;
import com.yunqu.uinterface.in.service.ServiceFactory;
import org.easitline.common.utils.kit.WebKit;

/**
 * 对于调用者的http请求，使用线程的方式统一进行处理；
 * 该类处理来自外部系统的调用，有签名验证、时间戳验证；鉴权严格
 * 处理后统一在输出流里回写处理结果
 */
public class ReceiveOutHandleThread extends Thread{

	private Logger logger = CommonLogger.logger;
			
	private JSONObject json=null;
	private HttpServletRequest request=null;
	private HttpServletResponse response=null;
	private BaseInterfaceServlet interfaceServlet=null;
	
	public ReceiveOutHandleThread(JSONObject json, HttpServletRequest request, HttpServletResponse response,
			BaseInterfaceServlet interfaceServlet) {
		this.json = json;
		this.request = request;
		this.response = response;
		this.interfaceServlet = interfaceServlet;
	}

	/**
	 * 对接口进行业务处理
	 */
	public void run() {
		
		//获取基本的请求参数
		String sender = json.getString("sender");
		String password = json.getString("password");
		String timestamp = json.getString("timestamp");
		String serialId = json.getString("serialId");
		String command = json.getString("command");
		String signature = json.getString("signature");
		String reqIp = WebKit.getIP(request);
		json.put("reqIp",reqIp);
		//检验参数是否完成
		if(StringUtils.isBlank(sender) || StringUtils.isBlank(password) || StringUtils.isBlank(timestamp) 
				||StringUtils.isBlank(command) ||StringUtils.isBlank(signature)){
			
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 请求参数不完整!");
			
			BaseResp resp = new BaseResp();
			resp.setRespCode(GWConstants.RET_CODE_MSG_FORMAT_ERROR);
			resp.setRespDesc("请求参数不完整!");
			resp.setSerialId(serialId);
			resp.setRespSerialId(IDGenerator.getDefaultNUMID());
			interfaceServlet.responseInfo(response, JSON.toJSONString(resp));
			return;
		}
		
		//TODO 检查sender、password是否存在
		
		
		
		//检查timestamp是否过期
		long miseconds = DateUtil.calDurationMills(timestamp);
		System.out.println(Constants.REQUSET_TIMESTAMP);
		if(miseconds>Constants.REQUSET_TIMESTAMP){
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 请求已过期，当前时间:"+DateUtil.getCurrentDateStr()+",请求时间:"+timestamp);
			BaseResp resp = new BaseResp();
			resp.setRespCode(GWConstants.RET_CODE_TIME_ERROR);
			resp.setRespDesc("请求已过期!");
			resp.setSerialId(serialId);
			resp.setRespSerialId(IDGenerator.getDefaultNUMID());
			interfaceServlet.responseInfo(response, JSON.toJSONString(resp));
			return;
		}
		
		//检查签名
		String beforeSign = sender+password+timestamp+serialId;
		String sign = SecurityUtil.encryptMsgByMD5(beforeSign);
		if(!signature.equalsIgnoreCase(sign)){
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 请求校验不通过,请求加密串:"+signature+",正确加密串:"+sign+",原串:"+beforeSign);
			BaseResp resp = new BaseResp();
			resp.setRespCode(GWConstants.RET_CODE_MSG_CHEK_ERROR);
			resp.setRespDesc("加密串错误:"+command);
			resp.setSerialId(serialId);
			resp.setRespSerialId(IDGenerator.getDefaultNUMID());
			interfaceServlet.responseInfo(response, JSON.toJSONString(resp));
			return;
		}
			
			
		//获取service处理对象
		BaseService service = ServiceFactory.getInstance().getService(command);
		if(service==null){
			//无法根据command类找到对应的服务类时，看传入的json里是否有serviceId，有则直接调用接口
			JSONObject j = executeByService(json);
			if(j!=null){
				//最后返回信息
				interfaceServlet.responseInfo(response, j.toJSONString());
				return;
			}
			
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 无法识别的操作类型:"+command);
			BaseResp resp = new BaseResp();
			resp.setRespCode(GWConstants.RET_CODE_COMMAND_ERROR);
			resp.setRespDesc("系统不支持操作类型:"+command);
			resp.setSerialId(serialId);
			resp.setRespSerialId(IDGenerator.getDefaultNUMID());
			interfaceServlet.responseInfo(response, JSON.toJSONString(resp));
			return;
		}
		//不同sevice根据command进行消息处理
		JSONObject retJson = service.handle(command,json);
		if(retJson==null){
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 系统内部无法处理:"+command);
			BaseResp resp = new BaseResp();
			resp.setRespCode(GWConstants.RET_CODE_OTHER_EXCEPTION);
			resp.setRespDesc("系统处理过程可能出错,无返回,操作类型:"+command);
			resp.setSerialId(serialId);
			resp.setRespSerialId(IDGenerator.getDefaultNUMID());
			interfaceServlet.responseInfo(response, JSON.toJSONString(resp));
			return;
		}
		retJson.put("serialId", serialId);
		String result = JSON.toJSONString(retJson);
		
		logger.debug(CommonUtil.getClassNameAndMethod(this)+" 请求处理完成后返回:"+result);
		//最后返回信息
		interfaceServlet.responseInfo(response, result);
		return;
	}
	
	
	/**
	 * 从传入的参数里获取服务类接口，直接调用服务类
	 * @param json
	 * @return
	 */
	private JSONObject executeByService(JSONObject json) {
		String serviceId = json.getString("serviceId");
		if(StringUtils.isBlank(serviceId)){
			JSONObject result = JsonUtil.createInfRespJson(json);
			result.put("respDesc", "请求参数中的serviceId不能为空!");
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 接口中未传入serviceId："+json.toJSONString());
			return result;
		}
		try {
			//找到服务类
			IService service = ServiceContext.getService(serviceId);
			if(service==null){
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 无法找到服务接口："+serviceId);
				return null;
			}
			return service.invoke(json);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 调用服务类异常,json="+json.toJSONString(),e);
		}
		return null;
	}

	public static void main(String[] args) {
		String sign = SecurityUtil.encryptMsgByMD5("PLANNINGYQ_855217172024-07-23 17:17:08H17217262282819824");
		System.out.println(sign);
	}
}
