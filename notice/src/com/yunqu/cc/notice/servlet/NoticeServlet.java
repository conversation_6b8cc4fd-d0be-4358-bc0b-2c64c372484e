package com.yunqu.cc.notice.servlet;

import javax.servlet.annotation.WebServlet;

import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.SyncInfoModel;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.service.SyncService;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.notice.base.AppBaseServlet;
import com.yunqu.cc.notice.base.CommonLogger;
import com.yunqu.cc.notice.inf.InfService;

@WebServlet("/NoticeServlet/*")
public class NoticeServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	private Logger logger = CommonLogger.logger;
	private InfService infService = new InfService();
	
	/**
	 * 查询通知
	 * @return
	 */
	public JSONObject actionForSearchUserNotice(){
		JSONObject  result = new JSONObject();
		result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
		try {
			UserModel user=UserUtil.getUser(getRequest());
			if(user==null){
//				logger.error(CommonUtil.getClassNameAndMethod(this)+"查询用户通知失败,用户没有登陆！");
				result.put("noticeNum", 0);
				return EasyResult.ok(result, "");
			}
			JSONObject json = new JSONObject();
			json.put("userAcc", user.getUserAcc());
			json.put("deptCode", user.getDeptCode());
			result = infService.srhUserNotice(json);
			

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"查询用户通知失败."+e.getMessage(), e);	
		}
		
		return EasyResult.ok(result, "");

		
	}
	
	/**
	 * 应答通知
	 * @return
	 */
	public JSONObject actionForAnswerUserNotice(){
		JSONObject  result = new JSONObject();
		result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
		try {
			UserModel user=UserUtil.getUser(getRequest());
			if(user==null){
//				logger.error(CommonUtil.getClassNameAndMethod(this)+"用户应答通知失败,用户没有登陆！");
				return EasyResult.ok(result, "");
			}
			SyncInfoModel model = SyncService.getSyncInfo("notice-srh-"+user.getUserAcc());
			model.setSyncTime(DateUtil.getCurrentDateStr());
			SyncService.updateSyncTime(model);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"应答用户通知失败."+e.getMessage(), e);	
		}
		
		return EasyResult.ok(result, "");

		
	}
}
