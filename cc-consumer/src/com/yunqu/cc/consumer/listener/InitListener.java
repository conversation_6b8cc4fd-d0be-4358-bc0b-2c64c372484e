package com.yunqu.cc.consumer.listener;

import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.consumer.base.CommonLogger;
import com.yunqu.cc.consumer.base.Constants;
import com.yunqu.cc.consumer.service.queue.local.QueueManager;
import com.yunqu.cc.consumer.utils.mq.MqBrokerControl;
import com.yunqu.cc.consumer.utils.thread.ScheduledTaskUtil;
import org.apache.log4j.Logger;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

@WebListener
public class InitListener implements ServletContextListener {

	public static boolean start = false;

	public Logger logger = CommonLogger.logger;


	@Override
	public void contextInitialized(ServletContextEvent servletContextEvent) {
		try {
			start = true;
			if("Y".equals(Constants.START_COMSUMER_FLAG)){
				MqBrokerControl instance = MqBrokerControl.getInstance();
			}

			// 初始化本地消息队列
			try {
				logger.info(CommonUtil.getClassNameAndMethod(this)+"开始初始化消息队列");
				QueueManager.getInstance().initAllQueues();
				logger.info(CommonUtil.getClassNameAndMethod(this)+"消息队列初始化完成");
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this)+"消息队列初始化失败", e);
			}

			ScheduledTaskUtil.initTimer(); //初始化定时执行器
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}

	@Override
	public void contextDestroyed(ServletContextEvent servletContextEvent) {
		try {
			if("Y".equals(Constants.START_COMSUMER_FLAG)) {
				MqBrokerControl.getInstance().shutDown();
			}
			ScheduledTaskUtil.endTimer(); //销毁定时执行器
			// 停止本地消息队列
			try {
				logger.info(CommonUtil.getClassNameAndMethod(this)+"开始停止消息队列");
				QueueManager.getInstance().stopAllQueues();
				logger.info(CommonUtil.getClassNameAndMethod(this)+"消息队列已停止");
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this)+"消息队列停止失败", e);
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}

}
