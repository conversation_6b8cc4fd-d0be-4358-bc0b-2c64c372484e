package com.yunqu.cc.consumer.model;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.IpUtil;
import com.yunqu.cc.consumer.base.CommonLogger;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ConcurrentNavigableMap;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import static com.yunqu.cc.consumer.base.QueryFactory.getQuery;

/**
 * 企微默认用户,企微管家账号使用次数同步器
 * @author: yuxuankun
 * @date: 2025/8/15 17:27
 */
public class OrderNoticeCountManager {

    private static final Logger log;

    private static final EasyCache cache;

    private static final ConcurrentHashMap<String,String> IdToWecomManagerMap;

    private static final ConcurrentHashMap<String, Integer> wecomManagerCountMap;

    private static final ConcurrentHashMap<String, Integer> oldWecomManagerCountMap;

    private static final ConcurrentHashMap<String, Integer> wecomDefaultUserCountMap;

    private static final ConcurrentHashMap<String, Integer> oldWecomDefaultUserCountMap;

    // 专用于随机选择的最小使用次数用户列表（线程安全）
    private static final CopyOnWriteArrayList<String> randomSelectionUserList;

    //原子索引计数器(采用循环链表方式)
    private static final AtomicInteger selectUserCircleIndex;

    // 业务字段哈希值到ManagerId列表的映射（支持一对多，线程安全）
    private static final ConcurrentHashMap<Integer, CopyOnWriteArrayList<String>> businessHashToManagerListMap;

    static {
        log = CommonLogger.getLogger("orderNoticeCountManager");
        cache = CacheManager.getMemcache();
        IdToWecomManagerMap = new ConcurrentHashMap<>();
        wecomManagerCountMap = new ConcurrentHashMap<>();
        oldWecomManagerCountMap = new ConcurrentHashMap<>();
        businessHashToManagerListMap = new ConcurrentHashMap<>();
        wecomDefaultUserCountMap = new ConcurrentHashMap<>();
        oldWecomDefaultUserCountMap = new ConcurrentHashMap<>();
        randomSelectionUserList = new CopyOnWriteArrayList<>();
        selectUserCircleIndex = new AtomicInteger(0);
    }

    /**
     * 根据业务字段获取配对信息
     * @param brandCode 品牌编码
     * @param productCode 产品编码
     * @param busiTypeCode 业务类型编码
     * @param orderSourceCode 订单来源编码
     * @return 配对信息，如果指定分组不存在则返回null
     */
    public static JSONObject getPair(String brandCode,
                                     String productCode,
                                     String busiTypeCode,
                                     String orderSourceCode){
        String Id = getWecomManagerByBusiness(brandCode, productCode, busiTypeCode, orderSourceCode);
        if (Id == null) {
            return null;
        }
        List<String> defaultUser = getWecomDefaultUserMinUsingCountByNum(2);
        if (defaultUser == null) {
            log.info("未找到可用的默认用户");
            return null;
        }
        String managerId = IdToWecomManagerMap.get(Id);
        if (managerId == null){
            log.info("企微管家ID无法映射到MANAGER_ID");
            return null;
        }
        //一个企微管家需拉两个默认用才能建群
        JSONObject pair = new JSONObject();
        pair.put("manager", managerId);
        pair.put("user1", defaultUser.get(0));
        pair.put("user2", defaultUser.get(1));
        return pair;
    }

    /**
     * 根据业务配对信息增加使用次数
     * @param pair 包含manager、user、hashCode的配对信息
     */
    public static void addCountByPair(JSONObject pair){
        String managerId = pair.getString("manager");
        String userId1 = pair.getString("user1");
        String userId2 = pair.getString("user2");
        
        if (managerId != null) {
            addWecomManagerUsingCount(managerId);
        }
        if (userId1 != null) {
            addWechatDefaultUserUsingCount(userId1);
        }
        if (userId2 != null) {
            addWechatDefaultUserUsingCount(userId2);
        }
    }

    /**
     * 根据业务字段获取使用次数最少的WecomManager
     * @param brandCode 品牌编码
     * @param productCode 产品编码
     * @param busiTypeCode 业务类型编码
     * @param orderSourceCode 订单来源编码
     * @return 使用次数最少的ManagerId，如果该业务组合不存在则返回null
     */
    public static String getWecomManagerByBusiness(String brandCode, String productCode, 
                                                   String busiTypeCode, String orderSourceCode){
        int hashCode = generateBusinessHashCode(brandCode, productCode, busiTypeCode, orderSourceCode);
        CopyOnWriteArrayList<String> managerList = businessHashToManagerListMap.get(hashCode);
        if (managerList == null || managerList.isEmpty()) {
            log.info("未找到匹配的WecomManager，hashCode: " + hashCode +
                    ", 业务参数: brandCode=" + brandCode + ", productCode=" + productCode + 
                    ", busiTypeCode=" + busiTypeCode + ", orderSourceCode=" + orderSourceCode);
            return null;
        }
        
        // 从符合条件的Manager中找出使用次数最少的
        String minUsageManager = null;
        int minUsageCount = Integer.MAX_VALUE;
        
        // CopyOnWriteArrayList 在迭代时是线程安全的，不会抛出 ConcurrentModificationException
        for (String managerId : managerList) {
            Integer currentCount = wecomManagerCountMap.get(managerId);
            if (currentCount != null && currentCount < minUsageCount) {
                minUsageCount = currentCount;
                minUsageManager = managerId;
            }
        }
        
        if (minUsageManager == null) {
            log.info("在匹配的WecomManager列表中未找到有效的Manager，hashCode: " + hashCode);
        }
        
        return minUsageManager;
    }

    /**
     * 生成业务字段的哈希值
     */
    private static int generateBusinessHashCode(String brandCode, String productCode, 
                                              String busiTypeCode, String orderSourceCode){
        String sb = (StringUtils.isBlank(brandCode)?"null":brandCode) + ";" +
                (StringUtils.isBlank(productCode)?"null":productCode) + ";" +
                (StringUtils.isBlank(busiTypeCode)?"null":busiTypeCode)  + ";" +
                (StringUtils.isBlank(orderSourceCode)?"null":orderSourceCode) ;
        return sb.hashCode();
    }

    public static String getWecomDefaultUserMinUsingCount(){
        if (wecomDefaultUserCountMap.isEmpty()) {
            log.info("微信默认用户列表为空1");
            return null;
        }
        if (randomSelectionUserList.isEmpty()){
            log.info("微信默认用户列表为空2");
            return null;
        }
        //以下计算索引保证原子性
        int index = selectUserCircleIndex.getAndIncrement() % randomSelectionUserList.size();
        return randomSelectionUserList.get(index);
    }

    public static List<String> getWecomDefaultUserMinUsingCountByNum(int n){
        if (wecomDefaultUserCountMap.isEmpty()) {
            log.info("微信默认用户列表为空1");
            return null;
        }
        if (randomSelectionUserList.isEmpty() || randomSelectionUserList.size() < n){
            log.info("微信默认用户列表为空 或 数量不满足"+ n +"个");
            return null;
        }
        List<String> result = new ArrayList<>();
        int indexMod = randomSelectionUserList.size();

        for (int i = 0; i < n; i++) {
            int index = selectUserCircleIndex.getAndIncrement() % indexMod;
            result.add(randomSelectionUserList.get(index));
        }
        return result;
    }

    public static void addWecomManagerUsingCount(String managerId){
        int i = wecomManagerCountMap.getOrDefault(managerId,0);
        wecomManagerCountMap.put(managerId,i+1);
    }

    public static void addWechatDefaultUserUsingCount(String staffWxid){
        int i = wecomDefaultUserCountMap.getOrDefault(staffWxid,0);
        wecomDefaultUserCountMap.put(staffWxid,i+1);
    }

    public static void loadWecomManager(){
        try{
            log.info("------------------------------ 企业微信管家使用次数相关数据结构开始重新构建 -----------------------------");
            boolean isFlushToDB = true;
            if(!wecomManagerCountMap.isEmpty()){
                //需要先加载到数据库再清除
                isFlushToDB = updateWecomManagerCountToDB();
                if (isFlushToDB) { wecomManagerCountMap.clear(); IdToWecomManagerMap.clear(); }
            }
            EasySQL sql = new EasySQL("select ID,MANAGER_ID,USE_COUNT,BRAND_CODE,PRODUCT_CODE,BUSI_TYPE_CODE,ORDER_SOURCE_CODE from WECOM_MANAGER_CONFIG where STATUS = '1'");
            List<JSONObject> managerList = getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
            if (managerList != null && !managerList.isEmpty()) {
                // 清空业务映射
                if (isFlushToDB) {
                    businessHashToManagerListMap.clear();
                }
                
                for (JSONObject manager : managerList){
                    //不使用 MANAGER_ID 的理由: 同个坐席可能被分配处理多种组合类型的工单
                    String Id = manager.getString("ID");
                    String managerId = manager.getString("MANAGER_ID");
                    if (StringUtils.isBlank(managerId)){ continue; }
                    Integer useCount = manager.getIntValue("USE_COUNT");
                    String brandCode = manager.getString("BRAND_CODE");
                    String productCode = manager.getString("PRODUCT_CODE");
                    String busiTypeCode = manager.getString("BUSI_TYPE_CODE");
                    String orderSourceCode = manager.getString("ORDER_SOURCE_CODE");
                    
                    //已成功将旧数据刷新至数据库,直接放入最新的数据
                    if (isFlushToDB || !wecomManagerCountMap.containsKey(Id)){
                        wecomManagerCountMap.put(Id,useCount);
                    }else{
                        //由于旧数据同步至数据库失败，所以旧数据保留,采用差值纠正
                        int diff = wecomManagerCountMap.get(Id) - oldWecomManagerCountMap.get(Id);
                        wecomManagerCountMap.put(Id,diff+useCount);
                    }
                    oldWecomManagerCountMap.put(Id,useCount);
                    // 建立业务字段到ManagerId列表的映射（支持一对多，线程安全）
                    int hashCode = generateBusinessHashCode(brandCode, productCode, busiTypeCode, orderSourceCode);
                    IdToWecomManagerMap.put(Id,managerId);
                    businessHashToManagerListMap.computeIfAbsent(hashCode, k -> new CopyOnWriteArrayList<>()).add(Id);
                }
                log.info("IdToWecomManagerMap: " + IdToWecomManagerMap);
                log.info("wecomManagerCountMap: " + wecomManagerCountMap);
                log.info("oldWecomManagerCountMap: " + oldWecomManagerCountMap);
                log.info("businessHashToManagerListMap: " + businessHashToManagerListMap);
            }
            log.info("-------------------- 企业微信管家使用次数相关数据结构重新构建完成, isFlushToDB: " + isFlushToDB + " --------------------");
        }catch (Exception e){
            log.error("加载企业微信管家失败: " + e.getMessage(),e);
        }
    }

    public static void loadWechatDefaultUser(){
        try{
            log.info("----------------------------- 企业默认用户使用次数相关数据结构开始重新构建 -----------------------------");
            boolean isFlushToDB = true;
            randomSelectionUserList.clear(); // 清空随机选择列表
            selectUserCircleIndex.set(0);
            if(!wecomDefaultUserCountMap.isEmpty()){
                //需要先加载到数据库再清除
                isFlushToDB = updateWechatDefaultUserCountToDB();
                if (isFlushToDB) { wecomDefaultUserCountMap.clear(); }
            }
            EasySQL sql = new EasySQL("select STAFF_WXID,USE_COUNT from DEFAULT_WECHAT_USER where STATUS = '1'");
            List<JSONObject> userList = getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
            if (userList != null && !userList.isEmpty()) {
                for (JSONObject user : userList){
                    String staffWxid = user.getString("STAFF_WXID");
                    if (StringUtils.isBlank(staffWxid)){ continue; }
                    Integer useCount = user.getIntValue("USE_COUNT");
                    //已成功将旧数据刷新至数据库,直接放入最新的数据
                    if (isFlushToDB || !wecomDefaultUserCountMap.containsKey(staffWxid)){
                        wecomDefaultUserCountMap.put(staffWxid,useCount);
                    }else{
                        //由于旧数据同步至数据库失败，所以旧数据保留,采用合并方式
                        int diff = wecomManagerCountMap.get(staffWxid) - oldWecomDefaultUserCountMap.get(staffWxid);
                        wecomManagerCountMap.put(staffWxid,diff+useCount);
                    }
                    oldWecomDefaultUserCountMap.put(staffWxid,useCount);
                    randomSelectionUserList.add(staffWxid);
                }
                log.info("wecomDefaultUserCountMap: " + wecomDefaultUserCountMap);
                log.info("oldWecomDefaultUserCountMap: " + oldWecomDefaultUserCountMap);
                log.info("randomSelectionUserList: " + randomSelectionUserList);
            }
            log.info("-------------------- 企业默认用户使用次数相关数据结构重新构建完成, isFlushToDB:" + isFlushToDB + " --------------------");
        }catch (Exception e){
            log.error("加载微信默认用户失败: " + e.getMessage(),e);
        }
    }

    public static boolean updateWecomManagerCountToDB(){
        try {
            //同时只允许一个服务器操作数据库
            String lock = cache.get("wecom_manager_count_lock");
            if (StringUtils.isNotBlank(lock)){
                log.info("企业微信管理员使用次数正在[" + lock + "]刷新至数据库");
                return false;
            }
            cache.put("wecom_manager_count_lock",IpUtil.getIpAddr(),60*1000);
            if (wecomManagerCountMap != null && !wecomManagerCountMap.isEmpty()){
                //使用Entry遍历
                for (ConcurrentNavigableMap.Entry<String, Integer> entry : wecomManagerCountMap.entrySet()){
                    String managerId = entry.getKey();
                    Integer useCount = entry.getValue();
                    Integer oldUseCount = oldWecomManagerCountMap.get(managerId);
                    String sql = "UPDATE WECOM_MANAGER_CONFIG SET USE_COUNT = USE_COUNT + ? WHERE ID = ?";
                    getQuery().execute(sql, new Object[] {(useCount-oldUseCount), managerId});
                    log.info("企业微信管理员["+managerId+"]新增["+ (useCount- oldUseCount)+"]次");
                }
            }
            return true;
        }catch (Exception e){
            log.error("更新企业微信管理员使用次数失败: " + e.getMessage(),e);
            return false;
        }finally {
            cache.delete("wecom_manager_count_lock");
        }
    }

    public static boolean updateWechatDefaultUserCountToDB(){
        try {
            //同时只允许一个服务器操作数据库
            String lock = cache.get("wechat_default_user_count_lock");
            if (StringUtils.isNotBlank(lock)){
                log.info("企业默认用户使用次数正在[" + lock + "]刷新至数据库");
                return false;
            }
            cache.put("wechat_default_user_count_lock", IpUtil.getIpAddr(),60*1000);
            log.info("企业默认用户使用次数开始刷新至数据库");
            if (wecomDefaultUserCountMap != null && !wecomDefaultUserCountMap.isEmpty()){
                //使用Entry遍历
                for (ConcurrentNavigableMap.Entry<String, Integer> entry : wecomDefaultUserCountMap.entrySet()){
                    String staffWxid = entry.getKey();
                    Integer useCount = entry.getValue();
                    Integer oldUseCount = oldWecomDefaultUserCountMap.get(staffWxid);
                    String sql = "UPDATE DEFAULT_WECHAT_USER SET USE_COUNT = USE_COUNT + ? WHERE STAFF_WXID = ?";
                    getQuery().execute(sql, new Object[] {(useCount - oldUseCount), staffWxid});
                    log.info("企业默认用户["+staffWxid+"]新增["+ (useCount- oldUseCount)+"]次");
                }
            }
            log.info("企业默认用户使用次数刷新至数据库结束!!!");
            return true;
        }catch (Exception e){
            log.error("更新微信默认用户使用次数失败: " + e.getMessage(),e);
            return false;
        }finally {
            cache.delete("wechat_default_user_count_lock");
        }
    }
}
