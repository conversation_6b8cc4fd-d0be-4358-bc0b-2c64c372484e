package com.yunqu.cc.consumer.service.queue.local.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.consumer.base.CommonLogger;
import com.yunqu.cc.consumer.base.Constants;
import com.yunqu.cc.consumer.model.OrderNoticeCountManager;
import com.yunqu.cc.consumer.service.queue.local.LocalMessageQueue;
import com.yunqu.cc.consumer.utils.common.MixGwUtil;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.kit.RandomKit;

import java.util.List;

import static com.yunqu.cc.consumer.base.QueryFactory.getQuery;

/**
 * 工单提醒服务队列
 */
public class OrderNoticeQueue extends LocalMessageQueue {
    private static Logger logger = CommonLogger.getLogger("orderNotice");

    // 单例模式
    private static volatile OrderNoticeQueue instance;

    /**
     * 获取工单提醒服务队列实例
     * @return 工单提醒服务队列实例
     */
    public static OrderNoticeQueue getInstance() {
        if (instance == null) {
            synchronized (OrderNoticeQueue.class) {
                if (instance == null) {
                    // 创建队列实例，队列名称为order-Notice，默认容量为1000，消费者线程数为3
                    instance = new OrderNoticeQueue("order-Notice", Constants.getOrderNoticeQueueCapacity(), Constants.getOrderNoticeQueueConsumerThreadNum());
                }
            }
        }

        // 分离实例创建和启动逻辑，增强线程安全性
        if (!instance.isRunning()) {
            synchronized (OrderNoticeQueue.class) {
                if (!instance.isRunning()) {
                    // 启动队列
                    instance.start();
                }
            }
        }

        return instance;
    }

    /**
     * 构造函数
     * @param queueName 队列名称
     * @param capacity 队列容量
     * @param consumerThreads 消费者线程数
     */
    private OrderNoticeQueue(String queueName, int capacity, int consumerThreads) {
        super(queueName, capacity, consumerThreads);
    }
    
    /**
     * 处理单信息提醒请求消息
     */
    @Override
    protected void processMessage(JSONObject resqJson) {
        try{
            logger.info(CommonUtil.getClassNameAndMethod(this) + "resqJson: " + resqJson);
            JSONArray dataArr = resqJson.getJSONArray("data");
            if (dataArr == null){ return; }
            List<JSONObject> list = dataArr.toJavaList(JSONObject.class);
            for (JSONObject data : list) {
                String userId, masterId;
                String phone = data.getString("phone");
                String serviceOrderNumber = data.getString("serviceOrderNumber");
                String brandName = data.getString("brandName");
                String prodName = data.getString("prodName");
                String brandCode = data.getString("brandCode");
                String productCode = data.getString("productCode");
                String busiTypeCode = data.getString("busiTypeCode");
                String orderSourceCode = data.getString("orderSourceCode");

                logger.info(CommonUtil.getClassNameAndMethod(this) + "data: " + data);
                //查询群聊状态
                JSONObject groupInfo = getGroupInfo(serviceOrderNumber);
                if (groupInfo != null) {
                    logger.info(CommonUtil.getClassNameAndMethod(this) + "groupInfo: " + groupInfo);
                    String groupName = groupInfo.getString("GROUP_CHAT_NAME");
                    String roomId = groupInfo.getString("CHAT_ID");
                    int state = groupInfo.getIntValue("STATE");
                    JSONObject pair = OrderNoticeCountManager.getPair(brandCode, productCode, busiTypeCode, orderSourceCode);
                    if (pair == null) {
                        logger.info(CommonUtil.getClassNameAndMethod(this) + "没有找到合适的企微管家!");
                        return;
                    }
                    logger.info(CommonUtil.getClassNameAndMethod(this) + "选中的企微管家,默认用户: " + pair);
                    String jumpUrl;
                    JSONObject param = null;
                    if (state == 0) { //无群聊
                        logger.info(CommonUtil.getClassNameAndMethod(this) + "进入无群聊处理流程!!!");
                        userId = pair.getString("user1") + ";" + pair.getString("user2") ;
                        masterId = pair.getString("manager");
                        jumpUrl = Constants.getParam("wecomJumpCreateGroupUrl") + "&userId=" + masterId + "&externalUserId=" + userId + "&serviceOrderNumber=" + serviceOrderNumber;
                        param = buildMessageParams(masterId, serviceOrderNumber, groupName, brandName, prodName, jumpUrl, "needCreateGroup");
                        logger.info(CommonUtil.getClassNameAndMethod(this) + "跳转链接为: " + jumpUrl);
                    } else if (state == 1) {
                        // groupState 的枚举值详见业务字典 "GROUP_STATE" ,目前枚举值为 1-使用中,2-待解散,3-已解散
                        int groupState = groupInfo.getIntValue("GROUP_STATE");
                        switch (groupState) {
                            case 1: //使用中
                                logger.info(CommonUtil.getClassNameAndMethod(this) + "群聊使用中,进入不建群流程!!!");
                                JSONObject wxGroupMsg = MixGwUtil.getWxGroupMasterId(roomId);
                                userId = wxGroupMsg.getString("owner");//群主ID
                                jumpUrl = Constants.getParam("wecomJumpToGroupUrl") + "&chatGroupId=" + roomId;
                                param = buildMessageParams(userId, serviceOrderNumber, groupName, brandName, prodName, jumpUrl, "groupExistedOrderComing");
                                logger.info(CommonUtil.getClassNameAndMethod(this) + "跳转链接为: " + jumpUrl);
                                break;
                            case 2: //待解散
                            case 3: //已解散
                                logger.info(CommonUtil.getClassNameAndMethod(this) + "当前群状态: " + state + "[2-待解散,3已解散],进入建群流程!!!");
                                userId = pair.getString("user1") + ";" + pair.getString("user2") ;
                                masterId = pair.getString("manager");
                                jumpUrl = Constants.getParam("wecomJumpCreateGroupUrl") + "&userId=" + masterId + "&externalUserId=" + userId + "&serviceOrderNumber=" + serviceOrderNumber;
                                param = buildMessageParams(masterId, serviceOrderNumber, groupName, brandName, prodName, jumpUrl, "needCreateGroup");
                                logger.info(CommonUtil.getClassNameAndMethod(this) + "跳转链接为: " + jumpUrl);
                                break;
                        }
                    }
                    logger.info(CommonUtil.getClassNameAndMethod(this) + "发送卡片入参: " + param);
                    if (param != null) {
                        JSONObject wbcResult = MixGwUtil.sendWxButtonCardMessage(param);
                        if (wbcResult != null && "000".equals(wbcResult.getString("respCode"))) {
                            OrderNoticeCountManager.addCountByPair(pair);
                            logger.info(CommonUtil.getClassNameAndMethod(this) + "发送成功, wbcResult: " + wbcResult);
                        } else {
                            logger.info(CommonUtil.getClassNameAndMethod(this) + "发送失败, wbcResult: " + wbcResult);
                        }
                    }
                }
            }
        }catch (Exception e){
            logger.error(CommonUtil.getClassNameAndMethod(this) + "获取处理单信息提醒请求消息异常-->"+ e.getMessage());
        }
    }

    //根据手机号码和服务单号,查看对应群聊状态
    private JSONObject getGroupInfo(String serviceOrderNumber){
        try{
            JSONObject res = new JSONObject();
            EasyQuery query = getQuery();
            EasySQL sql = new EasySQL("SELECT go.CHAT_ID,go.GROUP_CHAT_NAME,go.GROUP_STATE FROM C_NO_GROUP_ORDERS_MATCH gom");
            sql.append("LEFT JOIN C_NO_GROUP_ORDERS go ON gom.GROUP_ORDER_ID = go.ID ");
            sql.append(serviceOrderNumber,"WHERE gom.SERVICE_ORDER_NUMBER = ?");
            sql.append(DateUtil.addMonth(DateUtil.TIME_FORMAT,DateUtil.getCurrentDateStr(),-6), "AND gom.CREATE_TIME >= ?"); // 半年内的数据
            JSONObject json = query.queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl()); //1-使用中 2-待解散 3-已解散
            if(json != null){
                json.put("STATE",1); //有记录
                res = json;
            }else{
                res.put("STATE",0); //无记录,说明无群聊
            }
            return res;
        }catch (Exception e){
            logger.error(CommonUtil.getClassNameAndMethod(this) + "获取群聊状态异常-->"+ e.getMessage());
            return null; //异常
        }
    }

    public static JSONObject buildMessageParams(String userId,String serviceOrderNumber,String groupName,String brandName,String prodName,String jumpUrl,String type){
       try{
           JSONObject params = new JSONObject();
           params.put("userId",userId); //如果有多个,需要用"|"分割
           params.put("serviceOrderNumber",serviceOrderNumber);
           params.put("groupName",groupName);
           params.put("brandName",brandName);
           params.put("prodName",prodName);
           params.put("jumpUrl",jumpUrl);
           params.put("type",type);
           return params;
       }catch (Exception e){
           return null;
       }
    }

}