package com.yunqu.cc.consumer.utils.thread;

import com.yq.busi.common.util.IpUtil;
import com.yunqu.cc.consumer.base.CommonLogger;
import com.yunqu.cc.consumer.base.Constants;
import com.yunqu.cc.consumer.model.OrderNoticeCountManager;
import com.yunqu.cc.consumer.service.queue.local.QueueManager;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.utils.string.StringUtils;

import java.util.Iterator;
import java.util.List;
import java.util.concurrent.*;

/**
 * 适合执行"用于更新本地内存操作"的短间隔定时任务
 */
public class ScheduledTaskUtil {

	private static final Logger logger = CommonLogger.getLogger("scheduledTask");
	private static EasyCache cache = CacheManager.getMemcache();
	private static ScheduledExecutorService scheduler;
	private static List<ScheduledFuture<?>> scheduledFutureList = new CopyOnWriteArrayList<>();


	public static void initTimer(){
		// 创建单线程的定时任务调度器
		scheduler = Executors.newSingleThreadScheduledExecutor();
		executeScheduledTask();
		logger.info("定时器执行器初始化完成!");
	}

	public static void endTimer(){
		// 取消定时任务
		if (scheduledFutureList != null) {
			for (ScheduledFuture<?> task : scheduledFutureList) {
				if (task != null) {
					task.cancel(true);
				}
			}
			scheduledFutureList.clear();
			scheduledFutureList = null; // for gc
		}

		// 关闭调度器
		if (scheduler != null) {
			scheduler.shutdownNow();  // 立即关闭，不等待任务完成
			try {
				// 等待一段时间让任务终止
				if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
					logger.info("定时器执行器未能在5秒内正常关闭");
				}
			} catch (InterruptedException e) {
				Thread.currentThread().interrupt();
				logger.error("定时器执行器关闭时被中断: " + e.getMessage(),e);
			} finally {
				scheduler = null;  // for gc
				logger.info("定时器执行器关闭完成!");
			}
		}
	}
	//在此添加各种时间的定时任务
	private static void executeScheduledTask() {
		try {
			//需要在模块启动时只执行一次的任务
			OrderNoticeCountManager.loadWecomManager();
			OrderNoticeCountManager.loadWechatDefaultUser();

			//定时任务1 - 每2分钟刷新企微管家,默认用使用频率入库
			scheduledFutureList.add(scheduler.scheduleAtFixedRate(
					()->{
						OrderNoticeCountManager.loadWecomManager();
						OrderNoticeCountManager.loadWechatDefaultUser();
					},
					2,  // 初始延迟
					2,  // 执行间隔
					TimeUnit.MINUTES
			));
			//定时任务2 - 每5秒检查是否有同步器刷新标识
			scheduledFutureList.add(scheduler.scheduleAtFixedRate(
					()->{
						String ip = IpUtil.getIpAddr();
						if(StringUtils.isNotBlank(cache.get("noticeUpdateCountDefaultUser_" + ip))){
							OrderNoticeCountManager.loadWechatDefaultUser();
							cache.delete("noticeUpdateCountDefaultUser_" + ip);
						}
						if (StringUtils.isNotBlank(cache.get("noticeUpdateCountManager_" + ip))){
							OrderNoticeCountManager.loadWecomManager();
							cache.delete("noticeUpdateCountManager_" + ip);
						}
					},
					10,  // 初始延迟
					5,  // 执行间隔
					TimeUnit.SECONDS
			));
			//定时任务3 - 每30秒,刷新cc-consumer的心跳标记
            scheduledFutureList.add(scheduler.scheduleAtFixedRate(
                    QueueManager::addIpToQueueHeartCache,    //防止缓存淘汰
					30,  // 初始延迟
					30,  // 执行间隔
					TimeUnit.SECONDS
			));
		} catch (Exception e) {
			logger.error("定时器执行器运行失败: " + e.getMessage(),e);
		}
	}
}
