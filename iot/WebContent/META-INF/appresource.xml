<?xml version="1.0" encoding="UTF-8"?>
<!-- 
	id:必须全局唯一，命名方式：应用名称+菜单名称
	name：菜单或功能名称
	type：资源类型，1：应用 ，2：菜单，3：功能，9: 其它
	state：状态，0:正常，缺省 ，1：暂停
	portal:所属应用，不填写缺省为通用portal，根据菜单的归属，可以填写具体的所属的portal，例如：my_portal
	index:排序，菜单的显示按照升序进行排序。
	icon：菜单图标
 -->
<resources>
   <resource id="neworder" name="工单与回访" url="" type="2" portal="" state="0" order="4" index = "5">
		<resource id="neworder_iot" name="IOT主动服务" type="2" icon="" portal="" state="0" url="" index="">
			<resource id="iot_publish_list" name="主动服务发布" type="2" icon="" portal="" state="0" url="/iot/servlet/revisit?action=publishList" index="1">
		   		<resource id="iot_visit_publish" name="发布"   url="" type="3" portal="" icon="" state="0" index="11"/>
		   		<resource id="iot_visit_appoint" name="指派" url="" type="3" portal="" icon="" state="0" index="12"/>
		   		<resource id="iot_visit_recycle" name="回收"   url="" type="3" portal="" icon="" state="0" index="13" />
		    </resource>
		    <resource id="iot_handle_list" name="主动服务处理"  type="2" portal="" icon="" state="0" url="/iot/servlet/revisit?action=handleList" index="2">
		   		<resource id="iot_visit_apply" name="申请权限"   url="" type="3" portal="" icon="" state="0" index="20"/>
		   		<resource id="iot_visit-handle" name="处理权限"   url="" type="3" portal="" icon="" state="0" index="21"/>
		    </resource>
		    <resource id="iot_publish_record" name="主动服务发布记录" type="2" icon="" portal="" state="0" url="/iot/pages/revisit/revisit-publish-record.jsp" index="3"/>
		    <resource id="iot_result_list" name="主动服务结果查询" type="2" icon="" portal="" state="0" url="/iot/servlet/revisit?action=resultList" index="4">
		    	<resource id="iot_revisit_monitor" name="班长权限"   url="" type="3" portal="" icon="" state="0" index="14" />
		    	<resource id="iot_export_result_list" name="导出"   url="" type="3" portal="" icon="" state="0" index="15" />
		    </resource>
		    <resource id="iot_report_list" name="主动服务统计报表"  type="2" icon="" portal="" state="0" url="/iot/pages/revisit/revisit-result-stat.jsp" index="5"/>
			<resource id="revisit_upgrade_list" name="iot智能外呼配置页面"  type="2" icon="" portal="" state="0" url="/iot/pages/revisit/revisit-upgrade-list.jsp" index="6"/>
		</resource>
	</resource>
</resources>
