<!DOCTYPE html>
<html>

<head>
    <title>企微管家清单</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport"
          content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <!-- 基础的 css js 资源 -->
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css" />
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.0" />
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.0" />
    <style>
        .drawer-content .el-date-editor {
            width: 100%;
        }

        .btn {
            cursor: pointer;
        }

        .el-table_1_column_25 .cell {
            width: 100%;
            display: flex;
            justify-content: space-evenly;
            align-items: center;
        }

        .demo-drawer__content {
            padding: 16px 24px;
        }

        .demo-drawer__footer {
            width: 100%;
            display: flex;
            justify-content: space-evenly;
            align-items: center;
        }

        .paginationContainer {
            display: flex;
            justify-content: flex-end;
        }

        .el-table__body-wrapper,
        .el-scrollbar__wrap {

        & ::-webkit-scrollbar {
              height: 10px;
          }

        }

        /* 解决滚动条被固定列盖住的问题 */
        .el-table .el-table__fixed-right,
        .el-table .el-table__fixed {
            height: auto !important;
            bottom: 10px !important;
        }

        .el-table__fixed-body-wrapper .el-table__body {
            padding-bottom: 10px;
            /* 6px为滚动条宽度 */
        }

        .operation-btn {
            margin-right: 10px;
            color: #409EFF;
            cursor: pointer;
        }


    </style>
</head>

<body class="yq-page-full vue-box">
<div id="managerConfig" class="flex yq-table-page" v-loading="loading" element-loading-text="加载中..." v-cloak>
    <div class="yq-card">
        <div class="card-header">
            <div class="head-title">企微管家清单</div>
            <div class="yq-table-control">
                <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">新增</el-button>
                <el-button type="primary" plain size="small" icon="el-icon-upload2" @click="handleImport">批量导入</el-button>
            </div>
        </div>
        <div class="card-content">
            <!-- <el-form :model="searchForm" ref="searchForm" size="small" :inline="true">
                <el-form-item label="企微管家USER ID" prop="managerId">
                    <el-input v-model="searchForm.managerId" placeholder="请输入企微管家USER ID"></el-input>
                </el-form-item>
                <el-form-item label="使用人姓名" prop="managerName">
                    <el-input v-model="searchForm.managerName" placeholder="请输入使用人姓名"></el-input>
                </el-form-item>
                <el-form-item label="启用状态" prop="status">
                    <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                        <el-option label="启用" value="1"></el-option>
                        <el-option label="禁用" value="0"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item class="btns">
                    <el-button type="primary" size="small" plain icon="el-icon-refresh"
                        @click="handleReset">重置</el-button>
                    <el-button type="primary" size="small" icon="el-icon-search"
                        @click="handleSearch">搜索</el-button>
                </el-form-item>
            </el-form> -->
            <div class="yq-table">
                <el-table stripe :data="tableData.data.data" height="100%" fit ref="table" style="width: 100%">
                    <el-table-column prop="MANAGER_ID" label="企微管家USER ID" align="center" min-width="150">
                    </el-table-column>
                    <el-table-column prop="MANAGER_NAME" label="使用人（姓名）" align="center" min-width="120">
                    </el-table-column>
                    <el-table-column prop="CREATE_TIME" label="创建时间" align="center" min-width="150">
                    </el-table-column>

                    <el-table-column prop="BRAND_CODE" label="品牌名称" align="center" min-width="120">
                    </el-table-column>

                    <el-table-column prop="PRODUCT_CODE" label="产品型号" align="center" min-width="120">
                    </el-table-column>

                    <el-table-column prop="BUSI_TYPE_CODE" label="实施业务类型" align="center" min-width="140">
                    </el-table-column>

                    <el-table-column prop="ORDER_SOURCE_CODE" label="单据来源" align="center" min-width="120">
                    </el-table-column>
                    <el-table-column prop="STATUS" label="启用状态" align="center" min-width="100">
                        <template slot-scope="scope">
                            <el-tag :type="scope.row.STATUS === '1' ? 'success' : 'danger'">
                                {{ scope.row.STATUS === '1' ? '启用' : '禁用' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" width="150">
                        <template slot-scope="scope">
                            <span class="operation-btn" @click="handleEdit(scope.row)">编辑</span>
                            <span class="operation-btn" style="color: red;" @click="handleDelete(scope.row)">删除</span>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination background @current-change="onPageChange" @size-change="onPageSizeChange"
                               :current-page="tableData.pageIndex" :page-size="tableData.pageSize"
                               :page-sizes="[15, 30, 50, 100]" layout="total, prev, pager, next, jumper,sizes"
                               :total="tableData.data.totalRow">
                </el-pagination>
            </div>
        </div>
    </div>

    <!-- 新增/编辑抽屉 -->
    <el-drawer :title="dialogStatus === 'create' ? '新增企微管家' : '编辑企微管家'" :visible.sync="dialogVisible"
               direction="rtl" size="600px" :destroy-on-close="true" custom-class="demo-drawer">
        <div class="drawer-content yq-drawer-content">
            <el-form :model="formData" ref="managerForm" :rules="rules" size="small" label-width="135px">
                <el-form-item label="企微管家USER ID" prop="managerId">
                    <el-input v-model="formData.managerId" placeholder="请输入企微管家USER ID"></el-input>
                </el-form-item>
                <el-form-item label="使用人姓名" prop="managerName">
                    <el-input v-model="formData.managerName" placeholder="请输入使用人姓名"></el-input>
                </el-form-item>
                <el-form-item label="品牌名称" prop="brandCode">
                    <el-input v-model="formData.brandCode" placeholder="请输入品牌编码"></el-input>
                </el-form-item>
                <!--                    <el-form-item label="品牌名称" prop="brandName">-->
                <!--                        <el-input v-model="formData.brandName" placeholder="请输入品牌名称"></el-input>-->
                <!--                    </el-form-item>-->
                <el-form-item label="产品型号" prop="productCode">
                    <el-input v-model="formData.productCode" placeholder="请输入产品编码"></el-input>
                </el-form-item>
                <!--                    <el-form-item label="产品型号" prop="productModel">-->
                <!--                        <el-input v-model="formData.productModel" placeholder="请输入产品型号"></el-input>-->
                <!--                    </el-form-item>-->
                <el-form-item label="实施业务类型" prop="busiTypeCode">
                    <el-input v-model="formData.busiTypeCode" placeholder="请输入业务类型编码"></el-input>
                </el-form-item>
                <!--                    <el-form-item label="实施业务类型" prop="busiType">-->
                <!--                        <el-input v-model="formData.busiType" placeholder="请输入实施业务类型"></el-input>-->
                <!--                    </el-form-item>-->
                <el-form-item label="单据来源" prop="orderSourceCode">
                    <el-input v-model="formData.orderSourceCode" placeholder="请输入单据来源编码"></el-input>
                </el-form-item>
                <!--                    <el-form-item label="单据来源" prop="orderSource">-->
                <!--                        <el-input v-model="formData.orderSource" placeholder="请输入单据来源"></el-input>-->
                <!--                    </el-form-item>-->
                <el-form-item label="启用状态" prop="status">
                    <el-radio-group v-model="formData.status">
                        <el-radio label="1">启用</el-radio>
                        <el-radio label="0">禁用</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
        </div>
        <div class="yq-drawer-footer">
            <el-button size="small" @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" size="small" @click="submitForm">确 定</el-button>
        </div>
    </el-drawer>

    <!-- 导入弹窗 -->
    <el-dialog title="导入" :visible.sync="importDialogVisible" width="420px" :close-on-click-modal="false">
        <el-form label-width="80px" size="small">
            <el-form-item label="导入文件">
                <el-upload ref="upload" :action="uploadUrl" :data="uploadData" :before-upload="beforeUpload"
                           :on-success="handleUploadSuccess" :on-error="handleUploadError" :on-change="handleFileChange"
                           :auto-upload="false" :show-file-list="true" :limit="1" accept=".xls,.xlsx">
                    <el-button size="small" type="primary">选择文件</el-button>
                    <div slot="tip" class="el-upload__tip">只能上传 xls/xlsx 文件!,只能上传一个文件！</div>
                </el-upload>
            </el-form-item>
            <el-form-item label="下载模板">
                <el-button type="text" @click="handleDownloadTemplate">模板</el-button>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" plain size="small" @click="handleImportCancel">取 消</el-button>
            <el-button type="primary" size="small" @click="handleUpload" :loading="uploading">上 传</el-button>
        </div>
    </el-dialog>
</div>
</body>
<script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
<script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
<script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
<script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
<script>
    var appPage = new Vue({
        el: "#managerConfig",
        watch: {
            "tableData.data.data": function () {
                this.$nextTick(() => {
                    this.$refs.table.doLayout(); // 解决fixed固定行错行问题
                });
            },
        },
        data: function () {
            return {
                loading: false,
                dialogVisible: false,
                dialogStatus: 'create', // create 或 update
                searchForm: {
                    managerId: "",
                    managerName: "",
                    brandCode: "",
                    brandName: "",
                    productCode: "",
                    productModel: "",
                    busiTypeCode: "",
                    busiType: "",
                    orderSourceCode: "",
                    orderSource: "",
                    status: ""
                },
                tableData: {
                    pageType: 3,
                    pageIndex: 1,
                    pageSize: 15,
                    totalRow: 0,
                    data: {
                        data: [],
                        totalRow: 0
                    }
                },
                formData: {
                    id: "",
                    managerId: "",
                    managerName: "",
                    brandCode: "",
                    brandName: "",
                    productCode: "",
                    productModel: "",
                    busiTypeCode: "",
                    busiType: "",
                    orderSourceCode: "",
                    orderSource: "",
                    status: "1"
                },
                rules: {
                    managerId: [{ required: true, message: '请输入企微管家USER ID', trigger: 'blur' }],
                    managerName: [{ required: true, message: '请输入使用人姓名', trigger: 'blur' }],
                    brandCode: [{ required: true, message: '请输入品牌编码', trigger: 'blur' }],
                    busiTypeCode: [{ required: true, message: '请输入业务类型编码', trigger: 'blur' }],
                    orderSourceCode: [{ required: true, message: '请输入单据来源编码', trigger: 'blur' }],
                    status: [{ required: true, message: '请选择启用状态', trigger: 'change' }]
                },
                importDialogVisible: false,
                uploading: false,
                uploadUrl: '/wecom/servlet/ManagerConfig?action=Import',
                uploadData: {},
                fileList: []
            };
        },
        methods: {
            // 列表分页查询
            onPageChange: function (page) {
                this.tableData.pageIndex = page;
                this.tableData.data.data = null;
                this.getList();
            },
            onPageSizeChange: function (size) {
                this.tableData.pageSize = size;
                this.tableData.pageIndex = 1;
                this.tableData.data.data = null;
                this.getList();
            },
            handleReset: function () {
                this.$refs["searchForm"].resetFields();
            },
            handleSearch: function () {
                this.tableData.data.data = null;
                this.tableData.pageIndex = 1;
                this.getList();
            },
            // 获取企微管家清单列表
            getList: function () {
                this.loading = true;
                let params = {
                    ...this.searchForm,
                    pageIndex: this.tableData.pageIndex,
                    pageSize: this.tableData.pageSize,
                    pageType: this.tableData.pageType
                };

                yq.daoCall({
                    params: params,
                    controls: ['ManagerConfigDao.getList']
                }, (result) => {
                    this.loading = false;
                    if (result && result['ManagerConfigDao.getList']) {
                        const res = result['ManagerConfigDao.getList'];
                        this.tableData.data.data = res.data || [];
                        this.tableData.data.totalRow = res.totalRow || 0;
                        this.tableData.totalRow = res.totalRow || 0;
                    } else {
                        this.$message.error('获取数据失败');
                        this.tableData.data.data = [];
                        this.tableData.data.totalRow = 0;
                        this.tableData.totalRow = 0;
                    }
                }, { contextPath: "/wecom" });
            },
            // 处理新增功能
            handleAdd: function () {
                this.dialogStatus = 'create';
                this.resetFormData();
                this.dialogVisible = true;
            },
            // 处理编辑功能
            handleEdit: function (row) {
                this.dialogStatus = 'update';
                this.resetFormData();
                this.formData.id = row.ID;
                this.formData.managerId = row.MANAGER_ID;
                this.formData.managerName = row.MANAGER_NAME;
                this.formData.brandCode = row.BRAND_CODE || "";
                this.formData.brandName = row.BRAND_NAME || "";
                this.formData.productCode = row.PRODUCT_CODE || "";
                this.formData.productModel = row.PRODUCT_MODEL || "";
                this.formData.busiTypeCode = row.BUSI_TYPE_CODE || "";
                this.formData.busiType = row.BUSI_TYPE || "";
                this.formData.orderSourceCode = row.ORDER_SOURCE_CODE || "";
                this.formData.orderSource = row.ORDER_SOURCE || "";
                this.formData.status = row.STATUS;
                this.dialogVisible = true;
            },
            // 处理删除功能
            handleDelete: function (row) {
                this.$confirm('确定要删除该企微管家配置吗?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    yq.remoteCall(
                        "/wecom/servlet/ManagerConfig?action=Delete",
                        {
                            id: row.ID
                        },
                        (result) => {
                            if (result.state == 1) {
                                this.$message.success('删除成功');
                                this.getList();
                            } else {
                                this.$message.error(result.msg || '删除失败');
                            }
                        }
                    );
                }).catch(() => {
                    this.$message.info('已取消删除');
                });
            },
            // 重置表单数据
            resetFormData() {
                this.formData = {
                    id: "",
                    managerId: "",
                    managerName: "",
                    brandCode: "",
                    brandName: "",
                    productCode: "",
                    productModel: "",
                    busiTypeCode: "",
                    busiType: "",
                    orderSourceCode: "",
                    orderSource: "",
                    status: "1"
                };
            },
            // 提交表单
            submitForm() {
                this.$refs.managerForm.validate(
                    (valid) => {
                        if (valid) {
                            const action = this.dialogStatus === 'create' ? 'Add' : 'Update';
                            yq.remoteCall(
                                `/wecom/servlet/ManagerConfig?action=${action}`,
                                this.formData,
                                (result) => {
                                    if (result.state == 1) {
                                        this.$message.success(this.dialogStatus === 'create' ? '新增成功' : '更新成功');
                                        this.dialogVisible = false;
                                        this.getList();
                                    } else {
                                        this.$message.error(result.msg || (this.dialogStatus === 'create' ? '新增失败' : '更新失败'));
                                    }
                                }
                            );
                        } else {
                            return false;
                        }
                    });
            },
            // 批量导入
            handleImport() {
                this.fileList = [];
                this.uploading = false;
                this.importDialogVisible = true;
                // 确保弹窗打开时清理之前的文件
                this.$nextTick(() => {
                    if (this.$refs.upload) {
                        this.$refs.upload.clearFiles();
                    }
                });
            },
            // 上传前验证
            beforeUpload(file) {
                const isExcel = file.type === 'application/vnd.ms-excel' ||
                    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                const isLt2M = file.size / 1024 / 1024 < 2;

                if (!isExcel) {
                    this.$message.error('导入文件格式不正确！');
                    return false;
                }
                if (!isLt2M) {
                    this.$message.error('上传文件大小不能超过 2MB!');
                    return false;
                }
                return true;
            },
            // 文件变化处理
            handleFileChange(file, fileList) {
                this.fileList = fileList;
            },
            // 执行上传
            handleUpload() {
                if (!this.fileList || this.fileList.length === 0) {
                    this.$message.warning('请选择导入文件！');
                    return;
                }
                this.uploading = true;
                this.$refs.upload.submit();
            },
            // 上传成功
            handleUploadSuccess(res) {
                this.uploading = false;
                if (res.state === 1) {
                    this.$message.success(res.msg || '导入成功');
                    this.fileList = [];
                    this.$refs.upload.clearFiles();
                    this.importDialogVisible = false;
                    this.getList();
                } else {
                    this.$message.error(res.msg || '导入失败');
                }
            },
            // 上传失败
            handleUploadError(err, file, fileList) {
                this.uploading = false;
                this.$message.error('上传失败，请重试');
            },
            // 下载模板
            handleDownloadTemplate() {
                window.open('/wecom/servlet/ManagerConfig?action=Download');
            },
            // 取消导入
            handleImportCancel() {
                this.fileList = [];
                if (this.$refs.upload) {
                    this.$refs.upload.clearFiles();
                }
                this.uploading = false;
                this.importDialogVisible = false;
            }
        },
        mounted() {
            this.getList();
        },
    });
</script>

</html>