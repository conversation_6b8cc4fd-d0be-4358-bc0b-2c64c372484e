<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="renderer" content="webkit">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport">
    <meta content="IE=EmulateIE8" http-equiv="X-UA-Compatible">
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta name="robots" content="index,follow">
    <meta http-equiv="pragma" content="no-cache">
    <meta http-equiv="cache-control" content="no-cache">
    <meta http-equiv="expires" content="0">

    <link rel="stylesheet" href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css">
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.0">
    <link rel="stylesheet" href="/easitline-cdn/vue-yq/theme/resetElement.css?v=1.0.0">
    <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
    <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
    <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
    <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>
    <style>
        html,
        body {
            height: 100%;
            margin: 0;
            padding: 0;
        }

        #jumpPage {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .container {
            padding: 24px;
            flex: 1;
            overflow: auto;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .text {
            font-size: 22px;
            margin-bottom: 24px;
        }

        .footer {
            padding: 20px 36px;
            text-align: center;
        }

        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
    <div id="jumpPage" v-cloak>
        <div class="container">
            <img v-if="!isError" src="/wecom/static/image/2.webp" alt="" style="width: 45%;margin: auto">
            <img v-else src="/wecom/static/image/1.png" alt="">
        </div>
        <div class="footer">
            <div v-if="!isError" class="text">请点击确认到对应的跳转到相关群聊查看</div>
            <div v-else class="text">不好意思，跳转群聊失败请点击重试</div>
            <el-button :disabled="disabled" type="primary" @click="handleAction">确定</el-button>
        </div>
    </div>
    <script src="//res.wx.qq.com/open/js/jweixin-1.2.0.js"></script>
    <script src="https://open.work.weixin.qq.com/wwopen/js/jwxwork-1.0.0.js"></script>
    <script>
        var app = new Vue({
            el: '#jumpPage',
            data() {
                return {
                    disabled: false,
                    isError: false,
                    chatId: "",
                    userId: "",
                    externalUserId: "",
                    serviceOrderNumber: ""
                }
            },
            methods: {
                // 企微JSSDK初始化
                wecomJsSdkInit() {
                    yq.remoteCall("/wecom/servlet/config?action=sdkInit", { url: location.href }, result => {
                        if (result.state == 1) {
                            wx.agentConfig({
                                corpid: result.data.corpid,
                                agentid: result.data.agentId,
                                timestamp: result.data.timestamp,
                                nonceStr: result.data.nonceStr,
                                signature: result.data.signature,
                                jsApiList: ['openEnterpriseChat','closeWindow'],
                                success: (res) => {
                                    this.disabled = false;
                                },
                                fail: (res) => {
                                    console.error("SDK初始化失败:", JSON.stringify(res));
                                    if (res.errMsg.indexOf('function not exist') > -1) {
                                        alert('版本过低请升级');
                                    }
                                }
                            });
                        }
                    }, error => {
                        console.error(error);
                    });
                },
                // 处理操作按钮点击
                handleAction() {
                    // 判断是否有chatId，如果没有则使用userId和externalUserId
                    if (this.chatId) {
                        wx.openEnterpriseChat({
                            groupName: '讨论组',
                            userIds: [],
                            externalUserIds: [],
                            chatId: this.chatId,
                            success: (res) => {
                                wx.closeWindow();
                            },
                            fail: (res) => {
                                this.isError=true;
                                console.error("打开企业群聊失败:", JSON.stringify(res));
                                if (res.errMsg.indexOf('function not exist') > -1) {
                                    alert('版本过低请升级');
                                }
                            }
                        });
                    } else {
                        this.disabled = true;
                        // const userIds = this.userId ? [this.userId] : [];
                        // const externalUserIds = this.externalUserId ? [this.externalUserId] : [];

                        // 没有chatId时，使用userId和externalUserId
                        wx.openEnterpriseChat({
                            groupName: this.serviceOrderNumber || '讨论组',
                            userIds: this.userId||'',
                            externalUserIds: this.externalUserId||'',
                            success: (res) => {
                                this.disabled = false;
                                console.log("打开企业群聊成功:", JSON.stringify(res))
                                wx.closeWindow();

                            },
                            fail: (res) => {
                                this.isError=true;
                                this.disabled = false;
                                console.error("打开企业群聊失败:", JSON.stringify(res));
                                if (res.errMsg.indexOf('function not exist') > -1) {
                                    alert('版本过低请升级');
                                }
                            }
                        });
                    }
                }
            },
            created() {
                this.chatId = yq.p("chatGroupId");
                this.userId = yq.p("userId");
                this.externalUserId = yq.p("externalUserId");
                this.serviceOrderNumber = yq.p("serviceOrderNumber");
                // 加载JSSDK
                this.wecomJsSdkInit();
            }
        });
    </script>
</body>

</html>