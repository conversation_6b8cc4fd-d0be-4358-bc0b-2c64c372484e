<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>群工单列表</title>
    <style>
        #menuContent {display:none;position: absolute;border:1px solid rgb(170,170,170);max-width: 220px; max-height: 350px;z-index:10;overflow: auto;background-color: #f4f4f4}

        /* 群工单列表列宽度优化 */
        .col-group-name { width: 150px; min-width: 150px; }
        .col-create-date { width: 100px; min-width: 100px; }
        .col-steward { width: 80px; min-width: 80px; }
        .col-account { width: 100px; min-width: 100px; }
        .col-site-code { width: 80px; min-width: 80px; }
        .col-site-name { width: 120px; min-width: 120px; }
        .col-contact { width: 80px; min-width: 80px; }
        .col-area { width: 80px; min-width: 80px; }
        .col-user-name { width: 80px; min-width: 80px; }
        .col-phone { width: 110px; min-width: 110px; }
        .col-service-order { width: 250px; min-width: 250px; }
        .col-address { width: 200px; min-width: 200px; }
        .col-count { width: 70px; min-width: 70px; }
        .col-org { width: 100px; min-width: 100px; }
        .col-brand { width: 80px; min-width: 80px; }
        .col-product { width: 80px; min-width: 80px; }
        .col-model { width: 120px; min-width: 120px; }
        .col-unit { width: 100px; min-width: 100px; }
        .col-sales-person { width: 80px; min-width: 80px; }
        .col-sales-phone { width: 110px; min-width: 110px; }
        .col-status { width: 80px; min-width: 80px; }
        .col-norms { width: 70px; min-width: 70px; }
        .col-reason { width: 120px; min-width: 120px; }
        .col-check-time { width: 130px; min-width: 130px; }
        .col-checker { width: 120px; min-width: 120px; }
        .col-group-status { width: 70px; min-width: 70px; }
        .col-backup { width: 150px; min-width: 150px; }
        .col-business { width: 80px; min-width: 80px; }
        .col-create-time { width: 130px; min-width: 130px; }
        .col-creator { width: 80px; min-width: 80px; }
        .col-view-action { width: 140px; min-width: 140px; }
        .col-edit-action { width: 80px; min-width: 80px; }

        /* 右侧固定列样式 */
        .col-view-action.fixed-right-col,.col-edit-action.fixed-right-col {
            position: sticky;
            background-color: #fff;
            z-index: 10;
            border-left: 1px solid #ddd;
        }

        /* 会话记录查看与质检列固定在右侧 */
        .col-view-action.fixed-right-col {
            right: 80px; /* 修改工单列的宽度 */
        }

        /* 修改工单列固定在最右侧 */
        .col-edit-action.fixed-right-col {
            right: 0;
        }

        /* 固定列悬停效果 */
        .fixed-right-col:hover {
            background-color: #f8f9fa;
        }

        /* 表格容器支持横向滚动 */
        .table-container {
            flex:1;
            overflow: auto;
            white-space: nowrap;
            position: relative; /* 为sticky定位提供参考 */
           

        /* 表格单元格内容不换行 */
        .table th, .table td {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false" data-toggle="">
        <div class="ibox">
                <%--搜索栏--%>
            <div class="ibox-title clearfix">
                <div class="form-group">
                    <h5><span class="glyphicon glyphicon-list"></span> 群工单</h5>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">群名称</span>
                        <input class="form-control input-sm" name="GROUP_CHAT_NAME" width="140px">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">建群管家</span>
                        <input class="form-control input-sm" name="GROUP_CREATE_STEWARD" width="140px">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">稽查时间</span>
                        <input type="text" name="START_TIME" id="startDate" style="width: 180px;"   class="form-control input-sm" style="width:110px" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'endDate\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">
                        <span class="input-group-addon">-</span>
                        <input type="text" name="END_TIME" id="endDate"  style="width: 180px;" class="form-control input-sm" style="width:110px" onclick="WdatePicker({minDate:'#F{$dp.$D(\'startDate\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">稽查人</span>
                        <input class="form-control input-sm" name="CHECK_NAME" width="140px">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">创建人</span>
                        <input class="form-control input-sm" name="CREATE_USER" width="140px">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">服务工单</span>
                        <input class="form-control input-sm" name="SERVICE_ORDER_NUMBER" width="140px">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">创建时间</span>
                        <input type="text" name="CREATE_START_TIME" id="startDate1" style="width: 180px;"   class="form-control input-sm" style="width:110px" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'endDate1\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">
                        <span class="input-group-addon">-</span>
                        <input type="text" name="CREATE_END_TIME" id="endDate1"  style="width: 180px;" class="form-control input-sm" style="width:110px" onclick="WdatePicker({minDate:'#F{$dp.$D(\'startDate1\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">当前进度</span>
                        <select name="ORDER_STATE"  class="form-control input-sm"data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('ORDER_STATE')" style="width:140px">
                            <option value="">请选择</option>
                        </select>
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">群状态</span>
                        <select name="GROUP_STATE" class="form-control input-sm"data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('GROUP_STATE')" style="width:140px">
                            <option value="">请选择</option>
                        </select>
                    </div>
                    <div class="input-group input-group-sm">
                        <button type="button" class="btn btn-sm btn-default" onclick="GroupOrders.reload()">
                            <span class="glyphicon glyphicon-search"></span> 搜索</button>
                    </div>
                </div>
            </div>

                <%--表单展示--%>
            <div class="ibox-content" style="height: calc(100vh - 120px);display: flex;flex-direction: column;">
                <div class="table-container">
                    <table class="table table-auto table-bordered table-hover table-condensed text-c" data-auto-fill="10" id="tableHead" data-mars="GroupOrders.list">
                    <thead>
                    <tr>
                        <th class="text-c col-group-name">群聊名称</th>
                        <th class="text-c col-create-date">建群日期</th>
                        <th class="text-c col-steward">建群管家</th>
                        <th class="text-c col-account">建群账号</th>
                        <th class="text-c col-site-code">网点编码</th>
                        <th class="text-c col-site-name">网点名称</th>
                        <th class="text-c col-contact">网点对接人</th>
                        <th class="text-c col-area">所属运中</th>
                        <th class="text-c col-user-name">用户姓名</th>
                        <th class="text-c col-phone">用户电话</th>
                        <th class="text-c col-service-order">服务工单</th>
                        <th class="text-c col-address">用户地址</th>
                        <th class="text-c col-count">产品数量</th>
                        <th class="text-c col-org">产品主体</th>
                        <th class="text-c col-brand">产品品牌</th>
                        <th class="text-c col-product">产品品类</th>
                        <th class="text-c col-model">产品型号</th>
                        <th class="text-c col-unit">销售单位</th>
                        <th class="text-c col-sales-person">销售人</th>
                        <th class="text-c col-sales-phone">销售电话</th>
                        <th class="text-c col-status">当前进度</th>
                        <th class="text-c col-norms">是否规范</th>
                        <th class="text-c col-reason">不规范原因</th>
                        <th class="text-c col-check-time">稽查时间</th>
                        <th class="text-c col-checker">稽查人</th>
                        <th class="text-c col-group-status">群状态</th>
                        <th class="text-c col-backup">备注</th>
                        <th class="text-c col-business">业务属性</th>
                        <th class="text-c col-create-time">创建时间</th>
                        <th class="text-c col-creator">创建人</th>
                        <th class="text-c col-view-action fixed-right-col">会话记录查看与质检</th>
                        <th class="text-c col-edit-action fixed-right-col">修改工单</th>
                    </tr>
                    </thead>
                    <tbody id="dataList">
                    </tbody>
                </table>
                </div>
                <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td>{{:GROUP_CHAT_NAME}}</td>
										    <td>{{:GROUP_CREATE_TIME}}</td>
										    <td>{{:GROUP_CREATE_STEWARD}}</td>
										    <td>{{:GROUP_CREATE_ACC}}</td>
										    <td>{{:SITE_CODE}}</td>
										    <td>{{:SITE_NAME}}</td>
										    <td>{{:SITE_CONTACT}}</td>
										    <td>{{:AREA_CODE}}</td>
										    <td>{{:USER_NAME}}</td>
										    <td>{{:USER_PHONE}}</td>
										    <td>{{:SERVICE_ORDER_NUMBER}}</td>
										    <td>{{:USER_ADDRESS}}</td>
										    <td>{{:PRODUCT_COUNT}}</td>
										    <td>{{:ORG_CODE}}</td>
										    <td>{{:BRAND_NAME}}</td>
										    <td>{{:PROD_NAME}}</td>
										    <td>{{:PRODUCT_MODEL}}</td>
										    <td>{{:SALE_UNIT}}</td>
										    <td>{{:SALE_PEOPLE}}</td>
										    <td>{{:SALE_PHONE}}</td>
										    <td>{{dictFUN:ORDER_STATE 'ORDER_STATE'}}</td>
										    <td>{{dictFUN:IS_NORMS 'IS_NORMS'}}</td>
										    <td>{{dictMultiFUN:REASON 'NONSTANDARD_REASON'}}</td>
										    <td>{{:CHECK_TIME}}</td>
										    <td>{{:CHECK_NAME}}</td>
										    <td>{{dictFUN:GROUP_STATE 'GROUP_STATE'}}</td>
										    <td>{{:BACKUP}}</td>
										    <td>{{dictFUN:ORDER_SOURCE 'ORDER_SOURCE'}}</td>
										    <td>{{:CREATE_TIME}}</td>
										    <td>{{:CREATE_USER}}</td>
										<td class="col-view-action fixed-right-col">
                                          <a href="javascript:GroupOrders.detailed('{{:CHAT_ID}}')">查看</a>
<%--                                          &nbsp;<a href="javascript:GroupOrders.editData('{{:ID}}')">质检</a>--%>
                                        </td>
                                        <td class="col-edit-action fixed-right-col">
                                           <a href="javascript:GroupOrders.editData('{{:ID}}')">修改</a>
                                        </td>
									    </tr>
								   {{/for}}
							 </script>

                <div class="row paginate" id="page">
                    <jsp:include page="/pages/common/pagination.jsp">
                        <jsp:param value="25" name="pageSize"/>
                    </jsp:include>
                </div>
            </div>
        </div>
    </form>
</EasyTag:override>

<EasyTag:override name="script">
    <script type="text/javascript">
        // 本地化的dictMultiFUN函数 - 支持多选值字典转换（分号分隔）
        $.views.converters('dictMultiFUN', function(val, code) {
            if (!code) {
                console.error('没有传字典编号');
                return val;
            }

            if (!val) {
                return val;
            }

            // 获取字典数据
            var dict = dictJson[code];
            if (!dict) {
                dict = getDictByCode(code);
                if (dict.length == 0) {
                    console.error('根据字典编号找不到字典项');
                    return val;
                }
                dict = dict[controls].data;
                dictJson[code] = dict;
            }

            // 处理多选值（分号分隔）
            val = "" + val;
            var vals = val.split(";");
            var result = "";

            for (var j = 0; j < vals.length; j++) {
                var singleVal = vals[j].trim();
                var dictText = dict[singleVal];

                if (dictText) {
                    if (result.length > 0) {
                        result += ",";
                    }
                    result += dictText;
                } else {
                    // 如果没找到对应的字典值，保留原值
                    if (result.length > 0) {
                        result += ",";
                    }
                    result += singleVal;
                }
            }

            return result || val;
        });

        jQuery.namespace("GroupOrders");
        $(function() {
            $("#searchForm").render({});
        });
        GroupOrders.detailed = function(chatId){
            popup.layerShow({type:2,title:'查看',area:['900px','900px'],offset:'20px'},"${ctxPath}/pages/chatrecord/zjzxtoMeiti.jsp",{sessionId:chatId});
        }

        //修改工单
        GroupOrders.editData = function(id){
            popup.layerShow({type:2,title:'修改',area:['700px','700px'],offset:'20px'},"${ctxPath}/pages/order/group-order-edit.html",{ID:id});
        }
        GroupOrders.reload= function(){
            $("#searchForm").searchData();
        }
        GroupOrders.del=function(id){
            var ids=[id];
            if(confirm("确认要删除吗？")){
                ajax.remoteCall("${ctxPath}/servlet/Orders?action=delete",{ids:ids},function(result) {
                    if(result.state == 1){
                        layer.msg(result.msg,{icon: 1});
                        GroupOrders.reload();
                    }else{
                        layer.alert(result.msg,{icon: 5});
                    }
                });
            }
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>