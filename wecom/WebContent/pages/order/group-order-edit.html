<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="renderer" content="webkit">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport">
    <meta content="IE=EmulateIE8" http-equiv="X-UA-Compatible">
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta name="robots" content="index,follow">
    <meta http-equiv="pragma" content="no-cache">
    <meta http-equiv="cache-control" content="no-cache">
    <meta http-equiv="expires" content="0">
    <link href="/wecom/static/css/element-ui.css" rel="stylesheet">
    <link href="/wecom/static/css/scroll-bar.css" rel="stylesheet">
    <link href="/wecom/static/css/chat.css" rel="stylesheet">
    <script src="/wecom/static/js/common/axios.js"></script>
    <style>
        .el-input--small .el-input__inner {
            height: 33px !important
        }

        .el-divider--horizontal {
            margin: 10px 0 !important
        }

        .el-collapse {
            border-top: 0;
            border-bottom: 0;
        }

        .helpBtnDiv {

        }

        .helpBtnDiv button {
            width: 110px;
            margin-bottom: 10px;
            margin-left: 20px !important;
        }

        .product-box {
            position: relative;
            width: 100%;
            height: 160px;
        }

        .el-icon-delete {
            position: absolute;
            right: 0px;
            top: 0px;
            cursor: pointer;
        }

        .el-form-item__content {
            min-height: 29px;
        }

        .el-collapse-item__content {
            padding-bottom: 0px;
        }

        .el-collapse-item__header {
            height: 32px;
            font-size: 11px;
        }

        .el-form-item__label {
            font-size: 10px;
        }

        .el-switch {
            width: 150px;
        }

        .el-switch__label.is-active {
            width: 70px;
        }

        .color-red .el-form-item__label {
            color: red;
        }

        .color-blue .el-form-item__label {
            color: blue;
        }

        .color-blue {
            color: blue;
            font-size: 10px;
        }

        .pTips {
            margin: 5px 15px 5px 15px;
            font-size: 12px;
            color: #eb2b1d;
            font-weight: bold;
        }

        .buttonType {
            display: block;
            margin-left: auto;
            margin-right: auto;
        }

        [v-cloak] {
            display: none;
        }
    </style>
</head>
<body>
<div id="app">
    <div v-cloak>
        <el-form ref="formData" :model="formData" :rules="rules" size="mini" label-width="82px"
                 :hide-required-asterisk='true'>
            <el-row>
                <el-input type="textarea" v-model="input" @blur="TemplateParsing"
                          placeholder="请将需要录入的信息按模板粘贴到这里"></el-input>
                <el-col :span="6" :xs="12">
                    <el-form-item label="群名" prop="GROUP_CHAT_NAME">
                        <el-input v-model="formData.GROUP_CHAT_NAME" id="GROUP_CHAT_NAME"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="8" :xs="12">
                    <el-form-item label="建群日期" prop="GROUP_CREATE_TIME">
                        <el-date-picker v-model="formData.GROUP_CREATE_TIME" id="GROUP_CREATE_TIME"
                                        format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                                        :style="{width: '100%'}" type="datetime" clearable></el-date-picker>
                    </el-form-item>
                </el-col>

                <el-col :span="6" :xs="12">
                    <el-form-item label="建群管家" prop="GROUP_CREATE_STEWARD">
                        <el-input v-model="formData.GROUP_CREATE_STEWARD" id="GROUP_CREATE_STEWARD"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="6" :xs="12">
                    <el-form-item label="建群账号" prop="GROUP_CREATE_ACC">
                        <el-input v-model="formData.GROUP_CREATE_ACC"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="6" :xs="12">
                    <el-form-item label="网点编码" prop="SITE_CODE">
                        <el-input v-model="formData.SITE_CODE" @blur="getSiteMessage"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="6" :xs="12">
                    <el-form-item label="网点名称" prop="SITE_NAME">
                        <el-input v-model="formData.SITE_NAME"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="6" :xs="12">
                    <el-form-item label="网点对接人" prop="SITE_CONTACT">
                        <el-input v-model="formData.SITE_CONTACT"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="6" :xs="12">
                    <el-form-item label="所属运中" prop="AREA_CODE">
                        <el-input v-model="formData.AREA_CODE"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="6" :xs="12">
                    <el-form-item label="用户姓名" prop="USER_NAME">
                        <el-input v-model="formData.USER_NAME"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="6" :xs="12">
                    <el-form-item label="用户电话" prop="USER_PHONE">
                        <el-input v-model="formData.USER_PHONE" id="USER_PHONE"
                                  @blur="checkNum" :maxlength="20" clearable :style="{width: '100%'}">
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="6" :xs="12">
                    <el-form-item label="用户地址" prop="USER_ADDRESS">
                        <el-input v-model="formData.USER_ADDRESS"></el-input>
                    </el-form-item>
                </el-col>

                <!--主体品牌禁用取消 disabled-->
                <el-col :span="6" :xs="12">
                    <el-form-item label="销售单位" prop="SALE_UNIT">
                        <el-input v-model="formData.SALE_UNIT"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="24" :xs="24">
                    <el-form-item label="服务工单" prop="SERVICE_ORDER">
                        <div style="display: flex; align-items: center;">
                            <el-input v-model="serviceOrderInput"
                                      placeholder="请输入服务工单号"
                                      style="width: 200px; margin-right: 10px;">
                            </el-input>
                            <el-button type="primary" size="mini" @click="addServiceOrder">添加</el-button>
                        </div>
                        <div style="margin-top: 10px;">
                            <el-tag
                                    v-for="(order, index) in serviceOrders"
                                    :key="index"
                                    closable
                                    style="margin-right: 5px; margin-bottom: 5px;"
                                    @close="removeServiceOrder(index)">
                                {{order}}
                            </el-tag>
                        </div>
                    </el-form-item>
                </el-col>

            </el-row>

            <!--动态增加品类等相关信息-->
            <el-row>
                <el-form-item label="产品信息" prop="productList">
                    <el-button style="pointer-events: auto;" icon="el-icon-plus" @click="toAdd"></el-button>
                    <div style="pointer-events: auto;" class="product-box"
                         v-for="(item,index) in formData.productList" :key="index">
                        <i class="el-icon-delete" @click="toDel(index)" v-if="formData.productList.length>0"></i>
                        <el-col :span="6" :xs="11">
                            <div class="color-blue">产品主体</div>
                            <el-form-item :rules="rules.orgCode" label-width="0px" label="" :prop="`productList.${index}.orgCode`">
                                <el-select v-model="item.orgCode" :style="{width: '100%'}">
                                    <el-option v-for="(item, index) in orgCodeOptions" :key="index" :label="item"
                                               :value="index"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6" :xs="12">
                            <div class="color-blue" style="margin-left: 24px;">产品品牌</div>
                            <el-form-item :rules="rules.brandName" label-width="24px" label=""  :prop="`productList.${index}.brandName`">
                                <el-input v-model="item.brandName" :style="{width: '100%'}">
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6" :xs="11">
                            <div class="color-blue">产品品类</div>
                            <el-form-item :rules="rules.prodName" label-width="0px" label=""  :prop="`productList.${index}.prodName`">
                                <el-input v-model="item.prodName"
                                          @keyup.enter.native="openProductCodeDiv()" :style="{width: '100%'}">
                                    <el-button slot="append" icon="el-icon-search"
                                               @click="openProductCodeDiv(item,index)"></el-button>
                                </el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6" :xs="12">
                            <div style="font-size: 10px;margin-left: 24px;">产品型号</div>
                            <el-form-item :rules="rules.productModel" label-width="24px"  :prop="`productList.${index}.productModel`">
                                <el-input v-model="item.productModel"
                                          @keyup.enter.native="openProductModelDiv()" :style="{width: '100%'}">
                                    <el-button slot="append" icon="el-icon-search"
                                               @click="openProductModelDiv(item,index)"></el-button>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </div>

                </el-form-item>
            </el-row>
            <el-row>
                <el-col :span="6" :xs="12">
                    <el-form-item label="销售人员" prop="SALE_PEOPLE">
                        <el-input v-model="formData.SALE_PEOPLE"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="6" :xs="12">
                    <el-form-item label="销售人员电话" prop="SALE_PHONE">
                        <el-input v-model="formData.SALE_PHONE" id="SALE_PHONE"
                                  @blur="checkNum" :maxlength="20" clearable :style="{width: '100%'}">
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="24" :xs="24">
                    <el-form-item label="备注">
                        <el-input v-model="formData.BACKUP" id="backup" type="textarea"
                                  :autosize="{minRows: 4, maxRows: 4}"
                                  :style="{width: '100%'}"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="6" :xs="12">
                    <el-form-item label="业务属性" prop="ORDER_SOURCE">
                        <el-select v-model="formData.ORDER_SOURCE" id="ORDER_SOURCE" clearable
                                   :style="{width: '100%'}">
                            <el-option v-for="(item, index) in orderSourceOptions" :key="index" :label="item"
                                       :value="index" :disabled="item.disabled"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="6" :xs="12">
                    <el-form-item label="当前进度" prop="ORDER_STATE">
                        <el-select v-model="formData.ORDER_STATE" id="ORDER_STATE" clearable
                                   :style="{width: '100%'}" @change="handleFieldChange">
                            <el-option v-for="(item, index) in orderStateOptions" :key="index" :label="item"
                                       :value="index" :disabled="item.disabled"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="8" :xs="12">
                    <el-form-item label="是否规范" prop="IS_NORMS">
                        <el-select v-model="formData.IS_NORMS" id="IS_NORMS" clearable
                                   :style="{width: '100%'}" @change="handleReasonRules">
                            <el-option v-for="(item, index) in isNormsOptions" :key="index" :label="item"
                                       :value="index" :disabled="item.disabled"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="6" :xs="12">
                    <el-form-item label="不规范原因" prop="REASON">
                        <el-select v-model="formData.REASON" id="REASON" clearable multiple
                                   :style="{width: '100%'}" @change="handleFieldChange"
                                   placeholder="请选择不规范原因">
                            <el-option v-for="(item, index) in reasonOptions" :key="index" :label="item"
                                       :value="index.toString()" :disabled="item.disabled"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="8" :xs="12">
                    <el-form-item label="稽查时间" prop="CHECK_TIME">
                        <el-date-picker v-model="formData.CHECK_TIME" id="CHECK_TIME" format="yyyy-MM-dd HH:mm:ss"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        :style="{width: '100%'}" type="datetime" clearable @change="handleCheckTimeChange"></el-date-picker>
                    </el-form-item>
                </el-col>

                <el-col :span="6" :xs="12">
                    <el-form-item label="稽查人" prop="CHECK_NAME">
                        <el-input v-model="formData.CHECK_NAME"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="8" :xs="12">
                    <el-form-item label="群状态" prop="GROUP_STATE">
                        <el-select v-model="formData.GROUP_STATE" id="GROUP_STATE" clearable
                                   :style="{width: '100%'}" disabled>
                            <el-option v-for="(item, index) in groupStateOptions" :key="index" :label="item"
                                       :value="index" :disabled="item.disabled"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="8" :xs="12">
                    <el-form-item label="提醒时间" prop="REMIND_TIME">
                        <el-date-picker v-model="formData.REMIND_TIME" id="REMIND_TIME" format="yyyy-MM-dd HH:mm:ss"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        :style="{width: '100%'}" type="datetime" clearable></el-date-picker>
                    </el-form-item>
                </el-col>

            </el-row>
            <el-row>
                <el-col>
                    <el-button type="success" size="mini" class="buttonType" @click="submitGroupOrder('formData')"> 提交
                    </el-button>
                </el-col>
            </el-row>
        </el-form>

    </div>
</div>

<script type="text/javascript" src="/easitline-static/js/jquery.min.js"></script>
<script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
<script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js?v=20240123"></script>
<script type="text/javascript" src="/wecom/static/js/vue/vue.min.js"></script>
<script type="text/javascript" src="/wecom/static/js/element/element-ui.js"></script>
<script type="text/javascript" src="/wecom/static/js/common/common.js?v=20240123"></script>
<script type="text/javascript" src="/wecom/static/js/order/group-order-vue.js?v=20250814"></script>
<script type="text/javascript" src="/wecom/static/js/order/neworder.js?v=20240123"></script>
<script type="text/javascript" src="/wecom/static/js/order/neworder.layer.js?v=20240123"></script>
<script>
    //群聊Id
    var chatId = "";
</script>
</body>
</html>
