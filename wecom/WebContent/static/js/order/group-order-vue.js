var orderVue = new Vue({
    el: '#app',
    //初始化数据变量
    data() {
        return {
            //粘贴模板
            input: "",
            //获取稽查人信息 (格式：姓名账号)
            checkName: "",
            //工单录入表单数据
            productIndex: 0,
            //判断是企微页面还是工单修改页面
            // orderId: "",
            //表单数据
            formData: {
                //主键 用于后端新增或更新
                ID: "",
                //调用已经封装好的接口，必须驼峰
                productList: [{
                    orgCode: "",
                    brandName: "",
                    brandCode: "",
                    prodName: "",
                    prodCode: "",
                    productModel: "",
                    productCode:""
                }],
                //群工单
                chatId: "",
                GROUP_CHAT_NAME: "",
                GROUP_CREATE_TIME: "",
                GROUP_CREATE_STEWARD: "",
                GROUP_CREATE_ACC: "",
                SITE_CODE: "",
                SITE_NAME: "",
                SITE_CONTACT: "",
                AREA_CODE: "",
                USER_NAME: "",
                USER_PHONE: "",
                USER_ADDRESS: "",
                SALE_UNIT: "",
                SALE_PEOPLE: "",
                SALE_PHONE: "",
                BACKUP: "",
                ORDER_SOURCE: "",
                ORDER_STATE: "",
                IS_NORMS: "",
                REASON: [],
                CHECK_TIME: "",
                CHECK_NAME: "",
                GROUP_STATE: "1", // 默认设置为"使用中"
                //提醒时间
                REMIND_TIME: "",
                SERVICE_ORDER_NUMBER: "",
            },

            //SDK初始化重试次数
            sdkInitRetryCount: 0,
            maxRetryCount: 3,

            // 数据验证
            rules: {
                GROUP_CHAT_NAME: [{
                    required: true,
                    message: '请输入群名',
                    trigger: 'change'
                }],
                GROUP_CREATE_TIME: [{
                    required: true,
                    message: '请选择建群日期',
                    trigger: 'blur'
                }],
                GROUP_CREATE_STEWARD: [{
                    required: true,
                    message: '请输入建群管家',
                    trigger: 'change'
                }],
                GROUP_CREATE_ACC: [{
                    required: true,
                    message: '请输入建群账号',
                    trigger: 'change'
                }],
                SITE_CODE: [{
                    required: true,
                    message: '请输入网点编码',
                    trigger: 'change'
                }],
                SITE_NAME: [{
                    required: true,
                    message: '请输入网点名称',
                    trigger: 'change'
                }],
                SITE_CONTACT: [{
                    required: false,
                    message: '请输入网点对接人',
                    trigger: 'change'
                }],
                AREA_CODE: [{
                    required: true,
                    message: '请输入所属运中',
                    trigger: 'change'
                }],
                USER_NAME: [{
                    required: false,
                    message: '请输入用户姓名',
                    trigger: 'change'
                }],

                USER_PHONE: [{
                    required: false,
                    message: '请输入用户手机',
                    trigger: 'change'
                }],

                USER_ADDRESS: [{
                    required: true,
                    message: '请输入用户地址',
                    trigger: 'change'
                }],

                orgCode: [{
                    required: true,
                    message: '请选择产品主体',
                    trigger: 'change'
                }],
                brandName: [{
                    required: true,
                    message: '请输入产品品牌',
                    trigger: 'change'
                }],
                prodName: [{
                    required: true,
                    message: '请输入产品品类',
                    trigger: 'change'
                }],
                productModel: [{
                    required: false,
                    message: '请输入型号',
                    trigger: 'change'
                }],
                ORDER_SOURCE: [{
                    required: true,
                    message: '请选输入单据来源',
                    trigger: 'change'
                }],
                ORDER_STATE: [{
                    required: true,
                    message: '请选择当前进度',
                    trigger: 'change'
                }],
                IS_NORMS: [{
                    required: false,
                    message: '请确认是否规范',
                    trigger: 'change'
                }],
                CHECK_TIME: [{
                    required: false,
                    message: '请选择稽查时间',
                    trigger: 'blur'
                }],
                CHECK_NAME: [{
                    required: false,
                    message: '请输入稽查人',
                    trigger: 'change'
                }],
                GROUP_STATE: [{
                    required: false,
                    message: '请选择群状态',
                    trigger: 'change'
                }],
                REASON: [{
                    required: false,
                    message: '请选择不规范原因',
                    trigger: 'change'
                }]
            },

            //字典类型
            type: {
                param: ('ERROR_APPTYPE')
            },
            orderSourceType: {
                param: ('ORDER_SOURCE')
            },
            orderStateType: {
                param: ('ORDER_STATE')
            },
            isNormsType: {
                param: ('IS_NORMS')
            },
            groupStateType: {
                param: ('GROUP_STATE')
            },
            reasonType: {
                param: ('NONSTANDARD_REASON')
            },

            //字典下拉框
            orderSourceOptions: [], //单据来源
            orderStateOptions: [], //当前进度
            isNormsOptions: [], //是否规范
            groupStateOptions: [], //群状态
            reasonOptions: [], //不规范原因
            orgCodeOptions: [], //主体

            serviceOrderInput: '', // 服务工单输入框的值
            serviceOrders: [], // 存储所有服务工单号的数组
        }
    },

    methods: {
        //修改不规范原因判断规则
        handleReasonRules(newVal) {
            if (newVal === '是') {
                this.rules.REASON = [{
                    required: false,
                    message: '请选择不规范原因',
                    trigger: 'change'
                }];
                this.formData.REASON = ["1"];
            }
            if (newVal === '否') {
                this.rules.REASON = [{
                    required: true,
                    message: '请选择不规范原因',
                    trigger: 'change'
                }];
                this.formData.REASON = [];
            }
            // 更新稽查时间为当前时间
            this.formData.CHECK_TIME = this.getCurrentTime(0);
            // 更新稽查人信息
            this.formData.CHECK_NAME = this.checkName;
        },

        // 处理字段变化并更新稽查时间
        handleFieldChange() {
            this.formData.CHECK_TIME = this.getCurrentTime(0);
            // 更新稽查人信息
            this.formData.CHECK_NAME = this.checkName;
        },

        //封装查询方法
        webcall(url, param, callback) {
            axios.post(url, param, {
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                },
            })
                .then((result) => {
                    callback && callback(result);
                });
        },

        //提交群工单
        submitGroupOrder(formName) {
            //表单判空检验
            this.$refs['formData'].validate((valid) => {
                if (valid) {
                    //设置群ID
                    this.formData.chatId = chatId;
                    console.log("工单提交时设置群ID：",this.formData.chatId)
                    // 表单验证通过，执行提交操作
                    var vo = JSON.parse(JSON.stringify(orderVue.$data.formData));
                    // 更新表单数据中的服务工单字符串
                    vo.SERVICE_ORDER_NUMBER = this.serviceOrders.join(';');
                    // 将不规范原因数组转换为分号分隔的字符串
                    vo.REASON = Array.isArray(vo.REASON) ? vo.REASON.join(';') : vo.REASON;
                    ajax.remoteCall("/wecom/servlet/GroupOrders?action=Submit", vo, function (result) {
                        if (result.state === 1) { //后台出现异常会导致不进入该方法 无法再次查询获取工单id 导致重复提交
                            //关闭弹窗,重新加载父页面
                            window.parent.layer.closeAll();
                            window.parent.layer.msg("群工单提交成功", {icon: 1});
                            //重新获取群聊ID
                            orderVue.getGroupOrderByChatId()
                        } else {
                            layer.alert(result.msg);
                        }
                    });
                } else {
                    return false;
                }
            })
        },

        // 产品品类窗口
        openProductCodeDiv(item, index) {
            this.productIndex = index;
            var prodName = item.prodName;
            popup.layerShow({
                type: 2,
                title: '产品品类',
                area: ['100%', '100%'],
                offset: '20px',
                shadeClose: false
            }, "/neworder/pages/access/product-type-wecom.jsp?ccOrgCode=false&orgCode=orgCode&brandCode=brandCode&brandName=brandName&prodCode=prodCode&prodName=prodName&callback=1&wecom=1&productName=" + prodName + "&org=");
        },

        // 打开产品型号窗口
        openProductModelDiv(item, index) {
            this.productIndex = index;
            var brandCode = item.brandCode;
            var orgCode = item.orgCode;
            var prodCode = item.prodCode;
            var productModel = item.productModel;
            var param = encodeURI("brandCode=" + brandCode + "&orgCode=" + orgCode + "&prodCode=" + prodCode + "&productModel=" + productModel + "&productModelId=productModel&productCodeId=productCode&callback=1&type=1&wecom=1");
            popup.layerShow({
                type: 2,
                title: '产品型号查询',
                area: ['100%', '100%'],
                offset: '20px',
                shadeClose: false
            }, "/neworder/pages/access/product-list.jsp?" + param);
        },

        //校验手机号
        checkNum: function (event) {
            if (event) {
                if ((event.target.id == "customerMobilephone3") && (event.target.value.indexOf("-") != -1 || event.target.value.indexOf("转") != -1)) {
                    //电话号码3允许存特殊号码
                } else {
                    if (orderfunc.checkMobilephoneBlur(event.target.value)) {

                    }
                }

            }
        },

        //校验地址
        checkAddress: function (event) {
            if (event && !checkNull(event.target.value) && event.sourceCapabilities != null) {
                this.customerAddress = event.target.value;
                orderfunc.queryAreaInfoByAddress(event.target.value, 1);
            }
        },

        //获取单据来源字典
        getOrderSourceType() {
            let _this = this;
            var reqParam = {
                params: _this.orderSourceType,
                controls: ["dict.getDictList('ORDER_SOURCE')"],
            };
            var param = {data: JSON.stringify(reqParam)}; //将对象 reqParam 转换成 JSON 字符串，并将其赋值给变量 param 的 data 属性
            _this.webcall("/yq_common/webcall", param, function (result) {
                _this.orderSourceOptions = result.data["dict.getDictList('ORDER_SOURCE')"].data;
            });
        },

        //获取当前进度字典
        getOrderStateType() {
            let _this = this;
            var reqParam = {
                params: _this.orderStateType,
                controls: ["dict.getDictList('ORDER_STATE')"],
            };
            var param = {data: JSON.stringify(reqParam)}; //将对象 reqParam 转换成 JSON 字符串，并将其赋值给变量 param 的 data 属性
            _this.webcall("/yq_common/webcall", param, function (result) {
                _this.orderStateOptions = result.data["dict.getDictList('ORDER_STATE')"].data;
            });
        },

        //获取是否规范字典
        getIsNormsType() {
            let _this = this;
            var reqParam = {
                params: _this.isNormsType,
                controls: ["dict.getDictList('IS_NORMS')"],
            };
            var param = {data: JSON.stringify(reqParam)}; //将对象 reqParam 转换成 JSON 字符串，并将其赋值给变量 param 的 data 属性
            _this.webcall("/yq_common/webcall", param, function (result) {
                _this.isNormsOptions = result.data["dict.getDictList('IS_NORMS')"].data;
            });
        },

        //获取群状态字典
        getGroupStateType() {
            let _this = this;
            var reqParam = {
                params: _this.groupStateType,
                controls: ["dict.getDictList('GROUP_STATE')"],
            };
            var param = {data: JSON.stringify(reqParam)}; //将对象 reqParam 转换成 JSON 字符串，并将其赋值给变量 param 的 data 属性
            _this.webcall("/yq_common/webcall", param, function (result) {
                _this.groupStateOptions = result.data["dict.getDictList('GROUP_STATE')"].data;
            });
        },

        //获取不规范原因字典
        getReasonType() {
            let _this = this;
            var reqParam = {
                params: _this.reasonType,
                controls: ["dict.getDictList('NONSTANDARD_REASON')"],
            };
            var param = {data: JSON.stringify(reqParam)}; //将对象 reqParam 转换成 JSON 字符串，并将其赋值给变量 param 的 data 属性
            _this.webcall("/yq_common/webcall", param, function (result) {
                _this.reasonOptions = result.data["dict.getDictList('NONSTANDARD_REASON')"].data;
            });
        },

        //请求CSS
        getCSSData() {
            let req = {
                controls: ["comm.sysCode('ORG_CODE')"],
                params: {}
            }
            ajaxCall("/neworder/webcall", req, result => {
                //css
                this.orgCodeOptions = result["comm.sysCode('ORG_CODE')"].data; //主体下拉框

            }, error => {
                console.error(error);
            });
        },

        //企微初始化 - 优先使用父页面的 wx 对象
        wecomJsSdkInit() {
            console.log('群聊工单页面开始初始化企业微信 SDK...');
            console.log('检查是否可以使用父页面的 wx 对象...');

            // 尝试使用父页面的 wx 对象
            if (this.tryUseParentWx()) {
                return; // 如果成功使用父页面的 wx，直接返回
            }

            // 降级到使用子页面自己的 wx 对象
            console.log('无法使用父页面 wx 对象，使用子页面自己的 SDK...');
            this.initOwnWx();
        },

        // 尝试使用父页面的 wx 对象
        tryUseParentWx() {
            try {
                // 检查是否在 iframe 中
                if (window.parent === window) {
                    console.log('当前不在 iframe 中，无法访问父页面');
                    return false;
                }

                // 检查父页面的 wx 对象
                const parentWx = window.parent.wx;
                if (typeof parentWx === 'undefined') {
                    console.log('父页面的 wx 对象不存在');
                    return false;
                }

                // 检查父页面 wx 对象的关键方法
                if (typeof parentWx.agentConfig !== 'function') {
                    console.log('父页面的 wx.agentConfig 方法不存在');
                    return false;
                }

                console.log('✅ 检测到父页面的 wx 对象可用');
                console.log('父页面 wx 对象方法:', Object.getOwnPropertyNames(parentWx));

                // 将父页面的 wx 对象赋值给当前窗口
                window.wx = parentWx;

                console.log('✅ 成功使用父页面的 wx 对象');

                // 直接开始群聊 ID 获取，因为父页面已经完成了 SDK 配置
                this.getGroupChatId();

                return true;

            } catch (error) {
                console.error('访问父页面 wx 对象时发生错误:', error);
                console.log('可能的原因：跨域限制或父页面未初始化');
                return false;
            }
        },

        // 使用子页面自己的 wx 对象初始化
        initOwnWx() {
            console.log('开始使用子页面自己的 wx 对象初始化...');
            console.log('当前 wx 对象状态:', typeof wx);
            console.log('当前 window 对象中的 wx:', window.wx);

            // 检查是否在企业微信环境中
            if (typeof wx === 'undefined') {
                console.error('企业微信 JS SDK 未加载，请确认在企业微信环境中打开');
                console.log('当前页面 URL:', location.href);
                console.log('当前 User Agent:', navigator.userAgent);

                // 检查重试次数
                if (this.sdkInitRetryCount < this.maxRetryCount) {
                    this.sdkInitRetryCount++;
                    console.log(`群聊工单页面第 ${this.sdkInitRetryCount} 次重试初始化 SDK...`);
                    setTimeout(() => {
                        this.wecomJsSdkInit();
                    }, 1000);
                } else {
                    console.error('群聊工单页面 SDK 初始化重试次数已达上限，请手动刷新页面');
                }
                return;
            }

            console.log('✅ wx 对象存在，检查可用方法...');
            console.log('wx 对象的方法:', Object.getOwnPropertyNames(wx));

            // 检查 agentConfig 方法是否存在
            if (typeof wx.agentConfig !== 'function') {
                console.error('wx.agentConfig 方法不存在，可能是 SDK 版本问题或加载不完整');
                console.log('wx.agentConfig 类型:', typeof wx.agentConfig);

                // 检查重试次数
                if (this.sdkInitRetryCount < this.maxRetryCount) {
                    this.sdkInitRetryCount++;
                    console.log(`群聊工单页面第 ${this.sdkInitRetryCount} 次重试初始化 SDK...`);
                    setTimeout(() => {
                        this.wecomJsSdkInit();
                    }, 1000);
                } else {
                    console.error('群聊工单页面 SDK 初始化重试次数已达上限，请手动刷新页面');
                }
                return;
            }

            console.log('✅ wx.agentConfig 方法存在，开始配置初始化...');
            console.log("location.href:",location.href);
            ajaxCall("/wecom/servlet/config?action=sdkInit", {url: location.href}, result => {
                if (result.state === 1) {
                    try {
                        wx.agentConfig({
                            corpid: result.data.corpid,
                            agentid: result.data.agentId,
                            timestamp: result.data.timestamp,
                            nonceStr: result.data.nonceStr,
                            signature: result.data.signature,
                            // jsApiList: ['sendChatMessage',"getCurExternalContact","getContext","getCurExternalChat"], //传入需要使用的接口名称
                            jsApiList: ["getContext", "getCurExternalChat"], //群聊id获取接口测试
                            success: function (res) {// 回调
                                console.log("群聊工单 SDK 初始化成功:", res);
                                //获取群聊id
                                orderVue.getGroupChatId();
                            },
                            fail: function (res) {
                                console.error("群聊工单 SDK 初始化失败:", JSON.stringify(res));
                                if (res.errMsg && res.errMsg.indexOf('function not exist') > -1) {
                                    alert('企业微信版本过低，请升级到最新版本');
                                } else {
                                    alert('企业微信 SDK 初始化失败，请重新打开页面');
                                }
                            }
                        });
                    } catch (error) {
                        console.error('调用 wx.agentConfig 时发生错误:', error);
                    }
                } else {
                    console.error('获取 SDK 配置失败:', result);
                }
            }, error => {
                console.error('SDK 配置请求失败:', error);
            });
        },

        //获取群聊id
        getGroupChatId() {
            wx.invoke('getCurExternalChat', {}, function (res) {
                if (res.err_msg == "getCurExternalChat:ok") {
                    // this.formData.GROUP_CHAT_NAME = res.chatId;
                    chatId = res.chatId;
                    console.log("页面设置的chatId:", chatId)
                    //查询群工单记录
                    orderVue.getGroupOrderByChatId();
                } else {
                    layer.alert("当前聊天窗口无法使用群工单功能！请转移至群聊")
                }
            });
        },

        //根据群id查找工单记录
        getGroupOrderByChatId() {
            let _this = this;
            var reqParam = {
                params: {
                    "chatId": chatId
                },
                controls: ["GroupOrders.getGroupOrderByChatId"],
            };
            var param = {data: JSON.stringify(reqParam)};
            _this.webcall("/wecom/webcall", param, function (result) {
                //群id赋值
                _this.formData.chatId = chatId;
                console.log("_this.formData.chatId:",_this.formData.chatId);
                //判断是否查询美云销接口
                console.log("state:" + result.data["GroupOrders.getGroupOrderByChatId"].state);
                console.log("msg:" + result.data["GroupOrders.getGroupOrderByChatId"].msg);
                if (result.data["GroupOrders.getGroupOrderByChatId"].state === 1){
                    console.log("查询具体数据：", result.data["GroupOrders.getGroupOrderByChatId"].data)
                    _this.formData = result.data["GroupOrders.getGroupOrderByChatId"].data;
                    // 初始化服务工单数据
                    if (_this.formData.SERVICE_ORDER_NUMBER) {
                        _this.initServiceOrders();
                    }
                    _this.$forceUpdate()
                } else {
                    orderVue.getGroupDetail(); //调用美云销接口
                }
            });
        },

        //根据id查找工单记录
        getGroupOrderById(id) {
            let _this = this;
            var reqParam = {
                params: {
                    "ID": id
                },
                controls: ["GroupOrders.getGroupOrderById"],
            };
            var param = {data: JSON.stringify(reqParam)};
            _this.webcall("/wecom/webcall", param, function (result) {
                if (result.status === 200) {
                    console.log("查询具体数据：", result.data["GroupOrders.getGroupOrderById"].data)
                    _this.formData = result.data["GroupOrders.getGroupOrderById"].data;
                    //群id赋值
                    _this.formData.chatId = result.data["GroupOrders.getGroupOrderById"].data.CHAT_ID;
                    chatId = result.data["GroupOrders.getGroupOrderById"].data.CHAT_ID;
                    // 将不规范原因字符串转换为数组
                    if (_this.formData.REASON && typeof _this.formData.REASON === 'string') {
                        _this.formData.REASON = _this.formData.REASON.split(';').filter(item => item.trim() !== '');
                    } else if (!_this.formData.REASON) {
                        _this.formData.REASON = [];
                    }
                    // 初始化服务工单数据
                    if (_this.formData.SERVICE_ORDER_NUMBER) {
                        _this.initServiceOrders();
                    }
                }
            });
        },

        //调用美云销接口获取群聊基本信息
        getGroupDetail() {
            let _this = this;
            var reqParam = {
                params: {
                    "chatId": chatId
                },
                controls: ["GroupOrders.getGroupDetail"],
            };
            var param = {data: JSON.stringify(reqParam)};
            _this.webcall("/wecom/webcall", param, function (result) {
                //群id赋值
                _this.formData.chatId = chatId;
                if (result.data["GroupOrders.getGroupDetail"].respCode === "000") {
                    console.log("群聊基本数据：", result)
                    _this.formData.GROUP_CHAT_NAME = result.data["GroupOrders.getGroupDetail"].respData.data.name; //群聊名称
                    _this.formData.GROUP_CREATE_ACC = result.data["GroupOrders.getGroupDetail"].respData.data.owner; //建群管家账号
                    _this.formData.GROUP_CREATE_STEWARD = result.data["GroupOrders.getGroupDetail"].respData.data.ownerName; //建群管家
                    _this.formData.GROUP_CREATE_TIME = result.data["GroupOrders.getGroupDetail"].respData.data.buildDate;//建群时间

                    // 判断群聊名称是否为服务单号格式，如果是且当前没有服务工单，则自动添加
                    const groupChatName = result.data["GroupOrders.getGroupDetail"].respData.data.name;
                    const serviceOrderPattern = /^[Ff][Ww]\d{12}$/;
                    if (serviceOrderPattern.test(groupChatName) && _this.serviceOrders.length === 0 && !_this.formData.SERVICE_ORDER_NUMBER) {
                        // 将前两位字母转为大写，保持数字部分不变
                        const upperCaseOrder = groupChatName.substring(0, 2).toUpperCase() + groupChatName.substring(2);
                        _this.serviceOrders.push(upperCaseOrder);
                        // 更新表单数据中的服务工单字符串
                        _this.formData.SERVICE_ORDER_NUMBER = _this.serviceOrders.join(';');
                        console.log("自动识别群聊名称为服务单号：", upperCaseOrder);
                    }

                } else {
                    layer.alert("美云销接口异常,无法获取群聊基本信息");
                }
                //稽查人复制
                _this.formData.CHECK_NAME = _this.checkName;
                //单据来源赋值
                _this.formData.ORDER_SOURCE = "1";
                //当前进度赋值
                _this.formData.ORDER_STATE = "1";
                //是否规范赋值
                _this.formData.IS_NORMS = "未稽查";
                //稽查时间初始化 当前时间
                _this.formData.CHECK_TIME = orderVue.getCurrentTime(0);
            });
        },

        //模板解析
        TemplateParsing() {
            //判空
            if (this.input === "") {
                return;
            }
            // 使用换行符和冒号作为分隔符
            var lines = this.input.split('\n');
            var result = {};
            for (var i = 0; i < lines.length; i++) {
                var line = lines[i];
                //兼容英文冒号
                if (line.indexOf(':') !== -1) {
                    var parts = line.split(':');
                    var key = parts[0].trim(); // 去除左右空白字符
                    var value = parts[1].trim();
                    result[key] = value;
                }
                //兼容中文冒号
                if (line.indexOf('：') !== -1) {
                    var parts = line.split('：');
                    var key = parts[0].trim(); // 去除左右空白字符
                    var value = parts[1].trim();
                    result[key] = value;
                }
            }

            // 处理服务单号
            const serviceOrderKeys = ['服务单号', '服务工单号', '工单号'];
            for (const key of serviceOrderKeys) {
                if (result[key]) {
                    // 处理多个服务单号的情况，支持中英文逗号、分号、空格分隔
                    const orders = result[key].split(/[,，;；\s]+/).map(order => order.trim()).filter(order => order);
                    orders.forEach(order => {
                        // 设置输入框的值
                        this.serviceOrderInput = order;
                        // 调用添加方法（会自动进行格式验证）
                        this.addServiceOrder();
                    });
                }
            }

            //定义客户姓名以及相关泛化名称
            const customerNameKeys = ['客户姓名', '用户姓名', '业主姓名', '客户名', '用户名', '业主名', '客户名称', '用户名称', '业主名称','姓名'];
            let userName;
            //赋值
            for (const key of customerNameKeys) {
                if (result[key] !== undefined && result[key].trim() !== "") {
                    userName = result[key].trim();
                    break; // 找到第一个非空的客户姓名就跳出循环
                }
            }
            // 将找到的第一个非空客户姓名赋值给formData.USER_NAME
            this.formData.USER_NAME = userName || this.formData.USER_NAME; // 若所有键对应的值都为空，则用原来的值

            //定义客户地址以及相关泛化名称
            const customerAddressKeys = ['客户地址', '用户地址', '业主地址','地址'];
            let address;
            //赋值
            for (const key of customerAddressKeys) {
                if (result[key] !== undefined && result[key].trim() !== "") {
                    address = result[key].trim();
                    break;
                }
            }
            this.formData.USER_ADDRESS = address || this.formData.USER_ADDRESS; // 若所有键对应的值都为空，则用原来的值


            //定义客户电话以及相关泛化名称
            const customerPhoneKeys = ['客户电话', '用户电话', '业主电话', '客户号码', '用户号码', '业主号码'];
            let phone;
            //赋值
            for (const key of customerPhoneKeys) {
                if (result[key] !== undefined && result[key].trim() !== "") {
                    phone = result[key].trim();
                    break;
                }
            }
            this.formData.USER_PHONE = phone || this.formData.USER_PHONE; // 若所有键对应的值都为空，则用原来的值


            //定义安装机型以及相关泛化名称
            const productModelKeys = ['安装机型', '产品型号', '型号', '安装机器'];
            let productModel;
            //赋值
            for (const key of productModelKeys) {
                if (result[key] !== undefined && result[key].trim() !== "") {
                    productModel = result[key].trim();
                    break;
                }
            }
            this.formData.productList[0].productModel = productModel || this.formData.productList[0].productModel; // 若所有键对应的值都为空，则用原来的值


            //定义销售单位以及相关泛化名称
            const saleUnitKeys = ['销售单位', '销售公司', '经销单位', '经销商', '销售名'];
            let saleUnit;
            //赋值
            for (const key of saleUnitKeys) {
                if (result[key] !== undefined && result[key].trim() !== "") {
                    saleUnit = result[key].trim();
                    break;
                }
            }
            this.formData.SALE_UNIT = saleUnit || this.formData.SALE_UNIT; // 若所有键对应的值都为空，则用原来的值


            //定义网点名称以及相关泛化名称
            const saleNameKeys = ['网点名称', '安装单位', '网点名字'];
            let saleName;
            //赋值
            for (const key of saleNameKeys) {
                if (result[key] !== undefined && result[key].trim() !== "") {
                    saleName = result[key].trim();
                    break;
                }
            }
            this.formData.SITE_NAME = saleName || this.formData.SITE_NAME; // 若所有键对应的值都为空，则用原来的值


            //定义进场时间以及相关泛化名称
            const remindTimeKeys = ['安装日期', '安装时间', '进场时间', '进场日期', '提醒时间'];
            let remindTime;
            //赋值
            for (const key of remindTimeKeys) {
                if (result[key] !== undefined && result[key].trim() !== "") {
                    //格式化进场时间
                    remindTime = this.parseAndFormatToDate(result[key].trim());
                    break;
                }
            }
            // 若所有键对应的值都为空，则用原来的值
            this.$set(this.formData, 'REMIND_TIME', remindTime || this.formData.REMIND_TIME); //防止时间组件没有动态更新


            //定义分中心名称以及相关泛化名称
            const areaCodeKeys = ['分中心名称', '分中心名字', '分中心'];
            let areaCode;
            //赋值
            for (const key of areaCodeKeys) {
                if (result[key] !== undefined && result[key].trim() !== "") {
                    areaCode = result[key].trim();
                    break;
                }
            }
            this.formData.AREA_CODE = areaCode || this.formData.AREA_CODE; // 若所有键对应的值都为空，则用原来的值


            //定义安装师傅以及相关泛化名称
            const siteContactKeys = ['安装师傅', '勘测师傅'];
            let siteContact;
            //赋值
            for (const key of siteContactKeys) {
                if (result[key] !== undefined && result[key].trim() !== "") {
                    siteContact = result[key].trim();
                    break;
                }
            }
            this.formData.SITE_CONTACT = siteContact || this.formData.SITE_CONTACT; // 若所有键对应的值都为空，则用原来的值

            //单独赋值
            if (result['销售人员'] !== undefined && result['销售人员'].trim() !== "") {
                this.formData.SALE_PEOPLE = result['销售人员'].trim();
            }

            if (result['销售人员电话'] !== undefined && result['销售人员电话'].trim() !== "") {
                this.formData.SALE_PHONE = result['销售人员电话'].trim();
            }

            if (result['网点编码'] !== undefined && result['网点编码'].trim() !== "") {
                this.formData.SITE_CODE = result['网点编码'].trim();
            }

            if (result['安装品类'] !== undefined && result['安装品类'].trim() !== "") {
                this.formData.productList[0].prodName = result['安装品类'].trim();
            }

        },

        //获取稽查人
        getCheckName() {
            let _this = this;
            var reqParam = {
                params: {},
                controls: ["GroupOrders.getCheckName"],
            };
            var param = {data: JSON.stringify(reqParam)};
            _this.webcall("/wecom/webcall", param, function (result) {
                console.log("稽查人信息：", result.data["GroupOrders.getCheckName"].checkName);
                _this.checkName = result.data["GroupOrders.getCheckName"].checkName;
            });
        },

        //解析ISO 8601 格式的时间字符串
        convertISOToFormat(isoDateString) {
            // 首先去除时区部分 "+00:00"
            const dateWithoutTimeZone = isoDateString.slice(0, -6);

            // 创建一个新的日期对象
            const dateObject = new Date(dateWithoutTimeZone);

            // 获取年、月、日、小时、分钟和秒
            const year = dateObject.getFullYear();
            const month = ("0" + (dateObject.getMonth() + 1)).slice(-2);
            const day = ("0" + dateObject.getDate()).slice(-2);
            const hours = ("0" + dateObject.getHours()).slice(-2);
            const minutes = ("0" + dateObject.getMinutes()).slice(-2);
            const seconds = ("0" + dateObject.getSeconds()).slice(-2);

            // 拼接成目标格式
            const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            return formattedDate;
        },

        //解析中文日期 转化为"yyyy-MM-dd HH:mm:ss"格式 默认补0
        parseAndFormatToDate(inputDateString) {
            // 解析中文日期字符串
            const dateParts = inputDateString.match(/^(\d{4})(?:[-/.年]|月)?(\d{1,2})(?:[-/.年]|月)?(\d{1,2})(?:日|号)?$/);
            //格式判断
            if (!dateParts || dateParts.length !== 4) {
                // alert("进场时间格式错误,请重新选择");
                return;
            }

            const [, year, month, day] = dateParts;

            // JavaScript的月份是从0开始的，所以需要减1
            const parsedDate = new Date(year, parseInt(month) - 1, day);

            // 直接使用正确的分隔符生成格式化的日期时间字符串
            let time = `${parsedDate.getFullYear()}-${this.padZero(parsedDate.getMonth() + 1)}-${this.padZero(parsedDate.getDate())} 00:00:00`;
            return time;
        },

        //格式化
        padZero(num) {
            return ('0' + num).slice(-2);
        },

        //根据 网点名称查询其他信息 (调用工单接口)
        getSiteMessage() {
            let _this = this;
            var reqParam = {
                params: {
                    // areaName: _this.formData.AREA_CODE,
                    unitName: _this.formData.SITE_NAME,  //只能根据网点名称查询其他数据
                    unitCode: _this.formData.SITE_CODE,  //测试代码
                    orgCode: 'CS023' //家用中央和厨热的产品
                },
                //controls: ["comm.website"],
                controls: ["comm.newWebsiteCodeQuery"],
            };
            var param = {data: JSON.stringify(reqParam)};
            _this.webcall("/neworder/webcall", param, function (result) {
                //console.log("网点信息：", result.data["comm.website"]);
                console.log("网点信息：", result.data["comm.newWebsiteCodeQuery"]);
                if (result.data["comm.newWebsiteCodeQuery"].state === 1) {
                    var returnObj = result.data["comm.newWebsiteCodeQuery"].returnObj;
                    if(returnObj !== null && returnObj !== undefined){
                        var supUnitArchivesVO = result.data["comm.newWebsiteCodeQuery"].returnObj.supUnitArchivesVO;
                        if(supUnitArchivesVO !== null && supUnitArchivesVO !== undefined){
                            _this.formData.SITE_CODE = supUnitArchivesVO.unitCode;
                            _this.formData.SITE_NAME = supUnitArchivesVO.unitName;
                            _this.formData.SITE_CONTACT = supUnitArchivesVO.unitManager;
                            _this.formData.AREA_CODE = supUnitArchivesVO.branchName; //优化成分中心
                        }
                    }

                } else {
                    layer.alert("查询不到相关网点数据,请手动输入");
                }
            });
        },

        //获取当前时间
        getCurrentTime(addDay) {
            const date = new Date();
            // 将当前时间增加指定天数
            date.setDate(date.getDate() + addDay);

            const year = date.getFullYear();
            const month = ("0" + (date.getMonth() + 1)).slice(-2);
            const day = ("0" + date.getDate()).slice(-2);
            const hours = ("0" + date.getHours()).slice(-2);
            const minutes = ("0" + date.getMinutes()).slice(-2);
            const seconds = ("0" + date.getSeconds()).slice(-2);
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },

        //动态添加
        toAdd() {
            this.formData.productList.push({
                orgCode: "",
                brandName: "",
                brandCode: "",
                prodName: "",
                prodCode: "",
                productModel: "",
                productCode:"",
            })
        },

        //动态删除
        toDel(index) {
            this.formData.productList.splice(index, 1)
        },

        //企微侧边栏初始化
        init() {
            console.log('群聊工单页面开始初始化...');
            //延迟加载JSSDK，确保SDK文件完全加载
            this.$nextTick(() => {
                setTimeout(() => {
                    console.log('延迟初始化时间到，开始调用 wecomJsSdkInit...');
                    this.wecomJsSdkInit();
                }, 1000); // 减少延迟时间到1秒
            });
        },

        //群工单修改初始化
        ordersInit(id) {
            // this.orderId = id;
            this.getGroupOrderById(id);
        },

        // 添加服务工单
        addServiceOrder() {
            const orderInput = this.serviceOrderInput.trim();
            
            // 检查工单号格式 - 支持大小写FW
            const orderPattern = /^[Ff][Ww]\d{12}$/;
            if (!orderPattern.test(orderInput)) {
                layer.alert("不符合工单要求规范", {
                    icon: 2,
                    closeBtn: 0
                });
                return;
            }
            
            // 检查工单数量限制
            if (this.serviceOrders.length >= 20) {
                layer.alert("工单号数量超过20", {
                    icon: 2,
                    closeBtn: 0
                });
                return;
            }

            // 检查工单号是否已存在 - 忽略大小写
            const orderExists = this.serviceOrders.some(existingOrder => 
                existingOrder.toLowerCase() === orderInput.toLowerCase()
            );
            
            if (orderExists) {
                layer.alert("输入的工单号已经存在", {
                    icon: 2,
                    closeBtn: 0
                });
                return;
            }

            // 通过所有验证后添加工单 - 转换为大写
            if (orderInput) {
                // 将前两位字母转为大写，保持数字部分不变
                const upperCaseOrder = orderInput.substring(0, 2).toUpperCase() + orderInput.substring(2);
                this.serviceOrders.push(upperCaseOrder);
                this.serviceOrderInput = '';
                // 更新表单数据中的服务工单字符串
                this.formData.SERVICE_ORDER_NUMBER = this.serviceOrders.join(';');
            }
        },
        
        // 删除服务工单
        removeServiceOrder(index) {
            this.serviceOrders.splice(index, 1);
            // 更新表单数据中的服务工单字符串
            this.formData.SERVICE_ORDER_NUMBER = this.serviceOrders.join(';');
        },
        
        // 如果需要解析已有的服务工单数据
        initServiceOrders() {
            if (this.formData.SERVICE_ORDER_NUMBER) {
                this.serviceOrders = this.formData.SERVICE_ORDER_NUMBER.split(';').filter(order => order.trim());
            }
        },

        // 处理稽查时间变化
        handleCheckTimeChange() {
            // 更新稽查人信息
            this.formData.CHECK_NAME = this.checkName;
        },
    },
    //初始化
    mounted() {
        window.openProductCodeCallBack = (obj) => {
            let target=JSON.parse(JSON.stringify(obj))
            this.$nextTick(() => {
                // this.formData.productList[this.productIndex] = {...this.formData.productList[this.productIndex], ...target}
                this.formData.productList[this.productIndex].orgCode=target.orgCode||""
                this.formData.productList[this.productIndex].brandName=target.brandName||""
                this.formData.productList[this.productIndex].brandCode=target.brandCode||""
                this.formData.productList[this.productIndex].prodName=target.prodName||""
                this.formData.productList[this.productIndex].prodCode=target.prodCode||""
                if (this.formData.productList[this.productIndex].productModel !== ""){
                    this.formData.productList[this.productIndex].productModel= ""
                    this.formData.productList[this.productIndex].productCode= ""
                }
            })
            this.$forceUpdate()
        };
        window.openProductModelCallBack = (obj) => {
            let target=JSON.parse(JSON.stringify(obj))
            this.$nextTick(() => {
                // this.formData.productList[this.productIndex] = {...this.formData.productList[this.productIndex], ...target}
                this.formData.productList[this.productIndex].orgCode=target.orgCode||""
                this.formData.productList[this.productIndex].brandName=target.brandName||""
                this.formData.productList[this.productIndex].brandCode=target.brandCode||""
                this.formData.productList[this.productIndex].prodName=target.prodName||""
                this.formData.productList[this.productIndex].prodCode=target.prodCode||""
                this.formData.productList[this.productIndex].productModel=target.productModel||""
                this.formData.productList[this.productIndex].productCode=target.productCode||""
            })
            // this.formData.productList[this.productIndex].productModel=obj.productModel||"";
            // this.formData.productList[this.productIndex].productCode=obj.productCode||""
            this.$forceUpdate()
        };
        const urlParams = new URLSearchParams(window.location.search); // 获取父窗口的查询参数
        const id = urlParams.get('ID');
        //判断初始化方式
        if (id === null) { //企微侧边栏
            this.init();
        } else { //工单记录修改
            this.ordersInit(id);
        }
        //初始化获取字典
        this.getOrderSourceType()
        this.getOrderStateType();
        this.getIsNormsType();
        this.getGroupStateType();
        this.getReasonType();
        //调用css接口
        this.getCSSData();
        //获取稽查人信息
        this.getCheckName();
        // 初始化服务工单数据
        this.initServiceOrders();
    }
});
