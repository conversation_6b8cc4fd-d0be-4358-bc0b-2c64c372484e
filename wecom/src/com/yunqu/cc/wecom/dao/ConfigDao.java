package com.yunqu.cc.wecom.dao;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.text.StrBuilder;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.crypt.BASE64Util;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.wecom.base.AppDaoContext;
import com.yunqu.cc.wecom.base.CommonLogger;
import com.yunqu.cc.wecom.base.Constants;


@WebObject(name = "configDao")
public class ConfigDao extends AppDaoContext {
	
	@WebControl(name = "getConfigInfo",type = Types.RECORD)
	public JSONObject getConfigInfo() {
		EasySQL sql = new EasySQL("SELECT ID,USERID_LIST FROM CC_WECOM_CONFIG");
		return queryForRecord(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
	}

	/**
	 * 数据字典
	 * @return
	 */
	@WebControl(name = "getDict", type = Types.DICT)
	public JSONObject getDict() {
		String dictGroupCode = (String)this.getMethodParam(0);
		if(StringUtils.isBlank(dictGroupCode)){
			CommonLogger.logger.error(CommonUtil.getClassNameAndMethod(this)+" 无法查询数据字典,请求的数据字典code为空");
			return null;
		}
		String depCode = UserUtil.getUser(request).getEpCode();
		return DictCache.getJsonEnableDictListByGroupCode(depCode, dictGroupCode);
	}
	/**
	 * 获取满意度例句
	 * @return
	 */
	@WebControl(name = "getSatisfyExample",type = Types.TEXT)
	public JSONObject getSatisfyExample() {
		UserModel user = UserUtil.getUser(request);
		String custName = param.getString("custName");
		String custId = param.getString("custId");
		EasyCache cache = CacheManager.getMemcache();
		String value = cache.get("WECOM_MAX_SATISFY_"+custId);
		// 增加日志记录，方便排查问题
		CommonLogger.logger.info("收到满意度请求: custName=" + custName + ", custId=" + custId + ", user=" + user.getUserAcc());
		//同一个custId，会缓存10分钟数据
		if(StringUtils.isNotBlank(value)) {
			CommonLogger.logger.info("从缓存中获取满意度内容: custId=" + custId);
			return EasyResult.ok(value);
		}
		try {
			EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_YW);
			EasySQL sql = new EasySQL(" SELECT t1.*,t2.CODE DICT_GROUP_CODE,t2.EP_CODE  FROM C_CF_DICT t1 left join C_CF_DICTGROUP t2 on t1.DICT_GROUP_ID=t2.ID WHERE 1=1 ");
			sql.append(user.getEpCode()," AND t2.EP_CODE=? ");
			sql.append("Y"," AND t1.ENABLE_STATUS=? ");
			sql.append("Y"," and t2.ENABLE_STATUS=? ");
			sql.append("WECOM_SATISFY_EXAMPLE","and t2.code = ?");
			sql.append("order by t2.ID,t1.SORT_NUM");
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			if(list==null||list.size()==0) {
				CommonLogger.logger.info("未获取到满意度模板: user=" + user.getUserAcc() + ", custId=" + custId);
				return EasyResult.error(500,"未获取到满意度模板");
			}
			// 如果custName为空，尝试通过custId重新获取客户信息
			if(StringUtils.isBlank(custName) && StringUtils.isNotBlank(custId)) {
				CommonLogger.logger.warn("客户姓名为空，尝试通过custId重新获取: custId=" + custId);
				custName = getCustomerNameByCustId(custId);
				if(StringUtils.isNotBlank(custName)) {
					CommonLogger.logger.info("通过custId成功获取客户姓名: custId=" + custId + ", custName=" + custName);
				} else {
					CommonLogger.logger.error("通过custId无法获取客户姓名: custId=" + custId);
					custName = "客户"; // 使用默认值
				}
			}
			JSONObject data = list.get(0);
			String key = RandomKit.uniqueStr();
			cache.put("WECOM_" + key, "1", 60*60*24);
			//写入满意度记录，状态未评价
			EasyRecord record = new EasyRecord("C_NO_WECOM_SATISFY");
			record.set("ID", key);
			record.set("DATE_ID", DateUtil.getCurrentDateStr("yyyy-MM-dd"));
			record.set("USER_ACC", user.getUserAcc());
			record.set("USER_NAME", user.getUserName());
			record.set("CUST_NAME", custName);
			record.set("CUST_ID", custId);
			record.set("STATUS", 0);
			record.set("CREATE_TIMESTAMP", System.currentTimeMillis());
			record.set("CREATE_TIME", DateUtil.getCurrentDateStr());
			// 记录满意度信息到数据库前增加日志
			CommonLogger.logger.info("准备保存满意度记录: ID=" + key + ", CUST_NAME=" + custName + ", CUST_ID=" + custId + ", USER_ACC=" + user.getUserAcc());
			query.save(record);
			// 记录保存成功日志
			CommonLogger.logger.info("满意度记录保存成功: ID=" + key + ", CUST_NAME=" + custName + ", CUST_ID=" + custId);
			//字典中配置的满意度模板，需替换#URL#
			String satisfyStr = data.getString("BAKUP");
			String satisfyURL = "/yc-media/pages/media/satisfaction.html";
			String requestURL = satisfyStr.substring(satisfyStr.indexOf("#")+1,satisfyStr.lastIndexOf("#"));
			String longURL = requestURL + satisfyURL + "?id=" + key;
			CommonLogger.logger.info("长链接：" + longURL);
			String shortURL = longURL2ShortURL(longURL);
			StringBuffer returnSatisfyStr = new StringBuffer();
			returnSatisfyStr.append(satisfyStr);
			returnSatisfyStr.replace(satisfyStr.indexOf("#"),satisfyStr.lastIndexOf("#")+1, shortURL);
			cache.put("WECOM_MAX_SATISFY_"+custId, returnSatisfyStr.toString(),60*10);
			return EasyResult.ok(returnSatisfyStr.toString());
		} catch (Exception e) {
			CommonLogger.logger.error("生成满意度内容出错: custName=" + custName + ", custId=" + custId + ", user=" + user.getUserAcc() + ", error: " + e.getMessage(),e);
			return EasyResult.error(500,"系统错误");
		}
	}

	/**
	 * 通过客户ID获取客户姓名
	 * @param custId 客户ID
	 * @return 客户姓名
	 */
	private String getCustomerNameByCustId(String custId) {
		if(StringUtils.isBlank(custId)) {
			return null;
		}

		try {
			EasyCache cache = CacheManager.getMemcache();

			// 先尝试从缓存获取客户信息
			String cacheKey = "WECOM_EXTCUST_INFO_" + custId;
			String cachedInfo = cache.get(cacheKey);

			if(StringUtils.isNotBlank(cachedInfo)) {
				CommonLogger.logger.info("从缓存获取客户信息: custId=" + custId);
				JSONObject custInfo = JSONObject.parseObject(cachedInfo);
				String name = custInfo.getString("name");
				if(StringUtils.isNotBlank(name)) {
					return name.trim();
				}
			}

			// 缓存中没有或信息不完整，调用接口获取
			CommonLogger.logger.info("缓存中无客户信息，调用接口获取: custId=" + custId);
			JSONObject result = new JSONObject();
			JSONObject request = new JSONObject();
			request.put("command", "getExtCustInfo");
			JSONObject params = new JSONObject();
			params.put("userId", custId);
			request.put("params", params);

			IService service = ServiceContext.getService("MIXGW_QYWX_INTEFACE");
			result = service.invoke(request);

			if(result != null && result.getJSONObject("external_contact") != null) {
				JSONObject extCustInfo = result.getJSONObject("external_contact");
				String name = extCustInfo.getString("name");

				if(StringUtils.isNotBlank(name)) {
					// 更新缓存
					cache.put(cacheKey, extCustInfo.toJSONString(), 60*60);
					CommonLogger.logger.info("通过接口获取客户姓名成功: custId=" + custId + ", name=" + name);
					return name.trim();
				}
			}

			CommonLogger.logger.warn("接口返回的客户信息中没有姓名: custId=" + custId + ", result=" + result);
			return null;

		} catch (Exception e) {
			CommonLogger.logger.error("获取客户姓名失败: custId=" + custId + ", error=" + e.getMessage(), e);
			return null;
		}
	}

	private String longURL2ShortURL(String longURL) {
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		obj.put("command","longToShort");
		param.put("longLinkUrl",longURL);
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService("MIXGW_MCSP_INTEFACE");
			result = service.invoke(obj);
		} catch (ServiceException e) {
			CommonLogger.logger.error("IService请求失败,请求参数"+obj+",原因"+e.getMessage(),e);
		}
		String respCode = result.getString("respCode");
		if(GWConstants.RET_CODE_SUCCESS.equals(respCode)){
			JSONObject respData = result.getJSONObject("respData");
			if(respData==null) {
				return longURL;
			}
			JSONObject data = respData.getJSONObject("data");
			if(StringUtils.isBlank(data.getString("shortLinkUrl"))) {
				return longURL;
			}
			return data.getString("shortLinkUrl");
		}
		return longURL;
	}
}
