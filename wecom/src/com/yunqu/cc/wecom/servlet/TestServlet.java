/**
 * 
 */
package com.yunqu.cc.wecom.servlet;

import javax.servlet.annotation.WebServlet;

import com.alibaba.fastjson.JSONArray;
import com.yunqu.cc.wecom.base.CommonLogger;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.wecom.base.AppBaseServlet;
import com.yunqu.cc.wecom.base.Constants;
import com.yq.busi.common.util.DateUtil;
import org.easitline.common.core.web.EasyResult;

import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;

/**
 * <AUTHOR>
 *
 */

@WebServlet("/servlet/TestServlet")
public class TestServlet extends AppBaseServlet{

	public Logger logger = CommonLogger.getLogger("test");

	private static EasyCache cache = CacheManager.getMemcache();

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public void actionForTest(){
		try {
			String startDate = getPara("startDate");
			JSONObject params = new JSONObject();
			params.put("command", "syncWechatUserInfo");
			if(StringUtils.isNotBlank(startDate)){
				params.put("startDate", startDate+" 00:00:00");
				params.put("endDate", startDate+" 23:59:59");
			}
			IService service = ServiceContext.getService("CC_WECOM_COMMON_INTERFACE");
			JSONObject invoke = service.invoke(params);
		} catch (ServiceException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	/**
	 * 测试工单同步接口
	 */
	public JSONObject actionForTestOderSync() {
		JSONObject result = new JSONObject();
		try {
			getRequest().setCharacterEncoding("utf-8");
			getResponse().setCharacterEncoding("utf-8");
			getResponse().setContentType("application/json;charset=UTF-8");
			JSONArray data = getJSONArray();
			logger.info("TestServlet>>actionForTestOderSync>>同步工单数据"+data);
			if (data == null || data.isEmpty()){
				return EasyResult.fail("推送测试工单数据为空,请检查参数");
			}
			JSONObject params = new JSONObject();
			params.put("command", "synOrderRecord");
			params.put("data", data);
			result = ServiceContext.getService("CSSGW-ORDER-SYN").invoke(params);
		} catch (Exception e) {
			logger.error("TestServlet>>actionForTestOderSync>>同步订单接口异常"+e.getMessage(),e);
			return EasyResult.fail("推送测试工单失败");
		}
		return EasyResult.ok(result,"推送测试工单成功");
	}

	/**
	 * 同步半年数据 - 将 C_NO_GROUP_ORDERS 表中的SERVICE_ORDER_NUMBER字段拆分到C_NO_GROUP_ORDERS_MATCH表
	 * 支持断点续传，通过P_CREATE_TIME最新记录确定起始时间
	 */
	public JSONObject actionForSyncGroupOrdersHalfYearData() {
		long startTime = System.currentTimeMillis();
		logger.info("=== 开始执行半年C_NO_GROUP_ORDERS字段拆分到C_NO_GROUP_ORDERS_MATCH数据同步任务 ===");
		
		EasyQuery easyQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_YW);
		int totalProcessed = 0;
		int totalSuccess = 0;
		int totalFailed = 0;
		int pageSize = 10000; // 每页1万条数据
		easyQuery.setMaxRow(pageSize); //设置查询最大行数

		try {
			String lock = cache.get("SyncGroupOrdersHalfYearDataLock");
			if(StringUtils.isNotBlank(lock)){
				logger.info("同步任务执行中,请勿重复执行!");
				return EasyResult.fail("同步任务执行中,请勿重复执行!");
			}
			cache.put("SyncGroupOrdersHalfYearDataLock","Y",24*60*60);
			logger.info("=== 执行锁已添加 ===");
			// 计算同步时间范围
			String[] timeRange = calculateSyncTimeRange(easyQuery);
			String startTimeStr = timeRange[0];
			String endTimeStr = timeRange[1];
			
			logger.info("同步时间范围: " + startTimeStr + " 至 " + endTimeStr);
			
			// 获取总记录数
			int totalCount = getTotalRecordCount(easyQuery, startTimeStr, endTimeStr);
			if (totalCount == 0) {
				logger.info("未找到需要同步的数据");
				return EasyResult.fail("未找到需要同步的数据");
			}
			
			int totalPages = (int) Math.ceil(totalCount / (double) pageSize);
			logger.info("总记录数: " + totalCount + ", 总页数: " + totalPages + ", 每页: " + pageSize + " 条");
			
			// 分页处理数据
			for (int currentPage = 1; currentPage <= totalPages; currentPage++) {
				logger.info("开始处理第 " + currentPage + "/" + totalPages + " 页数据");
				
				try {
					// 处理当前页数据
					int[] pageResult = processPageData(easyQuery, startTimeStr, endTimeStr, currentPage, pageSize);
					int pageProcessed = pageResult[0];
					int pageSuccess = pageResult[1];
					int pageFailed = pageResult[2];
					
					totalProcessed += pageProcessed;
					totalSuccess += pageSuccess;
					totalFailed += pageFailed;
					
					logger.info("第 " + currentPage + " 页处理完成: 处理 " + pageProcessed + " 条, 成功 " + pageSuccess + " 条, 失败 " + pageFailed + " 条");
					if (pageFailed > 0){
						throw new Exception();
					}
				} catch (Exception e) {
					logger.error("第 " + currentPage + " 页处理失败，终止任务: " + e.getMessage(), e);
					return createErrorResult("第 " + currentPage + " 页处理失败，请手动重新触发继续处理: " + e.getMessage(), 
							totalProcessed, totalSuccess, totalFailed, System.currentTimeMillis() - startTime);
				}
			}
			
			long elapsedTime = System.currentTimeMillis() - startTime;
			logger.info("=== 半年数据同步任务完成 ===");
			logger.info("总处理时间: " + elapsedTime + "ms, 总处理: " + totalProcessed + " 条, 总成功: " + totalSuccess + " 条, 总失败: " + totalFailed + " 条");
			
			return createSuccessResult(totalProcessed, totalSuccess, totalFailed, elapsedTime);
			
		} catch (Exception e) {
			long elapsedTime = System.currentTimeMillis() - startTime;
			logger.error("半年数据同步任务执行异常: " + e.getMessage(), e);
			return createErrorResult("同步任务执行异常: " + e.getMessage(), 
					totalProcessed, totalSuccess, totalFailed, elapsedTime);
		}finally {
			cache.delete("SyncGroupOrdersHalfYearDataLock");
			logger.info("=== 执行锁已释放 ===");
		}
	}
	
	/**
	 * 计算同步时间范围
	 */
	private String[] calculateSyncTimeRange(EasyQuery easyQuery){
		String endTimeStr = DateUtil.getCurrentDateStr();
		String startTimeStr;
		
		try {
			// 查询C_NO_GROUP_ORDERS_MATCH表中P_CREATE_TIME的最新记录
			EasySQL maxTimeSql = new EasySQL("SELECT MAX(P_CREATE_TIME) as MAX_TIME FROM C_NO_GROUP_ORDERS_MATCH WHERE P_CREATE_TIME IS NOT NULL");
			String maxTime = easyQuery.queryForString(maxTimeSql.getSQL(), maxTimeSql.getParams());
			
			if (StringUtils.isNotBlank(maxTime)) {
				// 如果有最新记录，从该时间开始同步后续数据
				startTimeStr = maxTime;
				logger.info("发现已有同步记录，从最新时间开始: " + startTimeStr);
			} else {
				// 如果没有记录，同步半年数据
				Calendar calendar = Calendar.getInstance();
				calendar.add(Calendar.MONTH, -6); // 减6个月
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				startTimeStr = sdf.format(calendar.getTime());
				logger.info("未发现同步记录，同步半年数据，开始时间: " + startTimeStr);
			}
		} catch (Exception e) {
			logger.error("查询最新同步记录失败，使用默认半年时间范围: " + e.getMessage(),e);
			// 异常情况下，默认同步半年数据
			Calendar calendar = Calendar.getInstance();
			calendar.add(Calendar.MONTH, -6);
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			startTimeStr = sdf.format(calendar.getTime());
		}
		
		return new String[]{startTimeStr, endTimeStr};
	}
	
	/**
	 * 获取总记录数
	 */
	private int getTotalRecordCount(EasyQuery easyQuery, String startTime, String endTime) throws SQLException {
		EasySQL countSql = new EasySQL("SELECT COUNT(*) FROM C_NO_GROUP_ORDERS WHERE 1=1");
		countSql.append(startTime, " AND CREATE_TIME >= ?");
		countSql.append(endTime, " AND CREATE_TIME <= ?");
		countSql.append(" AND SERVICE_ORDER_NUMBER IS NOT NULL");
		
		return easyQuery.queryForInt(countSql.getSQL(), countSql.getParams());
	}
	
	/**
	 * 处理分页数据
	 * @return [处理数量, 成功数量, 失败数量]
	 */
	private int[] processPageData(EasyQuery easyQuery, String startTime, String endTime, int currentPage, int pageSize) throws SQLException {
		int processed = 0;
		int success = 0;
		int failed = 0;
		
		// 查询当前页数据
		EasySQL pageSql = new EasySQL("SELECT ID, CHAT_ID, SERVICE_ORDER_NUMBER, CREATE_TIME FROM C_NO_GROUP_ORDERS WHERE 1=1");
		pageSql.append(startTime, " AND CREATE_TIME >= ?");
		pageSql.append(endTime, " AND CREATE_TIME <= ?");
		pageSql.append(" AND SERVICE_ORDER_NUMBER IS NOT NULL");
		pageSql.append(" ORDER BY CREATE_TIME ASC");
		
		List<EasyRow> pageData = easyQuery.queryForList(pageSql.getSQL(), pageSql.getParams(), currentPage, pageSize);
		logger.info("查询到第 " + currentPage + " 页数据: " + pageData.size() + " 条");
		
		// 开启事务处理当前页数据
		easyQuery.begin();
		try {
			for (EasyRow row : pageData) {
				processed++;
				
				try {
					// 处理单条记录
					processSingleRecord(easyQuery, row);
					success++;
					
				} catch (Exception e) {
					failed++;
					logger.error("处理记录失败 ID: " + row.getColumnValue("ID") + ", 错误: " + e.getMessage(), e);
					throw e;
				}
			}

			easyQuery.commit();
			logger.info("第 " + currentPage + " 页事务提交成功");
		} catch (Exception e) {
			easyQuery.roolback();
			logger.error("第 " + currentPage + " 页事务回滚: " + e.getMessage(), e);
		}
		
		return new int[]{processed, success, failed};
	}
	
	/**
	 * 处理单条记录 - 将SERVICE_ORDER_NUMBER拆分并插入到C_NO_GROUP_ORDERS_MATCH表
	 */
	private void processSingleRecord(EasyQuery easyQuery, EasyRow row) throws SQLException {
		String groupOrderId = row.getColumnValue("ID");
		String chatId = row.getColumnValue("CHAT_ID");
		String serviceOrderNumber = row.getColumnValue("SERVICE_ORDER_NUMBER");
		String createTime = row.getColumnValue("CREATE_TIME");
		
		if (StringUtils.isBlank(serviceOrderNumber)) {
			return; // 跳过空的服务单号
		}
		
		// 检查是否已经处理过该记录
		EasySQL checkSql = new EasySQL("SELECT COUNT(*) FROM C_NO_GROUP_ORDERS_MATCH WHERE GROUP_ORDER_ID = ?");
		int existCount = easyQuery.queryForInt(checkSql.getSQL(), new Object[]{groupOrderId});
		if (existCount > 0) {
			logger.debug("记录已存在，跳过处理: " + groupOrderId);
			return;
		}
		
		// 按分号分割服务单号
		String[] serviceOrderArray = serviceOrderNumber.split(";");
		String currentTimeStr = DateUtil.getCurrentDateStr("yyyy-MM-dd HH:mm:ss");
		
		for (String singleOrderNumber : serviceOrderArray) {
			singleOrderNumber = singleOrderNumber.trim();
			if (StringUtils.isBlank(singleOrderNumber)) {
				continue; // 跳过空的服务单号
			}
			
			// 创建C_NO_GROUP_ORDERS_MATCH记录
			EasyRecord matchRecord = new EasyRecord("C_NO_GROUP_ORDERS_MATCH", "ID");
			matchRecord.put("ID", RandomKit.uniqueStr());
			matchRecord.put("GROUP_ORDER_ID", groupOrderId);
			matchRecord.put("CHAT_ID", chatId);
			matchRecord.put("SERVICE_ORDER_NUMBER", singleOrderNumber);
			matchRecord.put("CREATE_TIME", currentTimeStr);
			matchRecord.put("P_CREATE_TIME", createTime); // 原群工单创建时间
			
			easyQuery.save(matchRecord);
			//logger.debug("插入关联记录: 群工单ID=" + groupOrderId + ", 服务单号=" + singleOrderNumber);
		}
	}
	
	/**
	 * 创建成功结果
	 */
	private JSONObject createSuccessResult(int totalProcessed, int totalSuccess, int totalFailed, long elapsedTime) {
		JSONObject result = new JSONObject();
		result.put("success", true);
		result.put("message", "半年数据同步完成");
		result.put("totalProcessed", totalProcessed);
		result.put("totalSuccess", totalSuccess);
		result.put("totalFailed", totalFailed);
		result.put("elapsedTimeMs", elapsedTime);
		result.put("elapsedTimeStr", formatElapsedTime(elapsedTime));
		return EasyResult.ok(result, "同步任务执行成功");
	}
	
	/**
	 * 创建错误结果
	 */
	private JSONObject createErrorResult(String errorMessage, int totalProcessed, int totalSuccess, int totalFailed, long elapsedTime) {
		JSONObject result = new JSONObject();
		result.put("success", false);
		result.put("message", errorMessage);
		result.put("totalProcessed", totalProcessed);
		result.put("totalSuccess", totalSuccess);
		result.put("totalFailed", totalFailed);
		result.put("elapsedTimeMs", elapsedTime);
		result.put("elapsedTimeStr", formatElapsedTime(elapsedTime));
		return EasyResult.error(500, String.valueOf(result));
	}
	
	/**
	 * 格式化耗时
	 */
	private String formatElapsedTime(long elapsedTimeMs) {
		long seconds = elapsedTimeMs / 1000;
		long minutes = seconds / 60;
		long hours = minutes / 60;
		
		if (hours > 0) {
			return hours + "小时" + (minutes % 60) + "分钟" + (seconds % 60) + "秒";
		} else if (minutes > 0) {
			return minutes + "分钟" + (seconds % 60) + "秒";
		} else {
			return seconds + "秒";
		}
	}
	
	/**
	 * 测试方法 - 验证数据同步功能
	 */
	public JSONObject actionForTestSyncHalfYearData() {
		logger.info("=== 开始测试半年数据同步功能 ===");
		
		try {
			// 测试数据库连接
			EasyQuery testQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_YW);
			
			// 测试查询C_NO_GROUP_ORDERS表
			EasySQL testSql = new EasySQL("SELECT COUNT(*) FROM C_NO_GROUP_ORDERS WHERE SERVICE_ORDER_NUMBER IS NOT NULL");
			int totalRecords = testQuery.queryForInt(testSql.getSQL(), testSql.getParams());
			logger.info("查询到C_NO_GROUP_ORDERS表中有服务单号的记录数: " + totalRecords);
			
			// 测试查询C_NO_GROUP_ORDERS_MATCH表
			EasySQL matchTestSql = new EasySQL("SELECT COUNT(*) FROM C_NO_GROUP_ORDERS_MATCH");
			int matchRecords = testQuery.queryForInt(matchTestSql.getSQL(), matchTestSql.getParams());
			logger.info("查询到C_NO_GROUP_ORDERS_MATCH表中的记录数: " + matchRecords);
			
			// 测试时间范围计算
			String[] timeRange = calculateSyncTimeRange(testQuery);
			logger.info("计算的时间范围: " + timeRange[0] + " 至 " + timeRange[1]);
			
			// 返回测试结果
			JSONObject result = new JSONObject();
			result.put("success", true);
			result.put("message", "测试成功");
			result.put("totalOrderRecords", totalRecords);
			result.put("totalMatchRecords", matchRecords);
			result.put("timeRangeStart", timeRange[0]);
			result.put("timeRangeEnd", timeRange[1]);
			
			return EasyResult.ok(result, "测试成功");
			
		} catch (Exception e) {
			logger.error("测试异常: " + e.getMessage(), e);
			JSONObject result = new JSONObject();
			result.put("success", false);
			result.put("message", "测试失败: " + e.getMessage());
			return EasyResult.error(500, String.valueOf(result));
		}
	}
	

}
