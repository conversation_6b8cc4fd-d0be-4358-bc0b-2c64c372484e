package com.yunqu.cc.wecom.servlet;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.Part;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.DateUtil;

import com.yunqu.cc.wecom.base.AppBaseServlet;
import com.yunqu.cc.wecom.base.Constants;
import org.apache.poi.ss.usermodel.*;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;
import java.util.List;
import java.util.ArrayList;

@WebServlet("/servlet/ManagerConfig")
@MultipartConfig
public class ManagerConfigServlet extends AppBaseServlet {

    EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_YW);
    private static EasyCache cache = CacheManager.getMemcache();

    public EasyResult actionForAdd() {
        getLogger().info("add执行");
        EasyRecord record = new EasyRecord("WECOM_MANAGER_CONFIG");
        JSONObject jsonObject = getJSONObject();
        
        // 获取关键字段
        String managerId = jsonObject.getString("managerId");
        String brandCode = jsonObject.getString("brandCode");
        String brandName = jsonObject.getString("brandName");
        String productCode = jsonObject.getString("productCode");
        String productModel = jsonObject.getString("productModel");
        String busiTypeCode = jsonObject.getString("busiTypeCode");
        String busiType = jsonObject.getString("busiType");
        String orderSourceCode = jsonObject.getString("orderSourceCode");
        String orderSource = jsonObject.getString("orderSource");
        
        // 必填项验证（除了managerName外都用code进行校验）
        if (StringUtils.isBlank(managerId)) {
            return EasyResult.fail("企微ID不能为空");
        }
        if (StringUtils.isBlank(brandCode)) {
            return EasyResult.fail("品牌名称不能为空");
        }
      /*  if (StringUtils.isBlank(productCode)) {
            return EasyResult.fail("产品型号不能为空");
        }*/
        if (StringUtils.isBlank(busiTypeCode)) {
            return EasyResult.fail("实施业务类型不能为空");
        }
        if (StringUtils.isBlank(orderSourceCode)) {
            return EasyResult.fail("单据来源不能为空");
        }
        
        // 检查组合唯一性：企微ID + 品牌编码 + 产品型号编码 + 实施业务类型编码 + 单据来源编码
        EasySQL checkSql = new EasySQL("select count(1) from YWDB.WECOM_MANAGER_CONFIG where 1 = 1");
        checkSql.append(managerId, "and MANAGER_ID = ?");
        checkSql.append(brandCode, "and BRAND_CODE = ?");
        checkSql.append(productCode, "and PRODUCT_CODE = ?");
        checkSql.append(busiTypeCode, "and BUSI_TYPE_CODE = ?");
        checkSql.append(orderSourceCode, "and ORDER_SOURCE_CODE = ?");
        
        try {
            boolean exists = query.queryForExist(checkSql.getSQL(), checkSql.getParams());
            if (exists) {
                getLogger().info("企微管家配置组合已存在：企微ID=" + managerId + ", 品牌编码=" + brandCode + ", 产品型号编码=" + productCode + ", 业务类型编码=" + busiTypeCode + ", 单据来源编码=" + orderSourceCode);
                return EasyResult.fail("该企微管家配置组合已存在，不能重复添加");
            }
        } catch (SQLException e) {
            getLogger().info("检查企微管家配置唯一性失败：" + e.getMessage(), e);
            return EasyResult.fail("检查配置唯一性失败");
        }
        
        record.set("ID", RandomKit.randomStr());
        record.set("MANAGER_ID", managerId);
        record.set("MANAGER_NAME", jsonObject.getString("managerName"));
        record.set("CREATE_TIME", DateUtil.getCurrentDateStr());
        record.set("STATUS", jsonObject.getString("status"));
        record.set("USE_COUNT", "0");

        // 补充新增字段
        record.set("BRAND_CODE", brandCode);
        record.set("BRAND_NAME", brandName);
        record.set("PRODUCT_CODE", productCode);
        record.set("PRODUCT_MODEL", productModel);
        record.set("BUSI_TYPE_CODE", busiTypeCode);
        record.set("BUSI_TYPE", busiType);
        record.set("ORDER_SOURCE_CODE", orderSourceCode);
        record.set("ORDER_SOURCE", orderSource);
        try {
            query.save(record);
            noticeUpdateCountManager();
            return EasyResult.ok();
        } catch (SQLException e) {
            getLogger().info("保存失败：" + e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    public EasyResult actionForUpdate() {
        EasyRecord record = new EasyRecord("WECOM_MANAGER_CONFIG");
        JSONObject jsonObject = getJSONObject();
        
        // 获取关键字段
        String id = jsonObject.getString("id");
        String managerId = jsonObject.getString("managerId");
        String brandCode = jsonObject.getString("brandCode");
        String brandName = jsonObject.getString("brandName");
        String productCode = jsonObject.getString("productCode");
        String productModel = jsonObject.getString("productModel");
        String busiTypeCode = jsonObject.getString("busiTypeCode");
        String busiType = jsonObject.getString("busiType");
        String orderSourceCode = jsonObject.getString("orderSourceCode");
        String orderSource = jsonObject.getString("orderSource");
        
        // 必填项验证（除了managerName外都用code进行校验）
        if (StringUtils.isBlank(managerId)) {
            return EasyResult.fail("企微ID不能为空");
        }
        if (StringUtils.isBlank(brandCode)) {
            return EasyResult.fail("品牌名称不能为空");
        }
        /*if (StringUtils.isBlank(productCode)) {
            return EasyResult.fail("产品型号不能为空");
        }*/
        if (StringUtils.isBlank(busiTypeCode)) {
            return EasyResult.fail("实施业务类型不能为空");
        }
        if (StringUtils.isBlank(orderSourceCode)) {
            return EasyResult.fail("单据来源不能为空");
        }
        
        // 检查组合唯一性：企微ID + 品牌编码 + 产品型号编码 + 实施业务类型编码 + 单据来源编码（排除当前记录）
        EasySQL checkSql = new EasySQL("select count(1) from YWDB.WECOM_MANAGER_CONFIG where 1 = 1");
        checkSql.append(managerId, "and MANAGER_ID = ?");
        checkSql.append(brandCode, "and BRAND_CODE = ?");
        checkSql.append(productCode, "and PRODUCT_CODE = ?");
        checkSql.append(busiTypeCode, "and BUSI_TYPE_CODE = ?");
        checkSql.append(orderSourceCode, "and ORDER_SOURCE_CODE = ?");
        checkSql.append(id, "and ID != ?");
        
        try {
            boolean exists = query.queryForExist(checkSql.getSQL(), checkSql.getParams());
            if (exists) {
                getLogger().info("企微管家配置组合已存在于其他记录中：企微ID=" + managerId + ", 品牌编码=" + brandCode + ", 产品型号编码=" + productCode + ", 业务类型编码=" + busiTypeCode + ", 单据来源编码=" + orderSourceCode);
                return EasyResult.fail("该企微管家配置组合已存在，不能重复使用");
            }
        } catch (SQLException e) {
            getLogger().info("检查企微管家配置唯一性失败：" + e.getMessage(), e);
            return EasyResult.fail("检查配置唯一性失败");
        }
        
        record.setPrimaryKey("ID");
        record.set("ID", id);
        record.set("MANAGER_ID", managerId);
        record.set("MANAGER_NAME", jsonObject.getString("managerName"));
        record.set("STATUS", jsonObject.getString("status"));

        // 补充更新字段
        record.set("BRAND_CODE", jsonObject.getString("brandCode"));
        record.set("BRAND_NAME", brandName);
        record.set("PRODUCT_CODE", jsonObject.getString("productCode"));
        record.set("PRODUCT_MODEL", productModel);
        record.set("BUSI_TYPE_CODE", jsonObject.getString("busiTypeCode"));
        record.set("BUSI_TYPE", busiType);
        record.set("ORDER_SOURCE_CODE", jsonObject.getString("orderSourceCode"));
        record.set("ORDER_SOURCE", orderSource);
        try {
            query.update(record);
            noticeUpdateCountManager();
            return EasyResult.ok();
        } catch (SQLException e) {
            getLogger().info("更新失败：" + e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    public EasyResult actionForUpdateCount() {
        EasySQL sql = new EasySQL();
        JSONObject jsonObject = getJSONObject();
        sql.append(jsonObject.getString("id"), "update YWDB.WECOM_MANAGER_CONFIG set USE_COUNT = USE_COUNT + 1 where ID = ?");
        try {
            query.executeUpdate(sql.getSQL(), sql.getParams());
            return EasyResult.ok();
        } catch (SQLException e) {
            getLogger().info("更新失败：" + e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    public EasyResult actionForDelete() {
        EasyRecord record = new EasyRecord("WECOM_MANAGER_CONFIG");
        JSONObject jsonObject = getJSONObject();
        record.setPrimaryKey("ID");
        record.set("ID", jsonObject.getString("id"));
        try {
            query.deleteById(record);
            noticeUpdateCountManager();
            return EasyResult.ok();
        } catch (SQLException e) {
            getLogger().info("删除失败：" + e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    /**
     * 导入企微管家配置数据
     * Excel文件格式：管家ID,管家姓名,品牌编码,品牌名称,产品型号编码,产品型号,实施业务类型编码,实施业务类型,单据来源编码,单据来源,状态
     */
    public EasyResult actionForImport() {
        try {
            Part filePart = getFile("file");
            if (filePart == null) {
                return EasyResult.error(400, "请选择要导入的文件");
            }

            Workbook workbook = WorkbookFactory.create(filePart.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            int maxRow = sheet.getLastRowNum();

            if (maxRow <= 0) {
                return EasyResult.error(400, "Excel文件中没有数据");
            }

            List<String> errorMessages = new ArrayList<>();
            int successCount = 0;
            int errorCount = 0;

            // 从第2行开始读取数据（第1行是标题）
            for (int i = 1; i <= maxRow; i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                try {
                    // 读取Excel各列数据
                    String managerId = getCellValueAsString(row.getCell(0));
                    String managerName = getCellValueAsString(row.getCell(1));
                    String brandCode = getCellValueAsString(row.getCell(2));
                    String brandName = getCellValueAsString(row.getCell(3));
                    String productCode = getCellValueAsString(row.getCell(4));
                    String productModel = getCellValueAsString(row.getCell(5));
                    String busiTypeCode = getCellValueAsString(row.getCell(6));
                    String busiType = getCellValueAsString(row.getCell(7));
                    String orderSourceCode = getCellValueAsString(row.getCell(8));
                    String orderSource = getCellValueAsString(row.getCell(9));
                    String status = getCellValueAsString(row.getCell(10));

                    // 验证必填字段
                    if (managerId == null || managerId.trim().isEmpty()) {
                        errorMessages.add("第" + (i + 1) + "行：管家ID不能为空");
                        errorCount++;
                        continue;
                    }
                    if (managerName == null || managerName.trim().isEmpty()) {
                        errorMessages.add("第" + (i + 1) + "行：管家姓名不能为空");
                        errorCount++;
                        continue;
                    }
                    if (brandName == null || brandName.trim().isEmpty()) {
                        errorMessages.add("第" + (i + 1) + "行：品牌名称不能为空");
                        errorCount++;
                        continue;
                    }
                   /* if (productModel == null || productModel.trim().isEmpty()) {
                        errorMessages.add("第" + (i + 1) + "行：产品型号不能为空");
                        errorCount++;
                        continue;
                    }*/
                    if (busiType == null || busiType.trim().isEmpty()) {
                        errorMessages.add("第" + (i + 1) + "行：实施业务类型不能为空");
                        errorCount++;
                        continue;
                    }
                    if (orderSource == null || orderSource.trim().isEmpty()) {
                        errorMessages.add("第" + (i + 1) + "行：单据来源不能为空");
                        errorCount++;
                        continue;
                    }

                    // 检查组合唯一性：企微ID + 品牌名称 + 产品型号 + 实施业务类型 + 单据来源
                    EasySQL checkSql = new EasySQL("SELECT COUNT(1) FROM YWDB.WECOM_MANAGER_CONFIG WHERE 1 = 1");
                    checkSql.append(managerId.trim(), " AND MANAGER_ID = ?");
                    checkSql.append(brandName.trim(), " AND BRAND_NAME = ?");
                    checkSql.append(productModel.trim(), " AND PRODUCT_MODEL = ?");
                    checkSql.append(busiType.trim(), " AND BUSI_TYPE = ?");
                    checkSql.append(orderSource.trim(), " AND ORDER_SOURCE = ?");

                    boolean exists = query.queryForExist(checkSql.getSQL(), checkSql.getParams());
                    if (exists) {
                        errorMessages.add("第" + (i + 1) + "行：该企微管家配置组合已存在（企微ID=" + managerId.trim() + ", 品牌=" + brandName.trim() + ", 产品型号=" + productModel.trim() + ", 业务类型=" + busiType.trim() + ", 单据来源=" + orderSource.trim() + "），跳过导入");
                        errorCount++;
                        continue;
                    }

                    // 创建记录
                    EasyRecord record = new EasyRecord("WECOM_MANAGER_CONFIG");
                    record.set("ID", RandomKit.randomStr());
                    record.set("MANAGER_ID", managerId.trim());
                    record.set("MANAGER_NAME", managerName.trim());
                    record.set("BRAND_CODE", brandCode != null ? brandCode.trim() : "");
                    record.set("BRAND_NAME", brandName != null ? brandName.trim() : "");
                    record.set("PRODUCT_CODE", productCode != null ? productCode.trim() : "");
                    record.set("PRODUCT_MODEL", productModel != null ? productModel.trim() : "");
                    record.set("BUSI_TYPE_CODE", busiTypeCode != null ? busiTypeCode.trim() : "");
                    record.set("BUSI_TYPE", busiType != null ? busiType.trim() : "");
                    record.set("ORDER_SOURCE_CODE", orderSourceCode != null ? orderSourceCode.trim() : "");
                    record.set("ORDER_SOURCE", orderSource != null ? orderSource.trim() : "");
                    record.set("CREATE_TIME", DateUtil.getCurrentDateStr());
                    record.set("STATUS", (status != null && ("1".equals(status.trim()) || "启用".equals(status.trim()))) ? "1" : "0");
                    record.set("USE_COUNT", "0");

                    query.save(record);
                    successCount++;

                } catch (Exception e) {
                    getLogger().error("处理第" + (i + 1) + "行数据时出错：" + e.getMessage(), e);
                    errorMessages.add("第" + (i + 1) + "行：数据格式错误 - " + e.getMessage());
                    errorCount++;
                }
            }

            // 构建返回结果
            JSONObject result = new JSONObject();
            result.put("successCount", successCount);
            result.put("errorCount", errorCount);
            result.put("totalCount", maxRow);

            if (!errorMessages.isEmpty()) {
                result.put("errors", errorMessages);
            }

            String message = String.format("导入完成！成功导入 %d 条记录", successCount);
            if (errorCount > 0) {
                message += String.format("，失败 %d 条记录", errorCount);
            }

            noticeUpdateCountManager();
            return EasyResult.ok(result, message);

        } catch (Exception e) {
            try {
                query.roolback();
            } catch (SQLException ex) {
                getLogger().error("回滚事务失败：" + ex.getMessage(), ex);
            }
            getLogger().error("导入失败：" + e.getMessage(), e);
            return EasyResult.error(500, "导入失败：" + e.getMessage());
        }
    }

    /**
     * 获取单元格值并转换为字符串
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellTypeEnum()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 处理数字类型，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return null;
            default:
                return cell.toString();
        }
    }

    /**
     * 下载企微管家配置导入模板
     */
    public void actionForDownload() {
        java.io.File file = new java.io.File(this.getRequest().getServletContext().getRealPath("/template/ManagerConfigTemplate.xls"));
        renderFile(file, "企微管家配置导入模板.xls");
    }

    //通知开启cc-consumer的服务器更新同步器
    public void noticeUpdateCountManager(){
        try{
            String value = cache.get("CC_CONSUMER_QUEUE_HEART_CACHE");
            if(StringUtils.isNotBlank(value)){
                List<String> ips = JSONArray.parseArray(value,String.class);
                ips.forEach(ip-> cache.put("noticeUpdateCountManager_"+ip,"Y",30));
                getLogger().info("已通知以下ip去更新企微管家使用次数同步器：" +value);
            }
        }catch (Exception e){
            getLogger().info("[noticeUpdateCountManager]>>> error:" + e.getMessage(), e);
        }
    }
}