package com.yunqu.cc.wecom.servlet;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.Part;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.DateUtil;

import com.yunqu.cc.wecom.base.AppBaseServlet;
import com.yunqu.cc.wecom.base.Constants;
import org.apache.poi.ss.usermodel.*;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;
import java.util.List;
import java.util.ArrayList;

@WebServlet("/servlet/WechatUser")
@MultipartConfig
public class WechatUserServlet extends AppBaseServlet {

    EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_YW);
    private static EasyCache cache = CacheManager.getMemcache();

    public EasyResult actionForAdd() {
        EasyRecord record = new EasyRecord("DEFAULT_WECHAT_USER");
        JSONObject jsonObject = getJSONObject();
        String staffWxid = jsonObject.getString("staffWxid");
        
        // 检查员工微信ID是否已存在
        if (StringUtils.isNotBlank(staffWxid)) {
            EasySQL checkSql = new EasySQL("select count(1) from YWDB.DEFAULT_WECHAT_USER where 1 = 1");
            checkSql.append(staffWxid, "and STAFF_WXID = ?");
            try {
                boolean exists = query.queryForExist(checkSql.getSQL(), checkSql.getParams());
                if (exists) {
                    getLogger().info("员工微信ID已存在：" + staffWxid);
                    return EasyResult.fail("员工微信ID已存在，不能重复添加");
                }
            } catch (SQLException e) {
                getLogger().info("检查员工微信ID唯一性失败：" + e.getMessage(), e);
                return EasyResult.fail("检查员工微信ID唯一性失败");
            }
        }
        
        record.set("ID", RandomKit.randomStr());
        record.set("STAFF_WXID", staffWxid);
        record.set("STAFF_NAME", jsonObject.getString("staffName"));
        record.set("CREATE_TIME", DateUtil.getCurrentDateStr());
        record.set("STATUS", jsonObject.getString("status"));
        record.set("USE_COUNT", "0");
        try {
            query.save(record);
            noticeUpdateCountDefaultUser();
            return EasyResult.ok();
        } catch (SQLException e) {
            getLogger().info("保存失败：" + e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    public EasyResult actionForUpdate() {
        EasyRecord record = new EasyRecord("DEFAULT_WECHAT_USER");
        JSONObject jsonObject = getJSONObject();
        String id = jsonObject.getString("id");
        String staffWxid = jsonObject.getString("staffWxid");
        
        // 检查员工微信ID是否已存在于其他记录中
        if (StringUtils.isNotBlank(staffWxid)) {
            EasySQL checkSql = new EasySQL("select count(1) from YWDB.DEFAULT_WECHAT_USER where 1 = 1");
            checkSql.append(staffWxid, "and STAFF_WXID = ?");
            checkSql.append(id, "and ID != ?");
            try {
                boolean exists = query.queryForExist(checkSql.getSQL(), checkSql.getParams());
                if (exists) {
                    getLogger().info("员工微信ID已存在于其他记录中：" + staffWxid);
                    return EasyResult.fail("员工微信ID已存在，不能重复使用");
                }
            } catch (SQLException e) {
                getLogger().info("检查员工微信ID唯一性失败：" + e.getMessage(), e);
                return EasyResult.fail("检查员工微信ID唯一性失败");
            }
        }
        
        record.setPrimaryKey("ID");
        record.set("ID", id);
        record.set("STAFF_WXID", staffWxid);
        record.set("STAFF_NAME", jsonObject.getString("staffName"));
        record.set("STATUS", jsonObject.getString("status"));
        try {
            query.update(record);
            noticeUpdateCountDefaultUser();
            return EasyResult.ok();
        } catch (SQLException e) {
            getLogger().info("更新失败：" + e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    public EasyResult actionForUpdateCount() {
        EasySQL sql = new EasySQL();
        JSONObject jsonObject = getJSONObject();
        sql.append(jsonObject.getString("id"), "update YWDB.DEFAULT_WECHAT_USER set USE_COUNT = USE_COUNT + 1 where ID = ?");
        try {
            query.executeUpdate(sql.getSQL(), sql.getParams());
            return EasyResult.ok();
        } catch (SQLException e) {
            getLogger().info("更新失败：" + e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    public EasyResult actionForDelete() {
        JSONObject jsonObject = getJSONObject();
        EasyRecord record = new EasyRecord("DEFAULT_WECHAT_USER");
        record.setPrimaryKey("ID");
        record.set("ID", jsonObject.getString("id"));
        try {
            query.deleteById(record);
            noticeUpdateCountDefaultUser();
            return EasyResult.ok();
        } catch (SQLException e) {
            getLogger().info("删除失败：" + e.getMessage(), e);
            return EasyResult.fail();
        }
    }

    /**
     * 导入缺省微信用户配置数据
     * Excel文件格式：员工微信ID,员工姓名,状态
     */
    public EasyResult actionForImport() {
        try {
            Part filePart = getFile("file");
            if (filePart == null) {
                return EasyResult.error(400, "请选择要导入的文件");
            }

            Workbook workbook = WorkbookFactory.create(filePart.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            int maxRow = sheet.getLastRowNum();

            if (maxRow <= 0) {
                return EasyResult.error(400, "Excel文件中没有数据");
            }

            List<String> errorMessages = new ArrayList<>();
            int successCount = 0;
            int errorCount = 0;

            // 从第2行开始读取数据（第1行是标题）
            for (int i = 1; i <= maxRow; i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                try {
                    // 读取Excel各列数据
                    String staffWxid = getCellValueAsString(row.getCell(0));
                    String staffName = getCellValueAsString(row.getCell(1));
                    String status = getCellValueAsString(row.getCell(2));

                    // 验证必填字段
                    if (StringUtils.isBlank(staffWxid)) {
                        errorMessages.add("第" + (i + 1) + "行：员工微信ID不能为空");
                        errorCount++;
                        continue;
                    }
                    if (StringUtils.isBlank(staffName)) {
                        errorMessages.add("第" + (i + 1) + "行：员工姓名不能为空");
                        errorCount++;
                        continue;
                    }
                    if (StringUtils.isBlank(status)) {
                        errorMessages.add("第" + (i + 1) + "行：状态不能为空");
                        errorCount++;
                        continue;
                    }

                    // 检查员工微信ID是否已存在
                    EasySQL checkSql = new EasySQL("select count(1) from YWDB.DEFAULT_WECHAT_USER where 1 = 1");
                    checkSql.append(staffWxid.trim(), "and STAFF_WXID = ?");
                    
                    boolean exists = query.queryForExist(checkSql.getSQL(), checkSql.getParams());
                    if (exists) {
                        errorMessages.add("第" + (i + 1) + "行：员工微信ID已存在，跳过导入");
                        errorCount++;
                        continue;
                    }

                    // 创建记录
                    EasyRecord record = new EasyRecord("DEFAULT_WECHAT_USER");
                    record.set("ID", RandomKit.randomStr());
                    record.set("STAFF_WXID", staffWxid.trim());
                    record.set("STAFF_NAME", staffName.trim());
                    record.set("CREATE_TIME", DateUtil.getCurrentDateStr());
                    record.set("STATUS", (status != null && ("1".equals(status.trim()) || "启用".equals(status.trim()))) ? "1" : "0");
                    record.set("USE_COUNT", "0");

                    query.save(record);
                    successCount++;

                } catch (Exception e) {
                    getLogger().error("处理第" + (i + 1) + "行数据时出错：" + e.getMessage(), e);
                    errorMessages.add("第" + (i + 1) + "行：数据格式错误 - " + e.getMessage());
                    errorCount++;
                }
            }

            // 构建返回结果
            JSONObject result = new JSONObject();
            result.put("successCount", successCount);
            result.put("errorCount", errorCount);
            result.put("totalCount", maxRow);

            if (!errorMessages.isEmpty()) {
                result.put("errors", errorMessages);
            }

            String message = String.format("导入完成！成功导入 %d 条记录", successCount);
            if (errorCount > 0) {
                message += String.format("，失败 %d 条记录", errorCount);
            }

            noticeUpdateCountDefaultUser();
            return EasyResult.ok(result, message);

        } catch (Exception e) {
            try {
                query.roolback();
            } catch (SQLException ex) {
                getLogger().error("回滚事务失败：" + ex.getMessage(), ex);
            }
            getLogger().error("导入失败：" + e.getMessage(), e);
            return EasyResult.error(500, "导入失败：" + e.getMessage());
        }
    }

    /**
     * 获取单元格值并转换为字符串
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellTypeEnum()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 处理数字类型，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return null;
            default:
                return cell.toString();
        }
    }

    /**
     * 下载缺省微信用户配置导入模板
     */
    public void actionForDownload() {
        java.io.File file = new java.io.File(this.getRequest().getServletContext().getRealPath("/template/WechatUserTemplate.xls"));
        renderFile(file, "缺省微信用户配置导入模板.xls");
    }

    //通知开启cc-consumer的服务器更新同步器
    public void noticeUpdateCountDefaultUser(){
        try{
            String value = cache.get("CC_CONSUMER_QUEUE_HEART_CACHE");
            if(StringUtils.isNotBlank(value)){
                List<String> ips = JSONArray.parseArray(value,String.class);
                ips.forEach(ip-> cache.put("noticeUpdateCountDefaultUser_"+ip,"Y",30));
                getLogger().info("已通知以下ip去更新企微默认用使用次数同步器：" +value);
            }
        }catch (Exception e){
            getLogger().info("[noticeUpdateCountManager]>>> error:" + e.getMessage(), e);
        }
    }
}