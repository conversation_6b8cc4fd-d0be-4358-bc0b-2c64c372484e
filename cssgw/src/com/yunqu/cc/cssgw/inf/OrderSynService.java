package com.yunqu.cc.cssgw.inf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;
import com.yq.busi.common.util.security.SecurityUtil;
import com.yunqu.cc.cssgw.base.CommonLogger;
import com.yunqu.cc.cssgw.base.Constants;
import org.apache.log4j.Logger;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;
import java.sql.SQLException;

import static com.yq.busi.common.util.IDGenerator.getDefaultNUMID;

public class OrderSynService extends IService {

    private static EasyQuery query =  EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);

    public static Logger logger = CommonLogger.getLogger("orderSyn");

    public static Logger delLogger = CommonLogger.getLogger("orderDelete");



    @Override
    public JSONObject invoke(JSONObject params) throws ServiceException {
        logger.info(CommonUtil.getClassNameAndMethod(this) + "OrderSynService服务接受CS工单推送入库======>>params:"+params);
        String command = params.getString("command");
        long startTime = System.currentTimeMillis();
        try {
            if ("synOrderRecord".equals(command)) {
                return synOrderRecord(params.getJSONArray("data"));
            } else if ("deleteOrderRecordByJob".equals(command)){
                return deleteOrderRecordByJob();
            } else {
                JSONObject result = new JSONObject();
                result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
                result.put("respDesc", "不存在的command,请检查！");
                return result;
            }
        } finally {
            long endTime = System.currentTimeMillis();
            long elapsedTime = endTime - startTime;
            logger.info(CommonUtil.getClassNameAndMethod(this) + "OrderSynService服务接受CS工单推送入库结束,耗时"+elapsedTime);
        }
    }

    private JSONObject synOrderRecord(JSONArray data) {
        JSONObject result = new JSONObject();
        try {
            for (int i = 0; i < data.size(); i++) {
                JSONObject dataObj = data.getJSONObject(i);
                
                // 参数判空校验
                if (StringUtils.isBlank(dataObj.getString("phone"))) {
                    result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
                    result.put("respDesc", "手机号码不能为空");
                    return result;
                }
                if (StringUtils.isBlank(dataObj.getString("serviceOrderNumber"))) {
                    result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
                    result.put("respDesc", "服务单号不能为空");
                    return result;
                }
                if (StringUtils.isBlank(dataObj.getString("orgCode"))) {
                    result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
                    result.put("respDesc", "主体编码不能为空");
                    return result;
                }
                if (StringUtils.isBlank(dataObj.getString("orgName"))) {
                    result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
                    result.put("respDesc", "主体名称不能为空");
                    return result;
                }
                if (StringUtils.isBlank(dataObj.getString("brandCode"))) {
                    result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
                    result.put("respDesc", "品牌编码不能为空");
                    return result;
                }
                if (StringUtils.isBlank(dataObj.getString("brandName"))) {
                    result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
                    result.put("respDesc", "品牌名称不能为空");
                    return result;
                }
                if (StringUtils.isBlank(dataObj.getString("prodCode"))) {
                    result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
                    result.put("respDesc", "品类编码不能为空");
                    return result;
                }
                if (StringUtils.isBlank(dataObj.getString("prodName"))) {
                    result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
                    result.put("respDesc", "品类名称不能为空");
                    return result;
                }
//                if (StringUtils.isBlank(dataObj.getString("productCode"))) {
//                    result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
//                    result.put("respDesc", "产品编码不能为空");
//                    return result;
//                }
//                if (StringUtils.isBlank(dataObj.getString("productModel"))) {
//                    result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
//                    result.put("respDesc", "产品型号不能为空");
//                    return result;
//                }
                if (StringUtils.isBlank(dataObj.getString("busiTypeCode"))) {
                    result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
                    result.put("respDesc", "实施业务类型编码不能为空");
                    return result;
                }
                if (StringUtils.isBlank(dataObj.getString("busiType"))) {
                    result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
                    result.put("respDesc", "实施业务类型不能为空");
                    return result;
                }
                if (StringUtils.isBlank(dataObj.getString("orderSourceCode"))) {
                    result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
                    result.put("respDesc", "单据来源编码不能为空");
                    return result;
                }
                if (StringUtils.isBlank(dataObj.getString("orderSource"))) {
                    result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
                    result.put("respDesc", "单据来源不能为空");
                    return result;
                }
            }
            
            //提交工单提醒服务
            JSONObject json = new JSONObject();
            json.put("sender", "cssgw");
            json.put("password", "YQ_85521717");
            json.put("serialId", getDefaultNUMID());
            json.put("command","QyWeiXinOrderNotice");
            json.put("serviceId", "ORDER_NOTICE_SERVICE");
            json.put("timestamp", DateUtil.getCurrentDateStr());
            String sign = SecurityUtil.encryptMsgByMD5(json.getString("sender")+json.getString("password")+json.getString("timestamp")+json.getString("serialId"));
            json.put("signature", sign);
            json.put("data",data);
            try {
                long start = System.currentTimeMillis();
                String gatewayUrl = getRandomGatewayUrl(Constants.getUnifiedGatewayUrl());
                logger.info("提交工单提醒服务url:" + gatewayUrl + ",参数:" + json.toJSONString() );
                //默认5s超时机制
                HttpResp resp = HttpUtil.sendPost(gatewayUrl, json.toJSONString(), HttpUtil.TYPE_JSON, GWConstants.ENCODE_UTF8, 5);
                long end = System.currentTimeMillis();
                logger.info("工单提醒服务服务调用结果: " + resp.getResult());
                logger.info("提交工单提醒服务服务耗时:"+(end-start));
            } catch (Exception e) {
                logger.error("工单提醒服务SENSITIVE-WORD-IService请求失败,原因: "+e.getMessage());
            }
            for (int i = 0; i < data.size(); i++) {
                JSONObject dataObj = data.getJSONObject(i);
                EasyRecord record = new EasyRecord("C_NO_SERVICE_ORDER_MATCH", "ID");
                record.put("ID", RandomKit.uniqueStr());
                record.put("CREATE_TIME", DateUtil.getCurrentDateStr());
                record.put("PHONE", dataObj.getString("phone"));
                record.put("SERVICE_ORDER_NUMBER", dataObj.getString("serviceOrderNumber"));
                record.put("ORG_CODE", dataObj.getString("orgCode"));
                record.put("ORG_NAME", dataObj.getString("orgName"));
                record.put("BRAND_CODE", dataObj.getString("brandCode"));
                record.put("BRAND_NAME", dataObj.getString("brandName"));
                record.put("PROD_CODE", dataObj.getString("prodCode"));
                record.put("PROD_NAME", dataObj.getString("prodName"));
                record.put("PRODUCT_CODE", dataObj.getString("productCode"));
                record.put("PRODUCT_MODEL", dataObj.getString("productModel"));
                record.put("BUSI_TYPE_CODE", dataObj.getString("busiTypeCode"));
                record.put("BUSI_TYPE", dataObj.getString("busiType"));
                record.put("ORDER_SOURCE_CODE", dataObj.getString("orderSourceCode"));
                record.put("ORDER_SOURCE", dataObj.getString("orderSource"));
                query.save(record);
            }
            result.put("respCode", GWConstants.RET_CODE_SUCCESS);
            result.put("respDesc", "工单同步服务成功");
            return result;
        } catch (Exception e) {
            logger.error(CommonUtil.getClassNameAndMethod(this)+" 处理工单推送入库出现异常:"+e.getMessage());
            result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
            result.put("respDesc", "工单同步服务出现异常");
            return result;
        }
    }

    /**
     *  定时任务调用  每天凌晨2点执行 删除一个月以前的工单记录
     * @return JSONObject
     */
    private JSONObject deleteOrderRecordByJob() {
        JSONObject result = new JSONObject();
        try {
            String deleteTime = DateUtil.addMonth(DateUtil.TIME_FORMAT, DateUtil.getCurrentDateStr(), -1);
            String deleteMatchTime = DateUtil.addMonth(DateUtil.TIME_FORMAT, DateUtil.getCurrentDateStr(), -6);
            if (StringUtils.isBlank(deleteTime) || StringUtils.isBlank(deleteMatchTime)){
                result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
                result.put("respDesc", "定时任务删除相关工单记录日期错误");
                return result;
            }
            EasySQL sql = new EasySQL("DELETE FROM C_NO_SERVICE_ORDER_MATCH ");
            sql.append(deleteTime, "WHERE CREATE_TIME < ?",false);
            delLogger.info(CommonUtil.getClassNameAndMethod(this) + "定时任务删除一个月前S系统同步工单记录sql:"+sql.getSQL() + ",参数:" + deleteTime);

//            EasySQL matchSql = new EasySQL("DELETE FROM C_NO_GROUP_ORDERS_MATCH ");
//            matchSql.append(deleteMatchTime, "WHERE CREATE_TIME < ?",false);
//            delLogger.info(CommonUtil.getClassNameAndMethod(this) + "定时任务删除半年前群工单关联表匹配记录sql:"+matchSql.getSQL() + ",参数:" + deleteMatchTime);

            query.begin();
            query.execute(sql.getSQL(), sql.getParams());
//            query.execute(matchSql.getSQL(), matchSql.getParams());
            query.commit();

            result.put("respCode", GWConstants.RET_CODE_SUCCESS);
            result.put("respDesc", "定时任务删除相关工单记录成功");
            delLogger.info(CommonUtil.getClassNameAndMethod(this) + "定时任务删除相关工单记录完成");
            return result;
        } catch (Exception e) {
            delLogger.error(CommonUtil.getClassNameAndMethod(this)+" 定时任务删除相关工单记录出现异常:"+e.getMessage());
            try {
                query.roolback();
            } catch (SQLException ex) {
                delLogger.error(CommonUtil.getClassNameAndMethod(this)+" 定时任务删除相关工单记录回滚数据出现异常:"+ex.getMessage());
                throw new RuntimeException(ex);
            }
            result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
            result.put("respDesc", "定时任务删除相关工单记录出现异常");
            return result;
        }
    }

    /**
     * 从分号分隔的URL列表中随机选择一个URL
     * @param urlsStr 分号分隔的URL字符串
     * @return 随机选择的单个URL
     */
    private static String getRandomGatewayUrl(String urlsStr) {
        if(StringUtils.isBlank(urlsStr)) {
            return "";
        }

        // 如果不包含分号，则直接返回原URL
        if(!urlsStr.contains(";")) {
            return urlsStr;
        }

        // 分割URL字符串并随机选择一个
        String[] urls = urlsStr.split(";");
        if(urls.length == 0) {
            return urlsStr;
        }

        int randomIndex = (int)(Math.random() * urls.length);
        return urls[randomIndex];
    }
}
