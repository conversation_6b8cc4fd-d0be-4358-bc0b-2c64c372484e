<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<body>
	<style>
		.blank-page{padding: 20px 40px;overflow-y: hidden;border: solid 1px #cccccc; background-color: #ffffff;margin: 10px auto;margin-bottom: 100px}
        .blank-page .header-title{text-align:center;color:#333;font-size:19px}
		 
        .blank-page .p-title{border-bottom:1px solid #cccccc;font-size:16px;color:#555;margin-top:15px;margin-bottom:15px}
        .blank-page .p-title>span{border-bottom:2px solid #00a0f0;padding:2px 6px}       
        .chat-record{padding:20px;border:1px solid #eee;}
        .chat-record .record{margin-bottom:20px;}
        .record .datetime{font-size:12px}
        .record .chat-content{margin-top:10px;padding:5px 10px;border-radius:2px;line-height:1.5;width:fit-content;width:-webkit-fit-content;width:-moz-fit-content;}
        .record.text-l .chat-content{background:#eee;color:#999;}
        .record.text-r .chat-content{background:#9cea74;color:#636262;}
        .label-list{padding:15px;}
        .label-list span.label{margin-right:10px;margin-bottom:10px;display:inline-block;padding:8px;}
        .label-list span.label a{color:#fff}
        span.highlight{color:red;cursor:pointer}
        ul{padding-left:10px}
        ul li{list-style:none}
	</style>
		<div id="chatmain" class="col-md-6">
		<input type="hidden" id="chatType" name="chatType" value=""/>
			<p class="p-title"><span>标签</span></p>
			<div id="mydList" class="label-list" data-mars-reload="false">
			</div>
			
			<p class="p-title">
				<span>聊天记录</span>
				<button type="button" class="btn btn-sm" onclick="reotyTep()"> 转接记录</button>
				<a id="chat-video" href="javascript:record.chatVideo()" style="display:none;" class="btn btn-sm btn-link pull-right">视频会话</a>		
			</p>
			
			<div class="chat-record" id="char-record" data-mars="sessionTask.findCallRecordDetal">
			 </div>
		</div>				
		
		<div id="zjmain" class="col-md-6"  style="overflow: auto;">
			<ul class="nav nav-tabs" role="tablist">
		      <li role="presentation" class="active"><a href="#home" aria-controls="home" role="tab" data-toggle="tab">质检信息</a></li>
		      <li role="presentation"><a href="#profile" aria-controls="profile" role="tab" data-toggle="tab">历史记录</a></li>
		    </ul>
			 <div class="tab-content">
			<!--  table页第一个 -->
			    <div role="tabpanel" class="tab-pane active" id="home">
			    	 <p class="p-title"><span>呼叫信息</span></p>
					    <table id="callInfo" class="table table-vzebra" data-container="#callInfo" 
					    	data-template="callInfo-template" data-mars="sessionTask.findCallInfoByQcId">
					    </table>
						<script id="callInfo-template" type="text/x-jsrender">
									{{for list}}
									{{if #getIndex()==0}}
									<tbody>
							            <tr>
							               <td width="80px">坐席：</td>
							               <td>{{agentName:AGENT_NAME WX_AGENT_USERID}}</td>
							               <td width="80px">客户名称：</td>
							               <td>{{:CUSTOMER_NAME}}</td>
							               <td width="80px">请求类型：</td>
							               <td>{{custValFun:CONTACT_ORDER_SERV_TYPE_NAME "queries.getBigConsultree"}}</td>
							            </tr>
							            <tr>
							               <td  width="80px">通话类型：</td>
							               <td>{{dictFUN:DIRECTION "CALL_DIRECTION"}}</td>
							               <td  width="80px">自动质检：</td>
							               <td>{{:AUTO_QC_SCORE}}</td>
							               <td  width="80px">坐席监听率：</td>
							               <td >{{rateFun:MEDIA_LISTEN_RATE}}</td>
							            </tr>
										{{if EDIT_QC_ACC}}
										<tr>
							               <td  width="80px">质检人账号</td>
							               <td>{{:QC_ACC}}</td>
							               <td  width="80px">质检人姓名</td>
							               <td >{{:QC_NAME}}</td>
							               <td  width="80px">修改人账号</td>
							               <td>{{:EDIT_QC_ACC}}</td>
							            </tr>
							            <tr>
							               <td  width="80px">修改人姓名</td>
							               <td >{{:EDIT_QC_NAME}}</td>
							               <td  width="80px"></td>
							               <td></td>
							               <td  width="80px"></td>
							               <td></td>
							            </tr>
										{{else}}
										<tr>
							               <td  width="80px">质检人账号</td>
							               <td>{{:QC_ACC}}</td>
							               <td  width="80px">质检人姓名</td>
							               <td >{{:QC_NAME}}</td>
							               <td width="80px">满意度</td>
							               <td>{{:SATISF_NAME}}</td>
							            </tr>
										{{/if}}
							        </tbody>
									{{/if}}
									{{/for}}
									<tr>
					               		<td>总体评价</td>
								   		<td colspan="5">
								       		<textarea id="ztpj" onblur="record.asyncZtpj(this)" data-rules="required" class="form-control input-sm" rows="3"></textarea>
								   		</td>
									</tr>
								</script>
						
					    <p class="p-title">
					    	<span>自动质检评分(<span style="color: red;" id="totalAutoScore">0</span>)</span>
					    	<button type="button" id="moreBtn" class="btn btn-sm btn-link" style="float: right" onclick="toggleMore()" title="展开"> 展开 <span class="glyphicon glyphicon-menu-up"></span>&nbsp;</button>
					    </p>
						<div class="form-group" style="display:none" id="more">
					    <table class="table table-auto table-bordered table-condensed" data-mars="sessionTask.findAutoQCByQcIdNew">
					  		<thead>
					  			<tr>
					  				<th>考评项</th>
					  				<th>考评子项</th>
					  				<th>评分</th>
					  			</tr>
					  		</thead>
				            <tbody id="tbody-auto">
				              </tbody>
					    </table>
					    <p class="p-title">
					    	<span>自动质检点评</span>
					    </p>
				        <table class="table table-edit table-vzebra">
					        <tbody>
								<tr>
					               <td style="width:70px">总体评价</td>
								   <td colspan="5">
								       <textarea id="autoZTPJ" name="" class="form-control input-sm" rows="4" readonly="readonly" style="width:100%"></textarea>
								   </td>
								</tr>
					         </tbody>
					    </table>
					    </div>
					    <p class="p-title">
					    	<input class="hidden" name="totalScore" id="QCtotalScore" >
					    	<span>人工质检评分(<span style="color: red;" id="totalScore">0</span>)</span>
							<span class="pull-right" style="margin-top: -11px;">
								<select class="form-control input-sm" id="MANUAL_QC_KPI_ID" name="manualQcKpiId" style="width:200px;"
										data-mars="indicators.indicatorsDict('01','02')">
									<option value="">请选择考评标准</option>
								</select>
							</span>
					    </p>
					    <div id="rgzj" data-mars="sessionTask.findHandmadeQc" data-mars-prefix="handmadeQcScores."></div>
					   <p class="p-title"><span>点评</span></p>
				        <table class="table table-edit table-vzebra" data-mars-prefix="handmadeQc.">
					        <tbody id="tbody-handmadeQc">
					            <tr>
					               <td width="60px">事业部</td>
					               <td>
									  <!-- <select class="form-control input-sm" id="PRODUCT_TYPE" name="handmadeQc.PRODUCT_TYPE" style="width:120px" data-cust-mars="comm.sysCode('ORG_CODE')" class="form-control input-sm" data-cust-context-path="/neworder">
                                          <option value="" selected="selected">--请选择--</option>
                                      </select> -->
									  <select class="form-control input-sm" name="handmadeQc.PRODUCT_TYPE" id="PRODUCT_TYPE" style="width: 120px"  data-cust-context-path="/neworder"  data-cust-mars="comm.sysCode('ORG_CODE')">
                                          <option value="">请选择</option>
                                      </select>
					               </td>
					               <td>质检建议</td>
					               <td>
					                  <select id="QC_ADVICE" name="handmadeQc.QC_ADVICE" data-rules="required" class="form-control input-sm" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('QC_ADVICE')">
					                  </select>
					               </td>
					               <td>关键类型</td>
					               <td>
					                   <select id="KEY_TYPE" name="handmadeQc.KEY_TYPE" data-rules="required" class="form-control input-sm" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('QC_KEY_TYPE')">
					                  </select>
					               </td>
					            </tr>
					            <tr>
					               <td>扣分项</td>
					               <td>
					                   <select id="ADD_POINTS" name="handmadeQc.ADD_POINTS" data-rules="required" class="form-control input-sm" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('QC_ADD_POINTS')">
					                  </select>
					               </td>
<%--					               <td>负激励项</td>--%>
<%--					               <td>--%>
<%--					                   <select id="ZFJL_ITEMS" name="handmadeQc.ZFJL_ITEMS" data-rules="required" class="form-control input-sm" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('QC_ZFJL_ITEMS')">--%>
<%--					                   </select>--%>
<%--					               </td>--%>
								    <td>需要辅导</td>
					               <td>
					                   <select id="NEED_TUTORING" name="handmadeQc.NEED_TUTORING" data-rules="required" class="form-control input-sm" data-mars="task.sfYn">
					                   </select>
					               </td>
					            </tr>
					            <tr>
					               <td>不满意原因</td>
					               <td>
					                  <select id="NOT_SATIS_REASON" name="handmadeQc.NOT_SATIS_REASON" data-rules="required" class="form-control input-sm" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('QC_NOT_SATIS_REASON')">
					                  </select>
					               </td>
					               <td>聊天内容</td>
					               <td colspan="3"><input id="CALL_CONTENT" name="handmadeQc.CALL_CONTENT" class="form-control input-sm" type="text" value=""/></td>
					            </tr>
								<tr style="display:none;">
					               <td>总体评价</td>
								   <td colspan="5">
								       <textarea id="EVALUATION" name="handmadeQc.EVALUATION" data-rules="required" class="form-control input-sm" rows="3"></textarea>
								   </td>
								</tr>
								<tr>
					               <td>待定原因</td>
								   <td colspan="5">
								       <textarea id="PENDING_REASON" name="handmadeQc.PENDING_REASON" class="form-control input-sm" rows="3"></textarea>
								   </td>
								</tr>
					         </tbody>
					    </table>
					   <p class="p-title"><span>VOC改善点</span></p>
					   <table class="table table-edit table-vzebra" data-mars-prefix="handmadeQc.">
					        <tbody id="tbody-wtType">
					            <tr>
					               <td>分类</td>
					               <td>
					                  <select id="WT_ONE" name="handmadeQc.WT_CATAGORY" class="form-control input-sm" data-type-code="5" data-context-path="/agentconfig" data-mars="generalOther.findByTypeCodeAPid">
					                  	<option value="">无</option>
					                  </select>
					               </td>
					               <td>细项</td>
					               <td>
					                  <select id="WT_TWO" name="handmadeQc.WT_ITEM" class="form-control input-sm">
					                  	<option value="">无</option>
					                  </select>
					               </td>
					               <td>改善点</td>
					               <td>
					                  <select id="WT_THREE" name="handmadeQc.WT_IMPROVE" class="form-control input-sm">
					                  	<option value="">无</option>
					                  </select>
					               </td>
					            </tr>
					            <tr>
					               <td>详情</td>
					               <td colspan="5">
								       <textarea id="WT_FOUR" name="handmadeQc.WT_CONTENT" class="form-control input-sm" rows="3"></textarea>
								   </td>
					            </tr>
					         </tbody>
					    </table>
			    	</div>
			   <!--  table页 -->
			    <div role="tabpanel" class="tab-pane" id="profile">
			    	<p class="p-title"><span>质检日志</span></p>
			    	<table class="table table-auto table-bordered table-condensed"
			    			data-container="#qcLogList" data-template="qcLogList-template" data-mars="qcLog.findByQcId">
				  		<thead>
				  			<tr>
								<th>操作</th>
				  				<th>操作类型</th>
				  				<th>操作人账号</th>
				  				<th>日志内容</th>
				  				<th>创建时间</th>
				  			</tr>
				  		</thead>
			            <tbody id="qcLogList">
			            </tbody>
				    </table>
					<script id="qcLogList-template" type="text/x-jsrender">
							{{for list}}
					            <tr>
					               <td>
								   	<a href="javascript:void(0)" onclick="record.viewHisZj('{{:QC_RECORD_ID}}', '{{:QC_SCORE_ID}}')">查看</a>
								   </td>
					               <td>{{dictFUN:OPER_TYPE "QC_OPER_TYPE"}}</td>
					               <td>{{:CREATE_ACC}}</td>
					               <td>{{:CONTENT}}</td>
					               <td>{{:CREATE_TIME}}</td>
					            </tr>
							{{/for}}
					</script>
			    </div>
			  </div>
		  </div>
	<script type="text/javascript" src="${ctxPath }/static/lib/weixin/qq-wechat-emotion-parser.min.js"></script>
	<script type="text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	<script type="text/javascript" src = "${ctxPath}/pages/ywCommon/js/zjzxto.js" ></script>
	<script type="text/javascript">
	jQuery.namespace("record");
	var mainScore=0.7;
	var secondary=0.3;
	function toggleMore(){
	  var btn = $("#moreBtn").find(".glyphicon");
	  $("#more").slideToggle('fast');
	  btn.toggleClass("glyphicon glyphicon-menu-down glyphicon glyphicon-menu-up")
	}

	$.views.converters("rateFun", function(val) {
		var rate = "0";
		if (val && val.indexOf(".") == 0) {
			rate = "0" + val + "%";
		}
		else if (val && val != "0") {
			rate = val + "%";
		}
		return rate;
	});
	//坐席姓名
	$.views.converters("agentName", function(val1,val2) {
		if(typeof(val2)!="undefined"&&val2!=""){
			return val1 + "（企微成员ID：" + val2 + "）";
		}
		return val1;
	});
	
	function openBigImg(url) {
		var js = '<script type="text\/javascript">'+rotateImg.toString()+'rotateImg("bigImg")<\/script>';
    	var index = window.top.layer.open({type:1, area: ['300px', '195px'], maxmin: true, content: '<div id="bigImg" style="width:100%; height:100%; overflow:scroll; text-align:center;"><img src="'+url+'"/></div>'+js,shade: 0.1,fixed: false,shadeClose:true,offset:'20px'});
    	window.top.layer.full(index);
	}
	var canCount = 0;
	function initVideoChatBtn(){
		var qcId = $('#qcId').val();	 
		ajax.daoCall({
			params: {qcId: qcId},
			controls: ['sessionVideo.queryCount']
		}, function(data) {
			var result = data['sessionVideo.queryCount'];
			if(result.data&&result.data.videoCount>0){
				canCount = result.data.canCount
			    $("#chat-video").show();	
			}
		})
	}
	function GetScore(){
		ajax.remoteCall("${ctxPath}/servlet/task?action=GetScore",{},function(result) { 
			if(result.state == 1){
				var data=result.data
				mainScore=data.mainScore;
				secondary=data.secondary;
			}
		});
	}
	$(function(){
		GetScore()
		  requreLib.setplugs('slimscroll',function(){
				 $('#char-record').slimScroll({  
		               height: '500px',
		               color: '#ddd'
		         });
			});
			//初始化问题类型
			$('#WT_ONE').render();
			$('#WT_ONE').change(function() {
				var val1 = $(this).val();
				$('#WT_TWO').data('pid', val1);
				$('#WT_TWO').data('typeCode', '5');
				$('#WT_TWO').data('contextPath', '/agentconfig');
				$('#WT_TWO').data('mars', 'generalOther.findByTypeCodeAPid');
				$('#WT_TWO').render();
			})
			$('#WT_TWO').change(function() {
				var val1 = $(this).val();
				$('#WT_THREE').data('pid', val1);
				$('#WT_THREE').data('typeCode', '5');
				$('#WT_THREE').data('contextPath', '/agentconfig');
				$('#WT_THREE').data('mars', 'generalOther.findByTypeCodeAPid');
				$('#WT_THREE').render();
			});
			
			initVideoChatBtn();
	});
	
    $('span.highlight').on('click', function(){
    	  var ul = $("#table-templ").clone();
		  var tipsHtml=ul.html();
		  layer.tips(tipsHtml, this,{tips: [1, '#3595CC']}); //在元素的事件回调体中，follow直接赋予this即可
	})

    //同步聊天记录框的高度
    function asyncChatHeight() {
		var zjmainHeight = $('#zjmain').height();
		var delHeight = 0;
		$('#chatmain>[class!=slimScrollDiv]').each(function(index){
			delHeight += $(this).outerHeight(true);
		})
		$('#chatmain').height(zjmainHeight);
	 	$('#char-record').slimScroll({
             height: (zjmainHeight-delHeight)+'px',
             color: '#ddd'
        });
    }
	//图片旋转
    function rotateImg(parentId) {
		$.getScript("${ctxPath}/static/lib/jquery/jquery.rotate.min.js").done(function() {
			  $('#'+parentId+' img').click(function() {
				  var rotate = $(this).data('rotate');
				  if (rotate) {
					  $(this).data('rotate', rotate+1);
				  } else {
					  $(this).data('rotate', 1);
					  rotate = 1;
				  }
				  $(this).parent('div').rotate({animateTo: 90*rotate});
			  })
		})
    }


    function reotyTep(){
		var tempNum = 0;
		$("#chatType").val("");
		$('#editForm').render({
			success: function(result) {
				setTimeout(function() {
					var recordDetalData = result['sessionTask.findCallRecordDetal'].data;
					buildChat(recordDetalData);
					rotateImg('char-record');
				}, 0);
			}
		});
		document.getElementById("char-record").innerHTML = "";
		//构造聊天信息
		function buildChat(data) {
			var tempMydQc = {};
			var keywordQc = {};
			var records = {};
			var params = {};
			$('#mydList').empty();
			$.each(data, function(k, v) {
				if (!tempMydQc[v.NAME] && v.NAME) {
					buildMydList(v.KEYWORD_DIR_ID, v.NAME);
					tempMydQc[v.NAME] = '0';
				}

				if (!keywordQc[v.KEYWORD]) {
					keywordQc[v.KEYWORD] = {};
				}
				if (!keywordQc[v.KEYWORD][v.RECORD_DETAIL_ID]) {
					var content;
					if (records[v.RECORD_DETAIL_ID] && records[v.RECORD_DETAIL_ID].CONTENT) {
						content = records[v.RECORD_DETAIL_ID].CONTENT;
						var type = records[v.RECORD_DETAIL_ID].TYPE;
						if (type == '01' || type == '03' || !type) {
							try {
								var json = eval('('+content+')');
								if (json && json.url && json.name) {
									content = '<a href="'+json.url+'">'+json.name+'</a>';
								} else if (content.indexOf('mp3')!=-1||content.indexOf('wav')!=-1||(content.indexOf('.mp4')!=-1&&content.endsWith(".mp4"))) {
									content = '<audio controls title="音频" src="'+content+'" />';
								} else {
									content = qqWechatEmotionParser(content);
									content = replaceContent(content, v.KEYWORD, v.RECORD_DETAIL_ID, v.KEYWORD_DIR_ID, params);
								}
							} catch (e) {
								if (content.indexOf('mp3')!=-1||content.indexOf('wav')!=-1||(content.indexOf('.mp4')!=-1&&content.endsWith(".mp4"))) {
									content = '<audio controls title="音频" src="'+content+'" />';
								} else {
									content = qqWechatEmotionParser(content);
									content = replaceContent(content, v.KEYWORD, v.RECORD_DETAIL_ID, v.KEYWORD_DIR_ID, params);
								}
							}
						} else if (type == '02') {
							content = '<img ondblclick="openBigImg(\''+content+'\')" style="width: 100%; height: auto;max-width: 100%; display: block;" src="'+content+'"/>';
						} else if (type == '04') {
							var json = eval('('+content+')');
							content = '<a href="'+json.url+'">'+json.name+'</a>';
						} else if (type == '06') {
							content = '<video controls title="视频" src="'+content+'" style="max-width:100%;height:200px"/>';
						} else if (type == '07') {
							content = '<audio controls title="音频" src="'+content+'" />';
						}
					} else {
						content = v.CONTENT;
						var type = v.TYPE;
						if (type == '01' || type == '03' || !type) {
							try {
								var json = eval('('+content+')');
								if (json && json.url && json.name) {
									content = '<a href="'+json.url+'">'+json.name+'</a>';
								} else if (content.indexOf('mp3')!=-1||content.indexOf('wav')!=-1||(content.indexOf('.mp4')!=-1&&content.endsWith(".mp4"))) {
									content = '<audio controls title="音频" src="'+content+'" />';
								} else {
									content = qqWechatEmotionParser(content);
									content = replaceContent(content, v.KEYWORD, v.RECORD_DETAIL_ID, v.KEYWORD_DIR_ID, params);
								}
							} catch (e) {
								if (content.indexOf('mp3')!=-1||content.indexOf('wav')!=-1||(content.indexOf('.mp4')!=-1&&content.endsWith(".mp4"))) {
									content = '<audio controls title="音频" src="'+content+'" />';
								} else {
									content = qqWechatEmotionParser(content);
									content = replaceContent(content, v.KEYWORD, v.RECORD_DETAIL_ID, v.KEYWORD_DIR_ID, params);
								}
							}
						} else if (type == '02') {
							content = '<img ondblclick="openBigImg(\''+content+'\')" style="width: 100%; height: auto;max-width: 100%; display: block;" src="'+content+'"/>';
						} else if (type == '04') {
							var json = eval('('+content+')');
							content = '<a href="'+json.url+'">'+json.name+'</a>';
						} else if (type == '06') {
							content = '<video controls title="视频" src="'+content+'" style="max-width:100%;height:200px"/>';
						} else if (type == '07') {
							content = '<audio controls title="音频" src="'+content+'" />';
						}
					}
					if (!records[v.RECORD_DETAIL_ID]) {
						var record = {
							CONTENT: content,
							WITHDRAW:v.WITHDRAW,
							AGENT_ACC: v.AGENT_ACC,
							BEGIN_TIME: v.BEGIN_TIME,
							END_TIME: v.END_TIME,
							USER_TYPE: v.USER_TYPE,
							CUSTOMER_NAME: v.CUSTOMER_NAME,
							AGENT_NAME: v.AGENT_NAME,
							HANGUP_TYPE: v.HANGUP_TYPE
						};
						records[v.RECORD_DETAIL_ID] = record;
					} else {
						var record = records[v.RECORD_DETAIL_ID];
						record.CONTENT = content;
					}
				}
				keywordQc[v.KEYWORD][v.RECORD_DETAIL_ID] = '0';
			});

			var firstRecords;
			var userClient="";
			var red="";
			$.each(records, function(k, v) {
				if (!firstRecords) {
					firstRecords = records[k];
				}
				var content = v.CONTENT;
				var param = params[k];
				if (param) {
					$.each(param, function(k, v) {
						content = content.replace(k, v);
					})
				}

				var $name = v.AGENT_NAME;
				if(v.USER_TYPE == '02'){
					$name = v.CUSTOMER_NAME;
				}else if(v.USER_TYPE == '03'){
					$name = "【机器人】";
					var j = eval("(" + content + ")");
					if(j!=null){
						content = j.content;
					}
				}else if(v.USER_TYPE == '04'){
					$name = "【系统消息】";
				}
				var $div, $span, $span2, $contentDiv;
				$contentDiv = $('<div data-targetid="'+ k +'" class="chat-content">'+ content +'</div>');
				if (!v.USER_TYPE || v.USER_TYPE == '01' || v.USER_TYPE == '03' || v.USER_TYPE == '04') {//客服

					var new_date = new Date(userClient); //新建一个日期对象
					var old_date = new Date(v.BEGIN_TIME); //设置过去的一个时间点，"yyyy-MM-dd HH:mm:ss"格式化日期
					var difftime = ( old_date- new_date)/1000; //计算时间差,并把毫秒转换成秒
				   	var minutes = parseInt(difftime%3600/60);
				   	if(minutes>=1){
				   		if(minutes>=1){
					   		$span = $('<span class="datetime" style="color: red;">'+v.BEGIN_TIME+'</span>');
					   	}else{
							$span = $('<span class="datetime">'+v.BEGIN_TIME+'</span>');
					   	}
				   	}else{
						$span = $('<span class="datetime">'+v.BEGIN_TIME+'</span>');
				   	}

					$div = $('<div class="record text-r clearfix"></div>');
					$span2 = $('<span class="datetime" style="display: block;">'+ $name +'</span>');
					$div.append('<div class="pull-right"></div>');
					$div.find('div.pull-right').append($span2);
					$div.find('div.pull-right').append($span);
					$div.find('div.pull-right').append($contentDiv);
					if(v.WITHDRAW==1){
						var $withdrawdiv = $('<div style="font-size: 12px;color: #959393;">已撤回</div>');
						$div.find('div.pull-right').append($withdrawdiv);
					}
					red=v.USER_TYPE;
				} else if (v.USER_TYPE == '02') {//客户
					red=v.USER_TYPE;
					userClient=v.BEGIN_TIME;
					$span = $('<span class="datetime">'+v.BEGIN_TIME+'</span>');
					$div = $('<div class="record text-l"></div>');
					$span2 = $('<span class="datetime" style="display: block;">'+ $name +'</span>');
					$div.append($span2);
					$div.append($span);
					$div.append($contentDiv);
				}
				$('#char-record').append($div);
			});
		}
		//构造标签信息
		function buildMydList(dirId, name) {
			$('#mydList').append('<span class="label label-success">'+
					'<a id="'+ dirId +'" href="javascript:void(0)" onclick="record.keywordHighlight(this, \''+dirId+'\')">'+name+'</a>'+
				'</span>');
		}

		function replaceContent(str, targetStr, recordDetailId, dirId, params) {
			tempNum = tempNum? tempNum: 0;
			var beginIndex = str.indexOf(targetStr);
			if (targetStr && beginIndex != -1) {
				var endIndex = targetStr.length;
				var repParam = '#['+tempNum+']';
				var repStr = "<span tag='"+dirId+"' style='color: red;' onmouseover='record.keywordTips(this)'>"+targetStr+"</span>";
				str = str.replace(targetStr, repParam);
				if (!params[recordDetailId]) {
					params[recordDetailId] = {};
				}
				params[recordDetailId][repParam] = repStr;
				tempNum++;
				return replaceContent(str, targetStr, recordDetailId, dirId, params, tempNum);
			} else {
				return str;
			}
		}
	}

	$(function(){
		buildQcInfo();
	})

	//重新渲染质检评分区域
	function buildByManualQcKpiId(){
		// 重新获取呼叫信息
		var params = JSON.stringify(form.getJSONObject("#editForm"));
		var result = getControlsData("sessionTask.findCallInfoByQcId", '/PerformExamWeb' ,params);
		var callInfoData = result['sessionTask.findCallInfoByQcId'].data[0];
		standardType = callInfoData.STANDARD_TYPE;
		keyTypeDictCode = callInfoData.KEY_TYPE_DICT;

		changeMarsByStandardType(standardType);
		record.manualQcKpiIdRender(callInfoData);

		buildQcScoreInfo();

		renderByStandardType(standardType,true);
	}

	// 构建质检评分区域信息
	function buildQcScoreInfo() {
		setTimeout(function() {
			var params = JSON.stringify(form.getJSONObject("#editForm"));
			var result = getControlsData("sessionTask.findHandmadeQc", '/PerformExamWeb' ,params);
			var handmadeQCData = result['sessionTask.findHandmadeQc'].data;

			randerHandmadeQCScore(handmadeQCData);

			asyncChatHeight();

			$('.btn-link').click(function() {
				setTimeout(function() {
					asyncChatHeight();
				}, 500);
			})


			$('#tbody-handmadeQc select').not('#PRODUCT_TYPE, #EVALUATION').val('')
			$('#tbody-handmadeQc input').not('#PRODUCT_TYPE, #EVALUATION').val('')
			$('#tbody-handmadeQc textarea').not('#PRODUCT_TYPE, #EVALUATION').val('')
			$('#tbody-wtType select').val('')
			$('#tbody-wtType input').val('')
			$('#tbody-wtType textarea').val('')
		}, 0);
	}

	function buildQcInfo() {
		tempNum = 0;
		$('#editForm').custRender();
		$('#editForm').render({
			success: function(result) {
				setTimeout(function() {
					var callInfoData = result['sessionTask.findCallInfoByQcId'].data[0];
					$('#totalAutoScore').html(callInfoData.AUTO_QC_SCORE);
					standardType = callInfoData.STANDARD_TYPE;
					keyTypeDictCode = callInfoData.KEY_TYPE_DICT;
					changeMarsByStandardType(standardType);
					record.manualQcKpiIdRender(callInfoData);


					setTimeout(function() {
						if (result['sessionTask.findCallRecordDetal'] && result['sessionTask.findCallRecordDetal'].data) {
							var recordDetalData = result['sessionTask.findCallRecordDetal'].data;
							buildChat(recordDetalData);
						}
					}, 0);
					setTimeout(function() {
						if (result['sessionTask.findAutoQCByQcIdNew'] && result['sessionTask.findAutoQCByQcIdNew'].data) {
							var autoQCData = result['sessionTask.findAutoQCByQcIdNew'].data;
							buildAutoQC(autoQCData);
						}
						if (result['sessionTask.findHandmadeQc'] && result['sessionTask.findHandmadeQc'].data) {
							var handmadeQCData = result['sessionTask.findHandmadeQc'].data;
							buildHandmadeQC(handmadeQCData);
						}

						asyncChatHeight();

						$('.btn-link').click(function() {
							setTimeout(function() {
								asyncChatHeight();
							}, 500);
						})

						rotateImg('char-record');

						if (handmadeQCData && handmadeQCData.length > 0){
							//将值丢进属性里，防止elementCustRender渲染的时候回显不到值
							$("#KEY_TYPE").attr('data-value',handmadeQCData[0].KEY_TYPE);
						}
						renderByStandardType(standardType)

						var type = '${param.type}';
						//判断是否可以编辑
						type = type || '1';
						if (type == '2') {
							$('#editForm').find('select,input,textarea').prop('disabled', 'disabled');
						}
					}, 0);
				}, 0);
			}
		});
	}

	function buildHandmadeQcScore(types,items) {
		// 清空旧的区域
		$('#rgzj').empty();
		//获取数据字典
		var kpiCategoryTypeDict = getControlsData(kpicontrols, '/yq_common');
		//开始构建表格
		$.each(types, function (type, categorys) {
			var isOpen = type.indexOf('Z') == 0 ? true : false;
			buildKPTable(categorys, items, isOpen, kpiCategoryTypeDict[kpicontrols].data[type]);
		})

		//初始化备选项多选
		$('select[name^="handmadeQcScores.BAKUP&"]').select2({
			placeholder: '请选择',
			theme: "bootstrap"
		});

		//备选项内容变更事件
		$('select[name^="handmadeQcScores.BAKUP&"]').on("change", function (e) {
			debugger
			var $bakup = $(this).parent().parent().find('input[name^="handmadeQcScores.TBAKUP"]');
			var data = $(this).select2("data");
			var val = '';
			$.each(data, function (k, v) {
				val += v.text;
			})

			//setEVALUATION($bakup, val);
			changeTrColor();
		});
		//评分项变更事件
		$('select[name^="handmadeQcScores.SCORE"]').on("change", function (e, a) {
			var itemId = $(this).data('itemId');
			var scoreId = $(this).find('option:selected').attr('sid');
			var defselectBakup = $(this).find('option:selected').data('defselectBakup');
			var score = $(this).val();
			//统计分数
			calculateScore();

			var bakups = findBakups(itemId, scoreId);

			var data = [];
			var dataVals = '';
			for (var i in bakups) {
				if (defselectBakup == 'Y') {
					data.push({id: bakups[i].ID, text: bakups[i].CONTENT, selected: true});
					dataVals += bakups[i].CONTENT;
				} else {
					data.push({id: bakups[i].ID, text: bakups[i].CONTENT, selected: false});
				}
			}

			var $select = $('select[name="handmadeQcScores.BAKUP&' + itemId + '"]');
			if ($select.length == 0) {
				$select = $('<select style="width:300px" name="handmadeQcScores.BAKUP&' + itemId + '" class="form-control input-sm" multiple="multiple"></select>');
				$select.append('<option value="">请选择</option>');
				$('#td_' + itemId).append($select);
			}
			var instance = $select.data('select2');
			if (instance) {
				$select.select2('destroy').empty();
			}
			$select.select2({
				placeholder: '请选择',
				theme: "bootstrap",
				data: data
			});
			setEVALUATION($select.parent().parent().find('input[name^="handmadeQcScores.TBAKUP"]'), dataVals);
			$select.on("change", function (e) {
				var $bakup = $(this).parent().parent().find('input[name^="handmadeQcScores.TBAKUP"]');
				var data = $(this).select2("data");
				var val = '';
				$.each(data, function (k, v) {
					val += v.text;
				})
				setEVALUATION($bakup, val);
			});

			//自动匹配关键类型
			if (parseInt(score) < 0) {
				setKeyTypeByScore(this);
			}else {//ouchengjian
				var qcKpiCategoryType = $(this).attr('data-type');
				if (qcKpiCategoryType.substring(0, 1) == ('S')) {
					var cancelKeyType = true;
					var scoresLength = $('select[name^="handmadeQcScores.SCORE"]').length;
					for (let i = 0; i < scoresLength; i++) {
						let scoreItem = $('select[name^="handmadeQcScores.SCORE"]')[i];
						var scoreItemType = $(scoreItem).attr('data-type');
						var scoreItemValue = $(scoreItem).val();
						if (scoreItemType.substring(0, 1) == ('S') && parseInt(scoreItemValue) < 0) {
							cancelKeyType = false;
							setKeyTypeByScore(scoreItem);
						}
					}
					if (cancelKeyType){
						$('#KEY_TYPE').val($('#KEY_TYPE').find('option:first').val());
					}
				}
			}

			// if (parseInt(score) === 0) {
			// 	$('#KEY_TYPE').val("00");
			// 	$(this).prop('selected', 'selected');
			// }

			changeTrColor();
		})

		$('input[name^="handmadeQcScores.TBAKUP"]').on('change', function (e) {
			var appendData = $(this).val();
			setEVALUATION($(this), appendData);
		})
	}

	function buildHandmadeEvaluate(types,items) {
		for (var j in types) {
			var categorys = types[j];
			for (var i in categorys) {
				//点评
				fillRecord(categorys[i][0], 'handmadeQc.', 'tbody-handmadeQc');
				$('#ztpj').val($('#EVALUATION').val());

				//点评默认选中
				$('#tbody-handmadeQc select').each(function () {
					var name = $(this).attr('name');
					if (name.indexOf('.') != -1) {
						var fix = name.substr(0, name.indexOf('.') + 1);
						var end = name.substr(name.indexOf('.') + 1, name.length);
						if (categorys[i][0][end]) {
							$(this).val(categorys[i][0][end]);
						} else {
							$(this).find('option[value="00"]').prop('selected', 'selected');
							$(this).find('option[value="0"]').prop('selected', 'selected');
							$(this).find('option[value="N"]').prop('selected', 'selected');
						}
					}
				})

				var PRODUCT_TYPE = $('#PRODUCT_TYPE').val();
				if (!PRODUCT_TYPE || PRODUCT_TYPE == '00') {
					var qcId = $('#qcId').val();
					ajax.daoCall({
						params: {qcId: qcId},
						controls: ['sessionTask.findPL']
					}, function (data) {
						var result = data['sessionTask.findPL'].data;
						if (result) {
							$('#PRODUCT_TYPE').val(result.ORG_CODE);
						}
					})
				}

				//问题类型
				if (categorys[i][0].WT_CATAGORY) {
					$('#WT_ONE').val(categorys[i][0].WT_CATAGORY);
					$('#WT_TWO').data('pid', categorys[i][0].WT_CATAGORY);
					$('#WT_TWO').data('typeCode', '5');
					$('#WT_TWO').data('contextPath', '/agentconfig');
					$('#WT_TWO').data('mars', 'generalOther.findByTypeCodeAPid');
					$('#WT_TWO').render({
						success: function (result) {
							$('#WT_TWO').val(categorys[i][0].WT_ITEM);
						}
					});
					$('#WT_THREE').data('pid', categorys[i][0].WT_ITEM);
					$('#WT_THREE').data('typeCode', '5');
					$('#WT_THREE').data('contextPath', '/agentconfig');
					$('#WT_THREE').data('mars', 'generalOther.findByTypeCodeAPid');
					$('#WT_THREE').render({
						success: function (result) {
							$('#WT_THREE').val(categorys[i][0].WT_IMPROVE);
						}
					});
					$('#WT_FOUR').val(categorys[i][0].WT_CONTENT);
				}
				break;
			}
			break;
		}
	}

	//人工质检
	function buildHandmadeQC(data, type) {
		if (!data || data.length <= 0) {
			return;
		}
		//处理数据结构
		var types = {};//分类类型
		var items = {};//分类细项
		$.each(data, function(k, v) {
			var categorys = types[v.TYPE];
			if (!categorys) {
				categorys = {};
			}
			var category = categorys[v.CATEGORY_NAME];
			if (!category) {
				categorys[v.CATEGORY_NAME] = [];
			}
			if (!items[v.ITEM_NAME]) {
				if (v.SCORE_OPTION_ID)
					items[v.ITEM_NAME] = [{SCORE_OPTION_ID: v.SCORE_OPTION_ID, SCORE_OPTION: v.SCORE_OPTION}];
				categorys[v.CATEGORY_NAME].push(v);
			} else {
				if (v.SCORE_OPTION_ID)
					items[v.ITEM_NAME].push({SCORE_OPTION_ID: v.SCORE_OPTION_ID, SCORE_OPTION: v.SCORE_OPTION});
			}
			types[v.TYPE] = categorys;
		})

		var h = document.documentElement.clientHeight;//获取页面可见高度
		document.getElementById("zjmain").style.height=h-160+"px";

		//构建点评区域
		buildHandmadeEvaluate(types,items);

		//构建质检评分区域
		buildHandmadeQcScore(types,items);

		type = type || '1';
		if (type == '2') {
			$('#editForm').find('select,input,textarea').prop('disabled', 'disabled');
		}
		//统计分数
		calculateScore();
	}

	//设置总体评价
	function setEVALUATION(e, val) {
		e.val(val);

		if (!val) {
			val = '';
		}
		var oid = e.data('oid');
		var $oId = $('#BAKUP_OLDDATA_'+ oid);
		var oldBakupData = $oId.val();

		var oldData = $('#EVALUATION').val();
		if (oldData) {
			if (oldBakupData) {
				oldData = oldData.replace(oldBakupData, val);
			} else {
				oldData = oldData+val;
			}
			//$('#EVALUATION').val(oldData);
		} else {
			//$('#EVALUATION').val(val);
		}
		$oId.val(val);
	}
	//通过考评细项id获取分数项
	function findScores(itemId) {
		var result;
		var controls = 'task.findItemScoreByItemId';
		ajax.daoCall({
			params: {itemId: itemId},
			controls: [controls]
		}, function(data) {
			result = data[controls].data;
		}, {
			async: false
		})
		return result;
	}
	//通过考评细项id和分值id获取备选项
	function findBakups(itemId, scoreId) {
		var result;
		var controls = 'task.findItemBakupByScoreId';
		ajax.daoCall({
			params: {itemId: itemId, scoreId: scoreId},
			controls: [controls]
		}, function(data) {
			result = data[controls].data;
		}, {
			async: false
		})
		return result;
	}

	//人工质检创建表格核心js
	function buildKPTable(categorys, items, isOpen, typeName) {
		if(!categorys || categorys.length <= 0){
			//如果备选项为空，则不显示该行
			return;
		}
		var tableId = 'tableId_'+typeName;
		var divId = 'divId_'+typeName;
		var buttonId = 'buttonId_'+typeName;

		var $button;
		var $div;
		if (isOpen) {
			$button = $('<button id="'+buttonId+'" type="button" class="btn btn-sm btn-link" title="展开"> 展开'+typeName+' <span class="glyphicon glyphicon-menu-up"></span>&nbsp;</button>');
			$button.find(".glyphicon").toggleClass("glyphicon glyphicon-menu-down glyphicon glyphicon-menu-up");
			$div = $('<div class="form-group" id="'+divId+'"></div>');
		} else {
			$button = $('<button id="'+buttonId+'" type="button" class="btn btn-sm btn-link" title="展开"> 展开'+typeName+' <span class="glyphicon glyphicon-menu-up"></span>&nbsp;</button>');
			$div = $('<div class="form-group" style="display:none" id="'+divId+'"></div>');
		}
		//生成表格
		var $table = $('<table class="table table-auto table-bordered table-condensed"></table>');
		var $thead = $('<thead>'
				+'<tr>'
				+'<th>考评项</th><th>考评子项</th><th>评分</th><th>备选项</th><th>备注</th>'
				+'</tr>'
				+'</thead>');
		var $tbody = $('<tbody id="'+tableId+'"></tbody>');
		$table.append($thead);
		$table.append($tbody);

		$div.append($table);
		$('#rgzj').append($button);
		$('#rgzj').append($div);
		//绑定收缩按钮事件
		$button.click(function() {
			$div.slideToggle('fast');
			$(this).find(".glyphicon").toggleClass("glyphicon glyphicon-menu-down glyphicon glyphicon-menu-up");
		})

		var defSelectScores;//默认选中的分值项
		var bakupSelected;//数据库里选中的备选项
		$.each(categorys, function(k1, v1) {
			$.each(v1, function(k2, v2) {
				var scores = findScores(v2.ITEM_ID);
				var bakupValues = items[v2.ITEM_NAME];

				var defScoreId;//用于记录第一次进入时候的分值id

				var $tr = $('<tr></tr>');
				if (k2 == 0) {
					var $categoryTd = $('<td rowspan="'+ v1.length +'"   data-maxval="'+ v2.CATEGORY_SCORE +'" >'+ v2.CATEGORY_NAME + '('+ v2.CATEGORY_SCORE +')' +'</td>');
					$tr.append($categoryTd);
				}
				$tr.append('<td>'+ v2.ITEM_NAME +'('+ v2.ITEM_SCORE +')'+
						'<input name="handmadeQcScores.CATEGORY_ID" type="hidden" value="'+ v2.CATEGORY_ID +'"/>'+
						'<input name="handmadeQcScores.ITEM_ID" type="hidden" value="'+ v2.ITEM_ID +'"/>'+
						'</td>');

				var $scoreTd = $('<td></td>');
				if (scores) {
					var $scoreSelect = $('<select data-item-id="'+v2.ITEM_ID+'"  data-item-name="'+v2.ITEM_NAME+'" name="handmadeQcScores.SCORE&'+v2.ITEM_ID+'" data-maxItemValue="'+v2.ITEM_SCORE+'" data-type="'+v2.TYPE+'" data-rules="required" class="form-control input-sm" value="'+v2.SCORE+'"></select>');
					$scoreSelect.append('<option value="">请选择</option>');
					var selectedTag;//用于过滤默认选中的，当数据库已经有值的时候不能去默认选中的值
					$.each(scores, function(k, v) {
						var defselectBakup = v.SELECT_OPTION;
						//数据库的记录进行选中
						if (parseFloat(v.SCORE) == parseFloat(v2.SCORE)) {
							selectedTag = v;
							defScoreId = v.ID;
							$scoreSelect.append('<option sid="'+v.ID+'" value="'+ v.SCORE +'" data-defselect-bakup="'+defselectBakup+'" selected="selected">'+ v.SCORE_TEXT +'</option>');
						} else if (!selectedTag && v.SELECT_DEFAULT == 'Y') {//默认选中分数
							defSelectScores = v;
							defScoreId = v.ID;
							$scoreSelect.append('<option sid="'+v.ID+'" value="'+ v.SCORE +'" data-defselect-bakup="'+defselectBakup+'" selected="selected">'+ v.SCORE_TEXT +'</option>');
						} else {
							$scoreSelect.append('<option sid="'+v.ID+'" value="'+ v.SCORE +'" data-defselect-bakup="'+defselectBakup+'">'+ v.SCORE_TEXT +'</option>');
						}
					})
					$scoreTd.append($scoreSelect);
				}
				$tr.append($scoreTd);

				var $bakupTd = $('<td id="td_'+v2.ITEM_ID+'"></td>');
				var bakups = findBakups(v2.ITEM_ID, defScoreId);//获取分值下面的备选项
				if (bakups) {
					var $bakupSelect = $('<select style="width:300px" name="handmadeQcScores.BAKUP&'+ v2.ITEM_ID +'" class="form-control input-sm" multiple="multiple"></select>');
					$bakupSelect.append('<option value="">请选择</option>');
					$.each(bakups, function(k, v) {
						for (var i in bakupValues) {
							if (bakupValues[i].SCORE_OPTION_ID == v.ID) {
								bakupSelected = v;
								$bakupSelect.append('<option selected value="'+ v.ID +'">'+ v.CONTENT +'</option>');
								return;
							}
						}
						if (bakupValues) {
							// 如果有备选项值，直接添加选项
							$bakupSelect.append('<option value="'+ v.ID +'">'+ v.CONTENT +'</option>');
						} else if(defSelectScores) {
							// 如果有默认选中的分数
							if (defSelectScores.SELECT_OPTION == 'Y') {
								// 如果默认选中，添加带selected属性的选项
								$bakupSelect.append('<option selected value="'+ v.ID +'">'+ v.CONTENT +'</option>');
							} else {
								// 否则添加普通选项
								$bakupSelect.append('<option value="'+ v.ID +'">'+ v.CONTENT +'</option>');
							}
						} else {
							// 其他情况直接添加选项
							$bakupSelect.append('<option value="'+ v.ID +'">'+ v.CONTENT +'</option>');
						}
					})
					$bakupTd.append($bakupSelect);
				}
				$tr.append($bakupTd);

				$tr.append('<td>'+
						'<input data-oid="'+v2.ITEM_ID+'" name="handmadeQcScores.TBAKUP&'+ v2.ITEM_ID +'" class="form-control input-sm" type="text" value="'+v2.BAKUP+'"/>'+
						'<input id="BAKUP_OLDDATA_'+v2.ITEM_ID+'" type="hidden" value="'+v2.BAKUP+'" />'+
						'</td>');
				$('#'+tableId).append($tr);
			})
		})
		changeTrColor();
	}


	function changeTrColor(color) {
		$('select[name^="handmadeQcScores.SCORE&"]').each(function(index) {
			var score = $(this).val();
			if (score && parseInt(score)<=0) {
				// $(this).parent('td').parent('tr').find('td').css('color', color || 'red');
				// $(this).parent('td').parent('tr').find('td').find('select').css('color', color || 'red');
				// $(this).parent('td').parent('tr').find('td').find('input').css('color', color || 'red');
			} else {
				$(this).parent('td').parent('tr').find('td').css('color', '');
				$(this).parent('td').parent('tr').find('td').find('select').css('color', '');
				$(this).parent('td').parent('tr').find('td').find('input').css('color', '');
			}
		})
	}

	//构造自动自检信息
	function buildAutoQC(data) {
		if (!data || data.length <= 0) {
			return;
		}
		var autoZTPJ = data[0].SYS_EVALUATION;
		var categorys = {};
		$.each(data, function(k, v) {
			if (!categorys[v.CATEGORY_NAME]) {
				categorys[v.CATEGORY_NAME] = [];
			}
			categorys[v.CATEGORY_NAME].push(v);
		})
		$.each(categorys, function(k1, v1) {
			$.each(v1, function(k2, v2) {
				var $tr = $('<tr></tr>');
				if (k2 == 0) {
					var $categoryTd = $('<td rowspan="'+ v1.length +'">'+ v2.CATEGORY_NAME +'</td>');
					$tr.append($categoryTd);
				}
				$tr.append('<td>'+ v2.ITEM_NAME +'</td>');
				$tr.append('<td>'+ (v2.SCORE?v2.SCORE:'无') +'</td>');
				$('#tbody-auto').append($tr);
			})
		})
		$('#autoZTPJ').text(autoZTPJ);
	}
	//构造聊天信息
	function buildChat(data) {
		var tempMydQc = {};
		var keywordQc = {};
		var records = {};
		var params = {};
		$('#mydList').empty();
		$.each(data, function(k, v) {
			if (!tempMydQc[v.NAME] && v.NAME) {
				buildMydList(v.KEYWORD_DIR_ID, v.NAME);
				tempMydQc[v.NAME] = '0';
			}

			if (!keywordQc[v.KEYWORD]) {
				keywordQc[v.KEYWORD] = {};
			}
			if (!keywordQc[v.KEYWORD][v.RECORD_DETAIL_ID]) {
				var content;
				if (records[v.RECORD_DETAIL_ID] && records[v.RECORD_DETAIL_ID].CONTENT) {
					content = records[v.RECORD_DETAIL_ID].CONTENT;
					var type = records[v.RECORD_DETAIL_ID].TYPE;
					if (type == '01' || type == '03' || !type) {
						try {
							var json = eval('('+content+')');
							if (json && json.url && json.name) {
								content = '<a href="'+json.url+'">'+json.name+'</a>';
							} else if (content.indexOf('mp3')!=-1||content.indexOf('wav')!=-1||(content.indexOf('.mp4')!=-1&&content.endsWith(".mp4"))) {
								content = '<audio controls title="音频" src="'+content+'" />';
							} else {
								content = qqWechatEmotionParser(content);
								content = replaceContent(content, v.KEYWORD, v.RECORD_DETAIL_ID, v.KEYWORD_DIR_ID, params);
							}
						} catch (e) {
							if (content.indexOf('mp3')!=-1||content.indexOf('wav')!=-1||(content.indexOf('.mp4')!=-1&&content.endsWith(".mp4"))) {
								content = '<audio controls title="音频" src="'+content+'" />';
							} else {
								content = qqWechatEmotionParser(content);
								content = replaceContent(content, v.KEYWORD, v.RECORD_DETAIL_ID, v.KEYWORD_DIR_ID, params);
							}
						}
					} else if (type == '02') {
						content = '<img ondblclick="openBigImg(\''+content+'\')" style="width: 100%; height: auto;max-width: 100%; display: block;" src="'+content+'"/>';
					} else if (type == '04') {
						var json = eval('('+content+')');
						content = '<a href="'+json.url+'">'+json.name+'</a>';
					} else if (type == '06') {
						content = '<video controls title="视频" src="'+content+'" style="max-width:100%;height:200px"/>';
					} else if (type == '07') {
                        content = '<audio controls title="音频" src="'+content+'" />';
                    }
				} else {
					content = v.CONTENT;
					var type = v.TYPE;
					if (type == '01' || type == '03' || !type) {
						try {
							var json = eval('('+content+')');
							if (json && json.url && json.name) {
								content = '<a href="'+json.url+'">'+json.name+'</a>';
							} else if (content.indexOf('mp3')!=-1||content.indexOf('wav')!=-1||(content.indexOf('.mp4')!=-1&&content.endsWith(".mp4"))) {
								content = '<audio controls title="音频" src="'+content+'" />';
							} else {
								content = qqWechatEmotionParser(content);
								content = replaceContent(content, v.KEYWORD, v.RECORD_DETAIL_ID, v.KEYWORD_DIR_ID, params);
							}
						} catch (e) {
							if (content.indexOf('mp3')!=-1||content.indexOf('wav')!=-1||(content.indexOf('.mp4')!=-1&&content.endsWith(".mp4"))) {
								content = '<audio controls title="音频" src="'+content+'" />';
							} else {
								content = qqWechatEmotionParser(content);
								content = replaceContent(content, v.KEYWORD, v.RECORD_DETAIL_ID, v.KEYWORD_DIR_ID, params);
							}
						}
					} else if (type == '02') {
						content = '<img ondblclick="openBigImg(\''+content+'\')" style="width: 100%; height: auto;max-width: 100%; display: block;" src="'+content+'"/>';
					} else if (type == '04') {
						var json = eval('('+content+')');
						content = '<a href="'+json.url+'">'+json.name+'</a>';
					} else if (type == '06') {
						content = '<video controls title="视频" src="'+content+'" style="max-width:100%;height:200px"/>';
					} else if (type == '07') {
                        content = '<audio controls title="音频" src="'+content+'" />';
                    }
				}
				if (!records[v.RECORD_DETAIL_ID]) {
					var record = {
						CONTENT: content,
						WITHDRAW:v.WITHDRAW,
						AGENT_ACC: v.AGENT_ACC,
						BEGIN_TIME: v.BEGIN_TIME,
						END_TIME: v.END_TIME,
						USER_TYPE: v.USER_TYPE,
						CUSTOMER_NAME: v.CUSTOMER_NAME,
						AGENT_NAME: v.AGENT_NAME,
						HANGUP_TYPE: v.HANGUP_TYPE
					};
					records[v.RECORD_DETAIL_ID] = record;
				} else {
					var record = records[v.RECORD_DETAIL_ID];
					record.CONTENT = content;
				}
			}
			keywordQc[v.KEYWORD][v.RECORD_DETAIL_ID] = '0';
		});

		var firstRecords;
		var userClient="";
		var red="";
		$.each(records, function(k, v) {
			if (!firstRecords) {
				firstRecords = records[k];
			}
			var content = v.CONTENT;
			var param = params[k];
			if (param) {
				$.each(param, function(k, v) {
					content = content.replace(k, v);
				})
			}

			var $name = v.AGENT_NAME;
			if(v.USER_TYPE == '02'||v.USER_TYPE == '2'){
				$name = v.CUSTOMER_NAME;
			}else if(v.USER_TYPE == '03'||v.USER_TYPE == '3'){
				$name = "【机器人】";
				try {
					var j = eval("(" + content + ")");
					if(j!=null){
						content = j.content;
					}
				} catch (e) {
					console.log("消息格式不为json，不解析")
				}
			}else if(v.USER_TYPE == '04'||v.USER_TYPE == '4'){
				$name = "【系统消息】";
			}
			var $div, $span, $span2, $contentDiv;
			$contentDiv = $('<div data-targetid="'+ k +'" class="chat-content">'+ content +'</div>');
			if (!v.USER_TYPE || v.USER_TYPE == '01'||v.USER_TYPE == '1'  || v.USER_TYPE == '03' ||v.USER_TYPE == '3'|| v.USER_TYPE == '04'||v.USER_TYPE == '4') {//客服

				var new_date = new Date(userClient); //新建一个日期对象
				var old_date = new Date(v.BEGIN_TIME); //设置过去的一个时间点，"yyyy-MM-dd HH:mm:ss"格式化日期
				var difftime = ( old_date- new_date)/1000; //计算时间差,并把毫秒转换成秒
				var minutes = parseInt(difftime%3600/60);
				if(minutes>=1){
					if(minutes>=1){
						$span = $('<span class="datetime" style="color: red;">'+v.BEGIN_TIME+'</span>');
					}else{
						$span = $('<span class="datetime">'+v.BEGIN_TIME+'</span>');
					}
				}else{
					$span = $('<span class="datetime">'+v.BEGIN_TIME+'</span>');
				}

				$div = $('<div class="record text-r clearfix"></div>');
				$span2 = $('<span class="datetime" style="display: block;">'+ $name +'</span>');
				$div.append('<div class="pull-right"></div>');
				$div.find('div.pull-right').append($span2);
				$div.find('div.pull-right').append($span);
				$div.find('div.pull-right').append($contentDiv);
				if(v.WITHDRAW==1){
					var $withdrawdiv = $('<div style="font-size: 12px;color: #959393;">已撤回</div>');
					$div.find('div.pull-right').append($withdrawdiv);
				}
				red=v.USER_TYPE;
			} else if (v.USER_TYPE == '02'||v.USER_TYPE == '2') {//客户
				red=v.USER_TYPE;
				userClient=v.BEGIN_TIME;
				$span = $('<span class="datetime">'+v.BEGIN_TIME+'</span>');
				$div = $('<div class="record text-l"></div>');
				$span2 = $('<span class="datetime" style="display: block;">'+ $name +'</span>');
				$div.append($span2);
				$div.append($span);
				$div.append($contentDiv);
			}
			$('#char-record').append($div);
		});
	}
	//构造标签信息
	function buildMydList(dirId, name) {
		$('#mydList').append('<span class="label label-success">'+
				'<a id="'+ dirId +'" href="javascript:void(0)" onclick="record.keywordHighlight(this, \''+dirId+'\')">'+name+'</a>'+
				'</span>');
	}

	function replaceContent(str, targetStr, recordDetailId, dirId, params) {
		tempNum = tempNum? tempNum: 0;
		var beginIndex = str.indexOf(targetStr);
		if (targetStr && beginIndex != -1) {
			var endIndex = targetStr.length;
			var repParam = '#['+tempNum+']';
			var repStr = "<span tag='"+dirId+"' style='color: red;' onmouseover='record.keywordTips(this)'>"+targetStr+"</span>";
			str = str.replace(targetStr, repParam);
			if (!params[recordDetailId]) {
				params[recordDetailId] = {};
			}
			params[recordDetailId][repParam] = repStr;
			tempNum++;
			return replaceContent(str, targetStr, recordDetailId, dirId, params, tempNum);
		} else {
			return str;
		}
	}

	
	//关键字鼠标移上去提示事件
	record.keywordTips = function(element) {
    	$this = $(element);
    	var dirId = $this.attr('tag');
    	if (dirId) {
    		var $dir = $('#'+ dirId);
    		var text = $dir.text();
        	layer.tips(text, $this, {
      		  tips: [1, '#0FA6D8'], //还可配置颜色
      		  time:1000
      		});
    	}
    }
	
	//点击标签事件
	var oldIndex;
	var oldDirId;
	var oldTop = 0;
	record.keywordHighlight = function(element, dirId) {
		if ($('span[tag="'+ oldDirId +'"]')[oldIndex]) {
			$($('span[tag="'+ oldDirId +'"]')[oldIndex]).removeClass('label label-success');
		}
		if (dirId != oldDirId) {
			oldDirId = dirId;
			oldIndex = 0;
			oldTop = 0;
		} else {
			oldIndex++;
		}
		var targetElement = $('span[tag="'+ oldDirId +'"]')[oldIndex];
		if (targetElement) {
			$(targetElement).addClass('label label-success');
			oldTop += $(targetElement).position().top;
			$("#char-record").animate({  
			     scrollTop: oldTop 
			}, {duration: 200,easing: "swing"});
		} else {
			oldIndex = -1;
			oldTop = 0;
			record.keywordHighlight(element, dirId);
		}
    }
	
	//查看质检记录
	record.viewHisZj = function(qcId, qcScoreId) {
		popup.openTab("${ctxPath}/pages/sessionTask/zjzxto2.jsp?qcId="+qcId+"&rgQcScoreId="+qcScoreId,'查看质检记录',{});
	}
	
	record.chatVideo = function(){	
		if(canCount>0){
			var shadeClose = '${param.shadeClose}';
			var qcId = $('#qcId').val();
			var popupJson = {type:2,title:"视频会话详情",maxmin:true,area:['1020px','592px'],offset:"10px",shadeClose:false,
			   end:function(){
				  $("body .shadow").remove();
			}};
			if(shadeClose=='false'){
				popupJson = $.extend({shade:0,shadeClose:shadeClose},popupJson);
			}
			popup.layerShow(popupJson,"${ctxPath}/pages/record/video-sync-play.jsp",{qcId:qcId});
		}else{
			layer.msg("视频文件未生成，请稍后再试！",{icon:2,time:2000});
		}			 		
	}
	
	record.checkBakup = function() {
		var result = true;
		$('select[name^="handmadeQcScores.BAKUP&"]').each(function() {
			var n = $(this);
			var vals = $(n).val();
			if (!vals || vals.length == 0) {
				$(n).tooltip("destroy"); 
 				var placement=$(n).data("placement");
 				if(placement===undefined||placement=='') placement="auto top";
 				$(n).tooltip({animation:false,title:'请选择',trigger:"focus",placement: placement});
 				$(n).tooltip("show");
 				result = false;
			} else {
				$(n).tooltip("destroy"); 
			}
		})
		return result;
	}
	record.checkPendingReason = function() {
		var result = true;
		var n = $('#PENDING_REASON');
		var vals = $(n).val();
		if (!vals || vals.length == 0) {
			$(n).tooltip("destroy"); 
				var placement=$(n).data("placement");
				if(placement===undefined||placement=='') placement="auto top";
				$(n).tooltip({animation:false,title:'请选择',trigger:"focus",placement: placement});
				$(n).tooltip("show");
				result = false;
		} else {
			$(n).tooltip("destroy"); 
		}
		return result;
	}
	
	record.asyncZtpj = function(e) {
		var value = $(e).val();
		$('#EVALUATION').val(value);
	}

	//考评标准下拉框渲染
	record.manualQcKpiIdRender = function (callInfoData) {
		if (!callInfoData) return

		var kpiId = $('#MANUAL_QC_KPI_ID').val();
		if (!kpiId){
			kpiId = callInfoData.MANUAL_QC_KPI_ID
		}
		var channelId = callInfoData.CHANNEL_ID;
		$('#MANUAL_QC_KPI_ID').data('mars', "indicators.indicatorsDict('02','02','"+channelId+"')");
		$('#MANUAL_QC_KPI_ID').render({success:function () {
			$('#MANUAL_QC_KPI_ID').val(kpiId);
			$('#MANUAL_QC_KPI_ID').data('old-value',kpiId);
		}});

		// 监听考评标准切换事件
		$('#MANUAL_QC_KPI_ID').on('change', function() {
			var kpiElement = $(this);
			var oldValue = kpiElement.data('old-value') || kpiElement.val(); // 获取旧值
			var newValue = kpiElement.val();

			if(!newValue) {
				layer.alert('请选择考评标准');
				kpiElement.val(oldValue); // 还原为旧值
				return;
			}

			// 弹出确认提示
			layer.confirm('切换考评标准将重新加载评分项，是否继续？', {
				btn: ['确定','取消']
			}, function(index){
				// 用户选择确定
				$('#rgQcScoreId').val(''); //清空旧的人工质检记录ID
				buildByManualQcKpiId(); //重新构建评分页面
				kpiElement.data('old-value', newValue); // 更新旧值
				layer.close(index);
				layer.msg('切换成功');
			}, function(index){
				// 用户选择取消
				kpiElement.val(oldValue); // 还原为旧值
				layer.close(index);
			});
		});

	}
	</script>
</body>