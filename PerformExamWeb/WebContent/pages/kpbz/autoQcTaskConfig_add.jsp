<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>新增自动质检任务</title>
	<style>
	    #serMenuContent {display:none;position: absolute;border:1px solid rgb(170,170,170);max-width: 220px; max-height: 350px;z-index:10;overflow: auto;background-color: #f4f4f4}
	    #zxSerMenuContent {display:none;position: absolute;border:1px solid rgb(170,170,170);max-width: 220px; max-height: 350px;z-index:10;overflow: auto;background-color: #f4f4f4}
	    .type{
	  	  display: none;
	    }
	    .AppealType{display: none}
	    .zxAppealType{display: none}
	    .taskData_0{
	  	  display: none;
	    }
	    .orderType_1{
	  	  display: none;
	    }
	    
    	.cascader-all {
			width: 100%!important;
		  }
	  	.cascader-input{
		  width: 100%!important;
		  height: 100%!important;
	  	}
		.cascader-model-input{
			width: 170px!important;
			height: 30px!important;
		}
	</style>
	 
</EasyTag:override>
<EasyTag:override name="content">
	<form id="easyformAdd" data-mars="autoQc.getRecord" style="margin-top: 10px;"  autocomplete="off" data-mars-prefix="autoQc." data-pk="${param.ID}">
		  <input type="hidden" id="autoQcId" name="autoQc.ID" class="form-control input-sm" value="${param.ID}">
		  <input type="hidden" id="taskData" name="autoQc.TASK_DATA" class="form-control input-sm" value="${param.taskData}">
		  <table class="table table-yewuAuto table-vzebra">
                 <tbody>
                     <tr>
                         <td class="required" width="40px">任务名称</td>
                         <!-- <td colspan="3">
                             <input type="text" name="autoQc.TASK_NAME" maxlength="99" class="form-control input-sm" data-rules="required">
                         </td> -->
                         <td  colspan="3">
                             <select name="autoQc.TASK_ID" id = "taskId"  class="form-control input-sm"  data-mars="comm.taskIdDict('${param.taskData}')" data-rules="required">
                             	<option value="" selected="selected">--请选择--</option>
                             </select>
                         </td>
                         
                     </tr>
                      <tr class="">
                         <td class="required">质检对象</td>
                         <td  colspan="3">
                             <select name="autoQc.ORDER_TYPE" id = "orderType"  class="form-control input-sm" onchange="changeTaskData(this.value)" data-rules="required">
                                     <option value="01" selected="selected">接入单</option>
                                     <option value="02" >咨询工单</option>
                                     <option value="03" >无工单录音</option>
                             </select>
                         </td>
                     </tr>
                     <tr class="orderType">
                         <td class="required">主体</td>
                         <td  colspan="3">
                             <select name="autoQc.ORG_CODE" id = "orgCode"  class="form-control input-sm" onchange="changeOrg(this.value)" data-cust-context-path="/neworder" data-cust-mars="comm.sysCode('ORG_CODE')" data-rules="required">
                             </select>
                         </td>
                     </tr>
                    <tr class="orderType_0">
                     	<td width="40px">诉求类型</td>
                     	<td>
	                     	 <select name="autoQc.SERVICE_REQUIRE_LEVEL" id="serviceRequireLevel" class="form-control input-sm" onchange="yewuAuto.changeAppealType(this)">
		                              <option value="">--请选择</option>
	                                  <option value="1">服务请求</option>
	                                  <option value="2">服务请求大类</option>
	                                  <option value="3">服务请求小类</option>
	                         </select>
                           </td>
                     </tr>
                     <tr class="AppealType orderType_0" >
                     		<td width="40px">服务请求</td>
	                        <td>
						    	<input type="hidden"  id ="serviceRequireCode" />
						    	<input type="hidden" name ="autoQc.SERVICE_REQUIRE_TYPE_CODE" id ="serviceRequireTypeCode" />
						    	<input type="hidden" name ="autoQc.SERVICE_REQUIRE_ITEM_CODE" id ="serviceRequireItemCode" />
						    	<input type="hidden" name ="autoQc.SERVICE_REQUIRE_ITEM_CODE2" id ="serviceRequireItemCode2" />
	                        	<input type="text" name ="autoQc.SERVICE_REQUIRE_NAME" data-rules="required" id="serviceRequireShow" readonly="readonly" class="form-control input-sm"  onclick="srTree.showMenu(this,'serMenuContent')">
	                        	  <div id="serMenuContent" class="menuContent">
						    		<input type="hidden" id = "serviceRequireTreeHidden" data-mars = "comm.serviceRequire" />
						         	<ul id="serviceRequireTree" class="ztree" style="margin-top:0; width:100%; height:auto;"></ul>
						    	</div>
	                        </td>
                     </tr>
                     <tr class="orderType_1">
                     	<td width="40px">类型</td>
                     	<td>
	                     	 <select name="autoQc.ZX_SERVICE_REQUIRE_LEVEL" id="zxServiceRequireLevel" class="form-control input-sm" onchange="yewuAuto.changeZxAppealType(this)">
		                              <option value="">--请选择</option>
	                                  <option value="1">咨询类型</option>
	                                  <option value="2">咨询类型大类</option>
	                                  <option value="3">咨询类型小类</option>
	                         </select>
                           </td>
                     </tr>
                
                    <!--  <tr class="zxAppealType orderType_1" > -->
                     <tr class="orderType_1">
                     		<td width="40px">咨询类型</td>
	                        <td>
						    	 <div id="zxServiceRequire">
									<div class="block">
									  <el-cascader 
									  	v-model="zxServiceRequire"
									    :options="options"
									    :props="props"
									    :show-all-levels="false"
									    :filterable="true"
									    collapse-tags
									    style="width:100%"
									    ></el-cascader>
									</div>
									</div>
	                        </td>
                     </tr>
                    
                     <tr>
                         <td class="required">质检类型</td>
                         <td>
                            <label class="radio-inline">
				                <input type="radio" value="1" onclick="changeType('1')" checked="checked" name="autoQc.QC_TYPE"> 通用质检
				             </label>
				             <label class="radio-inline">
				                <input type="radio" value="2"  onclick="changeType('2')" name="autoQc.QC_TYPE"> 专项质检
				             </label>
                         </td>
                     </tr>
                   <!--   <tr>
                         <td >队列</td>
                         <td>
                             <select name="autoQc.ORG_CODE" id = "orgCode"  class="form-control input-sm" >
                             	<option value="" selected="selected">--请选择--</option>
                             </select>
                         </td>
                     </tr> -->
                     <tr class="type type0 type1">
                         <td >技能组</td>
                         <td>
                             <select name="autoQc.SKILL_CODE" id = "skillCode"  class="form-control input-sm" data-mars="comm.skilldDict('${param.taskData}')">
                             	<option value="" selected="selected">--请选择--</option>
                             </select>
                         </td>
                     </tr>
                     <tr class="type type1">
                         <td >渠道</td>
                         <td>
                              <select class="form-control" id="CHANNEL_ID" name="autoQc.CHANNEL_CODE" data-mars="Grouping.channe"  >	           		              	
	           		          </select>
                         </td>
                     </tr>
                     <tr class="type type0 type1">
                         <td >创建时间</td>
                         <td>
                     		 <div class="input-group input-group-sm">
								 <input autocomplete="off" type="text" name="autoQc.START_TIME" id="startDate" class="form-control input-sm" style="width:138px" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',autoPickDate:true})">
								 <span class="input-group-addon">-</span>	
							 	 <input autocomplete="off" type="text" name="autoQc.END_TIME" id="endDate" class="form-control input-sm" style="width:138px" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',autoPickDate:true})">									  
							   </div>
							    
                         </td>
                     </tr>
                     <tr>
                         <td class="required">是否启用</td>
                         <td>
                             <label class="radio-inline">
				                <input type="radio" value="Y" checked="checked"  name="autoQc.IS_ENABLE"> 启用
				             </label>
				             <label class="radio-inline">
				                <input type="radio" value="N"   name="autoQc.IS_ENABLE"> 禁用
				             </label>
                         </td>
                     </tr>
                     
                     <tr>
                         <td>备注</td>
                         <td colspan="3">
                            <textarea class="form-control input-sm" maxlength="200" name="autoQc.BAKUP" rows="5"></textarea>
                         </td>
                     </tr>
                  
                 </tbody>
	      </table>
	      
	          
		  <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" id="submit-form" onclick="yewuAuto.ajaxSubmitForm()">保存</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose();">关闭</button>
		  </div>
		  
		 <!--  <div id="serMenuContent" class="menuContent">
				<input type="hidden" id = "serviceRequireTreeHidden" data-mars = "comm.serviceRequire" />
				<ul id="serviceRequireTree" class="ztree" style="margin-top:0; width:100%; height:auto;"></ul>
		 </div> -->
		  
	</form>				
</EasyTag:override>

<EasyTag:override name="script">
	<link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css" />
	<script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
	<script type ="text/javascript" src = "/neworder/static/js/common.js" ></script>
		<script type="text/javascript" src="/neworder/static/js/comm/commOrder.js"></script>
		<script type="text/javascript" src="/neworder/static/js/comm/commServiceTree.js"></script>
	<link type="text/css" rel="stylesheet" href="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.css" />
	<script type="text/javascript" src="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.js"></script>
	 <script src="/online/static/plugins/layui/layui.all.js" type="text/javascript"></script>
	<script src="/online/static/js/cus.verify.js" type="text/javascript"></script>
	
	<link rel="stylesheet" href="/neworder/static/css/element-ui.css">
		<script type="text/javascript" src="/neworder/static/js/vue.min.js"></script>
		<script src="/neworder/static/js/elementui/element-ui.js"></script>

	
	<script type="text/javascript">
	srTree.sr_async.otherParam = ["orgCode",function() { if($("#orgCode").val().length>1){return ""}else{ return orderComm.queryOrgCode($("#orgCode").val())}},"showLevel",function() { return showLevel }];
	
	jQuery.namespace("yewuAuto");
	requreLib.setplugs("wdate");
	yewuAuto.multiSetting = {
		    	 buttonWidth: '300px',
				 allSelectedText: "全部",
				 nonSelectedText:"--请选择--",
				 nSelectedText:"个被选中",
				 selectAllNumber:false,
				 maxHeight:250,
				 includeSelectAllOption: true,
				 selectAllText:'全选',
				 enableFiltering: true,
				 marginTop: '-285px'
			};
	var Main = new Vue({
		el: '#zxServiceRequire',
	    data() {
		      return {
		        props: { multiple: true ,emitPath:false},
		        options: [],
		        zxServiceRequire: []
		      };
		  },
          methods: {
              setZxServiceRequire(index){
                  ajax.remoteCall("/PerformExamWeb/servlet/comm?action=GetConsultationType2",{index:index},result => { 
                	  debugger;
                      this.options = result.data;
                  })
              
              }
          },
          created(index) {
              this.setZxServiceRequire(index)
          }
	  })
		//Main.zxServiceRequire 值
		//Main.setZxServiceRequire 
	
		$(function(){
			init()
		});
		function init(){
			$("#serMenuContent").render({data:{"orgCode":""},success:function(result){
				srTree.nodes = result["comm.serviceRequire"].data;
				$.fn.zTree.init($("#serviceRequireTree"), srTree.setting,srTree.nodes);
				ListSrCodeTree=$.fn.zTree.getZTreeObj("serviceRequireTree");
				$("#serviceRequireTreeHidden").val('');
	    	}});
			$("#easyformAdd").render({
				success:function(result){
					if(result["autoQc.getRecord"].data!=null){
						changeType(result["autoQc.getRecord"].data.QC_TYPE)
					}
					
					changeOrg('','Y');
					
					$('#orgCode').attr("multiple","multiple");
		   			$('#orgCode').val("");
		   			$('#orgCode').multiselect( yewuAuto.multiSetting);
		   			if(result["autoQc.getRecord"].data.ORG_CODE&&result["autoQc.getRecord"].data.ORG_CODE!=""){
						var orgCode=result["autoQc.getRecord"].data.ORG_CODE;
						var orgCodes=orgCode.split(",");
						$('#orgCode').multiselect('select',orgCodes);
					}
		   			
		   			$('#CHANNEL_ID').attr("multiple","multiple");
		   			$('#CHANNEL_ID').val("");
		   			$('#CHANNEL_ID').multiselect( yewuAuto.multiSetting);
		   			if(result["autoQc.getRecord"].data.CHANNEL_CODE&&result["autoQc.getRecord"].data.CHANNEL_CODE!=""){
						var channelId=result["autoQc.getRecord"].data.CHANNEL_CODE;
						var channelIdS=channelId.split(",");
						$('#CHANNEL_ID').multiselect('select',channelIdS);
					}
		   			
		   			$("#orderType").change();
		   			if($("#orderType").val()=="01")	$("#serviceRequireLevel").change();
		   			if($("#orderType").val()=="02")	$("#zxServiceRequireLevel").change();
					if(result["autoQc.getRecord"].data.SERVICE_REQUIRE_LEVEL&&result["autoQc.getRecord"].data.SERVICE_REQUIRE_LEVEL=="1"){
						$("#serviceRequireCode").val(result["autoQc.getRecord"].data.SERVICE_REQUIRE_TYPE_CODE);
						$("#serviceRequireShow").val(result["autoQc.getRecord"].data.SERVICE_REQUIRE_NAME);
					}
					if(result["autoQc.getRecord"].data.SERVICE_REQUIRE_LEVEL&&result["autoQc.getRecord"].data.SERVICE_REQUIRE_LEVEL=="2"){
						$("#serviceRequireCode").val(result["autoQc.getRecord"].data.SERVICE_REQUIRE_ITEM_CODE);
						$("#serviceRequireShow").val(result["autoQc.getRecord"].data.SERVICE_REQUIRE_NAME);
					}
					if(result["autoQc.getRecord"].data.SERVICE_REQUIRE_LEVEL&&result["autoQc.getRecord"].data.SERVICE_REQUIRE_LEVEL=="3"){
						$("#serviceRequireCode").val(result["autoQc.getRecord"].data.SERVICE_REQUIRE_ITEM_CODE2);
						$("#serviceRequireShow").val(result["autoQc.getRecord"].data.SERVICE_REQUIRE_NAME);
					}
					//咨询工单
					if(result["autoQc.getRecord"].data.ZX_SERVICE_REQUIRE_LEVEL&&result["autoQc.getRecord"].data.ZX_SERVICE_REQUIRE_LEVEL=="1"){
						Main.zxServiceRequire=result["autoQc.getRecord"].data.ZX_SERVICE_REQUIRE_TYPE_CODE.split(",");
					}
					if(result["autoQc.getRecord"].data.ZX_SERVICE_REQUIRE_LEVEL&&result["autoQc.getRecord"].data.ZX_SERVICE_REQUIRE_LEVEL=="2"){
						Main.zxServiceRequire=result["autoQc.getRecord"].data.ZX_SERVICE_REQUIRE_ITEM_CODE.split(",");
					}
					if(result["autoQc.getRecord"].data.ZX_SERVICE_REQUIRE_LEVEL&&result["autoQc.getRecord"].data.ZX_SERVICE_REQUIRE_LEVEL=="3"){
						Main.zxServiceRequire=result["autoQc.getRecord"].data.ZX_SERVICE_REQUIRE_ITEM_CODE2.split(",");
					}
				}
			
			});
		}	
	
	    function changeOrg(obj,first){
	        var orgCode=obj;
	    	var autoId = $("#autoQcId").val();
	    	if(autoId){
	    		if(!first){
	    			$("#serviceRequireShow").val('');
	    		}
	    	}else{
	    		$("#serviceRequireShow").val('');
	    	}
	    	if($('#orgCode').val()&&$('#orgCode').val().length>1){//主体多选
	    		orgCode=""
	    	}
	    	//$("#branchCode").val('');
	    	
	    	$("#serMenuContent").render({data:{"orgCode":orgCode},success:function(result){
    			srTree.nodes = result["comm.serviceRequire"].data;
    			$.fn.zTree.init($("#serviceRequireTree"), srTree.setting,srTree.nodes);
    			$("#serviceRequireTreeHidden").val('');
	    	}});
	    	//$("#branchCode").render({data:{"orgCode":obj}});
	    	
	    }
	    function changeType(type){
	    	if(type=="2"){
	    		$(".type").hide();
	    		$(".type"+type).show();
	    	}else{
	    		$(".type").hide();
	    	}
	    		
	    }
	    function toggleMore(){
			var btn = $("#moreBtn").find(".glyphicon");
			$(".moreSearch").slideToggle('fast');
			btn.toggleClass("glyphicon glyphicon glyphicon-menu-up")
		}
		/* srTree.sr_async.otherParam = ["orgCode",function() { return $("#orgCode").val() }];
	    srTree.setting = {
	    		data : srTree.sr_data,
	    		callback :{
	    			onClick : zTreeSrOnclick
	    		},
	     		async : srTree.sr_async
	    }
		//服务请求的点击事件
		function zTreeSrOnclick(event,treeId,treeNode){
			var level = treeNode.level;
			if(level == 1){ 
				var ztreeObj = $.fn.zTree.getZTreeObj(treeId); 
				ztreeObj.expandNode(treeNode);
		    }else{
			    $("#serviceRequireLevel").val(level+1);
			    if(level == 0){
			    	$("#serviceRequireTypeCode").val(treeNode.id);
			    	$("#serviceRequireItemCode").val('');
			    	$("#serviceRequireShow").val(treeNode.name);
			    }else{
			    	$("#serviceRequireTypeCode").val('');
			    	$("#serviceRequireItemCode").val(treeNode.id);
			    	$("#serviceRequireShow").val(treeNode.name);
			    }
		    	srTree.hideMenu('serMenuContent');
		    }
		}*/
	    
	    yewuAuto.ajaxSubmitForm = function(){
			if($("#orderType").val() != '03') {
				if (!$("#orgCode").val() || $("#orgCode").val().length < 1) {
					layer.alert("主体不能为空");
					console.log($("#orgCode").val());
					return;
				}
			}
			 if(form.validate("#easyformAdd")){
				yewuAuto.insertData(); 
			 };
		}
	    yewuAuto.insertData = function(){
	    	if($("#startDate").val()!=""||$("#endDate").val()!=""){
	    		if($("#startDate").val()==""||$("#endDate").val()==""){
	    			layer.alert("创建时间请补充完整")
	    			return;
	    		}
	    	}
	    	 var data = form.getJSONObject("#easyformAdd");
	    	
	    	 var taskIdcheckText=$("#taskId").find("option:selected").text(); 
	    	 data['autoQc.TASK_NAME'] = taskIdcheckText;
	    	 //var checkText=$("#orgCode").find("option:selected").text(); 
	    	 //data['autoQc.ORG_NAME'] = checkText;
	    	 var orgCode="";
			 var orgName="";
			 if($("#orgCode").val()&&$("#orgCode").val()!=""){
				
				$('#orgCode option:selected').each(function(e,key){
					orgCode+=orgCode==""?""+key.value+"":","+key.value+""
					orgName+=orgName==""?""+key.text+"":","+key.text+""
				})
			 }
			 data["autoQc.ORG_CODE"]=orgCode;
			 data["autoQc.ORG_NAME"]=orgName;
			 
			 var channelCode="";
			 if($("#CHANNEL_ID").val()&&$("#CHANNEL_ID").val()!=""){
				$('#CHANNEL_ID option:selected').each(function(e,key){
					channelCode+=channelCode==""?""+key.value+"":","+key.value+""
				})
			 }
			 data["autoQc.CHANNEL_CODE"]=channelCode;
			if($("#orderType").val()=="02"){
				$("#serviceRequireLevel").val("");
				$("#serviceRequireShow").val("");
				$("#serviceRequireTreeHidden").val("");
			}
			 if($("#serviceRequireShow").val()==""||$("#orderType").val()=="02"){//咨询单或者未选择类型
				 data['autoQc.SERVICE_REQUIRE_TYPE_CODE'] ="";
				 data['autoQc.SERVICE_REQUIRE_ITEM_CODE'] ="";
				 data['autoQc.SERVICE_REQUIRE_ITEM_CODE2'] ="";
			 }else if($("#serviceRequireShow").val()!=""&&$("#serviceRequireCode").val()==""){
				 //输入框有内容 却无法获取code值 判断为非法输入
				 layer.alert("非法输入，请勾选服务请求")
    			 return;
			 }else if($("#serviceRequireLevel").val()=="1"){
				 data['autoQc.SERVICE_REQUIRE_TYPE_CODE'] =$("#serviceRequireCode").val();
				 data['autoQc.SERVICE_REQUIRE_ITEM_CODE'] ="";
				 data['autoQc.SERVICE_REQUIRE_ITEM_CODE2'] ="";
			 }else if($("#serviceRequireLevel").val()=="2"){
				 data['autoQc.SERVICE_REQUIRE_TYPE_CODE'] ="";
				 data['autoQc.SERVICE_REQUIRE_ITEM_CODE'] =$("#serviceRequireCode").val();
				 data['autoQc.SERVICE_REQUIRE_ITEM_CODE2'] ="";
			 }else if($("#serviceRequireLevel").val()=="3"){
				 data['autoQc.SERVICE_REQUIRE_TYPE_CODE'] ="";
				 data['autoQc.SERVICE_REQUIRE_ITEM_CODE'] ="";
				 data['autoQc.SERVICE_REQUIRE_ITEM_CODE2'] =$("#serviceRequireCode").val();
			 }
			 //咨询工单
			 if(Main.zxServiceRequire.length==0||$("#orderType").val()=="01"){//接入单或者未选择类型
				 data['autoQc.ZX_SERVICE_REQUIRE_TYPE_CODE'] ="";
				 data['autoQc.ZX_SERVICE_REQUIRE_ITEM_CODE'] ="";
				 data['autoQc.ZX_SERVICE_REQUIRE_ITEM_CODE2'] ="";
			 }else if($("#zxServiceRequireLevel").val()=="1"){
				 data['autoQc.ZX_SERVICE_REQUIRE_TYPE_CODE'] =Main.zxServiceRequire.join(",");;
				 data['autoQc.ZX_SERVICE_REQUIRE_ITEM_CODE'] ="";
				 data['autoQc.ZX_SERVICE_REQUIRE_ITEM_CODE2'] ="";
			 }else if($("#zxServiceRequireLevel").val()=="2"){
				 data['autoQc.ZX_SERVICE_REQUIRE_TYPE_CODE'] ="";
				 data['autoQc.ZX_SERVICE_REQUIRE_ITEM_CODE'] =Main.zxServiceRequire.join(",");;
				 data['autoQc.ZX_SERVICE_REQUIRE_ITEM_CODE2'] ="";
			 }else if($("#zxServiceRequireLevel").val()=="3"){
				 data['autoQc.ZX_SERVICE_REQUIRE_TYPE_CODE'] ="";
				 data['autoQc.ZX_SERVICE_REQUIRE_ITEM_CODE'] ="";
				 data['autoQc.ZX_SERVICE_REQUIRE_ITEM_CODE2'] =Main.zxServiceRequire.join(",");;
			 }
			 
    		ajax.remoteCall("${ctxPath}/servlet/autoQc?action=zhiJianAdd",data,function(result) { 
    			if(result.state == 1){
    				layer.msg(result.msg,{icon: 1,time:1200},function(){
    					parent.kpbzAuto.loutDate();
    					popup.layerClose();
    				});
    			}else{
    			      layer.alert(result.msg);
    			}
    		  }
    		);
	    	}
	    yewuAuto.changeAppealType=function(ths){
	    	$("#serviceRequireCode").val('');
	    	$("#serviceRequireShow").val('');
	    	var type=$(ths).val();
	    	$(".AppealType").hide();
	    	if(type!=""){
		    	$(".AppealType").show();
	    	} 
	    	if(type==1){
	    		$("#serMenuContent").render({data:{"orgCode":""},success:function(result){
	    			var data= result["comm.serviceRequire"].data;//isParent改成false 无需选择 服务请求大类 
	    				$.each(data,function(index,items){
	    					items.isParent=false
	    				});
					srTree.nodes = data;
					$.fn.zTree.init($("#serviceRequireTree"), srTree.setting,srTree.nodes);
					$("#serviceRequireTreeHidden").val('');
		    	}});
	    	}else if(type==2){
	    		showLevel=0
	    		$("#serMenuContent").render({data:{"orgCode":""},success:function(result){
					srTree.nodes = result["comm.serviceRequire"].data;
					$.fn.zTree.init($("#serviceRequireTree"), srTree.setting,srTree.nodes);
					$("#serviceRequireTreeHidden").val('');
		    	}});
	    	}else if(type==3){
	    		showLevel=1
	    		$("#serMenuContent").render({data:{"orgCode":""},success:function(result){
					srTree.nodes = result["comm.serviceRequire"].data;
					$.fn.zTree.init($("#serviceRequireTree"), srTree.setting,srTree.nodes);
					$("#serviceRequireTreeHidden").val('');
		    	}});
	    	}
    	}
	    
	    function changeTaskData(val){
	    	if(val=='02'){//咨询工单
	    		$(".orderType_0").css("display","none");
		    	$(".orderType_1").css("display","table-row");
		    	$(".orderType").css("display","table-row");
	    	}else if(val=='01'){//接入单
	    		$(".orderType_0").css("display","table-row");
		    	$(".orderType_1").css("display","none");
		    	$(".orderType").css("display","table-row");
	    	}else{//无工单
	    		$(".orderType_0").css("display","none");
		    	$(".orderType_1").css("display","none");
		    	$(".orderType").css("display","none");

	    	}
	    }
	    
	  
	    yewuAuto.changeZxAppealType=function(ths){
	    	Main.setZxServiceRequire(ths.value);
    	}
	    
	    
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
