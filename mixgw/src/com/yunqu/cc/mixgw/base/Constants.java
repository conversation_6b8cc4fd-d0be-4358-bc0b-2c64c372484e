package com.yunqu.cc.mixgw.base;

import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.ParamUtil;

/**
 * 常量
 */
public class Constants {

	public final static String DS_NAME = "yw-ds";     //默认数据源名称

	public final static String MARS_DS_NAME = "mars-ds";     //Mars数据源

	public final static String APP_NAME = "mixgw";     //应用

	/**
	 * 发送满意度公众号类型  -- 美的服务号
	 */
	public final static String MIDEA_TYPE = "01";
	/**
	 * 发送满意度公众号类型  -- COLMO公众号
	 */
	public final static String COLMO_TYPE = "02";

	public final static String QIYE_APPSECRET = ConfigUtil.getString(APP_NAME, "WORK_WEIXIN_CORPSECRET","sY3WfCLO-XIc2MNHbHAs5SnkFt7r3HtJzA-rOJg-ox0");     //企业微信corpsecret
	public final static String QIYE_CORPID = ConfigUtil.getString(APP_NAME, "WORK_WEIXIN_CORPID","wwc56202ba0a29b2fb");     //企业微信corpid
	public final static String QIYE_AGENT_ID = ConfigUtil.getString("wecom","WECOM_AGENT_ID","1000005");// 企微侧边栏应用id



	//电商用户访问轨迹查询接口的URL
	public static final String ELEC_BROWSED_ITME_URL = ConfigUtil.getString(APP_NAME, "ELEC_BROWSED_ITME_URL");

	//电商用户访问轨迹查询接口的APPID
	public static final Object ELEC_APPID = ConfigUtil.getString(APP_NAME, "ELEC_APPID");

	//电商用户访问轨迹查询接口的APPKEY
	public static final String ELEC_APPKEY = ConfigUtil.getString(APP_NAME, "ELEC_APPKEY");

	//电商用户访问轨迹查询接口的NOTICEID
	public static final Object ELE_NOTICEID = ConfigUtil.getString(APP_NAME, "ELEC_NOTICEID");;

	//电商用户访问轨迹查询接口的SOURCE值
	public static final Object ELEC_SOURCE = ConfigUtil.getString(APP_NAME, "ELEC_SOURCE");

	//电商用户访问轨迹查询接口的VERSION值
	public static final Object ELEC_VERSION = ConfigUtil.getString(APP_NAME, "ELEC_VERSION");

	//洗悦家访问产品列表的url
	public static final String WEI_XIN_CS_PRODUCT_URL = ConfigUtil.getString(APP_NAME, "WEI_XIN_CS_PRODUCT_URL");;

	//优惠券获取接口
	public static final String WEI_XIN_CS_GETCARDCODE_URL = ConfigUtil.getString(APP_NAME, "WEI_XIN_CS_GETCARDCODE_URL");

	//优惠券获取接口
	public static final String WEI_XIN_URL = ConfigUtil.getString(APP_NAME, "WEI_XIN_URL");;
	//优惠券获取接口
	public static final String WX_XYJ_URL = ConfigUtil.getString(APP_NAME, "WX_XYJ_URL");;

	/**
	 * 接口请求地址
	 */
	public static final String WX_CS_NEW_BASE_URL = ConfigUtil.getString(APP_NAME, "WX_CS_CARD_NEW_BASE_URL","http://weixincs.midea.com/ccss-ipms-cis-rpc/uat");

	//洗悦家处理优惠券等接口的前缀
	public static final String WEI_XIN_CS_CARD_BASE_URL = ConfigUtil.getString(APP_NAME, "WEI_XIN_CS_CARD_BASE_URL");

	//根据服务号查询洗悦家订单详情接口地址
	public static final String WEI_XIN_CS_ORDER_URL = ConfigUtil.getString(APP_NAME, "WEI_XIN_CS_ORDER_URL");

	//ECM订单查询的URL地址
	public static final String ECM_ORDER_URL = ConfigUtil.getString(APP_NAME, "ECM_ORDER_URL");
	//美云销地址旧
	public static final String ECM_URL = ConfigUtil.getString(APP_NAME, "ECM_URL","https://mcsp-sit.midea.com");//http://ec.midea.com/uat/open/api
	//美云销新地址
	public static final String ECM_NEW_URL = ConfigUtil.getString(APP_NAME, "ECM_NEW_URL","https://mcsp-api-sit.midea.com");//https://mcsp-api-sit.midea.com

	/**
	 * 物流查询地址
	 */
	public static final String LOGISTICS_URL = ConfigUtil.getString(APP_NAME, "LOGISTICS_URL", "");
	/**
	 * 物流鉴权clientId
	 */
	public static final String LOGISTICS_CLIENTID = ConfigUtil.getString(APP_NAME, "LOGISTICS_CLIENTID", "");
	/**
	 * 物流鉴权clientSecret
	 */
	public static final String LOGISTICS_CLIENTSECRET = ConfigUtil.getString(APP_NAME, "LOGISTICS_CLIENTSECRET", "");
	/**
	 * 物流鉴权accessToken
	 */
	public static final String LOGISTICS_ACTOKEN = ConfigUtil.getString(APP_NAME, "LOGISTICS_ACTOKEN", "");

	/**LDAP的IP地址*/
	public static final String LDAP_APP_ID = ConfigUtil.getString(APP_NAME,"LDAP_APP_ID");

	/**LDAP的端口号*/
	public static final String LDAP_PORT = ConfigUtil.getString(APP_NAME,"LDAP_PORT");;

	/**LDAP的应用id*/
	public static final String LDAP_IP = ConfigUtil.getString(APP_NAME,"LDAP_IP");;

	/**LDAP的应用密码*/
	public static final String LDAP_APP_PWD = ConfigUtil.getString(APP_NAME,"LDAP_APP_PWD");

	/**美的会员等级查询URL*/
	public static final String MIDEA_VIP_URL = ConfigUtil.getString(APP_NAME,"MIDEA_VIP_URL","http://cmms2.midea.com/ccrm2-core/userApi/getVipUserInfo");

	/**美的会员等级查询URL2*/
	public static final String MIDEA_VIP_URL2 = ConfigUtil.getString(APP_NAME,"MIDEA_VIP_URL2","");

	/**美的会员等级查询appKey*/
	public static final String MIDEA_VIP_APPKEY = ConfigUtil.getString(APP_NAME,"MIDEA_VIP_APPKEY","c8c35003cc4c408581043baad45bce5b");

	/**美的会员等级查询secret*/
	public static final String MIDEA_VIP_SECRET = ConfigUtil.getString(APP_NAME,"MIDEA_VIP_SECRET","0dc6fe93a8154fcaab629353ab800bb4");

	/**美的app消息推送appKey*/
	public static final String MIDEA_APP_APPKEY = ConfigUtil.getString(APP_NAME,"MIDEA_VIP_SECRET","");
	/**美的app消息推送secret*/
	public static final String MIDEA_APP_SECRET = ConfigUtil.getString(APP_NAME,"MIDEA_VIP_SECRET","");

	/**短链接查询appKey*/
	public static final String CMSMS_APPKEY = ConfigUtil.getString(APP_NAME,"CMSMS_APPKEY","c8c35003cc4c408581043baad45bce5b");
	/**短链接查询secret*/
	public static final String CMSMS_SECRET = ConfigUtil.getString(APP_NAME,"CMSMS_SECRET","0dc6fe93a8154fcaab629353ab800bb4");

	/**打卡记录**/
	public static final String WEI_XIN_EMP_TIMECARD_URL = ConfigUtil.getString(APP_NAME,"WEI_XIN_EMP_TIMECARD_URL","");
	/**接口密匙**/
	public static final String SIGNATURE_KEY = ConfigUtil.getString(APP_NAME,"SIGNATURE_KEY","");
	/**打卡记录接口LIBNAME**/
	public static final String LIBNAME = ConfigUtil.getString(APP_NAME,"LIBNAME","midea");
	/**打卡记录接口FUNID**/
	public static final String SDFUNID = ConfigUtil.getString(APP_NAME,"SDFUNID","100000");
	/**打卡记录接口FUNID**/
	public static final String HFFUNID = ConfigUtil.getString(APP_NAME,"HFFUNID","100000");

	public static final String ECM_APPKEY = ConfigUtil.getString(APP_NAME,"ECM_APPKEY","203767886");

	public static final String ECM_APPSECRET = ConfigUtil.getString(APP_NAME,"ECM_APPSECRET","ldxmjqnls4gt0qbig9atorwki1lde8mr");

	public static final String ECM_HOST = ConfigUtil.getString(APP_NAME,"ECM_HOST","ec.midea.com");

	public static final String ECM_HOST_URL = ConfigUtil.getString(APP_NAME,"ECM_HOST_URL","/uat/open");

	//电商订单查询接口的URL
	public static final String ELEC_GET_DEAL_DETAIL_URL = ConfigUtil.getString(APP_NAME, "ELEC_GET_DEAL_DETAIL_URL","http://**************/next/mfop_deal/getdealdetail");
	//电商优惠券查询接口的URLgetusercouponinfo
	public static final String ELEC_GET_COUPON_INFO_URL = ConfigUtil.getString(APP_NAME, "ELEC_GET_COUPON_INFO_URL","http://**************/next/outer_assets/getusercouponinfo");
	//商品信息接口 获取商品包装信息，类目信息，基本信息 活动信息 优惠券信息
	public static final String ELEC_COMPACT_DETAIL_URL = ConfigUtil.getString(APP_NAME, "ELEC_COMPACT_DETAIL_URL","http://sitm.midea.cn/next/detail_o/compactdetail");
	//商城链接获取
	public static final String ELEC_GEN_SHORT_SCHEME = ConfigUtil.getString(APP_NAME, "ELEC_GEN_SHORT_SCHEME","http://**************/next/mfop_deal/genshortscheme");
	//商城链接获取(2024年12月智慧家版本)
	public static final String LONG_TO_SHORT = ConfigUtil.getString(APP_NAME, "LONG_TO_SHORT","/api/cms_api/cc-web/mcsp/content/external/shortLink/longToShort4MiniApp.do");
	//商城链接获取前先通过坐席手机号获取对应的合伙人sm
	public static final String GET_SM_BY_PHONE = ConfigUtil.getString(APP_NAME, "GET_SM_BY_PHONE","/api/cms_api/mtc-seller-backend/seller/getSellerByMobile");
	//线下购买记录接口
	public static final String CCS_OFFLINE_URL = ConfigUtil.getString(APP_NAME, "CCS_OFFLINE_URL","http://10.16.80.163:8001/ccs-web/ext-api/getBillInfoByCustomMobile");
	//企业微信url
	public static final String WORK_WEIXIN_URL = ConfigUtil.getString(APP_NAME, "WORK_WEIXIN_URL","http://172.16.10.224:50002");;
	//短链接生成url
	public static final String GENERATE_SMS_URL = ConfigUtil.getString(APP_NAME, "GENERATE_SMS_URL","http://10.16.85.47/cmsms-new/extShortUrlApi/generateSmsUrl");
	/**
	 * 4A接口
	 */
	public static final String MEDIA_4A = ConfigUtil.getString(APP_NAME, "MEDIA_4A","https://c4aapisit.midea.com/api/user/search");
	/**
	 * 4A接口appId
	 */
	public static final String MEDIA_4AAPPID = ConfigUtil.getString(APP_NAME, "MEDIA_4AAPPID","10064");
	/**
	 * 4A接口appKey
	 */
	public static final String MEDIA_4AAPPKEY = ConfigUtil.getString(APP_NAME, "MEDIA_4AAPPKEY","secff708128ea77d65152ed81f2595e2cb2");
	/**
	 * 调用4A接口的渠道
	 */
	public static final String MEDIA_4ACHANNEL = ConfigUtil.getString(APP_NAME, "MEDIA_4ACHANNEL","gh_58f8a50fddbb");

	/**
	 * 是否关注
	 */
	public static final String WCP_IS_USER_SUBSCRIBED_URL = ConfigUtil.getString(APP_NAME, "WCP_IS_USER_SUBSCRIBED_URL","https://weixincs.midea.com/wcp/sic/api/WCP_MP_IS_USER_SUBSCRIBED/");
	/**
	 * 调用中控发送消息
	 */
	public static final String WCP_MP_SEND_TEMPLATE_URL = ConfigUtil.getString(APP_NAME, "WCP_MP_SEND_TEMPLATE_URL","https://weixincs.midea.com/wcp/sic/api/WCP_MP_SEND_TEMPLATE/");

	/**
	 * 根据unionid获取用户信息url (包含openId) 测试环境：https://weixincs.midea.com/wcp/sic/api/WCP_MP_GET_USER_BY_UNIONID/
	 */
	public static final String WCP_GET_USER_BY_UNIONID_URL = ConfigUtil.getString(APP_NAME, "WCP_GET_USER_BY_UNIONID_URL","https://weixin.midea.com/alicloud/wcp/sic/api/WCP_MP_GET_USER_BY_UNIONID/");
	/**
	 * 获取小程序scheme码地址 美的服务
	 */
	public static final String WCP_GET_SCHEME_URL = ConfigUtil.getString(APP_NAME, "WCP_GET_SCHEME_URL","https://weixincs.midea.com/wcp/sic/api/WCP_MA_GENERATE_SCHEME/d3cd5a215b7447f986eaef2364b9ef40/meimeics");

	//获取大数据平台未激活设备列表
	public static final String BD_GET_UNACTIVATED_RODUCTLIST_URL = ConfigUtil.getString(APP_NAME, "BD_GET_UNACTIVATED_RODUCTLIST_URL");

	//美云销会员等级查询
	public static final String MCSP_GET_LEVEL_URL = ConfigUtil.getString(APP_NAME, "MCSP_GET_LEVEL_URL","http://mcsp-sit.midea.com/api/mcsp_uc/mcsp-uc-rule/rule/getLevel.do");

	//美云销会员等级调整
	public static final String MCSP_CHECK_GROWTH_RULE_URL = ConfigUtil.getString(APP_NAME, "MCSP_CHECK_GROWTH_RULE_URL","https://mcsp-sit.midea.com/api/mcsp_uc/mcsp-uc-growth/growth/checkGrowthRule.do");
	//美云销长连接改短链接
	public static final String MCSP_LONG_TO_SHORT_URL = ConfigUtil.getString(APP_NAME, "MCSP_LONG_TO_SHORT_URL","https://mcsp-sit.midea.com/api/mcsp_cc/cc-web/mcsp/content/external/shortLink/longToShort.do");
	//美云销长连接改短链接的租户编码
	public static final String MIDEA_TENANT_CODE = ConfigUtil.getString(APP_NAME, "MIDEA_TENANT_CODE","P001");

	//商城商品查询
	public static final String OTHER_PRODUCT_LIST_URL = ConfigUtil.getString(APP_NAME, "OTHER_PRODUCT_LIST_URL","https://10.133.145.107/next/score_product_r/queryproductlistbyscene");

	//商城兑换商品下单
	public static final String OTHER_DOEXCHAGE_URL = ConfigUtil.getString(APP_NAME, "OTHER_DOEXCHAGE_URL","https://10.133.145.107/next/score_product_r/doexchangescene");

	//美云销通用appid(后续对接美云销用这个配置申请接口权限)
	public static final String MCSP_APPID = ConfigUtil.getString(APP_NAME, "MCSP_APPID","kR3ACTpgRCKw");

	//美云销美的appid
	public static final String MCSP_MEDIA_APPID = ConfigUtil.getString(APP_NAME, "MCSP_MEDIA_APPID","wx48a68831127faa00");

	//美云销通用appsecret
	public static final String MCSP_APPSECRET = ConfigUtil.getString(APP_NAME, "MCSP_APPSECRET","9e226ec2059aac71e9a487b3ba8b0f1ede5c55f3");

	//美云销来源系统
	public static final String MCSP_ORIGINSYSTEM = ConfigUtil.getString(APP_NAME, "MCSP_ORIGINSYSTEM","CxWEiJm7tgWo");
	
	
	//企微相关接口基础地址
	public static String getWechatWorkBaseUrl(){
		String mcspUrl = ParamUtil.getParam(APP_NAME,"MCSP_URL");
		CommonLogger.getCommLogger("wechat").info("mcsp_url:"+mcspUrl);
		return mcspUrl+"/api/rms_api/wec-backstage";
	}

	//企微接口APPKEY
	public static String getWechatWorkAppKey(){
		String mcspQiyewxAppkey = ParamUtil.getParam(APP_NAME,"MCSP_QIYEWX_APPKEY");
		CommonLogger.getCommLogger("wechat").info("mcspQiyewxAppkey:"+mcspQiyewxAppkey);
		return mcspQiyewxAppkey;
	}

	//企微接口SECRET
	public static String getWechatWorkSecret(){
		String mcspQiyewxAppsecret = ParamUtil.getParam(APP_NAME,"MCSP_QIYEWX_APPSECRET");
		CommonLogger.getCommLogger("wechat").info("mcspQiyewxAppsecret:"+mcspQiyewxAppsecret);
		return mcspQiyewxAppsecret;
	}
	
	/**
	 * 地动仪接口appKey
	 */
	public static final String DDY_APPKEY = ConfigUtil.getString(APP_NAME, "DDY_APPKEY","");
	//地动仪接口统一请求地址
	public static final String DDY_SERVER_URL = ConfigUtil.getString(APP_NAME, "DDY_SERVER_URL","https://kdmpuat.midea.com/npsp");
	//获取地动仪大数据人群包数据准备情况
	public static final String DDY_GET_GROUP_VALID_TOUCH_APPID = ConfigUtil.getString(APP_NAME, "DDY_GET_GROUP_VALID_TOUCH_URL","/api/cc/groups/valid-touch");
	//发起地动仪人群数据同步请求
	public static final String DDY_GET_GROUP_SYNC_APPID = ConfigUtil.getString(APP_NAME, "DDY_GET_GROUP_SYNC","/api/cc/groups/sync");
	//根据加密手机号码查询所在人群包id和会员身份列表
	public static final String DDY_GET_CUST_PHONE_GROUP_APPID = ConfigUtil.getString(APP_NAME, "DDY_GET_CUST_PHONE_GROUP","/api/cc/groups");
	//解密数据接口（手机号码，用户姓名等）
	public static final String DSMP_DATA_SERVER_URL = ConfigUtil.getString(APP_NAME, "DSMP_DATA_SERVER_URL","https://dsmp.midea.com/openapi/kms");

	//地动仪接口统一请求地址
	public static final String DDY_PROBLEM_SERVER_URL = ConfigUtil.getString(APP_NAME, "DDY_PROBLEM_SERVER_URL","https://kdmpuat.midea.com/npsp");
	//问题单新增接口
	public static final String DDY_CREATE_PROBLEM_ORDER_APPID = ConfigUtil.getString(APP_NAME, "DDY_CREATE_PROBLEM_ORDER_APPID","/api/cc/createProblem");

	/**
	 * 大数据购买记录appid
	 */
	public static final String BIG_DATA_BUY_HISTORY_APPID = ConfigUtil.getString(APP_NAME, "BIG_DATA_BUY_HISTORY_APPID","4a155d6e196b4db39a37107d25fd5a97");;
	/**
	 * 大数据购买记录URL
	 */
	public static final String BIG_DATA_BUY_HISTORY_URL = ConfigUtil.getString(APP_NAME, "BIG_DATA_BUY_HISTORY_URL","https://bigdataserviceuat.midea.com/bdds/internet-ksei/api/cc/queryHisOrder");;


	public static final String MCSP_VIP_BASE_URL = ConfigUtil.getString(APP_NAME, "MCSP_VIP_BASE_URL","");


	public static final String AUDIT_LOG_URL = ConfigUtil.getString(APP_NAME, "AUDIT_LOG_URL","");


	public static final String AUDIT_LOG_APP_KEY = ConfigUtil.getString(APP_NAME, "AUDIT_LOG_APP_KEY","");


	public static final String AUDIT_LOG_SECRETID = ConfigUtil.getString(APP_NAME, "AUDIT_LOG_SECRETID","");
	
	/**
	 * 梦网AIM接口请求域名
	 */
	public static final String AIM_API_DOMAIN = ConfigUtil.getString(APP_NAME, "AIM_API_DOMAIN","https://tpl-aim.monyun.cn:57123");

	/**
	 * 梦网AIM接口请求域名（新）-用于申请智能短链接接口
	 */
	public static final String AIM_API_DOMAIN2 = ConfigUtil.getString(APP_NAME, "AIM_API_DOMAIN2","https://tpl-aim.monyun.cn:57123");

	//智能短信能力查询接口
	public static final String AIM_API_QUERYAIMABILITY = ConfigUtil.getString(APP_NAME, "AIM_API_QUERYAIMABILITY","/ApiService/v1/AddressManage/queryAimAbility");
	//智能短信短链申请接口
	public static final String AIM_API_APPLYAIMURL = ConfigUtil.getString(APP_NAME, "AIM_API_APPLYAIMURL","/ApiService/v1/SCodeManage/applyAimUrl");
	//智能短信公共模板列表查询接口
	public static final String AIM_API_LISTECTEMPLATES = ConfigUtil.getString(APP_NAME, "AIM_API_LISTECTEMPLATES","/ApiService/v1/TemplateManage/listECTemplates");
	//接口账号
	public static final String AIM_API_ACC = ConfigUtil.getString(APP_NAME, "AIM_API_ACC","AP1677");
	//接口密码
	public static final String AIM_API_PWD = ConfigUtil.getString(APP_NAME, "AIM_API_PWD","AIM0047test0261");
	
	
	/**
	 * 美云销请求地址
	 * 生产: https://mcsp-api.midea.com
	 * 测试: https://mcsp-api-sit.midea.com
	 */
	public static String getMcspURL() {
		return ParamUtil.getParam(APP_NAME,"MCSP_URL", "https://mcsp-api.midea.com");
	}
	/**
	 * 	美云销-用户中心-appkey
	 * 	生产: 6313b5d5a12d4cdca645523c68e47fdf
	 * 	测试：1e66018337a849ebb1fbf995585deb97
	 */
	public static String getMcspUcAppkey() {
		return ParamUtil.getParam(APP_NAME,"MCSP_UC_APPKEY", "6313b5d5a12d4cdca645523c68e47fdf");
	} 
	/**
	 * 	美云销-用户中心-appsecret
	 * 	生产：27909c1a36c243628ff4a30500a8ad98
	 * 	测试：30182fc095b94c0fb1b56beb31264001
	 */
	public static String getMcspUcAppSecret() {
		return ParamUtil.getParam(APP_NAME,"MCSP_UC_APPSECRET", "27909c1a36c243628ff4a30500a8ad98");
	}
	/**
	 *  美云销-履约中心-appkey
	 */
	public static String getMcspOfcAppKey() {
		return ParamUtil.getParam(APP_NAME,"MCSP_OFC_APPKEY", "6929c6c41b124431ac6947f4b43121af");
	}
	/**
	 * 美云销-履约中心-appsecret
	 */
	public static String getMcspOfcAppSecret() {
		return ParamUtil.getParam(APP_NAME,"MCSP_OFC_APPSECRET", "99382c2674b24f5fa4a37c049a9c8fb8");
	}
	/**
	 *  美云销-线上线下门店-appkey
	 */
	public static String getMcspStoreAppKey() {
		return ParamUtil.getParam(APP_NAME,"MCSP_STORE_APPKEY", "dbce5dbab7b04fe4bf3a180d49f0d213");
	}
	/**
	 * 美云销-线上线下门店-appsecret
	 */
	public static String getMcspStoreAppSecret() {
		return ParamUtil.getParam(APP_NAME,"MCSP_STORE_APPSECRET", "732425eba9c949b3aa69d96cb899a046");
	}
	/**
	 *  美云销-订单系统-appkey
	 *  sit环境
	 * key:23f37e3a37454e7b99de426df79da7af
	 * secret: 895d3c595aa041fb9146626335d5ba1b
	 * uat
	 * key:c5ec61667cba4da8bf511b0a6af5c1f3
	 * secret: b061e4be1ef64cf1b23566f99abe0bcf
	 * ver:
	 * key:c225adc95c0f45f590720259a5f7b9be
	 * secret: ae07722ff30c479da9ad6583c085b91e
	 * prd
	 * key:d0511145d1cd437e8f400cb25dcba6ad
	 * secret:5e87c7a94c844e2f9ea663c95a1229e2
	 */
	public static String getMcspOrderAppKey() {
		return ParamUtil.getParam(APP_NAME,"MCSP_ORDER_APPKEY", "d0511145d1cd437e8f400cb25dcba6ad");
	}
	/**
	 * 美云销-订单系统-appsecret
	 */
	public static String getMcspOrderAppSecret() {
		return ParamUtil.getParam(APP_NAME,"MCSP_ORDER_APPSECRET", "5e87c7a94c844e2f9ea663c95a1229e2");
	}
	
	/**
	 * ldap新接口地址
	 * @return
	 */
	public static String getLdapUrl() {
		return ParamUtil.getParam(APP_NAME,"LDAP_URL", "https://idmapi.midea.com");
	}
	/**
	 * ldap新接口应用的key
	 * @return
	 */
	public static String getLdapAppKey() {
		return ParamUtil.getParam(APP_NAME,"LDAP_NEW_APPKEY", "a57e23857acf4c71b81a1712b4dcbab0");
	}
	/**
	 * ldap新接口应用的SECRETID
	 * @return
	 */
	public static String getLdapSecretidV2() {
		return ParamUtil.getParam(APP_NAME,"LDAP_NEW_SECRETIDV2", "b81a1712b4dcbab0");
	}
	
	public static String getGvocAppId() {
		return ParamUtil.getParam(APP_NAME,"GVOC_APPID", "4a155d6e196b4db39a37107d25fd5a97");
	}
	public static String getGvocAppKey() {
		return ParamUtil.getParam(APP_NAME,"GVOC_APPKEY", "609527");
	}
	public static String getGvocPushVisitBackUrl() {
		return ParamUtil.getParam(APP_NAME,"GVOC_PUSH_VISIT_BACK_URL", "https://voc-uat.midea.com/voc/openapi/cc/pushVisitBack");
	}
	public static String getGvocSyncMessageDataUrl() {
		return ParamUtil.getParam(APP_NAME,"GVOC_SYNC_MESSAGE_DATA_URL", "https://apiuat.midea.com/D-GVOC/gvoc-openapi/voc/openapi/cc/syncMessageData");
	}

	/**
	 * 推送舆情工单到gvoc接口地址
	 * @return
	 */
	public static String pushSentimentOrderToGvoc() {
		return ParamUtil.getParam(APP_NAME,"PUSH_GVOC_SENTIMENT_ORDER_URL", "https://apiuat.midea.com/D-GVOC/gvoc-openapi/voc/openapi/cc/syncSentiment");
	}
	//数据银行
	public static String getDBANAppKey() {
		return ParamUtil.getParam(APP_NAME,"DBAN_APPKEY", "58a49d1c4ba048e4bf422da22afcfdec");
	}	
	public static String getDBANAppId() {
		return ParamUtil.getParam(APP_NAME,"DBAN_APPID", "601e695c12784d3598a5fcd8048afe3e");
	}
	public static String getDbanEncrypByDatatypeUrl() {
		return ParamUtil.getParam(APP_NAME,"DBAN_ENCRYPT_BY_DATATYPE_URL", "https://apipord.midea.com/d-dban/databank/paas-app-kms/encrypt-by-datatype");
	}

	public static String getDbanDecryptByDatatypeUrl() {
		return ParamUtil.getParam(APP_NAME,"DBAN_DECRYPT_BY_DATATYPE_URL", "https://apipord.midea.com/d-dban/databank/paas-app-kms/decrypt-batch-by-datatype");
	}
	
	public static String getWechatInfoUrl() {
		return ParamUtil.getParam(APP_NAME,"SYNC_WECHAT_USER_INFO", "https://mcsp-api-sit.midea.com/api/rms_api/wec-backstage");
	}
	public static String getWechatInfoAppKey() {
		return ParamUtil.getParam(APP_NAME,"SYNC_WECHAT_USER_APPKEY", "91f45727f4ac4c5aaba25fbe3e2b964b");
	}
	public static String getWechatInfoSecret() {
		return ParamUtil.getParam(APP_NAME,"SYNC_WECHAT_USER_SECRET", "9deb183305de4fe188b3a527457fa6a3");
	}
	
	/**
	 * mcsp鉴权方式1，非加密方式
	 */
	public static final int MCSP_AK_TYPE_NONE = 1;
	
	/**
	 * mcsp鉴权方式2，jwt
	 */
	public static final int MCSP_AK_TYPE_JWT = 2;


	/**
	 * 智慧眼-查询用户联网设备
	 */
	public static String getIntelDiagnosisUrl() {
		return ParamUtil.getParam(APP_NAME,"BARD_INTEL_DIAGNOSIS_URL", "https://dlife.midea.com/v1/rest/midealink/aftercare/getIntelDiagnosisList");
	}
	/**
	 * 智慧眼-获取故障排障方案
	 */
	public static String getFaultAnalysisList() {
		return ParamUtil.getParam(APP_NAME,"BARD_FAULT_ANALYSIS_URL", "https://dlife.midea.com/v1/rest/midealink/aftercare/getFaultAnalysisList");
	}

	/**
	 * iop地址（大模型接口以及AI陪练接口）
	 * @return
	 */
	public static String getIopUrl() {
		return ParamUtil.getParam(APP_NAME,"IOP_URL", "https://iop.midea.com");
	}

	/**
	 * callserver地址 用于轮询监控地址
	 * @return
	 */
	public static String getCallserverUrl() {
		return ParamUtil.getParam(APP_NAME,"CALLSERVER_URL", "");
	}

	/**
	 * getAndeAicallResultUrl 安德物流外呼回访结果回传地址
	 * @return
	 */
	public static String getAndeAicallResultUrl() {
		return ConfigUtil.getString(APP_NAME, "ANDE_AICALL_RESULT_URL","https://anapi-uat.annto.com/bop/T201904230000000014/advice/ccBackToCci");
	}

    public static String getUnifiedGatewayUrl() {
		return ConfigUtil.getString(APP_NAME, "UNIFIED_GATEWAY_URL");
	}

	public static String getWechatEnterprise(){
		return ConfigUtil.getString(APP_NAME, "WECHAT_ENTERPRISE","100");
	}

	public static String getWechatGroupJumpUrl(){
		return ConfigUtil.getString(APP_NAME, "WECHAT_GROUP_JUMP_URL","");
	}

	/**
	 * 质检敏感词监控接口
	 * @return
	 */
	public static String getSensitiveWordsQuality() {
		return ConfigUtil.getString(APP_NAME,"SENSITIVE_WORDS_QUALITY_URL", "");
	}

	/**
	 * 是否开启敏感词监控
	 */
	public static boolean  isOpenSensitiveWord() {
		String isOpenSensitiveWord = ConfigUtil.getString(APP_NAME, "IS_OPEN_SENSITIVE_WORD");
		return "Y".equals(isOpenSensitiveWord);
	}

}
