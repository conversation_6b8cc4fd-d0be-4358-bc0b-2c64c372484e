package com.yunqu.cc.mixgw.inf;

import org.apache.log4j.Logger;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.utils.AimSignUtil;
import com.yunqu.cc.mixgw.utils.HttpClientUtil;
/**
 *处理与梦网科技智能短信接口
 */
public class AimInterfaceService extends IService{
	
    private static Logger logger = CommonLogger.getCommLogger("aim");
    private static Logger longTime = CommonLogger.getCommLogger("longTime");

	@Override
	public JSONObject invoke(JSONObject resqJson) throws ServiceException {
		String command = resqJson.getString("command");
		long startTime = System.currentTimeMillis();
		try {
			if("queryAimAbility".equals(command)){ //查询号码智能短信能力
				return queryAimAbility(resqJson);
			}if("applyAimUrl".equals(command)){ //申请智能短链接
				return applyAimUrl(resqJson);
			}if("listECTemplates".equals(command)){ //获取智能短信公共模板列表查询接口
				return listECTemplates(resqJson);
			}else{
				JSONObject result = new JSONObject();
				result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
				result.put("respDesc", "不存在的command,请检查！");
				return result;
			}
		} finally {
			long endTime = System.currentTimeMillis();
			long elapsedTime = endTime - startTime;
			longTime.info(command+"接口"+elapsedTime);
		}
		
	}
	
	
	/**
	 * 根据SHA1加密手机号码查询智能短信能力
	 * @return
	 */
	public JSONObject queryAimAbility(JSONObject json) {
		JSONObject result = new JSONObject();
		String res = "";
		String appid = Constants.AIM_API_QUERYAIMABILITY;
		String url = Constants.AIM_API_DOMAIN+appid;
		json=json.getJSONObject("params");
		JSONObject header = AimSignUtil.getHeader();
		try{
			res = HttpClientUtil.post(url, json.toJSONString(), header, 3000);
			logger.info("参数="+json.toJSONString()+",根据SHA1加密手机号码查询智能短信能力请求,url="+url+",结果："+res);
			JSONObject parseObject = JSON.parseObject(res);
			String subCode = parseObject.getString("subCode");
			if("0".equals(subCode)){
				result.put("respCode", GWConstants.RET_CODE_SUCCESS);
				logger.info("根据SHA1加密手机号码查询智能短信能力请求,url="+url+",结果："+res);
			}else{
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				logger.info("根据SHA1加密手机号码查询智能短信能力请求,url="+url+",结果："+res);
			}
			result.put("respData", parseObject);
			result.put("respDesc", "查询成功");
		}catch(Exception e){
			logger.error("根据SHA1加密手机号码查询智能短信能力请求,url="+url+",结果="+res+",原因："+e.getMessage(),e);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "查询失败");
		}
		return result;
	}
	
	/**
	 * 申请智能短链接
	 * @return
	 */
	public JSONObject applyAimUrl(JSONObject json) {
		JSONObject result = new JSONObject();
		String res = "";
		String appid = Constants.AIM_API_APPLYAIMURL;
		String url = Constants.AIM_API_DOMAIN2+appid;
		json=json.getJSONObject("params");
		JSONObject header = AimSignUtil.getHeader();
		try{
			res = HttpClientUtil.post(url, json.toJSONString(), header, 3000);
			logger.info("参数="+json.toJSONString()+", 申请智能短链接请求,url="+url+",结果："+res);
			JSONObject parseObject = JSON.parseObject(res);
			String subCode = parseObject.getString("subCode");
			if("0".equals(subCode)){
				result.put("respCode", GWConstants.RET_CODE_SUCCESS);
				logger.info(" 申请智能短链接请求,url="+url+",结果："+res);
			}else{
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				logger.info(" 申请智能短链接请求,url="+url+",结果："+res);
			}
			result.put("respData", parseObject);
			result.put("respDesc", "查询成功");
		}catch(Exception e){
			logger.error(" 申请智能短链接请求,url="+url+",结果="+res+",原因："+e.getMessage(),e);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "查询失败");
		}
		return result;
	}
	
	/**
	 * 获取智能短信公共模板列表查询接口
	 * @return
	 */
	public JSONObject listECTemplates(JSONObject json) {
		JSONObject result = new JSONObject();
		String res = "";
		String appid = Constants.AIM_API_LISTECTEMPLATES;
		String url = Constants.AIM_API_DOMAIN+appid;
		json=json.getJSONObject("params");
		JSONObject header = AimSignUtil.getHeader();
		try{
			res = HttpClientUtil.post(url, json.toJSONString(), header, 3000);
			logger.info("参数="+json.toJSONString()+", 获取智能短信公共模板列表查询接口请求,url="+url+",结果："+res);
			JSONObject parseObject = JSON.parseObject(res);
			String subCode = parseObject.getString("subCode");
			if("0".equals(subCode)){
				result.put("respCode", GWConstants.RET_CODE_SUCCESS);
				logger.info(" 获取智能短信公共模板列表查询接口请求,url="+url+",结果："+res);
			}else{
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				logger.info(" 获取智能短信公共模板列表查询接口请求,url="+url+",结果："+res);
			}
			result.put("respData", parseObject);
			result.put("respDesc", "查询成功");
		}catch(Exception e){
			logger.error(" 获取智能短信公共模板列表查询接口请求,url="+url+",结果="+res+",原因："+e.getMessage(),e);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "查询失败");
		}
		return result;
	}
	
	public static void main(String[] args) {
		AimInterfaceService aimInterfaceService = new AimInterfaceService();
		JSONObject jsonObject = new JSONObject();
		JSONObject param = new JSONObject();
		param.put("page",1);
		param.put("size", 20);
		jsonObject.put("params", param);
		JSONObject listPublicTemplates = aimInterfaceService.listECTemplates(jsonObject);
		System.out.println(listPublicTemplates);
	}
	
}
