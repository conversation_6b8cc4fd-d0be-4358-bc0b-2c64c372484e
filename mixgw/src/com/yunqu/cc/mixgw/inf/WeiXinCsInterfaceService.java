package com.yunqu.cc.mixgw.inf;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.JsonUtil;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.CommonWeiXinCsLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.utils.CsUtil;
import com.yunqu.cc.mixgw.utils.HttpClientUtil;
import com.yunqu.cc.mixgw.utils.StringUtil;
import com.yunqu.cc.mixgw.utils.URLUtil;
import com.yunqu.cc.mixgw.utils.WebHttpUtil;


/**
* @Package：com.yunqu.cc.mixgw.inf
* @ClassName：WeiXinCsInterfaceService
* @Description：   <p> 处理与洗悦家公众号的接口 </p>
* @Author： - wubin
* @CreatTime：2018年5月18日 下午5:55:51
*
* @Modify By：
* @ModifyTime：  2018年5月18日
* @Modify marker：
* @version    V1.0
 */
public class WeiXinCsInterfaceService extends IService{

	private Logger logger = CommonWeiXinCsLogger.logger;
    private static Logger longTime = CommonLogger.getCommLogger("longTime");

	/**
	 * 所有请求入口
	 */
	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		String command = json.getString("command");
		long startTime = System.currentTimeMillis();
        try {
        	//业务系统查询洗悦家的商品列表时，通过调用该IService接口实现，该接口从洗悦家查询商品信息后返回
        	if(ServiceCommand.MIXGW_WEIXINCS_GETPRODUCTLIST4MENU.equals(command)){
        		return getProductList4Menu(json);
        	}
        	//业务系统查询洗悦家的根据商品号查询卡券类型时，通过调用该IService接口实现，该接口从洗悦家查询后返回
        	if(ServiceCommand.MIXGW_WEIXINCS_GETCARDTYPE.equals(command)){
        		return getCardType(json);
        	}
        	//业务系统查询洗悦家的优惠券时，通过调用该IService接口实现，该接口从洗悦家查询后返回
        	if(ServiceCommand.MIXGW_WEIXINCS_GETCARD.equals(command)){
        		return getCard(json);
        	}
        	//业务系统向洗悦家的 下单 时，通过调用该IService接口实现，该接口从洗悦家处理后返回
        	if(ServiceCommand.MIXGW_WEIXINCS_ADDPRODUCT.equals(command)){
        		
        		return addProduct(json);
        	}
        	//业务系统查询洗悦家的验证地址接口时时，通过调用该IService接口实现，该接口从洗悦家查询后返回
        	if(ServiceCommand.MIXGW_WEIXINCS_VALIDADDR.equals(command)){
        		
        		return vaildArr(json);
        	}
        	//（工单）业务系统查询洗悦家的验证地址接口时时，通过调用该IService接口实现，该接口从洗悦家查询后返回
        	
        	if(ServiceCommand.MIXGW_WEIXINCS_AREAVALID.equals(command)){
        		return areavalid(json);
        	}
        	
        	if(ServiceCommand.MIXGW_WEIXINCS_ORDER.equals(command)){
        		return getOrderInfo(json);
        	}
        	
        	if("orgCardList".equals(command)) {
        		return getOrgCardList(json);
        	}
        	
        	//根据服务单查询洗悦家订单详情
        	if ("getOrderInfoByFW".equals(command)){
        		return getOrderInfoByFW(json);
        	}
        	
        	logger.error(CommonUtil.getClassNameAndMethod(this)+" 无法识别的操作类型:"+command);
        	
        	JSONObject result = JsonUtil.createInfRespJson(json);
        	result.put("respDesc", "不可识别的command");
        	return result;
		} finally {
			long endTime = System.currentTimeMillis();
			long elapsedTime = endTime - startTime;
			longTime.info(command+"接口"+elapsedTime);
		}
	}

	/**
	 * 业务系统查询洗悦家的验证地址接口时时，通过调用该IService接口实现，该接口从洗悦家查询后返回
	 * @param json
	 * @return
	 */
	private JSONObject vaildArr(JSONObject json) {
		JSONObject result = JsonUtil.createInfRespJson(json);
		String address = json.getString("address");
		//非空判断
		if(StringUtils.isBlank(address) ){
			result.put("respDesc", "请求参数 address 不能为空!");
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 请求参数不能为空,address="+address);
			return result;
		}
		String url = Constants.ELEC_BROWSED_ITME_URL;
		if(StringUtils.isBlank(url)){
			result.put("respDesc", "电商接口url未配置!");
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 电商接口url未配置!");
			return result;
		}
		return null;
	}

	/**
	 * 业务系统向洗悦家的 下单 时，通过调用该IService接口实现，该接口从洗悦家处理后返回
	 * @param obj
	 * @return
	 */
	private JSONObject addProduct(JSONObject obj) {
	String	funcUrl = Constants.WEI_XIN_CS_CARD_BASE_URL+"addUcOrder";
		//funcUrl = "http://***************:8080/ccss-ipms-cis-rpc/uc/addUcOrder";
		Map<String, Object> params = CsUtil.formatPostParams(funcUrl, obj);
		JSONObject result = new JSONObject();
		try{
			logger.info("业务系统向洗悦家的 下单提交url= "+funcUrl+"参数："+obj.toJSONString());
			 String jsonString = JSON.toJSONString(obj.getJSONObject("skuCode"));

			String resp = WebHttpUtil.doPost(funcUrl,jsonString  ,"application/json");

			logger.info("业务系统向洗悦家的 下单结果返回= "+resp);
			JSONObject j = JSONObject.parseObject(resp);
				result.put("respCode", GWConstants.RET_CODE_SUCCESS);
				result.put("respDesc", "查询成功");
				result.put("respData", j);
		}catch(Exception e){
			System.out.println(e);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "程序异常！");
		}
		return result;
	}

	/**
	 * 业务系统查询洗悦家的优惠券时，通过调用该IService接口实现，该接口从洗悦家查询后返回
	 * @param json
	 * @return
	 */
	private JSONObject getCard(JSONObject json) {
		String agentCode=json.get("agentCode").toString();
		String cardId=json.get("cardId").toString();
		JSONObject result = JsonUtil.createInfRespJson(json);
		String url = Constants.WEI_XIN_CS_CARD_BASE_URL+"getCardCode?expireDay="+30+"&agentCode="+agentCode+"&cardId="+cardId;
		logger.error(" 洗悦家接口入参:"+url);

		//HttpResp resp = HttpUtil.post(url, "", "UTF-8");
		try {
			HttpResp resp = HttpUtil.sendGet(url, "", GWConstants.ENCODE_UTF8);
			if(resp!=null) {
				JSONObject resultJson = JsonUtil.toJSONObject(resp.getResult());
				logger.error(" 洗悦家接口返回:"+resultJson.toString());
				String error = resultJson.getString("error");
				if(StringUtils.isNotBlank(error)){
					result.put("respDesc", "洗悦家接口返回错误.");
					return result;
				}
				 JSONObject jsonObject= resultJson.getJSONObject("data");
				 if( jsonObject!=null){
					 result.put("respDate", jsonObject);//数据
					// result.put("respDate", jsonObject.getJSONObject("respDate"));//数据
						result.put("respCode", GWConstants.RET_CODE_SUCCESS);
						result.put("respDesc", "处理成功");
				 }else{
						result.put("respDesc", "洗悦家接口返回错误.");
				 }

			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"洗悦家接口调用异常:"+e);
			//e.printStackTrace();
		}
		return result;
	}

	/**
	 * 业务系统查询洗悦家的根据商品号查询卡券类型时，通过调用该IService接口实现，该接口从洗悦家查询后返回
	 * @param json
	 * @return
	 */
	private JSONObject getCardType(JSONObject json) {
		JSONObject result = JsonUtil.createInfRespJson(json);
		String skuCodeList = json.getString("skuCodeList");
		String orderAmount = json.getString("orderAmount");
		String skuCnt = json.getString("skuCnt");
		if(StringUtils.isBlank(skuCodeList)){
			result.put("respDesc", "商品编码 skuCodeList 不能为空!");
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 获取卡券类型失败:商品编码 skuCodeList 不能为空!");
			return result;
		}
		String url = Constants.WEI_XIN_CS_CARD_BASE_URL+"getCardList?skuCodeList="+skuCodeList+"&orderAmount="+orderAmount+"&skuCnt="+skuCnt;
		logger.error(CommonUtil.getClassNameAndMethod(this)+" 获取卡券类型地址"+url);

		try {
			HttpResp resp = HttpUtil.sendGet(url, "", GWConstants.ENCODE_UTF8);
			if(resp.success()){
				JSONObject resultJson = JsonUtil.toJSONObject(resp.getResult());
				System.out.println(resultJson.toJSONString());
				String code = resultJson.getString("code");
				if(!"0".equals(code)){
					result.put("respDesc", "洗悦家接口返回错误.");
					logger.error(CommonUtil.getClassNameAndMethod(this)+" 洗悦家接口返回错误:"+resp.getResult());
					return result;
				}
				//JSONObject dataJson = resultJson.getJSONObject("data");
				result.put("data", resultJson);
				result.put("respCode", GWConstants.RET_CODE_SUCCESS);
				result.put("respDesc", "查询成功");
			}else{
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				result.put("respDesc", "查询失败:"+resp.getCode()+","+resp.getResult());
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询可用卡券类型失败:"+resp.getCode()+","+resp.getResult());
			}

		} catch (Exception e) {
			result.put("respDesc", "查询可用卡券类型出现异常:"+e.getMessage());
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询可用卡券类型出现异常:"+e.getMessage(),e);
		}

		return result;
	}

	/**
	 * 业务系统查询洗悦家的商品列表时，通过调用该IService接口实现，该接口从洗悦家查询商品信息后返回
	 * @param json
	 * @return JSONObject 商品列表数据
	 */
	private JSONObject getProductList4Menu(JSONObject json) {
		JSONObject result = JsonUtil.createInfRespJson(json);
		String url = Constants.WEI_XIN_CS_PRODUCT_URL;
		//HttpResp resp = HttpUtil.post(url, "", "UTF-8");
		try {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 洗悦家接口商品列表地址、:"+url+"参数typeCode=NXYJ&channelCode=CC");
			HttpResp resp = HttpUtil.sendGet(url, "typeCode=NXYJ&channelCode=CC", GWConstants.ENCODE_UTF8);
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 洗悦家接口商品列表返回:"+resp);
			if(resp!=null) {
				JSONObject resultJson = JsonUtil.toJSONObject(resp.getResult());
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 洗悦家接口商品列表返回、:"+resultJson.toJSONString());
				String error = resultJson.getString("error");
				if(StringUtils.isNotBlank(error)){
					result.put("respDesc", "洗悦家接口返回错误.");
					logger.error(CommonUtil.getClassNameAndMethod(this)+" 洗悦家接口返回错误:"+resp.getResult());
					return result;
				}
				JSONArray contentArray = resultJson.getJSONArray("content");
				JSONArray allServiceList =  new JSONArray();
				JSONArray pmList =  new JSONArray();
				if(contentArray!=null && contentArray.size()>0){
					for(int i=0 ; i<contentArray.size(); i++){//each productMain
						JSONObject elecJson = contentArray.getJSONObject(i);
						String productMenuName = elecJson.getString("productMenuName");//产品分类名
						pmList.add(productMenuName);
						JSONArray serviesList = elecJson.getJSONArray("productMainList");//产品品类list
						if (serviesList !=null && serviesList.size()>0) {
							for (int j = 0; j < serviesList.size(); j++) {
								JSONObject serviceJson = serviesList.getJSONObject(j);
								JSONObject service = new JSONObject();
								BigDecimal price = serviceJson.getBigDecimal("price");//价格
								String sells = serviceJson.getString("sell"); // 销售详情  null 为空 or sell[{"price":123,"":""}] 优惠信息
								service.put("productMainId", serviceJson.getString("productMainId"));//产品品类id
								service.put("productMenuName", productMenuName);//产品分类名
								service.put("productName", serviceJson.getString("productName"));//商品名字
								service.put("typeCode", serviceJson.getString("typeCode"));//商品名字
								service.put("itemId", serviceJson.getString("itemId"));//商品编号
								service.put("descriptionShort", serviceJson.getString("descriptionShort")); //商品描述
								service.put("price", price);//原价
								if ( sells!=null && StringUtils.isNotBlank(sells)) {
									JSONObject sellsObject = JsonUtil.toJSONObject(sells);
									service.put("nowPrice", sellsObject.getBigDecimal("price"));//现价
								}else {
									service.put("nowPrice",price);//现价
								}
								allServiceList.add(service);//所有list节点下的所有service
							}
						}
					}
				}
				result.put("pmList", pmList);//productMenuName[]  用于前台的单独渲染展示 for jsp.js
 				result.put("respDate", allServiceList);//数据
				result.put("respCode", GWConstants.RET_CODE_SUCCESS);
				result.put("respDesc", "处理成功");
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"洗悦家接口调用异常:"+e);
			//e.printStackTrace();
		}
		return result;
	}
	/**
	 * 业务系统查询洗悦家的验证地址接口时，通过调用该IService接口实现，该接口从洗悦家查询后返回（工单）
	 * @param json
	 * @return
	 */
	private JSONObject areavalid(JSONObject json) {
		JSONObject result=new JSONObject();
		String level4=json.getJSONObject("params").get("level4").toString();
		String level3=level4.substring(0,7);
		//
		String url = Constants.WX_XYJ_URL+"/order/area/valid?type=NXYJ&level4="+level4+"&level3="+level3+"";
		logger.error(" 洗悦家（工单）接口:"+url);

		try {
			HttpResp resp = HttpUtil.sendGet(url, "", GWConstants.ENCODE_UTF8);
			logger.error(" 洗悦家（工单）接口返回:"+resp.getResult());
			if(resp!=null) {
				JSONObject resultJson = JsonUtil.toJSONObject(resp.getResult());
				logger.error(" 洗悦家（工单）接口返回:"+resultJson.toJSONString());
				String error = resultJson.getString("error");
				if(StringUtils.isNotBlank(error)){
					result.put("respDesc", "洗悦家接口返回错误.");
					result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
					return result;
				}
				 if( resultJson!=null){
					 result.put("respDate", resultJson);//数据
					// result.put("respDate", jsonObject.getJSONObject("respDate"));//数据
						result.put("respCode", GWConstants.RET_CODE_SUCCESS);
						result.put("respDesc", "处理成功");
				 }
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"洗悦家接口调用异常:"+e);
			//e.printStackTrace();
		}
		return result;
	}

	/**
	 * 获取订单信息
	 * @param json
	 * @return
	 */
	private JSONObject getOrderInfo(JSONObject json) {
		JSONObject result = new JSONObject();
		//JSONObject result = JsonUtil.createInfRespJson(json);
		String orderId = json.getString("orderId");

		String url = Constants.WEI_XIN_CS_CARD_BASE_URL+"query/order?orderId="+orderId;
		logger.error(" 取订单信息接口:"+url);
		try {
			HttpResp resp = HttpUtil.sendGet(url, "", GWConstants.ENCODE_UTF8);
			logger.error(" 取订单信息接口返回:"+resp.getResult());
			if(resp!=null) {
				JSONObject resultJson = JsonUtil.toJSONObject(resp.getResult());
				logger.error(" 取订单信息接口返回:"+resultJson.toJSONString());
				String error = resultJson.getString("error");
				if(StringUtils.isNotBlank(error)){
					result.put("respDesc", "取订单信息接口返回错误.");
					result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
					return result;
				}
				 if( resultJson!=null){
					 result.put("respDate", resultJson);//数据
					// result.put("respDate", jsonObject.getJSONObject("respDate"));//数据
						result.put("respCode", GWConstants.RET_CODE_SUCCESS);
						result.put("respDesc", "处理成功");
				 }
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"取订单信息接口调用异常:"+e);
			//e.printStackTrace();
		}
		return result;
	}

	/**
	 * 根据服务号查询洗悦家订单详情接口地址
	 * @param json
	 * @return
	 */
	private JSONObject getOrderInfoByFW(JSONObject json) {
		//创建返回值
		JSONObject result = new JSONObject();
		//获取服务单参数
		String cssServiceId = json.getString("serviceOrderNo"); //前端传递的参数名应该是这个
		//拼接接口url地址
		String url = Constants.WEI_XIN_CS_ORDER_URL + "?cssServiceId=" + cssServiceId;
		logger.error(" 取订单信息接口:"+url);
		try {
			HttpResp resp = HttpUtil.sendGet(url, "", GWConstants.ENCODE_UTF8);
			logger.error(" 取订单信息接口返回 resp:"+resp.getResult());
			if(resp!=null) {
				//格式化成json
				JSONObject resultJson = JsonUtil.toJSONObject(resp.getResult());
				logger.error(" 取订单信息接口返回 resultJson:"+resultJson.toJSONString());
				String error = resultJson.getString("error");
				if(StringUtils.isNotBlank(error)){
					result.put("respDesc", "取订单信息接口返回错误.");
					result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
					logger.error("失败返回请求result：" + result);
					return result;
				}
				if( resultJson!=null){
					result.put("data", resultJson);//数据
					// result.put("respDate", jsonObject.getJSONObject("respDate"));//数据
					result.put("respCode", GWConstants.RET_CODE_SUCCESS);
					result.put("respDesc", "处理成功");
					logger.error("成功返回请求result：" + result);
				}
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"根据服务号获取订单详情接口调用异常:"+e);
			//e.printStackTrace();
		}
		return result;
	}


	/**
	 * 查询可派发卡券列表
	 * @param json
	 * @return
	 */
	private JSONObject getOrgCardList(JSONObject json) {
		JSONObject result = new JSONObject();
		result.put("respDesc", "数据错误");
		result.put("respCode", GWConstants.RET_CODE_DATABASE_EXCEPTION);
		String url = Constants.WX_CS_NEW_BASE_URL + "/uc/listOrgCard";
		url += "?customerCode=" + json.getString("customerCode");
		logger.info("获取卡券优化接口url:"+url);
		try {
			HttpResp resp = HttpUtil.sendGet(url, "", GWConstants.ENCODE_UTF8);
			logger.info("获取卡券优化接口返回:"+resp.getResult());
			if(resp!=null) {
				JSONObject resultJson = JsonUtil.toJSONObject(resp.getResult());
				//JSONObject resultJson = JsonUtil.toJSONObject(StringUtil.sss());
				String code = resultJson.getString("code");
				String msg = resultJson.getString("msg");
				if("0".equals(code)) {
					JSONArray list = resultJson.getJSONArray("data");
					JSONArray pmList = StringUtil.getJSONKey(list, "customerName", "customerCode");
					result.put("pmList", pmList);
					result.put("respData", resultJson.get("data"));
					result.put("respCode", GWConstants.RET_CODE_SUCCESS);
					result.put("respDesc", "处理成功");
				}else {
					result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
					result.put("respDesc", "接口返回失败:" + msg);
				}
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"取订单信息接口调用异常:"+e);
		}
		return result;
	}


}
