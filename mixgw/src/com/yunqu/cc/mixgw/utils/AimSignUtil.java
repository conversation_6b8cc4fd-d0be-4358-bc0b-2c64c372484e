package com.yunqu.cc.mixgw.utils;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.log4j.Logger;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;


/**
 * AIM接口签名工具
 */
public class AimSignUtil {
	
	
    private static Logger logger = CommonLogger.getCommLogger("aim");

	public static JSONObject getHeader(){
		JSONObject json = new JSONObject();
		String timestamp = DateUtil.getCurrentDateStr("MMddHHmmss");
		json.put("account", Constants.AIM_API_ACC);
		json.put("pwd", createSign(timestamp));
		json.put("timestamp", timestamp);
		return json;
	}
	
	public static String createSign(String timestamp) {
		String sign = Constants.AIM_API_ACC.toUpperCase() + "********" + Constants.AIM_API_PWD + timestamp;
		return DigestUtils.md5Hex(sign);
	}
	
	public static void main(String[] args) {
		System.out.println(getHeader());
	}
}
