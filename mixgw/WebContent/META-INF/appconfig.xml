<?xml version="1.0" encoding="UTF-8"?>
<config>
	 <param key="ELEC_BROWSED_ITME_URL" name="电商用户访问轨迹查询接口的URL" type="string" description="电商用户访问轨迹查询接口的URL" value="http://*************/next/browsed_item/getbrowseditem"/>
	 <param key="ELEC_NOTICEID" name="电商用户访问轨迹查询接口的NOTICEID" type="string" description="电商用户访问轨迹查询接口的NOTICEID" value="1"/>
	 <param key="ELEC_APPID" name="电商用户访问轨迹查询接口的APPID" type="string" description="电商用户访问轨迹查询接口的APPID" value="appid4xintai"/>
	 <param key="ELEC_APPKEY" name="电商用户访问轨迹查询接口的APPKEY" type="string" description="电商用户访问轨迹查询接口的APPKEY" value="secret4xintai"/>
	 <param key="ELEC_SOURCE" name="电商用户访问轨迹查询接口的SOURCE值" type="string" description="电商用户访问轨迹查询接口的SOURCE值" value="1"/>
	 <param key="ELEC_VERSION" name="电商用户访问轨迹查询接口的VERSION值" type="string" description="电商用户访问轨迹查询接口的VERSION值" value="1"/>

	 <param key="WEI_XIN_CS_PRODUCT_URL" name="洗悦家访问产品列表的url" type="string" description="洗悦家访问产品列表的url" value="http://weixincs.midea.com/mall/product/getProductList4Menu"/>
	 <param key="WEI_XIN_CS_CARD_BASE_URL" name="洗悦家处理优惠券等接口的前缀" type="string" description="洗悦家处理优惠券、用户中心下单、验证地址等接口的前缀，如获取优惠券需要在代码里加上getCardType，验证地址，需要在代码里加上validAddr" value="http://weixincs.midea.com/mall/cc/"/>
	 <param key="WEI_XIN_CS_ORDER_URL" name="根据服务号查询洗悦家订单详情接口地址" type="string" description="根据服务号查询洗悦家订单详情接口地址" value="https://weixincs.midea.com/ccss-ipms-cis-rpc/uat/uc/getOrderDetailToCC"/>
	 <param key="STORE_AIR_CONDITIONER_URL" name="中央空调URL" type="string" description="旗舰店同步时访问目标为XML文件" value="http://www.midea.com/cn/shipflagshop/central_air/shipflagshop.xml"/>
	 <param key="STORE_OTHER_URL" name="其它类型URL" type="string" description="旗舰店同步时访问目标为XML文件" value="http://www.midea.com/cn/shipflagshop/search/shipflagshop.xml"/>
	 <param key="WEI_XIN_CS_GETCARDCODE_URL" name="优惠券获取接口" type="string" description="优惠券获取接口" value="http://weixincs.midea.com/mall/uc/getCardCode"/>
	 <param key="WEI_XIN_URL" name="洗悦家域名http://weixin.midea.com" type="string" description="洗悦家域名http://weixin.midea.com" value="http://weixin.midea.com"/>
	 <param key="WX_XYJ_URL" name="洗悦家地址前缀http://weixin.midea.com/ccss-ipms-cis" type="string" description="洗悦家地址前缀http://weixin.midea.com/ccss-ipms-cis" value="http://weixin.midea.com/ccss-ipms-cis"/>

	 <param key="ECM_ORDER_URL" name="(旧)ECM订单查询的URL地址" type="string" description="(旧)查询不同的接口，代码里再拼接不同的方法，如查询订单，加上 /order/selectOrdersForCSS" value="http://*************:8080/tc"/>
	 <param key="ECM_URL" name="美云销接口域名(旧)" type="string" description="美云销接口域名(旧)" value="https://mcsp-uat.midea.com"/>
	 <param key="MCSP_URL" name="美云销接口域名(新)"  type="string" description="美云销接口域名(新)" value="https://mcsp-api-uat.midea.com"/>

	 <!-- http://confluence.midea.com/pages/viewpage.action?pageId=229004997#id-查单系统接口文档-4.安得路由查询接口(uat环境)（开发人员须知）： -->
	 <param key="LOGISTICS_URL" name="物流查询地址" type="string" description="" value="https://luatapi.midea.com/"/>
	 <param key="LOGISTICS_CLIENTID" name="物流鉴权clientId" type="string" description="" value="6d318bf463c5498f96cbfb8d53155fac"/>
	 <param key="LOGISTICS_CLIENTSECRET" name="物流鉴权clientSecret" type="string" description="" value="4acc82cac84b46cbabc446b7ec93745e"/>
	 <param key="LOGISTICS_ACTOKEN" name="物流鉴权accessToken" type="string" description="" value="89b3f160-d7c0-48bb-93cf-2da46c4bb7c6"/>

	 <param key="LDAP_IP" name="LDAP的IP地址" type="string" description="在美的LDAP系统里申请后会提供IP" value="************"/>
	 <param key="LDAP_PORT" name="LDAP的端口号" type="string" description="在美的LDAP系统里申请后会提供端口" value="389"/>
	 <param key="LDAP_APP_ID" name="LDAP的应用id" type="string" description="在美的LDAP系统里申请后会提供应用id" value="ccscc_bind"/>
	 <param key="LDAP_APP_PWD" name="LDAP的应用密码" type="string" description="在美的LDAP系统里申请后会提供应用密码" value="zKtNO4"/>

	 <param key="MIDEA_VIP_URL" name="美的会员等级查询URL" type="string" description="美的会员等级查询URL" value="http://cmms2.midea.com/ccrm2-core/userApi/getVipUserInfo"/>
	 <param key="MIDEA_VIP_URL2" name="colmo会员等级查询URL" type="string" description="colmo会员等级查询URL" value="https://mcsp-sit.midea.com/api/mcsp_uc/mcsp-uc-member/member/getMemberInfo2B.do"/>
	 <param key="MIDEA_VIP_APPKEY" name="美的会员等级查询appKey" type="string" description="美的会员等级查询appKey" value="c8c35003cc4c408581043baad45bce5b"/>
	 <param key="MIDEA_VIP_SECRET" name="美的会员等级查询secret" type="string" description="美的会员等级查询secret" value="0dc6fe93a8154fcaab629353ab800bb4"/>

	 <param key="CMSMS_APPKEY" name="短链接平台推送appKey" type="string" description="短链接平台推送appKey" value="VlsPQoosfFX7"/>
	 <param key="CMSMS_SECRET" name="短链接平台推送secret" type="string" description="短链接平台推送secret" value="25bed6ee020417b9989c6212ff17bc73f2407c62"/>

     <param key="WEI_XIN_EMP_TIMECARD_URL" name="同步打卡数据的url" type="string" description="同步打卡数据的url" value="http://xx/faceapp/v1/queryLogExt"/>
     <param key="SIGNATURE_KEY" name="同步打卡数据的密匙" type="string" description="同步打卡数据的密匙" value="b5aeac51-02aa-4d5f-ab81-f13bf3ef9e17"/>
     <param key="LIBNAME" name="同步打卡数据的LIBNAME" type="string" description="同步打卡数据的LIBNAME" value="midea"/>
     <param key="SDFUNID" name="同步打卡数据的FUNID" type="string" description="同步打卡数据的FUNID" value="10000016"/>
     <param key="HFFUNID" name="同步打卡数据的FUNID" type="string" description="同步打卡数据的FUNID" value="10000017"/>

     <param key="ECM_APPKEY" name="ECM的APPKEY" type="string" description="ECM的APPKEY" value="203767886"/>
     <param key="ECM_APPSECRET" name="ECM的APPSECRET" type="string" description="ECM的APPSECRET" value="ldxmjqnls4gt0qbig9atorwki1lde8mr"/>
     <param key="ECM_HOST" name="接口地址如：ECM_HOST" type="string" description="接口地址如：ECM_HOST" value="ec.midea.com"/>
     <param key="ECM_HOST_URL" name="接口地址统一前缀如：/uat/open" type="string" description="接口地址统一前缀如：/uat/open" value="/uat/open"/>

     <param key="ELEC_GET_DEAL_DETAIL_URL" name="电商订单查询接口" type="string" description="电商订单查询接口如http://**************/next/mfop_deal/getdealdetail" value="http://**************/next/mfop_deal/getdealdetail"/>
     <param key="ELEC_GET_COUPON_INFO_URL" name="电商优惠券查询接口" type="string" description="电商优惠券查询接口如http://sitm.midea.cn/next/outer_assets/getusercouponinfo" value="http://sitm.midea.cn/next/outer_assets/getusercouponinfo"/>
     <param key="ELEC_COMPACT_DETAIL_URL" name="商品信息接口" type="string" description="商品信息接口https://sitm.midea.cn/next/detail_o/compactdetail" value="https://sitm.midea.cn/next/detail_o/compactdetail"/>
     <param key="ELEC_GEN_SHORT_SCHEME" name="商城链接获取接口" type="string" description="商城链接获取接口" value="http://**************/next/mfop_deal/genshortscheme"/>

     <param key="CCS_OFFLINE_URL" name="线下购买记录接口" type="string" description="线下购买记录接口http://************:8001/ccs-web/ext-api/getBillInfoByCustomMobile" value="http://************:8001/ccs-web/ext-api/getBillInfoByCustomMobile"/>

     <param key="WORK_WEIXIN_URL" name="企业微信域名地址" type="string" description="企业微信域名地址：https://*************:50002" value="https://*************:50002"/>
     <param key="WORK_WEIXIN_CORPSECRET" name="企业微信客服应用ID" type="string" description="企业微信客服应用ID" value="sY3WfCLO-XIc2MNHbHAs5SnkFt7r3HtJzA-rOJg-ox0"/>
     <param key="WORK_WEIXIN_CORPID" name="企业微信微信ID" type="string" description="企业微信微信ID" value="wwc56202ba0a29b2fb"/>

     <param key="GENERATE_SMS_URL" name="短链接生成地址" type="string" description="短链接生成地址" value="http://***********/cmsms-new/extShortUrlApi/generateSmsUrl"/>

	 <param key="MEDIA_4A" name="4A接口" type="string" description="4A接口" value="https://c4aapisit.midea.com/api/user/search"/>
	 <param key="MEDIA_4AAPPID" name="4A接口AppId" type="string" description="4A接口AppId" value="10064"/>
	 <param key="MEDIA_4AAPPKEY" name="4A接口AppKEY" type="string" description="4A接口AppKey" value="secff708128ea77d65152ed81f2595e2cb2"/>
	 <param key="WCP_IS_USER_SUBSCRIBED_URL" name="是否关注url" type="string" description="是否关注url：如https://weixincs.midea.com/wcp/sic/api/WCP_MP_IS_USER_SUBSCRIBED/" value="https://weixincs.midea.com/wcp/sic/api/WCP_MP_IS_USER_SUBSCRIBED/"/>
	 <param key="WCP_MP_SEND_TEMPLATE_URL" name="中控发送小程序消息url" type="string" description="中控发送小程序消息url" value="https://weixincs.midea.com/wcp/sic/api/WCP_MP_SEND_TEMPLATE/"/>
	 <param key="WCP_GET_SCHEME_URL" name="获取小程序scheme码地址" type="string" description="获取小程序scheme码地址" value="https://weixincs.midea.com/wcp/sic/api/WCP_MA_GENERATE_SCHEME/d3cd5a215b7447f986eaef2364b9ef40/meimeics"/>
	 <param key="WCP_GET_USER_BY_UNIONID_URL" name="根据unionid获取用户信息url" type="string" description="根据unionid获取用户信息url" value="https://weixin.midea.com/alicloud/wcp/sic/api/WCP_MP_GET_USER_BY_UNIONID/"/>
	 <param key="BD_GET_UNACTIVATED_RODUCTLIST_URL" name="获取大数据平台未激活设备列表地址" type="string" description="获取大数据平台未激活设备列表地址" value=""/>
	 <param key="MCSP_GET_LEVEL_URL" name="美云销获取会员等级信息接口" type="string" description="美云销获取会员等级信息接口" value="http://mcsp-sit.midea.com/api/mcsp_uc/mcsp-uc-rule/rule/getLevel.do"/>
	 <param key="MCSP_CHECK_GROWTH_RULE_URL" name="美云销调整会员等级接口" type="string" description="美云销调整会员等级接口" value="https://mcsp-sit.midea.com/api/mcsp_uc/mcsp-uc-growth/growth/checkGrowthRule.do"/>
	 <param key="OTHER_PRODUCT_LIST_URL" name="商城商品查询" type="string" description="商城商品查询" value="https://**************/next/score_product_r/queryproductlistbyscene"/>
	 <param key="OTHER_DOEXCHAGE_URL" name="商城兑换商品下单" type="string" description="商城兑换商品下单" value="https://**************/next/score_product_r/doexchangescene"/>

	 <param key="WX_CS_CARD_NEW_BASE_URL" name="峰终清洗券列表" type="string" description="峰终清洗券列表" value="http://weixincs.midea.com/ccss-ipms-cis-rpc/uat"/>
	 <param key="WECHAT_WORK_BASE_URL" name="企微相关接口" type="string" description="企微相关接口基础地址" value=""/>
	 <param key="WECHAT_WORK_APP_KEY" name="企微接口APPKEY" type="string" description="企微接口APPKEY" value="MaAodHlzRKZ1"/>
	 <param key="WECHAT_WORK_SECRET" name="企微接口SECRET" type="string" description="企微接口SECRET" value="d092d75585618a590c381157bfa69414f86fbfd1"/>
	 <param key="WECHAT_ENTERPRISE" name="企微主体ID" type="string" description="默认100,测试环境200" value="100" />
	 <param key="WECHAT_GROUP_JUMP_URL" name="企微跳转群聊h5页面URL" type="string" description="企微跳转群聊h5页面URL" value="" />

	<param key="DDY_SERVER_URL" name="地动仪接口地址" type="string" description="地动仪接口地址" value=""/>
	 <param key="DDY_GET_GROUP_VALID_TOUCH_URL" name="地动仪获取人群包数据准备情况接口" type="string" description="地动仪获取人群包数据准备情况接口" value=""/>
	 <param key="DDY_GET_GROUP_SYNC" name="地动仪人群包数据同步请求接口" type="string" description="地动仪人群包数据同步请求接口" value=""/>
	 <param key="DDY_GET_CUST_PHONE_GROUP" name="地动仪获取人群包接口" type="string" description="地动仪获取人群包接口" value=""/>
	 <param key="DDY_APPKEY" name="地动仪接口签名密钥" type="string" description="地动仪接口签名密钥" value=""/>
	 <param key="DSMP_DATA_SERVER_URL" name="DSMP数据安全接口地址" type="string" description="DSMP数据安全接口地址" value=""/>
	 <param key="VOC_SEARCH_TASK_URL" name="voc查询任务接口地址" type="string" description="voc查询任务接口地址" value=""/>
 	 <param key="DDY_CREATE_PROBLEM_ORDER_APPID" name="地动仪的问题单新增接口地址appid" type="string" description="问题单新增appid" value="/api/cc/createProblem"/>
 	 <param key="DDY_PROBLEM_SERVER_URL" name="地动仪问题单接口地址" type="string" description="地动仪问题单接口地址" value="https://kdmpuat.midea.com/npsp"/>

 	 <param key="MCSP_APPID" name="美云销商户中心APPID" type="string" description="美云销APPID/APPKEY(后续用此配置申请接口权限)" value="kR3ACTpgRCKw"/>
 	 <param key="MCSP_APPSECRET" name="美云销商户中心APPSECRET" type="string" description="美云销APPSECRET" value="9e226ec2059aac71e9a487b3ba8b0f1ede5c55f3"/>
 	 <param key="MCSP_ORIGINSYSTEM" name="美云销商户中心来源系统" type="string" description="美云销来源系统" value="CxWEiJm7tgWo"/>

 	 <param key="MCSP_LONG_TO_SHORT_URL" name="美云销长连接改短链接" type="string" description="美云销长连接改短链接" value="https://mcsp-sit.midea.com/api/mcsp_cc/cc-web/mcsp/content/external/shortLink/longToShort.do"/>
 	 <param key="MIDEA_TENANT_CODE" name="美云销的租户编码" type="string" description="美云销的租户编码" value="P001"/>

 	 <param key="BIG_DATA_BUY_HISTORY_APPID" name="大数据购买记录appid" type="string" description="大数据购买记录appid" value="4a155d6e196b4db39a37107d25fd5a97"/>
 	 <param key="BIG_DATA_BUY_HISTORY_URL" name="大数据购买记录URL" type="string" description="大数据购买记录url" value="https://bigdataserviceuat.midea.com/bdds/internet-ksei/api/cc/queryHisOrder"/>


 	 <param key="AUDIT_LOG_URL" name="审计日志接口地址" type="string" description="审计日志接口地址" value="https://applog-uat.midea.com/log2security?token="/>
 	 <param key="AUDIT_LOG_APP_KEY" name="审计日志接口appKey" type="string" description="审计日志接口appKey" value="dea7c71ba0335ea4889e49ba8cf75678"/>
 	 <param key="AUDIT_LOG_SECRETID" name="审计日志接口secretId" type="string" description="审计日志接口secretId" value="4a5da18d68cc434fb8458d5a885b1bd8"/>

	 <param key="VOC_KEYWORD_MATCH_URL" name="voc语义匹配地址" type="string" description="voc语义匹配地址" value="" index="99"/>
	 <param key="VOC_ANALYSIS_AGGREGATION_URL" name="voc情感分析聚类查询地址" type="string" description="voc情感分析聚类查询地址" value="" index="100"/>

	<param key="AIM_API_DOMAIN2" name="AIM智能短信接口域名（新）" type="string" description="AIM智能短信接口域名（新）" value=""/>
	<param key="AIM_API_DOMAIN" name="AIM智能短信接口域名" type="string" description="AIM智能短信接口域名" value=""/>
 	 <param key="AIM_API_QUERYAIMABILITY" name="AIM智能短信检查用户能力接口地址" type="string" description="AIM智能短信检查用户能力接口地址" value=""/>
 	 <param key="AIM_API_LISTECTEMPLATES" name="AIM智能短信查询模板接口地址" type="string" description="AIM智能短信查询模板接口地址" value=""/>
 	 <param key="AIM_API_APPLYAIMURL" name="AIM智能短信申请短链接口地址" type="string" description="AIM智能短信申请短链接口地址" value=""/>
 	 <param key="AIM_API_ACC" name="AIM智能短信接口鉴权账号" type="string" description="AIM智能短信接口鉴权账号" value=""/>
 	 <param key="AIM_API_PWD" name="AIM智能短信接口鉴权密码" type="string" description="AIM智能短信接口鉴权密码" value=""/>
 	 
	 <param key="ECM_NEW_URL" name="美云销接口新域名" type="string" description="美云销接口新域名" value="https://mcsp-api-sit.midea.com"/>
 	 
 	 <param key="LDAP_URL" name="ldap新接口域名" type="string" description="ldap新接口域名" value="https://idmapiuat.midea.com"/>
 	 <param key="LDAP_NEW_APPKEY" name="ldap新接口APPKEY" type="string" description="ldap新接口APPKEY" value=""/>
 	 <param key="LDAP_NEW_SECRETIDV2" name="ldap新接口SECRETID" type="string" description="ldap新接口SECRETID" value=""/>
 	 
 	 <param key="GVOC_PUSH_VISIT_BACK_URL" name="大数据voc提交回访记录接口" type="string" description="大数据voc提交回访记录接口" value="https://apiuat.midea.com/D-GVOC/gvoc-openapi/voc/openapi/cc/pushVisitBack"/>
 	 <param key="GVOC_APPID" name="大数据voc的APPID" type="string" description="大数据voc的APPID" value="4a155d6e196b4db39a37107d25fd5a97"/>
 	 <param key="GVOC_APPKEY" name="大数据voc的APPKEY" type="string" description="大数据voc的APPKEY" value="609527"/>

 	 <param key="MCSP_MEDIA_APPID" name="智慧家短链appId" type="string" description="智慧家短链appId" value=""/>
 	 <param key="GET_SM_BY_PHONE" name="智慧家短链获取前获取smToken" type="string" description="智慧家短链获取前获取smToken" value=""/>
 	 <param key="LONG_TO_SHORT" name="智慧家短链获取URL" type="string" description="智慧家短链获取URL" value=""/>

 	 <param key="ANDE_AICALL_RESULT_URL" name="安德物流回访结果推送地址" type="string" description="安德物流回访结果推送地址" value=""/>
	<param key="IS_OPEN_SENSITIVE_WORD" name="是否开启敏感词监控" type="string" description="是否开启敏感词监控" value="N"/>
 	 <param key="UNIFIED_GATEWAY_URL" name="统一网关URL" type="string" description="统一网关URL eg:https://ccuat.midea.com/uinterface/Receive.do 支持;分隔配置多个地址" value="https://ccuat.midea.com/uinterface/Receive.do"/>
	<param key="SENSITIVE_WORDS_QUALITY_URL" name="质检敏感词监控接口" type="string" description="质检敏感词监控接口" value=""/>
</config>
