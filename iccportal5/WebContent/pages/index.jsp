<%@ page import="com.yunqu.cc.iccportal5.base.Constants" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%--
<input type="hidden" value="<%=Constants.isOpen5gVideo()%>" id="isOpen5gVideo">
--%>
<%
	String isOpen5gVideoValue = (String)request.getAttribute("isOpen5gVideo");
%>

<!DOCTYPE HTML>
<html class="portal-body">
<head>
	<meta charset="utf-8">
	<meta name="renderer" content="webkit|ie-comp|ie-stand">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=0.5,maximum-scale=3.0,user-scalable=yes" />
	<title>美的.Midea</title>
	<link rel="stylesheet" href="/easitline-static/lib/bootstrap/css/bootstrap.min.css">
	<link href="/easitline-static/lib/font-awesome/css/font-awesome.min.css" rel="stylesheet">
    <link href="/easitline-static/lib/check/awesome-bootstrap-checkbox.css" rel="stylesheet">
    <link rel="stylesheet" href="${ctxPath}/static/css/indexSystem.css?t=1101v2">
    <link rel="stylesheet" href="${ctxPath}/static/css/chat.css">
	<link rel="stylesheet" href="${ctxPath}/static/css/icons.css">
	<script src="/easitline-static/js/jquery.min.js"></script>
	<script src="/easitline-static/lib/bootstrap/js/bootstrap.min.js"></script>

	<link type="text/css" rel="stylesheet" href="/easitline-static/lib/select2/css/select2-bootstrap.min.css" />
	<link type="text/css" rel="stylesheet" href="/easitline-static/lib/select2/css/select2.min.css" />
	<script type="text/javascript" src="/easitline-static/lib/select2/js/select2.min.js"></script>

	<script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
	<script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js?v=20180129"></script>
	<script type="text/javascript" src="/easitline-static/js/requreLib.js"></script>
	<script src="${ctxPath}/static/js/iscroll.js?v=20180207"></script>

	 <link rel="stylesheet" href="${ctxPath}/static/asset/css/modallayer.css">
	 <link rel="stylesheet" href="${ctxPath}/static/asset/css/skin/default.css">
    <script src="${ctxPath}/static/asset/js/source/util.js?t=20210330"></script>
    <script src="${ctxPath}/static/asset/js/source/modallayer.js?t=20210330"></script>

	<!-- ccbar相关脚本引入  开始-->
	<link rel="stylesheet" href="${entHost}/yc-ccbar/ccbar/ccbar.css">
	<link rel="stylesheet" href="${ctxPath}/static/css/ccbar-midea.css?t=0401v1">

	<script src="${entHost}/yc-ccbar/ccbar/ccbar.js?v=20190111" type="text/javascript"></script>
	<script src="${entHost}/yc-ccbar/ccbar/ccbar_websocket.js" type="text/javascript"></script>

	<script src="${ctxPath}/static/ccbar/ccbar_sdk.js?v=20250507" type="text/javascript"></script>
	<script src="${ctxPath}/static/ccbar/md5.js" type="text/javascript"></script>
	<!-- <script src="${ctxPath}/static/plugin/myCCbar.js" type="text/javascript"></script> -->
	<script src="${ctxPath}/static/plugin/ccbar-midea.js?v=20250424" type="text/javascript"></script>
	<% if (isOpen5gVideoValue.equals("1")) { %>
		<script src="${ctxPath}/static/ccbar/ccbar_webrtc.js" type="text/javascript"></script>
	<% } %>
<%--
	<script src="${ctxPath}/static/ccbar/ccbar_webrtc.js" type="text/javascript"></script>
--%>

	<script>
		(function(win, export_obj) {
		win['LogAnalyticsObject'] = export_obj;
		if (!win[export_obj]) {
		function _collect() {
		_collect.q.push(arguments);
		}
		_collect.q = _collect.q || [];
		win[export_obj] = _collect;
		}
		win[export_obj].l = +new Date();
		})(window, 'collectEvent');
	</script>
	<script async src="https://lf3-data.volccdn.com/obj/data-static/log-sdk/collect/5.0/collect-privity-v5.2.0.js"></script>
<%--	<script async src="https://mapnew5.midea.com/dataFinder-sdk/web-sdk.js"></script>--%>

	<!-- ccbar相关脚本引入  结束-->
	<style>
		body{min-width: 1350px!important;}
		.ccbar-box .ccbar-not-btn{cursor:not-allowed;}
		.ccbar-box .btn-holdCall{background-color:#17bbbb; color:#fff;}
		.ccbar-box .btn-unholdCall{background-color:#deb887; color:#fff;}
		.ccbar-box .btn-call{background-color:#ffda72; color:#fff;}
		.icon-outcall:before{content:''; background:url(static/images/ic_outcall.png) no-repeat; background-size:100% 100%;display:inline-block; width:16px; height:14px;vertical-align: middle;}
		.icon-holdCall:before{content:''; background:url(static/images/ic_keep.png) no-repeat; background-size:100% 100%;display:inline-block; width:16px; height:14px;vertical-align: middle;}
		.icon-unholdCall:before{content:''; background:url(static/images/ic_recover.png) no-repeat; background-size:100% 100%;display:inline-block; width:16px; height:14px;vertical-align: middle;}
		#chatWinBtn .chat-badge {
		  position: absolute;
		  top: 5px;
		  right: 4px;
		  z-index: 2;
		  box-sizing: border-box;
		  color: #fff;
		  background-color: red;
		  line-height: 14px;
		  font-size: 12px;
		  min-width: 14px;
		  text-align: center;
		  padding: 0 3px;
		  border-radius: 7px;
		}
		.hiddenChatPanel{
			display: none;
			position: fixed;
			z-index: 8887;
			height: 100%;
			width: 100%;
			top:0;
			left:0;
		}

		#ccbar_queueNum{
			position: absolute;
			left: 0;
    		top: -9px;
    		text-decoration: none;
			box-sizing: border-box;
		    color: #fff;
		    background-color: red;
		    line-height: 14px;
		    font-size: 12px;
		    min-width: 14px;
		    text-align: center;
		    padding: 0 3px;
		    border-radius: 7px;
		}
		#ccbar_queueNum:empty{display: none;}
		/*#transferIVR_BTN{display: none!important;}*/
		#transferIVR_BTN.fix_hide{display: none!important;}
		.modal-layer-show{
		-moz-user-select:none;
        -webkit-user-select:none;
        -ms-user-select:none;
        -khtml-user-select:none;
        user-select:none;
		}
		.modallayer-span::before{
		    content:none
		}
		.ccbar_panel_login{
		 height: 290px!important;
		}
		#modal-layer-minimize-taskbar{
		 width: 300px!important;
		}
		#modal-layer-minimize-taskbar >.modal-layer-minimize-item {
		 width: 100%!important;
		}

		#volteIcon{
			display: inline-block;
			width: 14px;
			height: 14px;
			background:#00ACFF url(${ctxPath}/static/ccbar/volte.png) no-repeat center;
			background-size: 10px auto;
			border-radius: 50%;
			display: none;
		}
	</style>

	<script>
		function selectText(element) {
	        var text = document.getElementById(element);
	        if (document.body.createTextRange) {
	            var range = document.body.createTextRange();
	            range.moveToElementText(text);
	            range.select();
	        } else if (window.getSelection) {
	            var selection = window.getSelection();
	            var range = document.createRange();
	            range.selectNodeContents(text);
	            selection.removeAllRanges();
	            selection.addRange(range);
	            /*if(selection.setBaseAndExtent){
	                selection.setBaseAndExtent(text, 0, text, 1);
	            }*/
	        } else {
	            alert("none");
	        }
	    }
        /* 全局埋点 */
        function global_menu_bury(ele) {
            console.log(ele, "ele1")
            if (top.collectEvent) {
                console.log(ele, "ele2")
                top.collectEvent("click_global_menu", {
                    element: ele,
                });
            }
        }
        function consultExpert_bury(ele) {
            console.log(ele, "ele1")
            if (top.collectEvent) {
                console.log(ele, "ele2")
                top.collectEvent("click_global_consultExpert", {
                    position: ele,
                });
            }
        }
        function voiceButton_bury(ele) {
            console.log(ele, "ele1")
            if (top.collectEvent) {
                console.log(ele, "ele2")
                top.collectEvent("click_global_voiceButton", {
                    element: ele,
                });
            }
        }
	</script>
</head>
<body >
    <!-- 聊天界面相关 -->
	<!-- 右下消息提示 -->
	<EasyTag:res resId="iccportal5_cb_consultation">
		<div class="chat-bottom-tips">
			<span id="expertsAsk-badge" class="chat-badge"></span>
			<a href="javascript:;" onclick="toggleChatPanel(this);consultExpert_bury('语音工作台')" class="chat-open-btn">
			   <i class="mIcon mMessageWhite"></i>
			</a>
		</div>
 	</EasyTag:res>

	<div id="portal-panel" class="portal-panel">
		<input type="hidden" value="<%=Constants.isOpen5gVideo()%>" id="isOpen5gVideo">
		<!-- 头部 -->
		<div class="portal-header">
			<a data-title="我的工作台-菜单" class="logo page-link"><img src="${ctxPath}/static/images/logo.png" alt="logo"></a>
			<div class="navbar-left-nav" id="userdate-box" style="padding: 5px 10px;line-height: 20px;position: absolute;left:120px;">
				<span id="skill_name" style="color:#919799;"></span>
				<span id="userdate-box-info" style="color:#919799;"></span>
				<!-- <span id="portal_curTitle">我的工作台-菜单</span> -->
			</div>
			<div class="navbar-left-nav" id="userdate-box-info" style="padding: 5px 10px;line-height: 20px;position: absolute;left:320px;">
				<span id="userdate-box-userinfo" style="color:#919799;"></span>
				<!-- <span id="portal_curTitle">我的工作台-菜单</span> -->
			</div>
			<div class="navbar-right-nav">
				<div id="ccbar" class="ccbar-box">
					<ul>
						<li class="ccbar-call dropdown ccbar-department needLogonFunc ccbarVoiceCtrl">
							<i class="iconfont icon-bumen"></i>
							<a href="javascript:void(0)" data-toggle="dropdown" onclick="voiceButton_bury('所属技能组')" data-hover="dropdown" class="userinfo-status" aria-expanded="false"> 
								<span class="cur-department" id="divisionName">请选择</span> <span class="caret"></span>
							</a>
							<ul class="dropdown-menu" id="divisionUL">
								  <li><a href="javascript:void(0)" onclick="javascript:changeDivision('','请选择')">请选择</a></li>
								<c:forEach items="${divisionDictList}"  var="dict">
								  <li><a href="javascript:void(0)" onclick="javascript:changeDivision('${dict.code }','${dict.name }')">${dict.name }</a></li>

							  </c:forEach>
					        </ul>
						</li>
						<li id="ccbar_callinfo"  class="ccbar-call ccbar-form needLogonFunc ccbarVoiceCtrl">
							<i class="iconfont icon-makecall"></i>
							<input type="text" id="customCalledInput" onfocus="voiceButton_bury('电话')" autocomplete="off" placeholder="外呼号码..." class="ccbar-input" />
							<!-- <a onclick="ccbarMidea.openAgentSel()" style="
							    padding: 3px 5px;
							    text-decoration: none;
							    cursor: pointer;
							    font-size: 12px;
							">| 选择坐席 <b class="caret"></b></a> -->
						</li>
						<li class="needLogonFunc ccbarVoiceCtrl">
							<span id="callNumb" class="ccbar-call needLogonFunc ccbarVoiceCtrl"></span>
							<a href="javascript:void(0)" id="customMakeCallBtn" title="外呼" onclick="voiceButton_bury('外呼')" data-ccbartype="makecall" class="ccbar-btn btn-makecall btn-hide-disabled disabled">
								<i class="iconfont icon-makecall"></i> 外呼
							</a>

							<a href="javascript:void(0)" data-title="咨询" onclick="voiceButton_bury('咨询')" data-toggle="tooltip" data-placement="bottom" data-ccbartype="consultationcall" class="ccbar-btn btn-agentready btn-hide-disabled disabled">
								<i class="iconfont icon-zixun"></i> 咨询
							</a>

							<a href="javascript:void(0)" data-title="转移" onclick="voiceButton_bury('转移')" data-toggle="tooltip" data-placement="bottom" data-ccbartype="sstransfer" class="ccbar-btn btn-agentready btn-hide-disabled disabled">
								<i class="iconfont icon-unholdCall"></i> 转移
							</a>

							<a href="javascript:void(0)" data-tag="true" onclick="voiceButton_bury('评价')" data-title="评价" data-toggle="tooltip" data-placement="bottom" data-ccbartype="sstransfer" id="transferIVR_BTN" class="ccbar-btn btn-agentready btn-hide-disabled disabled fix_hide">
								<i class="iconfont icon-unholdCall"></i> 评价
							</a>

							<a href="javascript:void(0)" data-title="三方通话" onclick="voiceButton_bury('三方')" data-toggle="tooltip" data-placement="bottom" data-ccbartype="conferencecall" class="ccbar-btn btn-makecall btn-hide-disabled disabled">
								<i class="iconfont icon-conferencecall"></i> 三方
							</a>


							<a href="javascript:void(0)" title="保持" data-toggle="tooltip" onclick="voiceButton_bury('保持')" data-placement="bottom"  data-ccbartype="holdcall" class="ccbar-btn btn-agentnotready btn-hide-disabled disabled" id="holdCallBtn">
								<i class="iconfont icon-holdCall"></i> 保持
							</a>

							<a href="javascript:void(0)" title="恢复" data-toggle="tooltip" onclick="voiceButton_bury('恢复')" data-placement="bottom"  data-ccbartype="unholdcall" class="ccbar-btn btn-agentready btn-hide-disabled disabled" id="unholdCallBtn">
								<i class="iconfont icon-unholdCall"></i> 恢复
							</a>

							<a href="javascript:void(0)" title="挂机" data-ccbartype="clearcall" onclick="voiceButton_bury('挂机')" class="ccbar-btn btn-clearcall disabled">
								<i class="iconfont icon-clearcall"></i> 挂机
							</a>
						</li>
						<li class="ccbar-split ccbar-info">
							<label class="ccbar-label">状态：<span style="text-align: center;"><i id="volteIcon"></i><font data-agentinfo="curstatus"  class="curstatus cur-agentstate">未签入</font></span></label>
							<label class="ccbar-label">时长：<span id="" data-ccbar-text="clock" style="color:#f91145">00:00:00</span></label>
						</li>
						<li class="ccbar-split ccbar-info needLogonFunc">
							<label class="ccbar-label">通话时长</label>
							<label class="ccbar-label"><span id="" data-ccbar-text="sum_clock" style="color:#f91145">00:00:00</span></label>
						</li>
						<li class="ccbar-split">





							<a href="javascript:void(0)" title="结束监听" data-toggle="tooltip" data-placement="bottom"  data-ccbartype="stopMonitor" class="ccbar-btn btn-agentready disabled">
								结束监听
							</a>
							<a href="javascript:void(0)" title="结束强插" data-toggle="tooltip" data-placement="bottom"  data-ccbartype="stopInvent" class="ccbar-btn btn-agentready disabled">
								结束强插
							</a>
							<a href="javascript:void(0)" title="结束话后整理" data-toggle="tooltip" data-placement="bottom"  data-ccbartype="workReady" class="ccbar-btn btn-agentready disabled">
								结束整理
							</a>

							<div class="agent-btn-group needLogonFunc dropdown">
								<a href="javascript:void(0)" title="坐席置闲" onclick="voiceButton_bury('置闲')"  data-ccbartype="agentready" class="ccbar-btn btn-agentready disabled">
									<i class="iconfont icon-xiaoxiu"></i> 置闲
								</a>

								<a href="javascript:void(0)" title="坐席置忙" onclick="voiceButton_bury('小休')"  data-ccbartype="agentnotready" class="ccbar-btn btn-agentnotready disabled " >
								<i class="iconfont icon-fanmang"></i> 小休
								</a>

								<a href="javascript:void(0)" title="坐席置忙" onclick="voiceButton_bury('可呼')" data-busytype="2" data-ccbartype="agentnotready" class="ccbar-btn btn-agentnotready disabled ccbarVoiceCtrl" style="border-radius: 0 10px 10px 0!important;">
								<i class="iconfont icon-fanmang"></i> 可呼
								</a>

								<!-- <span class="ccbarVoiceCtrl">
								<a href="javascript:void(0)"  data-toggle="dropdown" data-hover="dropdown" data-tag="true" data-title="坐席置忙" data-toggle="tooltip" data-placement="bottom"  data-ccbartype="agentnotready" class="ccbar-btn btn-agentnotready disabled" style="border-radius: 0 10px 10px 0!important;">
									<i class="iconfont icon-fanmang"></i> 置忙 <b class="caret"></b>
								</a>
								<ul class="dropdown-menu dropdown-menu-right" style="min-width: 80px;text-align: center;">
									<li>
										<a href="javascript:;" data-ccbartype="agentnotready" data-busytype="1">小休</a></li>
									<li>
										<a href="javascript:;" data-ccbartype="agentnotready" data-busytype="2">可呼</a>
									</li>

								</ul>
								</span> -->
							</div>




							<a href="javascript:void(0)" id="agent" onclick="voiceButton_bury(this.querySelector('span').innerText)" class="ccbar-btn btn-agent logoff">
								<i class="iconfont icon-jiuxu"></i> <span>签入</span>
							</a>

							 <div id="login-input" class="ccbr-login-input">
								<div id="voiceAgent" class="ccbr-login-agenttype" style="margin-bottom: 5px">
									<label for="voiceSwitch" class="mediaSwitch">
										<input type="radio" id="voiceSwitch" checked name="agenttype" value="voiceSwitch"/>
										<i class="radio-icon"></i>
										<span>语音坐席</span>
									</label>
									 <!-- <input id="ccbar-phone-agentId" value="600003@${entId}" placeholder="坐席工号/测试" type="text" class="login-input">  -->
									<input id="ccbar-phone-input" value="${userInfo.extensionNumber}" placeholder="话机号码" type="text" class="login-input">
								<!-- ${userInfo.userNo} -->
									<!-- <select id="ccbar-phone-select" placeholder="话机号码" type="text" class="login-input"></select> -->
								</div>

								<div id="multiMediaAgent" class="ccbr-login-agenttype">
									<label for="multiMediaSwitch" class="mediaSwitch">
										<input type="radio" id="multiMediaSwitch" name="agenttype" value="multiMediaSwitch"/>
										<i class="radio-icon"></i>
										<span>全媒体坐席</span>
									</label>

								</div>
								<a href="javascript:;" class="login-btn">签入</a>
							</div>
						</li>

					</ul>

				</div>

				<!-- 头像 -->
				<span class="navbar-userinfo dropdown">
					<a href="javascript:;" title="当前排队数" id="ccbar_queueNum" class="user-que-tips" style="display: none"></a>
					<img src="${ctxPath}/static/images/user.png" class="user-avatar">
					<a href="javascript:void(0)" data-toggle="dropdown" data-hover="dropdown" class="userinfo-name"><span class="caret"></span></a>
					<ul class="dropdown-menu" style="right: 0;left: inherit;top:30px;">
			            <li><a href="javascript:void(0)" onclick="javascript:personInfo();" class="iframe-href" data-title="个人信息">个人信息</a></li>
			            <li><a href="javascript:void(0)" onclick="javascript:modifyPwd();" class="iframe-href" data-title="修改密码">修改密码</a></li>
			           	<li >
			           		<div class="dropdown" style="display: none;">
<%--			           		<div class="dropdown">--%>
				           		<label class="checkbox checkbox-success needLogonFunc ccbarVoiceCtrl checkbox-inline" style="line-height: 18px;vertical-align: middle;">
								   		<input id="autoanswercall" type="checkbox" name="autoanswercall" name="agentState" /> <span style="padding-left: 1px"> 自动应答 </span>
								 </label>
			           		</div>
			            </li>
			            <li><a href="javascript:void(0)" onclick="javascript:getfailList();" data-title="未接来\去列表">未接来\去列表</a></li>
			           	<c:if test="${ not  empty  updateMipPwdUrl}">
			           		<li><a href="javascript:void(0)" onclick="javascript: window.open('${updateMipPwdUrl}');" class="iframe-href" data-title="修改密码">修改MIP密码</a></li>
			           	</c:if>
			           	<li><a href="javascript:void(0)" onclick="javascript:getCurrentNumber();" data-title="外呼号码">外显号码</a></li>
						<li><a href="javascript:void(0)" onclick="javascript:getCurrentEmployee();" data-title="所属主体">所属主体</a></li>
			            <li><a href="javascript:void(0)" onclick="javascript:myCCbar.forceLogoff('${userInfo.userNo}@${entId}');" data-title="强制签出">强制签出</a></li>
			            <li><a href="javascript:void(0)" onclick="delCssToken();myCCbar.logoff();">退出</a></li>
			        </ul>
				</span>
			</div>




		</div>
		<!-- 侧栏 -->
		<div class="portal-sidebar">
			<!-- 用户信息 -->
			<div class="sidebar-userinfo"></div>
			<ul class="sidebar-nav">
				<li class="cur"><a href="javascript:void(0);" data-url="${ctxPath}/pages/portal.jsp" onclick="global_menu_bury('首页')" data-title="首页" data-toggle="tooltip" data-close="disabled" data-container="body" data-placement="right"  title="首页" class="page-link"><i class="mIcon mMenu mMenuHome"></i></a></li>

				<EasyTag:res resId="iccportal5_cb_online">
					<li><a href="javascript:void(0);" data-id="agentChatIframe" data-url="${mediaPortalUrl}" onclick="global_menu_bury('全媒体工作台')" data-title="全媒体工作台" data-toggle="tooltip" data-container="body" data-placement="right" data-reload="false" data-close="disabled" title="全媒体工作台" class="page-link"><i class="mIcon mMenu mMenuMultiMedia"></i></a></li>
				</EasyTag:res>

				<!-- 来电自动弹出 -->
				<EasyTag:res resId="iccportal5_cb_voice">
					<li><a href="javascript:void(0);" data-url="${ctxPath}/portal?action=voice&phoneNum=" onclick="global_menu_bury('语音工作台')" data-title="语音工作台" data-toggle="tooltip" data-container="body" data-placement="right"  data-close="disabled" title="语音工作台" class="page-link" data-id="voiceMenu" id="voiceMenu"><i class="mIcon mMenu mMenuVoice"></i></a></li>
				</EasyTag:res>

				<!-- 上线后再植入该功能 -->
				<EasyTag:res resId="iccportal5_cb_note">
					<li><a href="javascript:openNoteIndex();" data-url="" onclick="global_menu_bury('知识库')" data-title="知识库" data-toggle="tooltip" data-container="body" data-placement="right" title="知识库" class="page-link" ><i class="mIcon mMenu mMenuItem7"></i></a></li>
				</EasyTag:res>

				<li><a href="javascript:void(0);" data-url="${ctxPath}/Servlet/userRes?action=userMenu" onclick="global_menu_bury('业务导航')" data-title="业务导航" data-toggle="tooltip" data-container="body" data-placement="right" title="业务导航" class="page-link"><i class="mIcon mMenu mMenuItem6"></i></a></li>

					<%--  <div class="list-column-main" style="top: -115px;" >
						<div class="scroll-box" >
							 <c:forEach items="${userRessList}" varStatus="resslist" var="userRess">
								  <div class="list-column">
									<div class="level-1-menu">${userRess.resName }</div>
									    <ul class="menu">
											<c:forEach items="${userRess.childs }" varStatus="list2" var="userRess2">
												<c:choose>
													<c:when test="${fn:length(userRess2.childs)==0}">
														<li class="level-3-menu"><a class="page-link" href="javascript:;" data-url="${userRess2.resUrl }" data-title="${userRess2.resName }">${userRess2.resName }</a></li>
													</c:when>
													<c:otherwise>
													    <li class="br"></li>
														<li class="level-2-menu">${userRess2.resName }</li>
														<c:forEach items="${userRess2.childs }" varStatus="list3" var="userRess3"><!-- 三级子菜单 -->
															<li class="level-3-menu"><a class="page-link" href="javascript:;" data-url="${userRess3.resUrl }" data-title="${userRess3.resName }">${userRess3.resName }</a></li>
														</c:forEach>
													</c:otherwise>
												</c:choose>
											</c:forEach>
									    </ul>
								  </div>
							  </c:forEach>


						</div>
					</div> --%>


				<li style="display: none;"><a href="javascript:void(0);" data-url="${ctxPath}/pages/质检.html" onclick="global_menu_bury('质检')" data-title="质检" data-toggle="tooltip" data-container="body" data-placement="right" title="质检" class="page-link"><i class="mIcon mMenu mMenuItem8"></i></a></li>

				<EasyTag:res resId="iccportal5_cb_webim">
					<li style="display: block;" id="chatWinBtn"><span class="chat-badge"></span><a href="javascript:openChatWin()" data-url="" onclick="global_menu_bury('席间交流')" data-title="席间交流" data-toggle="tooltip" data-container="body" data-placement="right" title="席间交流" class=""><i class="mIcon mMenu mMenuItem9"></i></a></li>
				</EasyTag:res>

				<EasyTag:res resId="iccportal5_cb_ami">
					<li style="display: block;" id="amiWinBtn"><span class="chat-badge"></span><a href="javascript:doShowAMI();" data-url="" onclick="global_menu_bury('语音转文本')" data-title="语音转文本" data-toggle="tooltip" data-container="body" data-placement="right" title="语音转文本" class=""><i class="mIcon mMenu mMenuItem5"></i></a></li>
				</EasyTag:res>

				<EasyTag:res resId="iccportal5_cb_css1">
					<li><a href="javascript:delCssToken();openCSS1();" data-url="" onclick="global_menu_bury('CSS用户建议')" data-title="CSS用户建议" data-toggle="tooltip" data-container="body" data-placement="right" title="CSS用户建议" class="page-link" ><i class="mIcon mMenu mMenuItem7"></i></a></li>
				</EasyTag:res>
				<EasyTag:res resId="iccportal5_cb_css2">
					<li><a href="javascript:delCssToken();openCSS2();" data-url="" onclick="global_menu_bury('CSS用户咨询投诉单')" data-title="CSS用户咨询投诉单" data-toggle="tooltip" data-container="body" data-placement="right" title="CSS用户咨询投诉单" class="page-link" ><i class="mIcon mMenu mMenuItem8"></i></a></li>
				</EasyTag:res>
			</ul>
		</div>
		<!-- 主体 -->
		<div class="portal-main">
			<!-- 页面模块 -->
			<!-- 单独的模块页面 -->
			<div id="portal_pages" class="module-group" style="display:none">

			</div>

			<!-- 选项卡页面组 -->
			<div id="portal_pages_group" class="pages-group-tabs-box">
				<div class="pages-group-tabs-header">
					<!-- tab条 -->
	                <nav class="contabs contabs-open" id="siteConTabs">
	                    <button id="tabMoveLeft" type="button" class="btn btn-icon btn-default pull-left hide">
	                        <i class="icon ycicon ycicon-double-left"></i>
	                    </button>
	                    <div class="contabs-scroll pull-left">
	                        <ul class="nav con-tabs">
	                            <li class="active noclose" data-id="" data-url="${ctxPath}/pages/portal.jsp" data-type="iframe">
	                                 <a data-id="" data-url="${ctxPath}/pages/portal.jsp" title="首页" rel="contents">
	                                    <span>首页</span>
	                                 </a>
	                            </li>
	                        </ul>
	                    </div>
	                    <div  id="tabDropdownBtns" class="btn-group pull-right">
	                        <button id="tabMoveRight" type="button" class="btn btn-icon btn-default hide">
	                            <i class="icon ycicon ycicon-double-right"></i>
	                        </button>
	                        <button type="button" class="btn btn-default dropdown-toggle btn-outline" data-toggle="dropdown" aria-expanded="false">
	                            <span class="caret"></span> <span class="sr-only">切换菜单</span>
	                        </button>
	                        <ul class="dropdown-menu dropdown-menu-right dropdown-icon" aria-labelledby="conTabsDropdown" role="menu">
	                            <li class="reload-page">
									<div class="addon">
										<i class="iconfont icon-flush"></i>
									</div>
									<a href="javascript:void(0)">刷新当前</a>
								</li>
								<li class="close-other">
									<div class="addon">
										<i class="iconfont icon-close"></i>
									</div>
									<a href="javascript:void(0)">关闭其他</a>
								</li>
	                            <li class="close-all">
									<div class="addon">
										<i class="iconfont icon-end"></i>
									</div>
									<a href="javascript:void(0)">关闭所有</a>
								</li>
	                        </ul>
	                    </div>
	                </nav>
				</div>
				<div class="pages-group-tabs-content">
					<div id="pageLoader" class="page-container-list pageLoaderBox">
						<div data-id="" data-type="iframe" data-title="首页" data-url="${ctxPath}/pages/portal.jsp" class="loader-page active">
						      <iframe name="targetIframe" src="${ctxPath}/pages/portal.jsp" style="border: 0;width: 100%;height: 100%;" frameborder="0"></iframe>
					    </div>
					</div>
				</div>

			</div>
		</div>
	</div>
	<iframe  id="logintrack_iframe" src="/iccportal5/index?action=logintrack" style="display: none"></iframe>
	<!-- 隐藏席间交流 -->
	<div class="hiddenChatPanel" onclick="hideChatWin()"></div>
    <script type="text/javascript" src="/easitline-static/lib/jquery/jquery.slimscroll.min.js"></script>
	<script src="${ctxPath}/static/js/portal.js?v=1026"></script>
	<script>
		var isOpen5gVideo = "${isOpen5gVideo}";
		$(function(){
			getUserInfo()
			agentBirthday();
			agentClockRemind();

		})
		$(document).ready(function(){
			// 根据坐席账号查询工号，再根据工号查询yc-base拿到是否自动应答状态，然后赋值autoAnswer
			var val = $(this).prop('checked');
			ajax.remoteCall("/agentconfig/webcall?action=user.getAutoAnswer", {}, function (result) {
				console.log(result)
				if (result.state === 1){
					val = result.data === null ? false : result.data;
					$("#autoanswercall").prop('checked',val);
				}
			});
			$(document).off('keydown');
			if($("#divisionUL").find("li a").length>0) $("#divisionUL").find("li a")[0].click();
			openChatWin();
			hideChatWin();

			toggleChatPanel($(".chat-open-btn"));toggleChatPanel($(".chat-open-btn")); //加载专家咨询
	    });

		//打开专家聊天
		var codeGroup={
				'hw001':{code:'60000',phone:'50000'},
				'test002':{code:'60001',phone:'50001'},
				'test003':{code:'60002',phone:'50002'},
				'test004':{code:'60003',phone:'50003'},
				'test005':{code:'60004',phone:'50004'},
				'admin@mars':{code:'60000',phone:'50000'}
		}
		// myCCbar.config('agent',{
		// 	account:'${userInfo.userAcc}',
		// 	code:'${userInfo.userNo}',
		// 	name:'${userInfo.userName}',
		// 	phone:'${userInfo.extensionNumber}'
		// });
		<%--
		//myCCbar.config('phone','${userInfo.extensionNumber}'||codeGroup['${userInfo.userAcc}'].phone);
		//myCCbar.config('code',codeGroup['${userInfo.userAcc}'].code+'@${entId}');
		--%>
		// myCCbar.config('phone','${userInfo.extensionNumber}');
		// myCCbar.config('code','${userInfo.userNo}'+'@${entId}');
		ccbarMidea.config.code='${userInfo.userNo}'+'@'+'${entId}';
		ccbarMidea.config.userNo='${userInfo.userNo}';
		ccbarMidea.config.pass='${agentPwd}';
		ccbarMidea.config.phone='${userInfo.extensionNumber}';
		ccbarMidea.config.userAcc='${userInfo.userAcc}';
		ccbarMidea.config.nickname='${userInfo.userName}';
		ccbarMidea.config.key='${entKey}';
		ccbarMidea.config.eid='${entId}';
		ccbarMidea.config.pid='${entPId}';
		ccbarMidea.config.host='${entHost}';
		ccbarMidea.config.mediagwUrl = '${mediagwUrl}';
		var ccbar_service_list = '${ccbarDomainList}';
		try{
			ccbarMidea.init();
		}catch(e){
			console.log('ccbar init error',e);
		}


		window.onload=function(){$(".scroll-box").slimScroll({color:'#aaa',height:800}); }
		/*专家咨询*/
		var _askPanel = null;
		var _askPanel_show = false
		var _modallayer;
		function toggleChatPanel(obj){
			if($(obj).length){
				$(obj).find("i").toggleClass("mMessageWhite mClose2")
			}
			if(!_askPanel){
				/* _askPanel = layer.open({id:'_askPanel_' ,type:2,zIndex:221,offset:'rb',shade:false,shadeClose:false,maxmin:true,type:2,title:"专家咨询",closeBtn:0,content:'/online/pages/portal/expertsAsk.html?v=1015',resize:true,area:['520px','360px'],success:function(){
					$("body").on('keydown',function(e){
						if(e.keyCode == 27){
							if(_askPanel_show){
								$(".chat-open-btn>i").toggleClass("mMessageWhite mClose2");
								_askPanel_show = false;
								$("#_askPanel_").addClass('hideExpertsAsk')
								$("#_askPanel_").parent().css({'zIndex':-1,'opacity':0});
								return false;
							}
						}

					});
					$("#_askPanel_").parent().css({
						top:'inherit',
						left:'inherit',
						right:60,
						bottom:20
					});
					$("#_askPanel_").siblings(".layui-layer-resize").addClass('expertsAsk-resize')
				}});
				_askPanel_show = true; */
				 var option = {
				      mask: false,
				      title: '专家咨询',
				      popupTime: 0,
				      contentFullContainer: true,
				      pageArea: [500, 350],
				      parentModalLayer:'portal_pages_group',
				      dragOverflow: true,
				      resize: true,
				      pageOption: {
				        scrolling: 'yes',
				        src: '/online/pages/portal/expertsAsk.html?v=1015'
				      }
				    }
				_askPanel=ModalLayer.page(option);
				_askPanel_show = true;
				$($(".fa-window-minimize")[0]).html('<a class="layui-layer-min" href="javascript:;"><cite></cite></a>');
				$($(".fa-expand-arrows-alt")[0]).html('<a class="layui-layer-ico layui-layer-max" href="javascript:;"></a>');
				$(".modal-layer-show").hide();
				$(".modal-layer-action-close").hide();
				document.getElementById('modal-layer-container').style.marginLeft = document.body.clientWidth-550+"px";
				document.getElementById('modal-layer-container').style.marginTop = document.body.clientHeight-450 +"px";
				/* $("body").on('keydown',function(e){
					if(e.keyCode == 27){
						if(_askPanel_show){
							$(".chat-open-btn>i").toggleClass("mMessageWhite mClose2");
							_askPanel_show = false;
							$("#_askPanel_").addClass('hideExpertsAsk')
							$("#_askPanel_").parent().css({'zIndex':-1,'opacity':0});
							return false;
						}
					}
				});
				$("#_askPanel_").parent().css({
					top:'inherit',
					left:'inherit',
					right:60,
					bottom:20
				});
				$("#_askPanel_").siblings(".layui-layer-resize").addClass('expertsAsk-resize') */
			}else{
				if(_askPanel_show){
					$('.modal-layer-show').hide();
					_askPanel_show = false;
					//$("#_askPanel_").addClass('hideExpertsAsk')
					//$("#_askPanel_").parent().css({'zIndex':-1,'opacity':0});
				}else{
					$('.modal-layer-show').show()
					_askPanel_show = true;
					//$("#_askPanel_").removeClass('hideExpertsAsk')
					//$("#_askPanel_").parent().css({'zIndex':221,'opacity':1});
				}
			}
		}
		$(function(){
			$("body").on('resize', function(event) {
				$("#_askPanel_").parent().css({
						top:'inherit',
						left:'inherit',
						right:60,
						bottom:20
					})
			});
		});
		//席间聊天
		var isOpenChat = false,isShowChat = false;
		var isFixedChatPanel = false;
		function openChatWin(){
			var callback_success = function(){
				isOpenChat = true;
			}
			var callback_cancel = function(){
				isOpenChat = false;
			}
			if(isShowChat){
				hideChatWin();
			}else{
				showChatWin();
			}

			if(isOpenChat){
				return;
			}
			popup.layerShow(
					{
						id:"chatWinLayerContent",
						shade:false,
						shadeClose:false,
						type:2,
						title:"席间交流",
						area:['80%','90%'],
						offset:'auto',
						zIndex:8888,
						maxmin:false,
						closeBtn:0,
						success:callback_success,
						end:callback_cancel,
						resizing:chatWinResizingCallback
					},
				"/webim/pages/index.jsp?agentId=${userInfo.userAcc}");
			isShowChat = true;
			//popup.layerShow({type:2,title:"",area:['955px','90%'],offset:'auto',maxmin:false,closeBtn:0,move:'.chat-agent-header'},"/webim/pages/index.jsp?agentId=${userInfo.userAcc}");
		}
		function callNumb() {
    ajax.remoteCall("${ctxPath}/webcall?action=callDao.getUserNumber", {}, function(result) {
        var datas = result.data.UserNumber.data;
        var dicts = result.data.dict.data;
        var currentNumber = datas.CURRENT_NUMBER;
        var numberlist = datas.NUMBER_LIST;
		var currentNumberName = dicts[currentNumber]
		if(!currentNumberName&&numberlist){
			var defaultNumber = numberlist.split(",")[0];
			currentNumber = dicts[defaultNumber]
		}
        $("#callNumb").html(currentNumberName);
    });
}

		// 等待文档加载完成后执行
		document.addEventListener('DOMContentLoaded', function() {
			// 获取按钮元素
			var loginBtn = document.querySelector('.login-btn');

			// 添加点击事件监听器
			loginBtn.addEventListener('click', function(event) {
				// // 阻止默认的链接跳转行为
				// event.preventDefault();

				callNumb();
			});
		});

		//提供给其他页面打开席间交流并发起会话
		var openLaunchChatFunc = null;
		var openLaunchChat = function(userAcct){
			try {
				showChatWin();
				openLaunchChatFunc&&openLaunchChatFunc(userAcct);
			} catch (e) {
			}
		}
		//席间交流窗口缩放
		var chatWinResizing = null;
		var chatWinResizingCallback = function(layero){
			try {
				chatWinResizing&&chatWinResizing(layero);
			} catch (e) {
			}
		}
		function getUserInfo(){
			ajax.remoteCall("${ctxPath}/Servlet/userRes?action=getUserInfo",'',function(result) {
			if(result.state==1){
			var menuObj = JSON.parse(result.data);

			initCollectEvent(menuObj.userAcc,menuObj.collectAddr)

	//监听当前打开页面进行父子页面传参（20250122）
				window.addEventListener('message', function (event) {
					let data = event.data;
					if (data.cmd === 'getUserInfo') {
						const iframe1 = document.querySelector(".loader-page.active").querySelector("iframe");
						try {
							if (iframe1 && iframe1.contentWindow) {
							iframe1.contentWindow.postMessage({'cmd': 'userInfo', 'userCode': menuObj.userAcc||""}, '*');//向子页面推送用户数据
							} else {
							throw new Error('Iframe or contentWindow is not available');
							}
						} catch (error) {
							console.error('Error sending message:', error);
						}
					}
				})
			}
			});
		}
		//初始化埋点sdk
		
		function initCollectEvent(userAcc,collectAddr) {
			let app_id = collectAddr?Number(collectAddr):''
			console.log(app_id,"app_id")
			window.collectEvent('init', {
			app_id: app_id||'', // 参考2.1节获取，注意类型是number而非字符串
			channel_domain: 'https://iotsdk.midea.com', // 设置私有化部署数据上送地址，参考2.2节获取
			log: true, // true:开启日志，false:关闭日志
			autotrack: false, // 全埋点开关，true开启，false关闭
			});
			// 此处可添加设置uuid、设置公共属性等代码
			window.collectEvent('config', {
			user_unique_id: userAcc
			});
			window.collectEvent('start'); // 通知SDK设置完毕，可以真正开始发送事件了
		}
		//隐藏席间交流
		function hideChatWin(){
			if(isFixedChatPanel){
				$(".hiddenChatPanel").hide();
				return;
			}
			isShowChat = false;
			$("#chatWinLayerContent").parent().hide();
			$(".hiddenChatPanel").hide();
		}
		//显示席间交流
		function showChatWin(){
			isShowChat = true;
			$("#chatWinLayerContent").parent().show();
			//showUnReadNum(0);
			$(".hiddenChatPanel").show();
		}
		//显示未读消息数
		function showUnReadNum(num){
			if(!num||num<=0||num=="0"){
				$("#chatWinBtn .chat-badge").text("");
				return;
			}
			if(num>99){
				$("#chatWinBtn .chat-badge").text("99+");
				return;
			}
			$("#chatWinBtn .chat-badge").text(num);
		}

		function setIsFixedChatPanel(target){
			if(!target){
				$(".hiddenChatPanel").show();
			}
			isFixedChatPanel = target;
		}
		//个人资料
		function personInfo(){
			popup.layerShow({type:1,title:'个人资料',area:['430px','360px'],offset:'100px'},"${ctxPath}/pages/person-info.jsp");
		}
		//修改密码
		function modifyPwd(){
			popup.layerShow({type:1,title:'修改密码',area:['400px','300px'],offset:'100px'},"${ctxPath}/pages/password-modify.jsp");
		}

		//打开知识库首页
		function openNoteIndex(){
			var url = "/slinfogw/jump?type=noteIndex&userAcc=${userInfo.userAcc }";
			window.open(url,'_blank');
		}
		//打开css用户建议
		function openCSS1(){
			var url = "Servlet/css?action=CSS1";
			window.open(url,'_blank');
		}
		//打开css用户建议
		function openCSS2(){
			var url = "Servlet/css?action=CSS2";
			window.open(url,'_blank');
		}
		function delCssToken(){
			 document.cookie = 'midea_sso_token=0;path=/;domain=midea.com;expires=' + new Date(0).toUTCString();
			 document.cookie = 'mideatest_sso_token=0;path=/;domain=midea.com;expires=' + new Date(0).toUTCString();
		}
	    function callChildrenFunc(funcName,iframeId,args){
		    document.getElementById(iframeId).contentWindow[funcName](args);
		}
	    function changeDivision(code,name){
	    	$("#divisionName").html(name);
	    	$("#divisionName").data('code',code);

	    }
	</script>
	<script type="text/javascript" src="${ctxPath}/static/plugin/amiAPI-1.0.js?t=20210329"></script>
	<script type="text/javascript" src="${ctxPath}/static/plugin/Client.js?t=20210329"></script>
	<script>
		var isShowAmi=false;
		$(function(){
			doShowAMI();//第一次启动
			doShowAMI();//第二次隐藏
		})
		//ami
		window.AMIOnReady=function(handler){
			if(handler){
				var phone=ccbarMidea.config.phone||top.document.getElementById('ccbar-phone-input');
				console.log('初始化AMI',phone);
				$('#selection').html('');
				handler.login('api','Ami_Csr@2018',phone);
				window.onunload=function(){
					handler.logout();
				}
				handler.onUpdate=function(data){
					console.log('更新ami内容：',data);
					if(data) $('#amiWindow')[0].children[0].contentWindow.voiceToWord(data);
				}
			}
		};
		function doShowAMI(){
			$('#amiWindow').parents('.layui-layer').toggle();
			if(isShowAmi) return;
			popup.layerShow({id:"amiWindow",shade:false,shadeClose:false,type:2,title:"实时语音转文本",area:['550px','450px'],offset:'auto',zIndex:999999,maxmin:true,closeBtn:0,success:function(e){

			},end:function(e){

			}},"${ctxPath}/pages/ami.jsp");
			isShowAmi=true;
		}
		//cookie
		function setCookie(cname,cvalue){
			setCookieTime(cname,cvalue,24)


		}
		//cookie
		function setCookieTime(cname,cvalue,time){
			 var failList=getCookie("failList");
			var d = new Date();
			d.setTime(d.getTime()+(time*60*60*1000));
			var expires = "expires="+d.toGMTString();
			document.cookie = cname+"="+cvalue+"; "+expires;

		}
		function getCookie(cname){
			var name = cname + "=";
			var ca = document.cookie.split(';');
			for(var i=0; i<ca.length; i++) {
				var c = ca[i].trim();
				if (c.indexOf(name)==0) { return c.substring(name.length,c.length); }
			}
			return "";
		}
		//保存来/去电的记录
		function setFailList(caller,called){
			ajax.remoteCall("${ctxPath}/index?action=missedCall",{caller:caller,called:called},function(result) {

			});

		}
		//获取来去记录

		function getfailList(){
			popup.layerShow({type:2,title:'未接来\去电列表',area:['630px','760px'],offset:'100px'},"${ctxPath}/pages/fail-call-list.jsp");
		}
		function GetDate(format) {
			  var date = new Date();
              var seperator1 = "-";
              var seperator2 = ":";
              var month = getNewDate(date.getMonth() + 1);
              var day = getNewDate(date.getDate());
              var hours = getNewDate(date.getHours());
              var minutes = getNewDate(date.getMinutes());
              var seconds = getNewDate(date.getSeconds());
              //统一格式为两位数
              function getNewDate(date) {
                  if (date <= 9) {
                      date = "0" + date;
                  }
                  return date;
              }
              var currentDate = date.getFullYear() + seperator1 + month + seperator1 + day
                  + " " + hours + seperator2 + minutes + seperator2 + seconds;
              return currentDate;
		}
		//外呼默认事业部
		function agentDepartment(){
			if('${agentDepartment}'==""){
				changeDivision('','请选择');
			}else{
				changeDivision('${agentDepartment}','${agentDepartmentName}');
			}
		}
		//是否员工生日
		function agentBirthday(){
			var Birthday= getCookie("birthday-${userInfo.userAcc}");
			if(Birthday==""){//没有提醒过
				ajax.remoteCall("${ctxPath}/index?action=birthday",null,function(result) {
					if(result){
						birthday();
						setCookie("birthday-${userInfo.userAcc}","1")
					}
				});
			}
		}
		//生日提醒
		function birthday(){
			popup.layerShow({
				type : 1,
				title : "新增",
				offset : '20',
				area : [ '1280px', '740px' ]
			}, "/config/servlet/birthday?action=Birthday", null);
		}

		// 是否需要打卡提醒
		function agentClockRemind() {
			var clock= getCookie("clock-${userInfo.userAcc}");
			if(clock==""){//没有提醒过
				ajax.remoteCall("${ctxPath}/index?action=clockRemind",null,function(result) {
					if(result == true){
						clockRemind();
						setCookie("clock-${userInfo.userAcc}","1")
					}
				});
			}
		}

		//打卡提醒
		function clockRemind(){
			layer.alert('您好，当天您未进行上班打卡哦！！！');
		}


		//外显号码选择
		function getCurrentNumber(){
			popup.layerShow({type:1,title:'外显号码',area:['400px','200px'],offset:'100px'},"${ctxPath}/pages/select-current-number.jsp");
		}
		function getCurrentEmployee(){
		popup.layerShow({type:1,title:'所属主体',area:['400px','200px'],offset:'100px'},"${ctxPath}/pages/select-current-employee.jsp");
		}
	</script>


	<!-- 专家咨询 -->
	<!-- <link rel="stylesheet" href="/online/static/css/expertsAsk.css"> -->
	<!-- <script src="/online/static/js/chat/expertsAsk.js"></script> -->
</body>
</html>