 <%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<!DOCTYPE HTML>
<html class="">
<head>
	<meta charset="utf-8">
	<meta name="renderer" content="webkit|ie-comp|ie-stand">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
	<title>首页-全媒体坐席-班长</title>
	<link rel="stylesheet" href="/easitline-static/lib/bootstrap/css/bootstrap.min.css"> 
	<script src="/easitline-static/js/jquery.min.js"></script>
	<script src="/easitline-static/lib/bootstrap/js/bootstrap.min.js"></script>
	<script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
	<script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js?v=20180129"></script>
	<script type="text/javascript" src="/yq_common/static/js/yq/extends.js"></script>
	<link rel="stylesheet" href="${ctxPath}/static/css/system.css">
	<link rel="stylesheet" href="${ctxPath}/static/lib/calendar/css/calendar.css">
	<link rel="stylesheet" href="${ctxPath}/static/css/icons.css">
	<style>
	body{min-width: 1500px}
	    .layer-foot{bottom: 0;
				    left: 0;
				    position: absolute;
				    width: 100%;
				    height: 48px;
				    line-height: 45px;
				    z-index: 2;
				    background-color: #f8f8f8;
				    text-align: center;
				    border-top: 1px solid #eee;}
		.shenglue{
			overflow: hidden;
			text-overflow:ellipsis;
			white-space: nowrap;
		}
		.list-callinfo-col2 li{width:50%;}
		.list-callinfo li > .box{height:72px; overflow:hidden;}
		.list-tabel{margin-bottom:0;}
		.list-tabel li{display:block; width:33%; float:left; margin-bottom:15px;}
		.list-tabel2 li{width:50%;}
		.list-tabel li.noborder{border-right:0;}
		.theme{display:none;}
	</style>
</head>
<body style="background-color: #f5f6f7" >
	<!--日程提醒模板 -->
	<div id="schedule-template" style="display: none;">
		<div id="schedule-header">
		   <!-- <ul class="new-tabs clearfix" id="easy-tab">
			    <li class="active"><a href="javascript:;" id="peixun" data-tab-id="tab-1">培训</a></li>
			    <li><a href="javascript:;" id="kaoshi" data-tab-id="tab-2">考试</a></li>
			    <li><a href="javascript:;" id="banci" data-tab-id="tab-3">班次</a></li> 
	        </ul> -->
        </div>
        <div id="schedule-content">
	        <div class="new-tab-content"  style="padding:10px">
			</div>
		</div>
	</div>
	<div class="container-fluid">
		<div class="index-panel">
			<div class="side-left" style="">
				<div class="index-user-info">
					<div class="useravatar"><img id="headImg" src="${ctxPath}/static/images/user-avatar-large.png" onerror="javascript:this.src='${ctxPath}/static/images/user-avatar-large.png'"
					    width="100px" height="100px" class="img-rounded" style="cursor:pointer" onclick="uploadAvatar()" alt=""></div>
					<div class="userinfo">
						<label id="userName">---</label>
						<p id="deptName">---</p>
						<p id="positions">---</p>
					</div>
				</div>
				<div class="index-menu">
					<ul>
						<li><a href="javascript:;"><i class="mIcon mPersonal"></i><span> 客户管理</span></a></span></li>
						<li><a href="javascript:personInfo()"><i class="mIcon mIntroduction"></i><span> 个人资料</span></a></li>
						<li><a href="javascript:modifyPwd()"><i class="mIcon mPassword"></i><span> 修改密码</span></a></li>
						
					</ul>
				</div>

				<!-- 快速入口 -->
				<div class="index-box mb-20">
					<div class="index-box-header">
						<span class=""><i class="mIcon mTitleIcon mTitleEnter"></i> 
							<a title="快速入口" onclick="openTab('/agentconfig/pages/personalise/quickEntry.jsp','快速入口');" href="javascript:void(0);">快速入口</a>
						</span>
					</div>
					<div class="index-box-content">
						<ul id="queckEntry" class="list-fastenter">
							<li><a href="#" title="话务管理">话务管理</a></li>
							<li><a href="#" title="工单管理">工单管理</a></li>
							<li><a href="#" title="话务分析">话务分析</a></li>
							<li><a href="#" title="接触历史">接触历史</a></li>
							<li><a href="#" title="知识库">知识库</a></li>
							<li><a href="#" title="语音管理">语音管理</a></li>
							<li><a href="#" title="客户管理">客户管理</a></li>
						</ul>
					</div>
				</div>

				<!-- 备忘录 -->
				<div class="index-box">
					<div class="index-box-header">
					    <span class="pull-right" style="padding-top:4px">
							<a href="javascript:addMemo()" title="添加备忘录"><i class="mIcon mEditOn"></i></a>
						</span> 
						<span class=""><i class="mIcon mTitleIcon mTitleBook"></i> 备忘录</span>
					</div>
					<div class="index-box-content">
						<ul id="momeryList" class="list-memorandum">
							<li><span class="remove-icon" onclick="delMemo()"  title="删除"><i class="mIcon mRemove"></i></span> <a href="javascript:;"><span class="_title">备忘录标题</span><span class="notice-date">昨天</span></a></li>
							<li><span class="remove-icon" onclick="delMemo()"  title="删除"><i class="mIcon mRemove"></i></span> <a href="javascript:;"><span class="_title">备忘录标题</span><span class="notice-date">2018-01-01</span></a></li>
							<li><span class="remove-icon" onclick="delMemo()"  title="删除"><i class="mIcon mRemove"></i></span> <a href="javascript:;"><span class="_title">备忘录标题</span><span class="notice-date">2018-01-01</span></a></li>
							<li><span class="remove-icon" onclick="delMemo()"  title="删除"><i class="mIcon mRemove"></i></span> <a href="javascript:;"><span class="_title">备忘录标题</span><span class="notice-date">2018-01-01</span></a></li>
						</ul>
					</div>
				</div>

			</div>
			<div class="side-center">
				
				<ul class="ul-tab">
					<li class="active" data-tab-id="tab-content-1">个人指标</li>
					<li data-tab-id="tab-content-2">团队指标</li>
				</ul>
				<div id="tab-content">
					<div id="tab-content-1" >
						<!-- 工作-->
						<div class="index-box mb-20 theme" id="theme1">
							<div class="index-box-header">
								<span class=""><i class="mIcon mTitleIcon mTitleWork"></i> <span class="title">工作</span></span>
								<span class="pull-right label label-info">当天</span>
							</div>
							<div class="index-box-content">
								<ul class="list-callinfo list">
								<!-- 
									<li><div class="box"><span>首次响应时长</span><span class="pull-right"><font class="bigger color-red">21</font> 秒</span></div></li>
									<li><div class="box"><span>平均响应时长</span><span class="pull-right"><font class="bigger color-red">12</font> 秒</span></div></li>
									<li><div class="box"><span>每小时平均接待量</span><span class="pull-right"><font class="bigger color-red">23</font> 个</span></div></li>
									<li><div class="box"><span>客户平均等待时长</span><span class="pull-right"><font class="bigger color-red">3</font> 秒</span></div></li>
									<li><div class="box"><span>接待总量</span><span class="pull-right"><font class="bigger color-red">146</font> 个</span></div></li>
									<li><div class="box"><span>最大服务时长</span><span class="pull-right"><font class="bigger color-red">18</font> 分钟</span></div></li>
									<li><div class="box"><span>平均服务时长</span><span class="pull-right"><font class="bigger color-red">5</font> 分钟</span></div></li>									
								 -->
								</ul>
							</div>
						</div>
		
						<!-- 工单 -->
						<div class="index-box mb-20 theme" id="theme2">
							<div class="index-box-header">
								<span class=""><i class="mIcon mTitleIcon mTitleOrder"></i> <span class="title">工单</span></span>
								<span class="pull-right label label-info">当天</span>
							</div>
							<div class="index-box-content">
								<div class="list-gongdan">
									<div class="row list">
									<!-- 
										<div class="col-md-6"><i class="mIcon mItemIcon mItemOrder"></i><span>提交售后工单量&nbsp;&nbsp;&nbsp;<font class="bigger color-red ml-15">3</font></span></div>							
										<div class="col-md-6"><i class="mIcon mItemIcon mItemOrder2"></i><span>暂存工单量&nbsp;&nbsp;&nbsp;<font class="bigger color-red ml-15">15</font></span></div>									
									 -->
									</div>
								</div>
							</div>
						</div>
						
						<!-- 留言 -->
						<div class="index-box mb-20 theme" id="theme3">
							<div class="index-box-header">
								<span class=""><i class="mIcon mTitleIcon mTitleMessage"></i> <span class="title">留言</span></span>
								<span class="pull-right label label-success">前一天</span>
							</div>
							<div class="index-box-content">
								<div class="list-gongdan">
									<div class="row list">
									<!-- 
										<div class="col-md-4"><i class="mIcon mItemIcon mItemMessage"></i><span>留言总量&nbsp;&nbsp;&nbsp;<font class="bigger color-red">3</font></span></div>									
										<div class="col-md-4"><i class="mIcon mItemIcon mItemMessage2"></i><span>未回复总量&nbsp;&nbsp;&nbsp;<font class="bigger color-red">15</font></span></div>		
										<div class="col-md-4"><i class="mIcon mItemIcon mItemTime"></i><span>未处理总量&nbsp;&nbsp;&nbsp;<font class="bigger color-red">8</font></span></div>									
									-->
									</div>
								</div>
							</div>
						</div>
		
		                <div class="row  theme" id="theme4">
		                	<div class="col-md-6">
		                	 	<!-- 质量 -->
								<div class="index-box mb-20">
									<div class="index-box-header">
										<span class=""><i class="mIcon mTitleIcon mTitleQuality"></i> <span class="title">质量</span></span>
										<span class="pull-right label label-success">前一天</span>
									</div>
									<div class="index-box-content">
										<div id="quality-chart" style="height:300px" class="charts"></div>
									</div>
								</div>
		                	</div>
		                	<div class="col-md-6" style="padding-left:0px">
		                	 	<!-- 满意度 -->
								<div class="index-box mb-20">
									<div class="index-box-header">
										<span class=""><i class="mIcon mTitleIcon mTitleGood"></i> 满意度</span>
										<span class="pull-right label label-success">前一天</span>
									</div>
									<div class="index-box-content">
										<div id="satisfaction-chart" style="height:300px" class="charts"></div>
									</div>
								</div>
		                	</div>
		                </div>
					</div>
					<div id="tab-content-2" style="display:none">
						<!-- 各技能组工作情况 -->
						<div class="index-box mb-20 theme" id="theme5">
							<div class="index-box-header">
								<span class=""><i class="mIcon mTitleIcon mTitleWork"></i> 各技能组工作情况</span>
							</div>
							<div class="index-box-content" style="padding: 20px;">
								<div class="m-panel" >
									<div class="m-panel-header">
										<span class="title">技能组名称</span>
									</div>
									<div class="m-panel-content">
										<ul class="list-tabel text-center list" >
											<!-- 
											<li>
												<p><font class="bigger color-red">12</font>秒</p>
												<p>平均响应时长</p>
											</li>
											<li>
												<p><font class="bigger color-red">13</font>秒</p>
												<p>客户平均等待时长</p>
											</li>
											<li>
												<p><font class="bigger color-red">89</font>分</p>
												<p>平均质检得分</p>
											</li>
											<li>
												<p><font class="bigger color-red">96</font>%</p>
												<p>平均满意度</p>
											</li>
											-->
										</ul>
									</div>
								</div>
							</div>
						</div>
		
						<!-- 各渠道工作情况 -->
						<div class="index-box mb-20 theme" id="theme6">
							<div class="index-box-header">
								<span class=""><i class="mIcon mTitleIcon mTitleWork"></i> 各渠道工作情况</span>
							</div>
							<div class="index-box-content" style="padding: 20px;">
								<div class="m-panel mb-20" >
									<div class="m-panel-header">
										<span class="title">小天鹅服务</span>
									</div>
									<div class="m-panel-content">
										<ul class="list-tabel text-center list" >
											<!-- 
											<li>
												<p><font class="bigger color-red">12</font>秒</p>
												<p>平均响应时长</p>
											</li>
											<li>
												<p><font class="bigger color-red">13</font>秒</p>
												<p>客户平均等待时长</p>
											</li>
											<li>
												<p><font class="bigger color-red">89</font>分</p>
												<p>平均质检得分</p>
											</li>
											<li>
												<p><font class="bigger color-red">96</font>%</p>
												<p>平均满意度</p>
											</li>
											-->
										</ul>
									</div>
								</div>
		
								<div class="m-panel" >
									<div class="m-panel-header">
										<span class="title">美的商城</span>
									</div>
									<div class="m-panel-content">
										<ul class="list-tabel text-center list" >
										<!-- 
											<li>
												<p><font class="bigger color-red">12</font>秒</p>
												<p>平均响应时长</p>
											</li>
											<li>
												<p><font class="bigger color-red">13</font>秒</p>
												<p>客户平均等待时长</p>
											</li>
											<li>
												<p><font class="bigger color-red">89</font>分</p>
												<p>平均质检得分</p>
											</li>
											<li>
												<p><font class="bigger color-red">96</font>%</p>
												<p>平均满意度</p>
											</li>
											-->
										</ul>
									</div>
								</div>
							</div>
						</div>
		
						<!-- 各渠道接通量 -->
						<div class="index-box mb-20 theme" id="theme7">
							<div class="index-box-header">
								<span class=""><i class="mIcon mTitleIcon mTitleWork"></i> <span class="title">各渠道接通量</span></span>
							</div>
							<div class="index-box-content">
								<div id="connection-chart" style="height: 300px;" class="charts"></div>
							</div>
						</div>                                 
					</div>
				</div>
			</div>
			<div class="side-right">
				<!-- 公告 -->
				<div class="index-box mb-20">
					<div class="index-box-header">
						<span class=""><i class="mIcon mTitleIcon mTitleHorn"></i> 公告</span>
						<span class="pull-right more" onclick="openNotesMore()"><a href="javascript:;">更多</a> &gt;</span>
					</div>
					<div class="index-box-content">
					   <ul class="list-notice" id="notes">
							<li>
								<div class="notice-icon">
									<i class="mIcon mNoticeIcon mNoticeSys"></i>
								</div>
								<div class="notice-info">
									<span class="notice-date">5分钟前</span>
									<h5>系统通知</h5>
									<p>这里是系统通知,关于系统...</p>
								</div>
							</li>
							<li>
								<div class="notice-icon">
								    <i class="mIcon mNoticeIcon mNoticePeixun"></i>
								</div>
								<div class="notice-info">
									<span class="notice-date">一小时前</span>
									<h5>日程提醒</h5>
									<p>关于2018-01-11的日程安排...</p>
								</div>
							</li>
						    <li>
								<div class="notice-icon">
									<i class="mIcon mNoticeIcon mNoticeTesk"></i>
								</div>
								<div class="notice-info">
									<span class="notice-date">2018-01-05 12:12:12</span>
									<h5>考试通知</h5>
									<p>这里是考试通知,关于系统...</p>
								</div>
							</li>
						    <li>
								<div class="notice-icon">
									<i class="mIcon mNoticeIcon mNoticePeixun"></i>
								</div>
								<div class="notice-info">
									<span class="notice-date">2018-01-05 12:12:12</span>
									<h5>培训通知</h5>
									<p>这里是培训通知,关于系统...</p>
								</div>
							</li>
							<li>
								<div class="notice-icon">
									<i class="mIcon mNoticeIcon mNoticeUrgent"></i>
								</div>
								<div class="notice-info">
									<span class="notice-date">2018-01-05 11:11:11</span>
									<h5>紧急通知</h5>
									<p>这里是紧急通知,关于系统...</p>
								</div>
							</li>
						</ul>
						
					</div>
				</div>
				<!-- 日历 -->
				<div class="index-box mb-20">
					<div class="index-box-header">
						<span class=""><i class="mIcon mTitleIcon mTitleCalendar"></i> 日历</span>
					</div>
					<div class="index-box-content" style="padding:20px">
						<table class="table table-bordered table-calendar" id="table-calendar">
							
						</table>
					</div>
				</div>
				
			</div>
		</div>
	</div>
	<script type="text/javascript" src="/easitline-static/lib/echarts/echarts.min.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/lib/calendar/js/calendar.js?v=20191023"></script>
	<script type="text/javascript" src="${ctxPath}/static/plugin/portal-layer.js?v=20250702"></script>
    <script type="text/javascript">
    	/* $(document).ready(function(){
    	  $(".btn1").click(function(){
    	    $("p").slideToggle();
    	  });
    	}); */
		$(function(){
			//加载头像
			loadHeadImg1();
			//获取快速入口
			getQueckEntry();
			//获取备忘录列表
			getMomeryList();
			//个人日历初始化
			calendarControl.init();
			//个人日历渲染
			calendarControl.renderData();
			//获取公告信息
			getNotes();
			//获取坐席个人信息
		    getUserInfo()
			//简易tab切换事件
			$(".ul-tab li").click(function(){
				$(this).addClass("active").siblings().removeClass("active");
				var tab_id = $(this).data("tab-id");
				$("#tab-content>div").hide();
				$("#"+tab_id).show();
				var charts=$("#"+tab_id).find('.charts');
				for(var i=0; i<charts.length; i++){
					var chart=charts[i].echarts;
					if(chart) chart.resize();
				}
				if(tab_id == "tab-content-2"){
					//初始化接通量的柱状图
					//initConnData()
				}
			})
			
			
			//饼图初始化
			var chart1 = echarts.init(document.getElementById('quality-chart'));
			var chart2 = echarts.init(document.getElementById('satisfaction-chart'));
			var option1 = {
				 title : {
			        text: '质检得分',
			        subtext: '88分',
			        textStyle: {color:'#666',fontSize:'14',fontWeight:'normal'},
			        subtextStyle: {color:'#F24949',fontSize:'20',fontWeight:'bold'},
			        x:'center',
			        y:'125'
			    },
			    series: [
			        {
			            name:'质检得分',
			            type:'pie',
			            radius: ['40%', '50%'],
			            labelLine: {
			                normal: {
			                    show: false
			                }
			            },
			            data:[
			                {value:12, name:''},
			                {value:88, name:''},
			            ]
			        }
			    ],
			    color:['#f5f6f7','#4DC1F7']
			    
			};
			var option2 = {
				title : {
			        text: '客户满意率',
			        subtext: '94%',
			        textStyle: {color:'#666',fontSize:'14',fontWeight:'normal'},
			        subtextStyle: {color:'#F24949',fontSize:'20',fontWeight:'bold'},
			        x:'center',
			        y:'125'
			    },
			    series: [
			        {
			            name:'客户满意率',
			            type:'pie',
			            radius: ['40%', '50%'],
			            labelLine: {
			                normal: {
			                    show: false
			                }
			            },
			            data:[
			                {value:6, name:''},
			                {value:94, name:''},
			            ]
			        }
			    ],
			    color:['#f5f6f7','#F24949']
			};
			chart1.setOption(option1);
			chart2.setOption(option2);
		})
		
		//初始化接通量的柱状图
		function initConnData(){
			var chart = echarts.init(document.getElementById('connection-chart'));
            var option =  {
			    color: ['#3398DB'],
			    tooltip : {
			        trigger: 'axis'
			    },
			    grid: {
			        left: '3%',
			        right: '4%',
			        bottom: '3%',
			        containLabel: true
			    },
			    xAxis : [
			        {
			            type : 'category',
			            data : ['美的会员', '美的服务', '小天鹅服务', '美的官网', '洗悦家', '小天鹅微商城', '美的商城','东芝微商城','美的企业号','美居','美的官方微商城'],
			            axisTick: {
			                alignWithLabel: true
			            }
			        }
			    ],
			    yAxis : [
			        {
			            type : 'value'
			        }
			    ],
			    series : [
			        {
			            name:'接通量',
			            type:'bar',
			            barWidth: '40%',
			            data:[10, 52, 200, 334, 390, 330, 220, 200, 334, 390, 200]
			        }
			    ]
			};
            chart.setOption(option);
		}
		//修改头像
		function uploadAvatar(){
			popup.layerShow({type:1,title:'修改头像',area:['500px','360px'],offset:'100px'},"${ctxPath}/pages/headImg-upload.jsp");
		}
		//个人资料
		function personInfo(){
			popup.layerShow({type:1,title:'个人资料',area:['430px','360px'],offset:'100px'},"${ctxPath}/pages/person-info.jsp");
		}
		//修改密码
		function modifyPwd(){
			popup.layerShow({type:1,title:'修改密码',area:['400px','300px'],offset:'100px'},"${ctxPath}/pages/password-modify.jsp");
		}
		//查询备忘录列表
		function getMomeryList(){
			$("#momeryList").children('li').remove();
			ajax.remoteCall("${ctxPath}/MemoryServlet?action=getMemoryList",'',function(result) { 
	    		if(result.state == 1){
	    			var data = JSON.parse(result.data); 
					for(var i=0;data!=null&&data.length>0&&i<data.length;i++){
						var menuObj = JSON.parse(data[i]); 
						var str = "<li><span class=\"remove-icon\" onclick=\"delMemo('"+menuObj.ID+"')\"  title=\"删除\"><i class=\"mIcon mRemove\"></i></span> <a href=\"javascript:;\"><span onclick=\"editMemo('"+menuObj.ID+"')\" class=\"_title\">"+menuObj.TITLE+"</span><span class=\"notice-date\">"+menuObj.REMIND_TIME+"</span></a></li>";
						$("#momeryList").append(str);
					}
				}
			}); 
		}
		//添加备忘录
		function addMemo(){
			popup.layerShow({type:1,title:'添加备忘录',area:['500px','460px'],offset:'100px'},"${ctxPath}/pages/memorandum-add.jsp");
		}
		//修改备忘录
		function editMemo(Id){
			popup.layerShow({type:1,title:'修改备忘录',area:['500px','460px'],offset:'100px'},"${ctxPath}/pages/memorandum-add.jsp?Id="+Id,{});
		}
		//删除备忘录
		function delMemo(Id){
			var data = new Object();
			data.Id = Id;
			layer.confirm('确定要删除该条备忘录吗？', {
				  btn: ['确定','取消'] //按钮
				}, function(index){
					ajax.remoteCall("${ctxPath}/MemoryServlet?action=momeryDelete",data,function(result) { 
			    		if(result.state == 1){
			    			layer.msg(result.msg,{icon: 1,offset:'60px',time:1000},function(){
								getMomeryList();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5,offset:'60px'});
						}
					}); 
					layer.close(index);
				}
		    );
		}
		//加载头像
		function loadHeadImg1(){
			ajax.remoteCall("${ctxPath}/UploadHandle?action=getHeadImg&isFull=y",'',function(result) { 
				if(result.state==1&&result.msg!=null&&result.msg!=""){
					$("#headImg").attr('src',result.msg);	
				}
				//layer.alert(result.msg,{icon: 5});
			}); 
		}
		//获取快速入口列表
		function getQueckEntry(){
			$("#queckEntry").children('li').remove();
			ajax.remoteCall("${ctxPath}/servlet/mainPageQuickEntry?action=getUserMenuList",'',function(result) { 
				if(result.state==1){
					var data = JSON.parse(result.data); 
					for(var i=0;data!=null&&data.length>0&&i<data.length;i++){
						var menuObj = JSON.parse(data[i]); 
						if(menuObj.menuUrl!=""){
							var str = "<li class='shenglue' ><a href='javascript:void(0);' onclick=\"openTab('"+menuObj.menuUrl+"','"+menuObj.menuName+"');\" title="+menuObj.menuName+">"+menuObj.menuName+"</a></li>";
							$("#queckEntry").append(str);
						}
					}
				}
				//layer.alert(result.msg,{icon: 5});
			}); 
		}
		//获取系统公告
		function getNotes(){
			$("#notes").children('li').remove();
			var str = "";
			ajax.remoteCall("${ctxPath}/NotesServlet?action=getNotes",'',function(result) { 
				console.log('查询到公告:'+result);
				if(result!=""&&result!=null&&result.data!=null&&result.state==1){
					var data = JSON.parse(result.data); 
					var noticeList = data.noticeList; 
					if(noticeList!=null){
						for(var i=0; i < noticeList.length; i++){
							var notesObj = noticeList[i]; 
							
							if(notesObj.TYPE_NAME == "日程提醒"){
								str += "<li><div class=\"notice-icon\"><i class=\"mIcon mNoticeIcon mNoticePeixun\"></i></div>";
							}else if(notesObj.TYPE_NAME == "考试通知"){
								str += "<li><div class=\"notice-icon\"><i class=\"mIcon mNoticeIcon mNoticeTesk\"></i></div>";
							}else if(notesObj.TYPE_NAME =="培训通知"){
								str += "<li><div class=\"notice-icon\"><i class=\"mIcon mNoticeIcon mNoticePeixun\"></i></div>";
							}else if(notesObj.TYPE_NAME =="紧急通知"){
								str += "<li><div class=\"notice-icon\"><i class=\"mIcon mNoticeIcon mNoticeUrgent\"></i></div>";
							}else {
								str += "<li><div class=\"notice-icon\"><i class=\"mIcon mNoticeIcon mNoticeSys\"></i></div>";
							}
							str += "<div onclick=\"notesDetils('"+notesObj.NOTICE_ID+"')\" class=\"notice-info\"><span  class=\"notice-date\">"+notesObj.PUBLISH_TIME+"</span><h5>"+notesObj.TYPE_NAME+"</h5>"+
							"<p>"+notesObj.NOTICE_TITLE+"</p></div></li>";
						}
					}
				}
				
				//当没有数据时
				if(str==""){
					str += "<li><div class=\"notice-icon\"><i class=\"mIcon mNoticeIcon \"></i></div>";
					str += "<div class=\"notice-info\"><span  class=\"notice-date\"></span><h5></h5>"+
					"<p align=\"center\">没有数据！</p></div></li>";
				}
				
				$("#notes").append(str);
				//layer.alert(result.msg,{icon: 5});
			});
		}
		//获取公告列表
		function openNotesMore(){
			var _url = "/note/servlet/noticeInfo?action=noticeInfoList";
			if(_url){
				var title = "收件箱页面";
				parent.popup.openTab(_url,title,null)
			}
		}
		//打开一个面板，mars提供的方式，以后遇到可以直接copy该方法
		function openTab(_url,title){
			parent.popup.openTab(_url,title,null);
		}
		//查看公告详情
		function notesDetils(noticeId){
			ajax.remoteCall("/note/servlet/noticeInfo?action=read&noticeId="+noticeId,null, function(result) { 
			});
			
			popup.layerShow({type:2,title:"查看公告",offset:'20px',area:['800px','680px']},"/note/servlet/noticeInfo?action=noticeInfoDetail&noticeId="+noticeId,{});
		} 
		//获取坐席个人信息
	    function getUserInfo(){
	    	ajax.remoteCall("${ctxPath}/Servlet/userRes?action=getUserInfo",'',function(result) { 
				if(result.state==1){
					var menuObj = JSON.parse(result.data); 
					
					$("#userName").html(menuObj.userName);
					$("#deptName").html(menuObj.deptName);
					$("#positions").html(menuObj.userPosition);
				}
			}); 
	    }
		
		//初始化视图:用于定时更新指标
		if(window.initView) window.initView('${ctxPath}/IndicatorsServlet?action=getIndicators',300);
	    //初始化定时器：用于查询通知
		if(window.getNotice) window.getNotice('/notice/NoticeServlet?action=searchUserNotice','/notice/NoticeServlet?action=answerUserNotice',30);
		
	    //定时刷新首页公告
	    console.log('启动定时更新首页公告:300000');
		setInterval(function(){
			getNotes();
		}, 300000);
		
		/**
		
		var websocket = null;
	    //判断当前浏览器是否支持WebSocket
	    if ('WebSocket' in window) {
	        websocket = new WebSocket("ws://localhost:8080/iccportal5/websocket");
	    }
	    else {
	        alert('当前浏览器 Not support websocket')
	    }

	    //连接发生错误的回调方法
	    websocket.onerror = function () {
	        alert("WebSocket连接发生错误");
	    };

	    //连接成功建立的回调方法
	    websocket.onopen = function () {
	    	alert("WebSocket连接成功");
	    }

	    //接收到消息的回调方法
	    websocket.onmessage = function (event) {
	    	alert(event.data);
	    }

	    //连接关闭的回调方法
	    websocket.onclose = function () {
	    	alert("WebSocket连接关闭");
	    }

	    //监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
	    window.onbeforeunload = function () {
	        closeWebSocket();
	    }

	    //将消息显示在网页上
	    function setMessageInnerHTML(innerHTML) {
	        document.getElementById('message').innerHTML += innerHTML + '<br/>';
	    }

	    //关闭WebSocket连接
	    function closeWebSocket() {
	        websocket.close();
	    }

	    //发送消息
	    function send() {
	        var message = document.getElementById('text').value;
	        websocket.send(message);
	    }
	    */
	</script>
</body>
</html> 

