(function () {
    var theme1 = '<div class="index-box mb-20">\
					<div class="index-box-header">\
						<span class=""><i class="mIcon mTitleIcon mTitleWork"></i> <span class="title">{group_name}</span></span>\
						<span class="pull-right label label-info">当天</span>\
					</div>\
					<div class="index-box-content">\
						<ul class="list-callinfo list">{content}</ul>\
					</div>\
				</div>';
    var theme2 = '<div class="index-box mb-20">\
						<div class="index-box-header">\
					<span class=""><i class="mIcon mTitleIcon mTitleOrder"></i> <span class="title">{group_name}</span></span>\
					<span class="pull-right label label-info">当天</span>\
				</div>\
				<div class="index-box-content">\
					<div class="list-gongdan">\
						<div class="row list">{content}</div>\
					</div>\
				</div>\
				</div>';
    var theme3 = '<div class="row">{content}</div>';
    var theme5 = '<div class="index-box mb-20">\
						<div class="index-box-header">\
					<span class=""><i class="mIcon mTitleIcon mTitleWork"></i> {group_name}</span>\
				</div>\
				<div class="index-box-content" style="padding: 20px;">\
					<div class="m-panel" >\
						<div class="m-panel-header">\
							<span class="title">技能组名称</span>\
						</div>\
						<div class="m-panel-content">\
							<ul class="list-tabel text-center list">{content}</ul>\
						</div>\
					</div>\
				</div>\
				</div>';
    var theme10 = '<div class="index-box mb-20">\
					<div class="index-box-header">\
						<span class=""><i class="mIcon mTitleIcon mTitleWork"></i> <span class="title">{group_name}</span></span>\
						<span class="pull-right label label-info">近一个月</span>\
					</div>\
					<div class="index-box-content">\
						<ul class="list-callinfo list">{content}</ul>\
					</div>\
				</div>';
    function doRequest(url) {
        $.ajax({
            url: url,
            success: function (res) {
                //console.log('指标请求结果：',res);
                var data = res;
                if (typeof res == 'string') data = JSON.parse(data);
                if (data.data && data.data.length > 0) {
                    //data={"msg":"","data":[{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"语音班组服务质量","indicator_name":"整体质量平均分","group_type":"02"},{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"语音班组服务质量","indicator_name":"整体平均分最高人","group_type":"02"},{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"语音坐席工作量","indicator_name":"待处理VOC量","group_type":"01"},{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"语音坐席工作量","indicator_name":"呼出总通话时长","group_type":"01"},{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"语音坐席工作量","indicator_name":"质量平均分","group_type":"01"},{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"语音班组服务质量","indicator_name":"整体平均分最低人","group_type":"02"},{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"语音坐席工作量","indicator_name":"呼出数","group_type":"01"},{"indicator_val":"0","indicator_show_type":"02","group_show_type":"03","group_name":"语音坐席质量","indicator_name":"质检平均分","group_type":"01"},{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"语音坐席工作量","indicator_name":"满意度","group_type":"01"},{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"个人售后工单量","indicator_name":"提交工单量","group_type":"01"},{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"语音班组服务质量","indicator_name":"班内呼入质量平均分","group_type":"02"},{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"全媒体坐席工作量","indicator_name":"平均响应时长","group_type":"01"},{"indicator_val":"0","indicator_show_type":"02","group_show_type":"03","group_name":"语音班组质量","indicator_name":"班组质检平均分","group_type":"02"},{"indicator_val":"0","indicator_show_type":"02","group_show_type":"03","group_name":"语音班组质量排名","indicator_name":"班组质量排名","group_type":"02"},{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"语音坐席工作量","indicator_name":"呼入量","group_type":"01"},{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"语音坐席工作量","indicator_name":"折算量","group_type":"01"},{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"个人售后工单量","indicator_name":"总工单量","group_type":"01"},{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"语音坐席工作量","indicator_name":"呼出每小时呼出平均量","group_type":"01"},{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"语音坐席工作量","indicator_name":"呼入每小时接入的平均量","group_type":"01"},{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"语音班组服务质量","indicator_name":"班内呼出质量平均分","group_type":"02"},{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"个人售后工单量","indicator_name":"暂存量","group_type":"01"},{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"语音话后满意度","indicator_name":"话后满意度平均分最低人","group_type":"02"},{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"语音坐席工作量","indicator_name":"呼入总通话时长","group_type":"01"},{"indicator_val":"0","indicator_show_type":"01","group_show_type":"01","group_name":"全媒体坐席工作量","indicator_name":"首次响应时长","group_type":"01"}],"state":1};
                    //console.log('转化JSON：',data);
                    var group = initData(data.data);
                    initTab(group.person, "tab-content-1");
                    initTab(group.term, "tab-content-2");

                }
            },
            error: function (er) {
                console.log('指标请求失败', er);
            }
        })
    }
    function initData(data) {
        //数据分组
        var group = { person: [], term: [] };
        for (var i = 0; i < data.length; i++) {
            var td = data[i];
            if (td.type == '01') {
                group.person.push(td);
            } else {
                group.term.push(td);
            }
        }
        //console.log('数据转化结果',group);
        return group;
    }
    function initTab(data, id) {
        var node = $('#' + id);
        node.html('');
        //console.log('构造视图',data);
        for (var i = 0; i < data.length; i++) {
            var str = '', cla = '';
            var td = data[i];
            switch (td.show_type) {
                case '01': initTheme1(node, td, theme1); break;
                case '02': initTheme2(node, td, theme2); break;
                case '03': initTheme3(node, td, theme3); break;
                case '04': initTheme4(node, td, theme4); break;
                case '05': initTheme5(node, td, theme5); break;
                case '06': initTheme6(node, td, theme6); break;
                case '07': initTheme7(node, td, theme7); break;
                case '10': initTheme10(node, td, theme10); break;

            }
        }

    }
    //自定义
    function initTheme10(node, data, theme) {
        let list = [];
        let type = ""
        let deptCode = '';
        let operatorAreaCode = '';
        let useAcc = '';
        // let typeObj={"012":1,"013":2,"014":3} //1:个人待办 2:班组待办 3:管理员待办
        let list0 = [{ name: "现金24小时待领", val: 0, key: 'over24Hours' }, { name: "应补未补-事中", val: 0, key: 'middle' },
            { name: "应补未补-事后", val: 0, key: 'after' }
        ]
        let list1 = [{ name: "现金24小时待领", val: 0, key: 'over24Hours' }, { name: "应补未补-事中", val: 0, key: 'middle' },
            { name: "应补未补-事后", val: 0, key: 'after' },
            { name: "额度不足-事中", val: 0, key: 'lackMiddle' },
            { name: "额度不足-事后", val: 0, key: 'lackAfter' }
        ]
        let list2 = [{ name: "现金24小时待领", val: 0, key: 'over24Hours' }, { name: "额度不足-事中", val: 0, key: 'lackMiddle' },
            { name: "额度不足-事后", val: 0, key: 'lackAfter' },
            { name: "超权限范围-事中", val: 0, key: 'exceedMiddle' },
            { name: "超权限范围-事后", val: 0, key: 'exceedAfter' }
        ]
        switch (data.code) {
            case '012': list = list0; type = 1; break;
            case '013': list = list1; type = 2; break;
            case '014': list = list2; type = 3; break;
        }

        // 调用接口获取待办数据
        $.ajax({
            url: '/PeakEnd/servlet/homeTodo',
            data: {
                action: 'getTodo',
                type: type
            },
            success: function (res) {
                if (res && res.data && res.state == 1) {
                    // 遍历接口返回数据
                    for (let key in res.data) {
                        // 在list中查找匹配的项
                        let item = list.find(item => item.key === key);
                        if (item) {
                            item.val = res.data[key];
                            deptCode = res.data['deptCode'] || '';
                            operatorAreaCode = res.data['operatorAreaCode'] || '';
                            useAcc = res.data['userAcc'] || '';
                        }
                    }
                    console.log('deptCode', operatorAreaCode, deptCode, useAcc);
                }
                // 渲染数据到页面
                renderTheme10(node, data, theme, list, deptCode, operatorAreaCode, useAcc);
            },
            error: function (err) {
                console.log('获取待办数据失败:', err);
                // 即使接口调用失败也渲染默认数据
                renderTheme10(node, data, theme, list, deptCode, operatorAreaCode, useAcc);
            }
        });
    }

    // 抽取渲染逻辑到单独的函数
    function renderTheme10(node, data, theme, list, deptCode, operatorAreaCode, useAcc) {
        var domain = window.location.host;
        var str = '';
        var mod = theme.replace(/{group_name}/, data.name);

        for (var i = 0; i < list.length; i++) {
            var td = list[i];
            var jumpUrl = "http://" + domain + "/" + (td.urlname || "");
            str += '<li><div class="box"><span>' + td.name + '</span><span class="pull-right todo-item" data-key="' + td.key + '" data-type="' + data.code + '"><a ><font class="bigger color-red">' + td.val + '</font></a> 条</span></div></li>';
        }
        node.append(mod.replace(/{content}/, str));
        // 添加点击事件 - 先解绑旧事件，避免重复绑定
        node.find('.todo-item').off('click').on('click', function () {
            var key = $(this).data('key');
            var type = $(this).data('type');
            var url = '/PeakEnd/pages/peakEnd/peakEnd-record.jsp';
            var params = {};

            // 获取近一个月的日期范围
            var monthRange = getMonthRange(false);
            var fourHoursTime = getHoursAgo(4);
            var title = '坐席补偿明细池'
            // 基于角色和待办项类型设置不同参数
            if (type === '012') { // 一线
                if (key === 'over24Hours') {
                    url = '/PeakEnd/pages/peakEnd/cashCompensate/peakEnd-cash-list.jsp';
                    title = '现金补偿执行情况（CSS）';
                    // 现金24小时待领
                    params = {
                        isOver24:1,
                        START_CREATE_TIME: monthRange.start,
                        END_CREATE_TIME: monthRange.end,
                        CREATE_ACC: useAcc
                    };
                } else if (key === 'middle') {
                    // 应补未补-事中
                    params = {
                        CREATE_ACC: useAcc, // 赠送人账号-与当前账号一致
                        COMPENSATE_TYPE: '1', // 补偿类型-事中补偿
                        START_CREATE_TIME: monthRange.start,
                        END_CREATE_TIME: monthRange.end,
                        SUBMIT_STATES: '3', // 是否补偿-应补未补
                    };
                } else if (key === 'after') {
                    // 应补未补-事后
                    params = {
                        CREATE_ACC: useAcc, // 赠送人账号-与当前账号一致
                        COMPENSATE_TYPE: '2', // 补偿类型-事后补偿
                        START_CREATE_TIME: monthRange.start,
                        END_CREATE_TIME: monthRange.end,
                        SUBMIT_STATES: '3', // 是否补偿-应补未补
                        FEEDBACK_RESULT: '1'
                    };
                }
            } else if (type === '013') { // 班长
                if (key === 'over24Hours') {
                    url = '/PeakEnd/pages/peakEnd/cashCompensate/peakEnd-cash-list.jsp';
                    title = '现金补偿执行情况（CSS）';
                    // 现金24小时待领
                    params = {
                        isOver24:1,
                        START_CREATE_TIME: monthRange.start,
                        END_CREATE_TIME: monthRange.end,
                        AREA_CODE: operatorAreaCode, // 运营区域-与当前账号一致
                        AGENT_DEPT: deptCode, // 坐席班组-与当前账号一致
                    };
                } else if (key === 'middle') {
                    // 应补未补-事中
                    params = {
                        CREATE_ACC: '', // 赠送人账号-与当前账号一致
                        COMPENSATE_TYPE: '1', // 补偿类型-事中补偿
                        START_CREATE_TIME: monthRange.start,
                        END_CREATE_TIME: monthRange.end,
                        SUBMIT_STATES: '3', // 是否补偿-应补未补
                        areaCode: operatorAreaCode, // 运营区域-与当前账号一致
                        agentDept: deptCode, // 坐席班组-与当前账号一致
                    };
                } else if (key === 'after') {
                    // 应补未补-事后
                    params = {
                        CREATE_ACC: '', // 赠送人账号-与当前账号一致
                        COMPENSATE_TYPE: '2', // 补偿类型-事后补偿
                        START_CREATE_TIME: monthRange.start,
                        END_CREATE_TIME: monthRange.end,
                        SUBMIT_STATES: '3', // 是否补偿-应补未补
                        areaCode: operatorAreaCode, // 运营区域-与当前账号一致
                        agentDept: deptCode, // 坐席班组-与当前账号一致
                        FEEDBACK_RESULT: '1'
                    };
                } else if (key === 'lackMiddle') {
                    // 事中额度不足
                    params = {
                        START_CREATE_TIME: fourHoursTime,
                        END_CREATE_TIME: monthRange.end,
                        SUBMIT_STATES: '2', // 不符合补偿
                        NON_CONFORMANCE_REASON: '12', // 不符合补偿-事中额度不足
                        areaCode: operatorAreaCode, // 运营区域-与当前账号一致
                        agentDept: deptCode, // 坐席班组-与当前账号一致
                        COMPENSATE_TYPE: '1', // 补偿类型-事中补偿
                    };
                } else if (key === 'lackAfter') {
                    // 事后额度不足
                    params = {
                        START_CREATE_TIME: fourHoursTime,
                        END_CREATE_TIME: monthRange.end,
                        SUBMIT_STATES: '2', // 不符合补偿
                        NON_CONFORMANCE_REASON: '12', // 不符合补偿-事后额度不足
                        areaCode: operatorAreaCode, // 运营区域-与当前账号一致
                        agentDept: deptCode, // 坐席班组-与当前账号一致
                        COMPENSATE_TYPE: '2', // 补偿类型-事后补偿
                    };
                }
            } else if (type === '014') { // 管理员
                if (key === 'over24Hours') {
                    url = '/PeakEnd/pages/peakEnd/cashCompensate/peakEnd-cash-list.jsp';
                    title = '现金补偿执行情况（CSS）';
                    // 现金24小时待领
                    params = {
                        isOver24:1,
                        START_CREATE_TIME: monthRange.start,
                        END_CREATE_TIME: monthRange.end,
                    };
                } else if (key === 'lackMiddle') {
                    // 事中额度不足
                    params = {
                        START_CREATE_TIME: monthRange.start,
                        END_CREATE_TIME: fourHoursTime,
                        SUBMIT_STATES: '2', // 不符合补偿
                        NON_CONFORMANCE_REASON: '12', // 不符合补偿-事中额度不足
                        COMPENSATE_TYPE: '1', // 补偿类型-事中补偿
                    };
                } else if (key === 'lackAfter') {
                    // 事后额度不足
                    params = {
                        START_CREATE_TIME: monthRange.start,
                        END_CREATE_TIME: fourHoursTime,
                        SUBMIT_STATES: '2', // 不符合补偿
                        NON_CONFORMANCE_REASON: '12', // 不符合补偿-事后额度不足
                        COMPENSATE_TYPE: '2', // 补偿类型-事后补偿
                    };
                } else if (key === 'exceedMiddle') {
                    // 事中超权限范围
                    params = {
                        START_CREATE_TIME: monthRange.start,
                        END_CREATE_TIME: monthRange.end,
                        SUBMIT_STATES: '2', // 不符合补偿
                        NON_CONFORMANCE_REASON: '13', // 不符合补偿-事中超权限范围
                        COMPENSATE_TYPE: '1', // 补偿类型-事中补偿
                    };
                } else if (key === 'exceedAfter') {
                    // 事后超权限范围
                    params = {
                        START_CREATE_TIME: monthRange.start,
                        END_CREATE_TIME: monthRange.end,
                        SUBMIT_STATES: '2', // 不符合补偿
                        NON_CONFORMANCE_REASON: '13', // 不符合补偿-事后超权限范围
                        COMPENSATE_TYPE: '2', // 补偿类型-事后补偿
                    };
                }
            }

            // 将参数转为URL query string
            var queryString = Object.keys(params).map(function (key) {
                return encodeURIComponent(key) + '=' + encodeURIComponent(params[key]);
            }).join('&');

            // 跳转到对应页面
            openTab(url + '?' + queryString, title);
        });
    }

    function initTheme1(node, data, theme) {
        var domain = window.location.host;
        //主题样式1
        var str = '';
        var mod = theme.replace(/{group_name}/, data.name);
        for (var i = 0; i < data.indicatorsList.length; i++) {
            var td = data.indicatorsList[i];
            var jumpUrl = "http://" + domain + "/" + td.urlname;
            var Opt = td.urlname == "" ? "" : 'onclick="openTab(\'' + jumpUrl + '\',\'路径\');"'
            str += '<li><div class="box"><span>' + td.name + '</span><span class="pull-right"><a href="javascript:void(0);" ' + Opt + '><font class="bigger color-red">' + td.val + '</font></a> ' + td.unit + '</span></div></li>';
        }
        node.append(mod.replace(/{content}/, str));
    }
    function initTheme2(node, data, theme) {
        //主题样式2
        var str = '';
        var clas = ['mItemOrder', 'mItemOrder2', 'mItemMessage', 'mItemMessage2', 'mItemTime'];
        var j = 0;
        var mod = theme.replace(/{group_name}/, data.name);
        for (var i = 0; i < data.indicatorsList.length; i++, j++) {
            var td = data.indicatorsList[i];
            if (i > clas.length - 1) j = 0;
            if (data.show_type == '01') {
                str += '<div class="col-md-4"><i class="mIcon mItemIcon ' + clas[j] + '"></i><span>' + td.name + '&nbsp;&nbsp;&nbsp;<font class="bigger color-red">' + td.val + '</font></span></div>';
            } else {
                str += '<div class="col-md-6"><i class="mIcon mItemIcon ' + clas[j] + '"></i><span>' + td.name + '&nbsp;&nbsp;&nbsp;<font class="bigger color-red ml-15">' + td.val + '</font></span></div>';
            }

        }
        node.append(mod.replace(/{content}/, str));
    }
    function initTheme3(node, data, theme) {
        //主题样式3,图表样式
        var str = '';
        var mod = theme.replace(/{group_name}/, data.name);
        for (var i = 0; i < data.indicatorsList.length; i++) {
            var td = data.indicatorsList[i];
            var day = data.data_scope == '01' ? '当天' : '前一天';
            if (data.show_type == '01') {
                str += '<div class="col-md-6"><div class="index-box mb-20"><div class="index-box-header"><span class=""><i class="mIcon mTitleIcon mTitleQuality"></i> <span class="title">' + data.name + '</span></span><span class="pull-right label label-success">' + day + '</span></div><div class="index-box-content"><div style="height:300px" class="charts"></div></div></div></div>';
            } else {
                str += '<div class="col-md-6"><div class="index-box mb-20"><div class="index-box-header"><span class=""><i class="mIcon mTitleIcon mTitleQuality"></i> <span class="title">' + data.name + '</span></span><span class="pull-right label label-success">' + day + '</span></div><div class="index-box-content"><div style="height:300px" class="charts"></div></div></div></div>';
            }
        }
        node.append(mod.replace(/{content}/, str));
        initCharts(node.children().eq(node.children().length - 1).find('.charts'), data.indicatorsList);
    }
    function initTheme4(node, data, theme) {
        //主题样式4
    }
    function initTheme5(node, data, theme) {
        //主题样式5
        var str = '';
        var mod = theme.replace(/{group_name}/, data.name);
        for (var i = 0; i < data.indicatorsList.length; i++) {
            var td = data.indicatorsList[i];
            str += '<li><p><font class="bigger color-red">' + td.val + '</font>秒</p><p>' + td.name + '</p></li>';
        }
        node.append(mod.replace(/{content}/, str));

    }
    function initTheme6(node, data, theme) {
        //主题样式6
    }
    function initTheme7(node, data, theme) {
        //主题样式7
    }
    function initCharts(nodes, data) {
        //初始化图形
        var j = 0, k = 0;
        for (var i = 0; i < data.length; i++) {
            if (data[i].show_type == '02' || data[i].show_type == '01') {
                j = j > 1 ? 0 : j;
                initPie(nodes[i], data[i], j++);
            } else if (data[i].show_type == '03') {
                k = k > 1 ? 0 : k;
                initRadar(nodes[i], data[i], k++);
            }
        }
    }
    function initPie(node, data, index) {
        var cols = [['#f5f6f7', '#4DC1F7'], ['#f5f6f7', '#F24949']];
        var option = {
            title: {
                textStyle: { color: '#666', fontSize: '14', fontWeight: 'normal' },
                subtextStyle: { color: '#F24949', fontSize: '20', fontWeight: 'bold' },
                x: 'center',
                y: '125'
            },
            series: [
                {
                    type: 'pie',
                    radius: ['40%', '50%'],
                    labelLine: {
                        normal: {
                            show: false
                        }
                    }
                }
            ]
        };
        option.color = cols[index];
        option.series[0].name = data.name;
        option.series[0].data = [{ value: 100 }, { value: data.val == '' ? 0 : parseInt(data.val) }];
        option.title.text = data.name;
        option.title.subtext = (data.val == '' ? '0' : data.val) + '%';
        if (!node.echarts) {
            node.echarts = echarts.init(node);
        }
        node.echarts.setOption(option);
    }
    function initRadar(node, data, index) {
        var option = {
            toolbox: {
                show: false
            },
            calculable: false,
            polar: [
                {
                    indicator: [],
                    radius: 90
                }
            ],
            series: [
                {
                    name: data.name,
                    type: 'radar',
                    itemStyle: {
                        normal: {
                            areaStyle: {
                                type: 'default'
                            }
                        }
                    },
                    data: []
                }
            ]
        };
        option.series[0].name = data.name;
        if (data.definedIndicators && data.definedIndicators.length > 0) {
            var val = [];
            var m = 0;
            for (var i = 0; i < data.definedIndicators.length; i++) {
                var td = data.definedIndicators[i];
                for (var k in td) {
                    if (m < parseInt(td[k]))
                        m = parseInt(td[k]);
                }
            }

            for (var i = 0; i < data.definedIndicators.length; i++) {
                var td = data.definedIndicators[i];
                for (var k in td) {
                    option.polar[0].indicator.push({ text: k, max: m });
                    val.push(parseInt(td[k]));
                }
            }
            option.series[0].data.push({ name: data.name, value: val });
        }
        //console.log(option);
        if (!node.echarts) {
            node.echarts = echarts.init(node);
        }
        node.echarts.setOption(option);
    }
    window.initView = function (url, seconds) {
        if (seconds == null || seconds == "" || seconds <= 0) {
            seconds = 10;
        }
        seconds = seconds * 1000;
        doRequest(url);
        setInterval(function () {
            doRequest(url);
        }, seconds);
    };

    /**
     * 获取系统通知
     * searchUrl：查询通知url
     * answerUrl：应答通知url，应答后的通知不再显示
     * seconds：通知轮询秒数
     */
    window.getNotice = function (searchUrl, answerUrl, seconds) {
        if (seconds == null || seconds == "" || seconds <= 30) {
            seconds = 30;
        }
        seconds = seconds * 1000;

        console.log('启动查询通知结果：' + seconds);

        searchUserNotice(searchUrl, answerUrl);
        setInterval(function () {
            searchUserNotice(searchUrl, answerUrl);
        }, seconds);
    };

    /**
     * 查询系统通知
     * searchUrl：查询通知url
     * answerUrl：应答通知url，应答后的通知不再显示
     */
    function searchUserNotice(searchUrl, answerUrl) {
        $.ajax({
            url: searchUrl,
            success: function (res) {
                var data = res;
                if (typeof res == 'string') {
                    data = JSON.parse(data);
                }
                if (data.data && data.data.noticeNum) {
                    //console.log('通知查询结果：',data.data);
                    if (data.data.notice) {
                        //data={"msg":"","data":{respDesc: "查询成功", noticeNum: 2, serialId: "153451030321600060", respCode: "000", notice: "您有如下通知:<br/>紧急公告:1<br/>通知:4<br/>"},"state":1};
                        var noticeNum = data.data.noticeNum;
                        if (noticeNum > 0) {
                            top.layer.msg(data.data.notice, {
                                icon: 1,
                                title: '提示',
                                offset: 'rb',
                                btn: '知道了',
                                btn1: function (index, layero) {
                                    answerUserNotice(answerUrl);
                                    top.layer.close(index);
                                },
                                btnAlign: 'c',
                                closeBth: true,
                                time: 0
                            });
                        }
                    }

                    if (data.data.popNotices) {
                        //top.popup.layerClose();
                        var popNotices = data.data.popNotices;
                        var len = popNotices.length;
                        for (var i = 0; i < len; i++) {
                            var poweaType = popNotices[i].weaType;
                            if (poweaType == 1) {
                                top.popup.openTab(popNotices[i].url, popNotices[i].title, {});
                            } else {
                                top.popup.layerShow({ type: 2, title: popNotices[i].title, time: 60000, offset: '80px', area: ['800px', '650px'] }, popNotices[i].url, {});
                            }
                        }

                    }
                }
            },
            error: function (er) {
                console.log('通知查询请求失败', er);
            }
        })
    };

    /**
     * 应答系统通知
     * answerUrl：应答通知url，应答后的通知不再显示
     */
    function answerUserNotice(url) {
        $.ajax({
            url: url,
            success: function (res) {

            },
            error: function (er) {
                console.log('通知应答请求失败', er);
            }
        })
    }

    /**
     * 获取近一个月的日期范围
     * @param {boolean} useExactTime 是否使用精确时间，默认为false
     * @returns {Object} 包含start和end的对象，表示开始和结束日期
     */
    function getMonthRange(useExactTime) {
        var end = new Date(); // 当前日期
        var start = new Date();
        start.setMonth(start.getMonth() - 1); // 一个月前的日期
        
        if (useExactTime !== true) {
            // 使用固定时间 00:00:00 和 23:59:59
            start.setHours(0, 0, 0, 0); // 设置为当天的 00:00:00
            end.setHours(23, 59, 59, 999); // 设置为当天的 23:59:59
        }
        
        return {
            start: formatDateTime(start),
            end: formatDateTime(end)
        };
    }

    function formatDateTime(date) {
        var year = date.getFullYear();
        var month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需要 +1
        var day = String(date.getDate()).padStart(2, '0');
        var hours = String(date.getHours()).padStart(2, '0');
        var minutes = String(date.getMinutes()).padStart(2, '0');
        var seconds = String(date.getSeconds()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }

    /**
     * 获取 N 小时前的 Date 对象
     * @param {number} hoursAgo 几小时前
     * @returns {string}
     */
    function getHoursAgo(hoursAgo) {
        var now = new Date();
        now.setHours(now.getHours() - hoursAgo);
        return formatDateTime(now);
    }

})();