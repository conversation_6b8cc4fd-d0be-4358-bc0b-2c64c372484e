package com.yunqu.cc.iccportal5.servlet;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Arrays;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import com.alibaba.fastjson.JSON;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.model.Dict;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ConfigUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.sso.UserPrincipal;
import org.easitline.common.core.web.EasyResult;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.ResModel;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.model.UserPosition;
import com.yq.busi.common.user.UserMgr;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.iccportal5.base.AppBaseServlet;
import com.yunqu.cc.iccportal5.base.CommonLogger;
import com.yunqu.cc.iccportal5.base.Constants;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;

@WebServlet("/Servlet/userRes")
public class UserResServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	private Logger logger = CommonLogger.logger;


	
	/**
	 * 导航
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public  String actionForUserMenu(){
		UserModel  user =UserUtil.getUser(this.getRequest());
		String menuPath = "/pages/menu-navigation.jsp";
		if(user==null){
			logger.info("会话失效，请重新登录!");
			return menuPath;
		}
		String acc = user.getUserAcc();
		HttpSession session = this.getRequest().getSession();
		//先从session中取
		List<ResModel> userRessList = (List<ResModel>)session.getAttribute("USER-MENU-LIST");
		List<List<ResModel>> userRessLists= (List<List<ResModel>>)session.getAttribute("USER-MENU-LIST2");
		//取不到，就从数据库里查询
		if(userRessList==null){
			userRessList = UserMgr.getUserRessList(acc, "2000", true,true);
			userRessLists=new ArrayList<List<ResModel>>() ;
			logger.info("测试");

			 List<ResModel> list=new ArrayList<ResModel>();//临时存储
			 String userInfo="userAcc="+user.getUserAcc()+"&userNo="+user.getUserNo()+"&userName="+user.getUserName()+"&endId="+user.getDept().getDeptId(); 
			 if(userRessList!=null){
				logger.info("userRessList:"+userRessList);
				 for(int i=0;i<userRessList.size();i++){
					 ResModel resModel = userRessList.get(i);
					/* if(resModel.getResUrl().indexOf("{userInfo}")>0){
						String url = resModel.getResUrl().replace("{userInfo}", userInfo);
						resModel.setResUrl(url);
						logger.info(url);

					 }*/
					 List<ResModel> childs = resModel.getChilds();
					 if(childs!=null){
						 for(int j=0;j<childs.size();j++){
							 ResModel resModel1 = childs.get(j);
							/* if(resModel1.getResUrl().indexOf("{userInfo}")>0){
									String url = resModel1.getResUrl().replace("{userInfo}", userInfo);
									resModel1.setResUrl(url);
									logger.info(url);
								 }
							 */
							 List<ResModel> childs2 = resModel1.getChilds();
							 if(childs2!=null){
								 for(int x=0;x<childs2.size();x++){
									 ResModel resModel2 = childs2.get(x);
									 if(resModel2.getResUrl().indexOf("{userInfo}")>0){
											String url = resModel2.getResUrl().replace("{userInfo}", userInfo);
											resModel2.setResUrl(url);
											logger.info(url);
										 }
									 
								 }
							 }
						 }
					 }
					
					 list.add(resModel);
					 if((i+1)%5==0||i==userRessList.size()-1){//每5个位一个或者最后一个List<ResModel>存入userRessLists
						 userRessLists.add(list);
						 list=new ArrayList<ResModel>();
					 }
				 }
			 }
			 session.setAttribute("USER-MENU-LIST", userRessList);
			 session.setAttribute("USER-MENU-LIST2", userRessLists);
		}
		this.setAttr("userRessLists", userRessLists);
		this.setAttr("userRessList", userRessList);
		//埋点
		this.setAttr("appSecrect", Constants.appSecrect);
		this.setAttr("appName", Constants.appName);
		this.setAttr("userName", user.getUserAcc());
		this.setAttr("track_Url", Constants.track_Url);
				
		return menuPath;
	}
	
	/**
	 * 个人资料
	 * @return
	 */
	public JSONObject actionForGetUserInfo(){
		JSONObject json = new JSONObject();
		UserModel user = UserUtil.getUser(this.getRequest());
		if(user==null){
			return EasyResult.fail("会话失效，请重新登陆！");
		}
		String userAcc = user.getUserAcc();
		//姓名
		String userName = user.getUserName();
		//部门
		String deptName = user.getDeptName();
		//工号
		String userNo = user.getUserNo();
		//职位
		List<UserPosition> positions = user.getPositions();
		String userPosition = "";
		for(int i=0;positions!=null&&positions.size()>0&&i<positions.size()-1;i++){
			userPosition += positions.get(i).getName()+'、';
		}
		if(positions!=null&&positions.size()>0){
			userPosition += positions.get(positions.size()-1).getName();
		}
		json.put("userAcc", userAcc);
		json.put("userName", userName);
		json.put("deptName", deptName);
		json.put("userNo", userNo);
		json.put("userPosition", userPosition);
		json.put("collectAddr", Constants.getHsCollectAddr());

		return EasyResult.ok(json.toString(), "");
	}
	
	/**
	 * 获取首页数据的JSON接口
	 * 
	 * @return 包含首页所需数据的JSON对象
	 */
	public JSONObject actionForFetchIndexData() {
	    try {
	        HttpServletRequest request = this.getRequest();
	        UserModel user = UserUtil.getUser(request);
			String acc = user.getUserAcc();
			Map<String,Object> result = new HashMap<String,Object>();

//	        List<ResModel> userRessList = UserMgr.getUserRessList(acc, "2000", true, true);
//	        result.put("userRessList", userRessList);
			//根据入参resId列表，在userRessList匹配符合的资源列表返回
//			List<ResModel> resList = new ArrayList<ResModel>();
//			for(int i=0;i<userRessList.size();i++){
//				ResModel resModel = userRessList.get(i);
//				if(resModel.getResId().equals("2000")){
//					resList.add(resModel);
//				}else{
//					List<ResModel> childs = resModel.getChilds();
//					if(childs!=null){
//						for(int j=0;j<childs.size();j++){
//							ResModel resModel1 = childs.get(j);
//							if(resModel1.getResId().equals("2000")){}
//						}
//					}
//				}
//			}




			JSONObject jsonObject = new JSONObject();
			Map<String, ResModel> ressMap = user.getRessMap();
			String resIds = this.getPara("resIds");
			logger.info("ressMap="+ JSON.toJSONString(ressMap));
			logger.info("resIds="+resIds);
			if(StringUtils.isNotBlank(resIds)){
				String[] redIdsList = resIds.split(",");
				logger.info("redIdsList="+JSON.toJSONString(redIdsList));


				EasyQuery query = ServerContext.getAdminQuery();
				EasySQL sql = new EasySQL();
				sql.append(" SELECT R.RES_ID,R.RES_NAME,R.RES_ICON,R.RES_TYPE,R.RES_URL,R.APP_ID,R.IDX_ORDER,R.P_RES_ID,R.RES_STATE FROM EASI_RES R WHERE EXISTS   ");
				sql.append(" (SELECT 1 FROM ( ");
				sql.append(" SELECT RR.RES_ID FROM EASI_ROLE_RES RR LEFT JOIN EASI_ROLE R ON RR.ROLE_ID = R.ROLE_ID LEFT JOIN EASI_ROLE_USER RU ON R.ROLE_ID = RU.ROLE_ID ");
				sql.append(" LEFT JOIN EASI_USER_LOGIN U ON RU.USER_ID=U.USER_ID WHERE 1=1 ");
				sql.append(acc," AND U.USER_ACCT=? ");
				sql.append(" AND RR.RES_ID IN (");
				StringBuffer buffer = new StringBuffer();
				for(String resId : redIdsList) {
					sql.append(resId,"");
					buffer.append("?,");
				}
				buffer.delete(buffer.length() - 1, buffer.length());
				sql.append(buffer.toString());
				sql.append(")");
				sql.append(" AND R.RES_STATE='0' ");
				sql.append(" UNION ");
				sql.append(" SELECT UR.RES_ID FROM EASI_USER_RES UR LEFT JOIN EASI_USER_LOGIN U ON UR.USER_ID = U.USER_ID WHERE 1=1 ");
				sql.append(acc," AND U.USER_ACCT=? ");
				sql.append(" AND UR.RES_ID IN (");
				for(String resId : redIdsList) {
					sql.append(resId,"");
				}
				sql.append(buffer.toString());
				sql.append(")");
				sql.append(" ) T WHERE T.RES_ID = R.RES_ID) ");
				sql.append(" AND R.RES_STATE='0' ORDER BY R.IDX_ORDER ASC ");
				try {
					Object[] params = sql.getParams();
					//params里面放入两次redIdsList,params转list
					List<Object> paramsList = new ArrayList<>();
					if (params != null) {
						for (Object param : params) {
							paramsList.add(param);
						}
					}
					if (redIdsList != null) {
						for (String resId : redIdsList) {
							paramsList.add(resId);
						}
						for (String resId : redIdsList) {
							paramsList.add(resId);
						}
					}
					

					logger.info("sql="+sql.getSQL()+",params="+JSON.toJSONString(paramsList));
					List<EasyRow>  list = query.queryForList(sql.getSQL(), paramsList.toArray());
					for (EasyRow row : list) {
						JSONObject resObject = new JSONObject();
						resObject.put("resId",row.getColumnValue("RES_ID"));
						resObject.put("resName",row.getColumnValue("RES_NAME"));
						jsonObject.put(row.getColumnValue("RES_ID"), resObject);
					}
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}

			logger.info("jsonObject="+jsonObject.toJSONString());

			result.put("entHost", ConfigUtil.getString(Constants.APP_NAME, "YC_ENT_HOST", ""));
	        result.put("entPId", ConfigUtil.getString(Constants.APP_NAME, "YC_ENT_PID", ""));
	        result.put("entId", ConfigUtil.getString(Constants.APP_NAME, "YC_ENT_ID", "1000"));
	        result.put("entKey", ConfigUtil.getString(Constants.APP_NAME, "YC_ENT_KEY", ""));
	        result.put("zxzjChannelId", ConfigUtil.getString(Constants.APP_NAME, "ZXZJ_CHANNEL_ID", ""));
	        result.put("zxzjChannelKey", ConfigUtil.getString(Constants.APP_NAME, "ZXZJ_CHANNEL_KEY", ""));
	        result.put("agentPwd", ConfigUtil.getString(Constants.APP_NAME, "YC_AGENT_PWD", "Midea@520"));
	        result.put("mediagwUrl", ConfigUtil.getString(Constants.APP_NAME, "YC_MEDIAGW_URL", ""));

	        result.put("userNo", user.getUserNo());
	        result.put("userPwd", user.getUserPwd());
	        result.put("extensionNumber", user.getExtensionNumber());
	        result.put("userAcc", user.getUserAcc());
	        result.put("resList", jsonObject.toJSONString());

	        result.put("mediaPortalUrl", Constants.MEDIA_PORTAL_URL);

	        List<Dict> allDictListByGroupCode = DictCache.getAllDictListByGroupCode(user.getEpCode(), "BUSINESS_DEPARTMENT");
	        result.put("divisionDictList", allDictListByGroupCode);

	        result.put("ccbarDomainList", ConfigUtil.getString(Constants.APP_NAME, "DOMAIN_NAME", ""));
//
	        String agentDepartment = "";
	        String agentDepartmentName = "";
	        EasySQL sql = new EasySQL();
	        sql.append(acc, " select  *  from C_YG_EMPLOYEE where USER_ACC=? ");
	        EasyRow row = this.getQuery().queryForRow(sql.getSQL(), sql.getParams());
	        if (row != null) {
	            for (Dict dict : allDictListByGroupCode) {
	                if (dict.getCode().equals(row.getColumnValue("DEPARTMENT"))) {
	                    agentDepartment = dict.getCode();
	                    agentDepartmentName = dict.getName();
	                }
	            }
	        }
	        result.put("agentDepartment", agentDepartment);
	        result.put("agentDepartmentName", agentDepartmentName);

	        String updateMipPwdUrl = Constants.UPDATE_MIP_PWD_URL;
	        if (StringUtils.isNotBlank(updateMipPwdUrl)) {
	            result.put("updateMipPwdUrl", updateMipPwdUrl);
	        }

	        result.put("appSecrect", Constants.appSecrect);
	        result.put("appName", Constants.appName);
	        result.put("userName", user.getUserAcc());
	        result.put("track_Url", Constants.track_Url);
			logger.info("result="+result);

	        return EasyResult.ok(result);
	    } catch (Exception e) {
	        logger.error(CommonUtil.getClassNameAndMethod(this) + " error:" + e.getMessage(), e);
	        return EasyResult.error(500, "获取失败：" + e.getMessage());
	    }
	}

	public JSONObject actionForCheckRes() {
		try {
			String ids = getPara("ids");
			if (StringUtils.isBlank(ids)) {
				return EasyResult.error(500, "ids is null!");
			}
			String[] resArr = ids.split(";");
			UserPrincipal user = (UserPrincipal) getRequest().getUserPrincipal();
			JSONObject res = new JSONObject();
			if (resArr != null && resArr.length > 0) {
				for (int i = 0; i < resArr.length; i++) {
					String resId = resArr[i];
					String resState = "Y";
					if (user == null || StringUtils.isBlank(resId) || !user.isResource(resId)) {
						resState = "N";
					}
					res.put(resId, resState);
				}
			}
			return EasyResult.ok(res);
		} catch (Exception e) {
			return EasyResult.error(500, "获取失败：" + e.getMessage());
		}
	}


}
