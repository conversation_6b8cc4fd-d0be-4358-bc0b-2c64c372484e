/**
 * <html>
 * <body>
 *  <P> Copyright 广州云趣信息科技有限公司</p>
 *  <p> All rights reserved.</p>
 *  <p> Created on 2018年5月25日 下午3:56:27</p>
 *  <p> Created by wubin</p>
 *  </body>
 * </html>
 */
package com.yunqu.cc.marketing.listener;

import com.yq.busi.common.base.ServiceID;
import com.yunqu.cc.marketing.base.Constants;
import com.yunqu.openapi.filter.SignFilter;
import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceContextListener;

import javax.servlet.FilterRegistration;
import javax.servlet.ServletContextEvent;
import javax.servlet.annotation.WebListener;
import java.util.ArrayList;
import java.util.List;

/**
* @Package：com.yunqu.cc.marketing.listener   
* @ClassName：InterfaceLinstener   
* @Description：   <p> </p>
* @Author： - wubin   
* @CreatTime：2018年5月25日 下午3:56:27   
* @Modify By：   
* @ModifyTime：  2018年5月25日
* @Modify marker：   
* @version    V1.0
*/
@WebListener
public class InterfaceLinstener extends ServiceContextListener{

	@Override
	protected List<ServiceResource> serviceResourceCatalog() {
		
		List<ServiceResource> list = new ArrayList<ServiceResource>();
		
		ServiceResource resource1 = new ServiceResource();
		resource1.appName = Constants.APP_NAME;
		resource1.className = "com.yunqu.cc.marketing.inf.RevisitInfService"	;
		resource1.description = "回访分配定时器";
		resource1.serviceId = ServiceID.SYS_MARKET;
		resource1.serviceName = "回访分配定时器";
		list.add(resource1);
		//提供给洗悦家的回调接口
		ServiceResource resource = new ServiceResource();
		resource.appName = Constants.APP_NAME;
		resource.className = "com.yunqu.cc.marketing.inf.WashCallBackInterfaceService"	;
		resource.description = "处理与洗悦家下单的回调结果处理接口";
		resource.serviceId = ServiceID.WASH_CALL_BACK;
		resource.serviceName = "处理与ECM对接的接口";
		list.add(resource);
		
		
		return list;
	}

}
