<?xml version="1.0" encoding="UTF-8"?>
<!-- 
	id:必须全局唯一，命名方式：应用名称+菜单名称
	name：菜单或功能名称
	type：资源类型，1：应用 ，2：菜单，3：功能，9: 其它
	state：状态，0:正常，缺省 ，1：暂停
	portal:所属应用，不填写缺省为通用portal，根据菜单的归属，可以填写具体的所属的portal，例如：my_portal
	index:排序，菜单的显示按照升序进行排序。
	icon：菜单图标
 -->
<resources>
   <resource id="marketing" name="服务营销管理" url="" type="2" portal="" state="0"  index = "80">
  		<resource id="marketing_manage" name="洗悦家营销资料管理" url="" type="2" portal="" state="0"  index = "1">
			<resource id="marketing_sheet" name="营销资料管理"   url="/marketing/pages/marketing/marketing-sheet.jsp" type="2" portal="" icon="" state="0" index="2"/>
			<resource id="marketing_min_upload" name="导入"  url="" type="3" portal="" icon="" state="0" index="2"/>
			<resource id="marketing_check" name="营销申请审核"  url="/marketing/pages/marketing/marketing-check-list.jsp" type="2" portal="" icon="" state="0" index="3"/>
		</resource>
		<resource id="marketing_deal" name="营销处理" url="" type="2" portal="" state="0"  index = "4">
		     <resource id="marketing_resource" name="营销资料处理"   url="/marketing/pages/marketing/marketing-resource-list.jsp" type="2" portal="" icon="" state="0" index="5"/>
		</resource>
		<resource id="marketing_result" name="洗悦家营销结果查询"   url="/marketing/pages/marketing/marketing-result-list.jsp" type="2" portal="" icon="" state="0" index="6"/>
		<resource id="marketing_report_forms" name="洗悦家营销报表"   url="/marketing/pages/marketing/marketing-list.jsp" type="2" portal="" icon="" state="0" index="7"/>
		<resource id="marketing_marketing_sms" name="营销短信模板维护"   url="/marketing/pages/config/marketingSMS-config.jsp" type="2" portal="" icon="" state="0" index="8"/>
		<resource id="marketing_marketing_couponConfig" name="商城营销卖场优惠券模板维护"   url="/marketing/pages/config/couponConfig.jsp" type="2" portal="" icon="" state="0" index="9"/>
		<resource id="marketing_product_sms" name="商城营销下发短信模块维护"   url="/marketing/pages/config/productSMS-config.jsp" type="2" portal="" icon="" state="0" index="10"/>
	</resource>
</resources>
