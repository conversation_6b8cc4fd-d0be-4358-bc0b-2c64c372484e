package com.yunqu.cc.jobs.servlet;


import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.jobs.base.AppBaseServlet;
import com.yunqu.cc.jobs.job.JobMgr;

@WebServlet("/servlet/dobse")
public class jobsServlet extends AppBaseServlet{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 立即执行
	 * @return
	 */
	public JSONObject actionForDiatelyQuery(){
		JSONObject json=this.getJSONObject();
		String biaoID=json.getString("Id");
		System.out.println(biaoID);
		Boolean text=JobMgr.getInstance().execJob(biaoID);
		if(text){
			return EasyResult.ok("成功");
		}else{
			return EasyResult.error(500, "失败");
		}
	}
}
