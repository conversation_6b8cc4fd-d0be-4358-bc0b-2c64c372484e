package com.yunqu.cc.jobs.base;

import com.yq.busi.common.util.ConfigUtil;
import org.easitline.common.core.context.AppContext;

/**
 * 常量
 * <AUTHOR>
 *
 */
public class Constants {

	public final static String DS_NAME = "yw-ds";     //默认数据源名称
	public final static String MARS_DS_NAME = "mars-ds";     //mars数据源名称
	
	public final static String APP_NAME = "jobs";     //应用
	
	
	
	public final static String SEND_STATUS_1 = "01";     //发送中 
	public final static String SEND_STATUS_2 = "02";     //发送完成
	
	public final static String SEND_TYPE_1 = "02";     //舆情短信标识
	
	public final static String RUN_MARS_NODE_1 = "1";     //节点1
	public final static String RUN_MARS_NODE_2 = "2";     //节点2
	public final static String RUN_MARS_NODE_3 = "3";     //节点2

	public static String getHeartBeatTaskService()
	{
		return 	ConfigUtil.getString(Constants.APP_NAME, "HEART_BEAT_TASK_SERVICE");
	}
	/**
	 * 告警发送短信电话
	 * 取原CC_VOC模块参数配置的，不另外配置
	 */
	public static String getAlarmPhone()
	{
		return 	ConfigUtil.getString("cc-voc", "ALARM_PHONE");
	}



}
