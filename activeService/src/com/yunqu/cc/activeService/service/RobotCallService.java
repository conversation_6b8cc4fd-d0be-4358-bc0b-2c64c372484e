package com.yunqu.cc.activeService.service;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.OrgUtil;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.base.Constants;
import com.yunqu.cc.activeService.model.RobotCallDTO;
import com.yunqu.cc.activeService.utils.RobotCallUtil;
import com.yunqu.cc.activeService.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.kit.RandomKit;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

public abstract class RobotCallService {

    protected Logger logger = CommLogger.getCommLogger("autocall");

    protected int serialId = 0;

    protected final EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME,Constants.YW_DS);
    protected final EasyCache cache = CacheManager.getMemcache();


    public abstract void autoRobotCall();


    protected List<JSONObject>  getData(String strategyType,boolean strategyTypeNull){
        String currentTime = DateUtil.getCurrentDateStr();
        String startTime = DateUtil.addDay(DateUtil.TIME_FORMAT, currentTime, -3);//扫描最近3天的发布记录
        String currentTimeHHmmss = DateUtil.getCurrentDateStr("HH:mm:ss");
        //查询出需要外呼的数据
        EasySQL searchDataSQL = new EasySQL("select t1.*,t2.PREFIX_CODE, t2.TASK_ID from C_NO_AS_ROBOT_AUTO_CALL t1");
        searchDataSQL.append("left join C_NO_AS_ROBOT_STRATEGY t2 on t2.CROWD_PACK_ID=t1.CROWD_PACK_ID ");
        searchDataSQL.append("where 1=1");
        searchDataSQL.append(Constants.HAS_PUBLISHED,"and t1.SERVICE_STATUS = ?");
        //只查询分配策略类型2的人群包
        if(strategyTypeNull){
            searchDataSQL.append(strategyType," and (t2.VALIDATION_STRATEGY = ? or t2.VALIDATION_STRATEGY is null) ");
        }else {
            searchDataSQL.append(strategyType," and t2.VALIDATION_STRATEGY = ?");
        }
        searchDataSQL.append(" and t2.IS_TIMELY != 'Y' ");
        searchDataSQL.append(" and t2.IS_OPEN = '1' ");
        searchDataSQL.append(startTime,"and t1.PUBLISH_TIME >= ?");//发布时间为近三天
        searchDataSQL.append(currentTime,"and t1.PUBLISH_TIME <= ?");
        //增加外呼时间段判断，过滤不在外呼时间段内的待外呼数据，提高写入OCS速度
        searchDataSQL.append(currentTimeHHmmss,"and ((t2.OUTBOUND_BEGIN_TIME1 < ?");
        searchDataSQL.append(currentTimeHHmmss,"and t2.OUTBOUND_END_TIME1 > ?)");
        searchDataSQL.append(currentTimeHHmmss,"or (t2.OUTBOUND_BEGIN_TIME2 < ?");
        searchDataSQL.append(currentTimeHHmmss,"and t2.OUTBOUND_END_TIME2 > ?))");
        searchDataSQL.append("order by t1.PUBLISH_TIME");

        List<JSONObject> dataList = new ArrayList<JSONObject>();
        query.setMaxRow(10000);

        try {
            dataList = query.queryForList(searchDataSQL.getSQL(), searchDataSQL.getParams(), new JSONMapperImpl());
//			autocallStartgy2Logger.info("[自动外呼"+serialId+"] 本次扫描到数据量：" + dataList.size() + ",开始进入校验发布流程");
        } catch (Exception e) {
            logger.info("[自动外呼"+serialId+"] 外呼记录查询error：" + e.getMessage(),e);
        }
        return dataList;
    }

    protected int getCrowdPackCurrCallNum(String crowdPackId,int dayMaxCallNum){
        /**
         * 写入缓存一个初始值，例初始值5000
         * 下次定时器进来比较初始值和策略值
         * 1.初始值>策略值，则比较缓存值和策略值，例策略值2000
         * 当缓存值>=策略值，则设置缓存值=策略值；例缓存值此时有3000，则需要把缓存值设置成策略值2000
         * 当缓存值<策略值，则继续；例缓存值此时有1000，则不修改继续往下走
         * 2.初始值<策略值，则缓存值加上策略值和初始值之差
         * 例策略值7000，差值2000，无论当前缓存值是多少都直接缓存值+2000
         */
        //拿到策略配置的当日最大外呼数量
        Integer originalDayMaxCallNum = cache.get("cache_as_originaldaymaxcallnum_" + crowdPackId);
        if(originalDayMaxCallNum == null) {
            originalDayMaxCallNum = dayMaxCallNum;
            cache.put("cache_as_originaldaymaxcallnum_" + crowdPackId,originalDayMaxCallNum,getCurrentTime2ZeroTime());
        }
        //获取缓存中剩余的可外呼数量，开始外呼时候数量-1
        Integer cacheDayMaxCallNum = cache.get("cache_as_robotcall_" + crowdPackId);
        if(cacheDayMaxCallNum == null) {
            cacheDayMaxCallNum = dayMaxCallNum;
        }
        if(originalDayMaxCallNum > dayMaxCallNum) {
            if(cacheDayMaxCallNum >= dayMaxCallNum) {
                cacheDayMaxCallNum = dayMaxCallNum;
            }
        }else if(originalDayMaxCallNum < dayMaxCallNum) {
            cacheDayMaxCallNum += dayMaxCallNum - originalDayMaxCallNum;
            cache.put("cache_as_originaldaymaxcallnum_" + crowdPackId,dayMaxCallNum,getCurrentTime2ZeroTime());
        }

        return cacheDayMaxCallNum;
    }

    protected void updateCrowdPackCurrCallCache(String crowdPackId,int cacheDayMaxCallNum){
        cache.put("cache_as_robotcall_"+crowdPackId, cacheDayMaxCallNum-1, getCurrentTime2ZeroTime());
    }

    protected void updateMaxLimitCallResult(String recordId) throws SQLException {
        //不允许外呼
        String updateSQL = "update C_NO_AS_ROBOT_AUTO_CALL set service_status = ?,result = ?,result_time = ? where id = ?";
        query.execute(updateSQL, Constants.OTHER_STATUS,"92",DateUtil.getCurrentDateStr(),recordId);
    }

    /**
     * 外呼，return前需要countDown
     * @param row
     */
    public void call(JSONObject robotStrategy, JSONObject row) throws Exception {
        JSONObject content = new JSONObject();
        content.put("autoId",row.getString("ID"));
        content.put("brandCode", StringUtils.isNotBlank(row.getString("BRAND_CODE"))?row.getString("BRAND_CODE"):robotStrategy.getString("BRAND_CODE_R"));//取策略数据
        content.put("brandName",StringUtils.isNotBlank(row.getString("BRAND_NAME"))?row.getString("BRAND_NAME"):robotStrategy.getString("BRAND_NAME_R"));
        content.put("productCode",StringUtils.isNotBlank(row.getString("PROD_CODE"))?row.getString("PROD_CODE"):robotStrategy.getString("PROD_CODE_R"));
        content.put("productName",StringUtils.isNotBlank(row.getString("PROD_NAME"))?row.getString("PROD_NAME"):robotStrategy.getString("PROD_NAME_R"));
        content.put("faultCode",StringUtils.isNotBlank(row.getString("FAULT_CODE"))?row.getString("FAULT_CODE"):robotStrategy.getString("FAULT_CODE"));
        content.put("faultLevel",StringUtils.isNotBlank(row.getString("FAULT_LEVEL"))?row.getString("FAULT_LEVEL"):robotStrategy.getString("FAULT_LEVEL"));
        content.put("skillGroupId",StringUtils.isNotBlank(row.getString("SKILL_GROUP_ID"))?row.getString("SKILL_GROUP_ID"):robotStrategy.getString("SKILL_GROUP_ID"));

        if(StringUtils.isNotBlank(row.getString("ORG_CODE"))){
            content.put("orgCode", OrgUtil.getOrgCode(row.getString("ORG_CODE")) );
            content.put("u_orgCode",OrgUtil.getOrgCode(row.getString("ORG_CODE")) );
        }
        content.put("u_brandCode",row.getString("BRAND_CODE"));//取数据
        content.put("u_brandName",row.getString("BRAND_NAME"));
        content.put("u_productCode",row.getString("PROD_CODE"));
        content.put("u_productName",row.getString("PROD_NAME"));
        content.put("u_skillGroupId",row.getString("SKILL_GROUP_ID"));
        content.put("u_faultCode",row.getString("FAULT_CODE"));
        content.put("u_faultLevel",row.getString("FAULT_LEVEL"));

        content.put("r_brandCode",robotStrategy.getString("BRAND_CODE_R"));//取策略数据
        content.put("r_brandName",robotStrategy.getString("BRAND_NAME_R"));
        content.put("r_productCode",robotStrategy.getString("PROD_CODE_R"));
        content.put("r_productName",robotStrategy.getString("PROD_NAME_R"));
        content.put("r_skillGroupId",robotStrategy.getString("SKILL_GROUP_ID"));
        JSONObject expandData = row.getJSONObject("EXPAND_DATA");
        content.put("extendPrivateData",expandData);

        //挂机短信相关
        boolean smsFlag = "1".equals(robotStrategy.getString("IS_SMS_OPEN"));//短信开关
        boolean shopSmsFlag = "1".equals(robotStrategy.getString("SMS_TYPE"));//短信类型 0-普通1-商城短信
        if(smsFlag) {
            String smsContent = robotStrategy.getString("SMS_CONTENT");
            //商城短信
            if(shopSmsFlag) {
                String partnerPhone = robotStrategy.getString("PARTNER_PHONE");
                String partnerUrl = robotStrategy.getString("SHOP_MINI_PROGRAM_URL");
                String url = RobotCallUtil.getShopSmsResult(partnerUrl,partnerPhone);
                smsContent = smsContent.replace("{shopMiniProgramUrl}", url);
            }
            content.put("s_phone",row.getString("CUST_PHONE"));
            content.put("s_orgCode",robotStrategy.getString("ORG_CODE"));
            content.put("s_brandCode",robotStrategy.getString("BRAND_CODE"));
            content.put("s_brandName",robotStrategy.getString("BRAND_NAME"));

            content.put("s_smsContent",smsContent);
        }
        //外呼开始结束时间设置
        int beginTime = 0;
        int endTime = 0;
        if(row.getBooleanValue("timeFlag1")) {
            beginTime = StringUtil.timeToSecond(robotStrategy.getString("OUTBOUND_BEGIN_TIME1"), 32400);
            endTime = StringUtil.timeToSecond(robotStrategy.getString("OUTBOUND_END_TIME1"), 64800);
        }else if(row.getBooleanValue("timeFlag2")) {
            beginTime = StringUtil.timeToSecond(robotStrategy.getString("OUTBOUND_BEGIN_TIME2"), 32400);
            endTime = StringUtil.timeToSecond(robotStrategy.getString("OUTBOUND_END_TIME2"), 64800);
        }
        content.put("manual", "99");
        String robotCallId = RandomKit.randomStr();
        //开始写入机器人外呼
        RobotCallDTO dto = RobotCallDTO.builder()
                //.recordId(row.getString("maxId"))
                //.chainN(row.getString("maxId"))
                //.chainId(row.getString("maxId"))
                .contactInfo(row.getString("phone"))
                .content(content.toJSONString())
                .taskId(robotStrategy.getString("TASK_ID"))
                .robotId(robotStrategy.getString("ROBOT_ID"))
                .dailyFrom(beginTime)
                .dailyTill(endTime)
                .add1(DateUtil.getCurrentDateStr("yyyy-MM-dd"))
                .add2(row.getString("ID"))
                .add3(robotCallId)
                .build();
        JSONObject busiData=new JSONObject();
        if(expandData!=null){
            busiData.putAll(expandData);
        }
        busiData.put("manual", "99");
        busiData.put("crowdPackId", row.getString("CROWD_PACK_ID"));
        dto.setBusiData(busiData.toJSONString());
//		logger.info("[自动外呼"+serialId+"]recordId：" + dto.getRecordId() + ",chainN:"+dto.getRecordId()+",chainId:"+dto.getRecordId());
        RobotCallUtil.addCall(dto);

        //loggerTime.info("[自动外呼"+loggerTimeId+"]外呼记录明细id：" + "入库成功");
        logger.info("[自动外呼"+serialId+"]外呼记录明细Id：" + row.getString("ID") + ",入库外呼表,参数:" + dto);
        //更新本条数据
        int callTimes = StringUtils.isBlank(row.getString("CALL_TIMES")) ? 0:Integer.valueOf(row.getString("CALL_TIMES"))+1;
        String updateSQL = "update C_NO_AS_ROBOT_AUTO_CALL set service_status = ?,call_times = ?,ROBOT_CALL_ID = ? where id = ?";
        query.execute(updateSQL, Constants.HAS_RECEIVED,callTimes, robotCallId,row.getString("ID"));
        //loggerTime.info("[自动外呼"+loggerTimeId+"]外呼记录明细id：" + "更新完记录");
    }

    protected void saveDoNotdisturbResult(String robotCallId) throws SQLException {
        //写入意图表--免打扰结果
        EasyRecord saveRecord = new EasyRecord("C_NO_AS_ROBOT_CALL_RESULT", "ID");
        saveRecord.set("ID", RandomKit.uniqueStr());
        saveRecord.set("ROBOT_CALL_ID", "");//c_no_as_crowd_robot.id
        saveRecord.set("REVISIT_RESULT", "97");
        saveRecord.set("RESULT_CONTENT", "用户免打扰");
        saveRecord.set("RESULT_TIME", DateUtil.getCurrentDateStr());
        saveRecord.set("CARD_ID", "");
        saveRecord.set("INTENTION_ID", "");
        saveRecord.set("INTENTION_NAME", "");
        saveRecord.set("INTENTION_TAG", "");
        saveRecord.set("INTENTION_RESULT", "");
        saveRecord.set("INTENTION_RESULT_TYPE", "");
        saveRecord.set("LAST_CALL_FLAG", "Y");//需要查询是否最后一次
        saveRecord.set("ROBOT_AUTO_CALL_ID", robotCallId);//c_no_as_robot_auto_call.id
        saveRecord.set("IS_RETURN_RESULT", Constants.SN_N);
        saveRecord.set("SORT_NUM", 0);
        query.save(saveRecord);

    }

    protected void saveRepeatCallResult(String robotCallId,String content) throws SQLException {

        EasyRecord saveRecord = new EasyRecord("C_NO_AS_ROBOT_CALL_RESULT", "ID");
        saveRecord.set("ID", RandomKit.uniqueStr());
        saveRecord.set("ROBOT_CALL_ID", "");//c_no_as_crowd_robot.id
        saveRecord.set("REVISIT_RESULT", "99");
        saveRecord.set("RESULT_CONTENT", content);
        saveRecord.set("RESULT_TIME", DateUtil.getCurrentDateStr());
        saveRecord.set("CARD_ID", "");
        saveRecord.set("INTENTION_ID", "");
        saveRecord.set("INTENTION_NAME", "");
        saveRecord.set("INTENTION_TAG", "");
        saveRecord.set("INTENTION_RESULT", "");
        saveRecord.set("INTENTION_RESULT_TYPE", "");
        saveRecord.set("LAST_CALL_FLAG", "Y");//需要查询是否最后一次
        saveRecord.set("ROBOT_AUTO_CALL_ID", robotCallId);//c_no_as_robot_auto_call.id
        saveRecord.set("IS_RETURN_RESULT", Constants.SN_N);
        saveRecord.set("SORT_NUM", 0);
        query.save(saveRecord);
    }


    protected void updateValidCallResult(EasyQuery query,String status,String result,String crowdId) throws SQLException {
        String updateSQL = "update C_NO_AS_ROBOT_AUTO_CALL set service_status = ?,result = ?,result_time = ? where id = ?";
        query.execute(updateSQL, status, result, DateUtil.getCurrentDateStr(), crowdId);
    }

    /**
     * 根据人群包id获取对应的机器人策略
     * @param crowdPackId
     * @return
     */
    public JSONObject getRobotStrategy(String crowdPackId) {
        String str = cache.get(Constants.ROBOT_STRATEGY_CACHEKEY + crowdPackId);
        if(str!=null) {
            JSONObject obj = JSONObject.parseObject(str);
            return obj;
        }
        EasySQL robotStrategySQL = new EasySQL("select t1.*,t2.PRIORITY from " + Constants.strategyTable.get(Constants.CHANNEL_ROBOT) + " t1");
        robotStrategySQL.append("left join C_NO_AS_CROWDPACK_PRIORITY t2 on t2.strategy_id = t1.id");
        robotStrategySQL.append("where 1=1");
        robotStrategySQL.append(crowdPackId,"and t2.crowd_pack_id = ?");
        robotStrategySQL.append("1","and t1.is_open = ?");
        robotStrategySQL.append("order by t2.strategy_level");
        try {
            List<JSONObject> list = query.queryForList(robotStrategySQL.getSQL(), robotStrategySQL.getParams(),new JSONMapperImpl());
            if(list==null||list.size()==0) {
                logger.info("[自动外呼]人群包id：" + crowdPackId + "无法查询到策略信息，sql：" + robotStrategySQL.getSQL() + ",参数：" + JSONObject.toJSONString(robotStrategySQL.getParams()));
                return null;
            }
            cache.put("robotStrategyCache_" + crowdPackId, list.get(0).toJSONString(), 60 * 60 * 5);
            return list.get(0);
        } catch (SQLException e) {
            logger.info("[自动外呼]查询策略记录error：" + e.getMessage(),e);
        }
        return null;
    }

    /**
     * 获取当前时间到明天0点时长
     * @return
     */
    protected static int getCurrentTime2ZeroTime() {
        //获取当前时间到明天0点时长
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return (int)(calendar.getTimeInMillis() - System.currentTimeMillis()) / 1000;
    }
}
