package com.yunqu.cc.activeService.service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import com.yunqu.cc.activeService.thread.ThreadPoolKit;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.base.Constants;
import com.yunqu.cc.activeService.model.RobotCallDTO;
import com.yunqu.cc.activeService.utils.RobotCallUtil;
import com.yunqu.cc.activeService.utils.StringUtil;

public class RevisitRobotService {
	
	private static final RevisitRobotService revisitRobotService = new RevisitRobotService();
	
	private RevisitRobotService(){}

	public static RevisitRobotService getInstance() {
		return revisitRobotService;
	}
	
	private final Logger loggerDis = CommLogger.getCommLogger("robot_distribute");
	private final Logger loggerErr = CommLogger.getCommLogger("robot_errDis");
	private final EasyCache cache = CacheManager.getMemcache();
	private final EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME,Constants.YW_DS);
	
	/**
	 * 机器人外呼任务
	 * @param publishObj 发布记录对象
	 * @throws SQLException 
	 */
	public void robotDataCall(JSONObject publishObj) throws SQLException {
		//机器人流程 0-外呼表add3字段不写入“activeService”，其他任意标识则写入；原用于区分主动服务模块
		String robotProcess = AppContext.getContext(Constants.APP_NAME).getProperty("ROBOT_PROCESS", "0");
		
		//查询机器人策略配置
		EasySQL robotStrategySQL = new EasySQL("select t1.*,t2.PRIORITY from " + Constants.strategyTable.get(Constants.CHANNEL_ROBOT) + " t1");
		robotStrategySQL.append("left join C_NO_AS_CROWDPACK_PRIORITY t2 on t2.strategy_id = t1.id");
		robotStrategySQL.append("where 1=1");
		robotStrategySQL.append(publishObj.getString("CROWD_PACK_ID"),"and t2.crowd_pack_id = ?");
		robotStrategySQL.append("1","and t1.is_open = ?");
		robotStrategySQL.append("order by t2.strategy_level");
		List<JSONObject> list = query.queryForList(robotStrategySQL.getSQL(), robotStrategySQL.getParams(),new JSONMapperImpl());
		if(list==null||list.size()==0) {
			loggerDis.info("[机器人外呼任务]人群包id：" + publishObj.getString("CROWD_PACK_ID") + "无法查询到策略信息，sql：" + robotStrategySQL.getSQL() + ",参数：" + JSON.toJSONString(robotStrategySQL.getParams()));
			return;
		}
		JSONObject strategyData = list.get(0);
		//判断当前时间是否在外呼时间内
		boolean timeFlag1 = StringUtil.checkTime(strategyData.getString("OUTBOUND_BEGIN_TIME1"),strategyData.getString("OUTBOUND_END_TIME1"),"HH:mm:ss");
		boolean timeFlag2 = StringUtil.checkTime(strategyData.getString("OUTBOUND_BEGIN_TIME2"),strategyData.getString("OUTBOUND_END_TIME2"),"HH:mm:ss");
		if(!timeFlag1&&!timeFlag2) {
			loggerDis.info("[机器人外呼任务]发布记录id：" + publishObj.getString("ID") + "不在外呼时间内，策略信息：" + strategyData);
			return;
		}
		//任务数量
		int freeRadio = strategyData.getIntValue("IDLE_RATIO");//人工空闲外呼比
		int publishNum = publishObj.getIntValue("PUBLISH_NUM");//发布的任务数量
		int freeUserNum = RobotCallUtil.getFreeAgentNum(strategyData.getString("SKILL_GROUP_ID"));//空闲坐席数量
		int needCallNum = freeRadio>=999 ? publishNum:freeUserNum*freeRadio;//机器人需要外呼数量
		if(needCallNum > publishNum) {
			needCallNum = publishNum;
		}
		//呼叫时间间隔
		String strategyId = strategyData.getString("ID");
		String cacheValue = cache.get("JOB_AS_ROBOTCALL_" + strategyId);
		if(StringUtils.isNotBlank(cacheValue)) {
			loggerDis.info("[机器人外呼任务]发布记录id：" + publishObj.getString("ID") + "不满足呼叫时间间隔，策略信息：" + strategyData + ",cacheValue:" +cacheValue);
			return;
		}
		//放进缓存，用于判断呼叫时间间隔
		int intervalTime = strategyData.getIntValue("INTERVAL_TIME");
		if(intervalTime == 0) {
			intervalTime = 1;
		}
		cache.put("JOB_AS_ROBOTCALL_" + strategyId, "1",intervalTime);
		//查询机器人数据列表
		EasySQL robotDataSQL = new EasySQL("select t1.* from c_no_as_crowd t1");
		robotDataSQL.append("join c_no_as_crowd_publish t2 on t1.publish_id = t2.id");
		robotDataSQL.append("where 1=1");
		robotDataSQL.append(publishObj.getString("ID"),"and t1.publish_id = ?");
		robotDataSQL.append(Constants.IS_DISTURB_OFF,"and t1.is_disturb = ?");
		robotDataSQL.append(Constants.REVISIT_CALLOUT_ROBOT,"and t2.callout_type = ?");
		robotDataSQL.append(Constants.HAS_PUBLISHED,"and t1.service_status = ?");
		robotDataSQL.append(needCallNum,"and rownum <= ?");
		query.setMaxRow(10000);
		List<EasyRow> robotList = query.queryForList(robotDataSQL.getSQL(), robotDataSQL.getParams());
		//如果数据为空，则表明这个人群包本次发布的数据已经外呼完毕，更新发布表状态
		if((needCallNum>0||freeRadio==999)&&robotList.size()==0) {
			String updateSQL = "update c_no_as_crowd_publish set publish_state = ? where id = ?";
			query.execute(updateSQL, Constants.HAS_REVISITED,publishObj.getString("ID"));
			loggerDis.info("[机器人外呼任务]当前有空闲坐席或无空闲坐席限制，但需要外呼人群包数量为0，发布记录id:" + publishObj.getString("ID") + "，发布记录改为已回访，sql：" + robotDataSQL.getSQL() + ",param:" + JSON.toJSONString(robotDataSQL.getParams()));
			return;
		}
		if(robotList==null||robotList.size()==0) {
			loggerDis.info("[机器人外呼任务]当前无空闲坐席，发布记录id:" + publishObj.getString("ID"));
			return;
		}
		String currentDate = EasyDate.getCurrentDateString("yyyy-MM-dd");
		String currentTime = EasyDate.getCurrentDateString();
		boolean smsFlag = "1".equals(strategyData.getString("IS_SMS_OPEN"));//短信开关
		boolean shopSmsFlag = "1".equals(strategyData.getString("SMS_TYPE"));//短信类型 0-普通1-商城短信
		//查询待外呼数量
		int robotCallingNum = RobotCallUtil.getCurrentRobotCallNum();
		//最大外呼并发量
		int maxCallNum = ConfigUtil.getInt(Constants.APP_NAME,"ROBOT_CALL_MAX_NUMBER", 100);
		//实际需要外呼的数量
		int realNeedCallNum = (maxCallNum-robotCallingNum) < 0 ? 0:maxCallNum-robotCallingNum;
		//游标
		int flag = 0;
		loggerDis.info("[机器人外呼任务]发布记录id:" + publishObj.getString("ID") + ",人群包数:" + robotList.size() + ",待外呼数：" + robotCallingNum + ",当前实际需要外呼数：" + realNeedCallNum);
//		EasyRow maxData = query.queryForRow("select max(record_id) as max from C_NO_ROBOT_CALL", new Object[] {});
//		long maxNum = maxData.getColumnValue("MAX") == null ? 1:Long.valueOf(maxData.getColumnValue("MAX"));
		HashSet<String> custPhoneSet = new HashSet<>();
		List<CompletableFuture<Void>> futures = new ArrayList<>();
		for(EasyRow row:robotList) {
			if (flag == realNeedCallNum) {
				break;
			}
			try {
				flag++;
				String custPhone = row.getColumnValue("CUST_PHONE");
				if (custPhoneSet.contains(custPhone)) {
					//本次任务存在同样号码的明细，滞留到下次再执行，保证本次多线程写入OCS外呼表不会重复写入
					loggerDis.info("[机器人外呼任务]人群包id：" + publishObj.getString("CROWD_PACK_ID") + "，" + custPhone + "重复，滞留下次执行");
					continue;
				}
				custPhoneSet.add(custPhone);
				loggerDis.info("[机器人外呼任务]外呼任务,id:" + row.getColumnValue("ID") + "写入线程池");
				CompletableFuture<Void> future = CompletableFuture.supplyAsync(() -> {

					try {
						String phone = RobotCallUtil.getFormatPhone(row.getColumnValue("CUST_PHONE"), "0757", strategyData.getString("PREFIX_CODE"));
						String cacheId="cache_as_autoRobotCall_" + phone;//一小时内是否有在呼的号码外呼（当有外呼结果回收 或者同步时，清掉当前缓存）
						String value = cache.get(cacheId);
						if(value==null||"".equals(value)){
							cache.put(cacheId,"1",60*60);
						}else{
							loggerDis.info("号码"+phone+"一小时内有未返回结果的外呼");
							return null;
						}

						//检查用户是否免打扰
						if(!RobotCallUtil.checkDoNotdisturbMobile(custPhone)){
							//免打扰不允许外呼
							String updateSQL = "update c_no_as_crowd set service_status = ?,result = ?,result_time = ? where id = ?";
							query.execute(updateSQL, Constants.OTHER_STATUS, "90", DateUtil.getCurrentDateStr(), row.getColumnValue("ID"));
							loggerErr.info("[机器人外呼任务]已外呼过，无法外呼。人群包明细ID：" + row.getColumnValue("ID") + ",手机号：" + phone + "已设置免打扰");
							cache.delete(cacheId);//释放一个小时限制缓存
							return null;
						}

						if (RobotCallUtil.isAllowToCall(phone, null,false)) {
							JSONObject content = new JSONObject();
							content.put("crowdId", row.getColumnValue("ID"));
							content.put("brandCode", strategyData.getString("BRAND_CODE_R"));//取策略数据
							content.put("brandName", strategyData.getString("BRAND_NAME_R"));
							content.put("productCode", strategyData.getString("PROD_CODE_R"));
							content.put("productName", strategyData.getString("PROD_NAME_R"));
							JSONObject extendPrivateData = new JSONObject();
							extendPrivateData.put("serviceOrderCode",row.getColumnValue("SERVICE_ORDER_CODE"));
							content.put("extendPrivateData",extendPrivateData);
							if (smsFlag) {
								String smsContent = strategyData.getString("SMS_CONTENT");
								if (shopSmsFlag) {
									String partnerPhone = strategyData.getString("PARTNER_PHONE");
									String partnerUrl = strategyData.getString("SHOP_MINI_PROGRAM_URL");
									String url = RobotCallUtil.getShopSmsResult(partnerUrl, partnerPhone);
									smsContent = smsContent.replace("{shopMiniProgramUrl}", url);
								}
								content.put("s_phone", row.getColumnValue("CUST_PHONE"));
								content.put("s_orgCode", strategyData.getString("ORG_CODE"));
								content.put("s_brandCode", strategyData.getString("BRAND_CODE"));
								content.put("s_brandName", strategyData.getString("BRAND_NAME"));
								content.put("s_smsContent", smsContent);
							}
							int beginTime = 0;
							int endTime = 0;
							if (timeFlag1) {
								beginTime = StringUtil.timeToSecond(strategyData.getString("OUTBOUND_BEGIN_TIME1"), 32400);
								endTime = StringUtil.timeToSecond(strategyData.getString("OUTBOUND_END_TIME1"), 64800);
							} else if (timeFlag2) {
								beginTime = StringUtil.timeToSecond(strategyData.getString("OUTBOUND_BEGIN_TIME2"), 32400);
								endTime = StringUtil.timeToSecond(strategyData.getString("OUTBOUND_END_TIME2"), 64800);
							}
							String taskId = strategyData.getString("TASK_ID");
							taskId = StringUtils.isBlank(taskId)?"1": taskId;
							content.put("manual", "99");
							//					maxNum = maxNum + 1;
							//					String id= IDGenerator.getIDByCurrentTime(20);
							RobotCallDTO dto = RobotCallDTO.builder()
									//							.recordId(String.valueOf(maxNum))
									//							.chainN(String.valueOf(maxNum))
									//							.chainId(String.valueOf(maxNum))
									.contactInfo(phone)
									.robotId(strategyData.getString("ROBOT_ID"))
									.content(content.toJSONString())
									.taskId(taskId)
									.dailyFrom(beginTime)
									.dailyTill(endTime)
									.add1(currentDate)
									.add2(row.getColumnValue("ID"))
									.add3("0".equals(robotProcess) ? "" : "activeService")
									.build();
							JSONObject busiData=new JSONObject();
							busiData.put("manual", "99");
							dto.setBusiData(busiData.toJSONString());
							//写入机器人外呼表
							RobotCallUtil.addCall(dto);
							//更新本条数据
							String updateSQL = "update c_no_as_crowd set service_status = ?,agent_name = ?,PRIORITY = ? where id = ?";
							query.execute(updateSQL, Constants.HAS_RECEIVED, "robot", strategyData.getString("PRIORITY"), row.getColumnValue("ID"));
							loggerDis.info("[机器人外呼任务]更新C_NO_AS_CROWD,id:" + row.getColumnValue("ID") + ",sql：" + updateSQL);
						} else {
							//不允许外呼
							String updateSQL = "update c_no_as_crowd set service_status = ?,result = ?,result_time = ? where id = ?";
							query.execute(updateSQL, Constants.OTHER_STATUS, "91", DateUtil.getCurrentDateStr(), row.getColumnValue("ID"));
							loggerErr.info("[机器人外呼任务]已外呼过，无法外呼。人群包明细ID：" + row.getColumnValue("ID") + ",手机号：" + phone + "当月已外呼，不进行外呼");
							//写入主动服务机器人外呼记录表
							String insertSQL = "insert into c_no_as_crowd_robot(id,crowd_id,end_time,manual,record_type) values(?,?,?,?,?)";
							query.execute(insertSQL, RandomKit.uniqueStr(), row.getColumnValue("ID"), currentTime, "0", 2);
							loggerErr.info("[机器人外呼任务]已外呼过，无法外呼。写入主动服务机器人外呼记录表，sql:" + insertSQL);
						}
					} catch (Exception e) {
						loggerDis.error(CommonUtil.getClassNameAndMethod(this) + "人群包ID：" + row.getColumnValue("ID") + "发布异常！" + e.getMessage(), e);
					}
					return null;
				}, ThreadPoolKit.getExecutor());
				futures.add(future);
			}catch (Exception e){
				loggerDis.info("线程error：" + e.getMessage(),e);
			}
		}
		//主线程阻塞，等待线程池处理完毕
		try {
			loggerDis.info("等待所有任务执行完成中，执行数量="+futures.size());
			CompletableFuture<Void> voidCompletableFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
			voidCompletableFuture.join();
			loggerDis.info("等待所有任务执行完成");
		} catch (Exception e) {
			loggerDis.info("[机器人外呼任务]人群包id：" + publishObj.getString("CROWD_PACK_ID") + "异常");
		}
		loggerDis.info("[机器人外呼任务]人群包id：" + publishObj.getString("CROWD_PACK_ID") + "写入OCS结束");
	}


}
