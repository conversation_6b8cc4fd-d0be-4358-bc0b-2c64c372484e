package com.yunqu.cc.activeService.utils;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import com.yunqu.cc.activeService.service.UserDndService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.base.Constants;
import com.yunqu.cc.activeService.model.RobotCallDTO;
import com.yunqu.cc.activeService.service.AutoRobotCallService;

/**
 * 机器人外呼
 * <AUTHOR>
 */
public class RobotCallUtil {

	private static final EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.YW_DS);
	
	private static final Logger logger = CommLogger.getCommLogger("robot_call");
	private static final Logger recycleLogger = CommLogger.getCommLogger("robot_call_recycle");
	private static final Logger autocallLogger = CommLogger.getCommLogger("autocall");
	private static final Logger strategyLogger = CommLogger.getCommLogger("strategy");
	private static final Logger autocallStartgy2Logger = CommLogger.getCommLogger("autocall_startgy2");


	private static final EasyCache cache = CacheManager.getMemcache();

	/**
	 * 写入外呼表
	 * @param robotCallDTO
	 * @throws SQLException
	 */
	public static void addCall(RobotCallDTO robotCallDTO) throws SQLException {
		String insertSQL = "insert into c_no_robot_call(contact_info,"
				+ "content,task_id,robot_id,daily_from,daily_till,add1,add2,add3,busi_data) values("
				+ "?,?,?,?,?,?,?,?,?,?)";
		//写入机器人外呼表 busi_data {"orderId":"1207670266"|"manual":"2"}
		query.execute(insertSQL,
				robotCallDTO.getContactInfo(),
				robotCallDTO.getContent(),
				robotCallDTO.getTaskId(),
				robotCallDTO.getRobotId(),
				robotCallDTO.getDailyFrom(),
				robotCallDTO.getDailyTill(),
				robotCallDTO.getAdd1(),
				robotCallDTO.getAdd2(),
				robotCallDTO.getAdd3(),
				robotCallDTO.getBusiData()
				);
		
	}
	
	
	
	/**
	 * 外呼手机号格式化
	 * @param phone
	 * @param localNumber
	 * @return
	 */
	public static String getFormatPhone(String phone,String localNumber,String callKey){
		String newPhone = phone;
		if(StringUtils.isBlank(callKey)){
			 callKey = Constants.CALL_KEY;
		}
		if(newPhone.contains("-")){
			newPhone = newPhone.replace("-","");
			return newPhone;
		}else{
			if(newPhone.startsWith("0")){//固话
				return callKey+newPhone;
			}else if(StringUtils.isBlank(newPhone) || newPhone.length() < 7){
				return callKey+newPhone;
			}
			phone = newPhone.substring(0,7);
			String sql = "SELECT CODE,CITY FROM TELNUM_REGION WHERE NUM = ? ";
			EasyRow row;
			try {
				row = query.queryForRow(sql, new Object[]{phone});
				if(row != null &&row.getColumnValue("CODE").equals(localNumber) ){//是否归属地号码
					return callKey+newPhone;
				}else{
					return callKey+"0"+newPhone;
				}
			} catch (SQLException e) {
				// TODO Auto-generated catch block
				logger.error("error:" + e.getMessage(), e);
			}
		}
		return newPhone;
	}


	public static boolean checkWhiteMobile(String phone) {
		boolean result = false;
		//手机号白名单,主要用于业务测试,免去外呼限制条件
		String whiteMolbeListStr = cache.get("ROBOT_CALL_WHITEMOBILE_LIST");
		if (StringUtils.isBlank(whiteMolbeListStr)) {
			whiteMolbeListStr = ConfigUtil.getString(Constants.APP_NAME, "ROBOT_CALL_WHITEMOBILE_LIST");
			cache.put(whiteMolbeListStr, 600);//白名单10分钟刷新一次，提高执行效率
		}
		if (StringUtils.isNotBlank(whiteMolbeListStr)) {
			String[] whiteMolbeList = whiteMolbeListStr.split(",|，");
			for (String m : whiteMolbeList) {
				if (phone.indexOf(m) != -1) {
					result = true;
				}
			}
		}
		return result;
	}

	/**
	*@title 校验用户是否免打扰
	*@date 2024/7/3
	*@version 1.0
	*/
	public static boolean checkDoNotdisturbMobile(String phone) {
		//手机号白名单,主要用于业务测试,免去外呼限制条件
		if(checkWhiteMobile(phone)){
			return true;
		}
		boolean result = true;
		JSONObject json = new JSONObject();
		JSONObject params = new JSONObject();
		params.put("phone",phone);
		params.put("type",1);
		params.put("channelId",3);
		json.put("params",params);
		UserDndService userDndService = UserDndService.getInstance();
		try {
			JSONObject strategy = userDndService.getStrategy(json);
			JSONObject respData = strategy.getJSONObject("respData");
			if(respData!=null){
				Boolean data =  respData.getBoolean("isExist");
				if(data!=null){
					strategyLogger.info("校验用户是否免打扰时isExist = " + data + "，用户手机号： " + phone);
					if (data) {
						strategyLogger.info("用户免打扰，不进行外呼，开始记录免打扰记录表： " + respData + "，用户手机号： " + phone);
						JSONArray jsonArray = respData.getJSONArray("data");
						json.put("command", "saveRemindRecord");
						params.put("CHANNEL_NAME", "AI主动外呼");
						params.put("STRATEGY_ID", jsonArray.getJSONObject(0).getString("STRATEGY_ID"));
						params.put("STRATEGY_NAME", jsonArray.getJSONObject(0).getString("STRATEGY_NAME"));
						params.put("CROWD_PACK_ID", jsonArray.getJSONObject(0).getString("CROWD_PACK_ID"));
						params.put("CROWD_NAME", jsonArray.getJSONObject(0).getString("CROWD_NAME"));
						params.put("CREATE_ACC", "SYSTEM");
						json.put("params", params);
						userDndService.saveRemindRecord(json);
					}
					result = !data.booleanValue();
				}
			}
		} catch (Exception e) {
			strategyLogger.info("校验用户是否免打扰时出现异常 = " + e.getMessage() + "，用户手机号： " + phone);
		}
		return result;
	}

		/**
        *  判断手机号是否允许外呼
         * @param phone 带有前叉码的号码
         * @param originalPhone 原始号码，不带前叉码
         * @param isAutoCall 是否自动外呼表的数据 C_NO_AS_ROBOT_AUTO_CALL
         * @return
         */
	public static boolean isAllowToCall(String phone,String originalPhone,Boolean isAutoCall) throws Exception {

		//手机号白名单,主要用于业务测试,免去外呼限制条件
		if(checkWhiteMobile(phone)){
			return true;
		}

		String today = DateUtil.getCurrentDateStr("yyyy-MM-dd");
		String lastThirtyDay = DateUtil.addDay("yyyy-MM-dd", today, -30);
		//查询通话记录表近30天被呼叫次数
		String sql="select count(1) from c_pf_call_record where called = ? and RINGING_TIME >= ?";
		int count = query.queryForInt(sql, new Object[]{phone,lastThirtyDay+" 00:00:00"});
		if(count >0) {
			return false;
		}
//
		int successNum = 0;
		int noBodyNum = 0;
//			int allNum = 0;//近30天所有机器人外呼数量，预留
		if(isAutoCall){//外呼机器人的

			EasySQL sql2 = new EasySQL("select");
			//call_result is null 外呼表记录没有外呼结果 or 有效通话call_result=33，一律不入OCS表，防止重复外呼
			sql2.append("sum(case when call_result=33 or call_result is null then 1 else 0 end) as success_num ");//呼叫成功数量/待呼
			//sql2.append("count(1) as all_num");//近30天全部数量
			sql2.append("from c_no_robot_call where 1=1");
			sql2.append(phone,"and contact_info = ?");
			sql2.append(lastThirtyDay,"and add1 >= ?");
			EasyRow row = query.queryForRow(sql2.getSQL(), sql2.getParams());
			if(StringUtils.isNotBlank(row.getColumnValue("SUCCESS_NUM"))) {
				successNum = Integer.valueOf(row.getColumnValue("SUCCESS_NUM"));
			}

//				if(StringUtils.isNotBlank(row.getColumnValue("ALL_NUM"))) {
//					allNum = Integer.valueOf(row.getColumnValue("ALL_NUM"));
//				}
			//重呼
			String lastThirtyDateTime = lastThirtyDay+" 00:00:00";
			EasySQL sql3 = new EasySQL();
			sql3.append("select sum(case when REVISIT_RESULT = 3 or REVISIT_RESULT = 2 then 1 else 0 end) as nobody_num ");//无人接听数量  无人接听/无法接通
			sql3.append("from (");//
			sql3.append("select t2.REVISIT_RESULT ");
			sql3.append(" from C_NO_AS_ROBOT_AUTO_CALL t1");
			sql3.append("left join  C_NO_AS_ROBOT_CALL_RESULT t2 on t2.ROBOT_AUTO_CALL_ID=t1.id  ");
			sql3.append(" where 1=1");
			sql3.append(originalPhone,"and CUST_PHONE = ?");
			sql3.append(lastThirtyDateTime,"and PUBLISH_TIME >= ?");
			sql3.append("Y"," and LAST_CALL_FLAG =? ");
			sql3.append("UNION all ");
			sql3.append("select t2.REVISIT_RESULT ");
			sql3.append(" from C_NO_AS_ROBOT_AUTO_CALL_HIS t1");
			sql3.append("left join  C_NO_AS_ROBOT_CALL_RESULT t2 on t2.ROBOT_AUTO_CALL_ID=t1.id  ");
			sql3.append(" where 1=1");
			sql3.append(originalPhone,"and CUST_PHONE = ?");
			sql3.append(lastThirtyDateTime,"and PUBLISH_TIME >= ?");
			sql3.append("Y"," and LAST_CALL_FLAG =?");
			sql3.append(")");
			EasyRow row1 = query.queryForRow(sql3.getSQL(), sql3.getParams());
			if(StringUtils.isNotBlank(row1.getColumnValue("NOBODY_NUM"))) {
				noBodyNum = Integer.valueOf(row1.getColumnValue("NOBODY_NUM"));
			}

//			autocallLogger.info("sql3="+sql3.getSQL()+",param="+sql3.getParams());
			autocallLogger.info("noBodyNum="+noBodyNum);
		}else{

			EasySQL sql2 = new EasySQL("select");
			//call_result is null 外呼表记录没有外呼结果 or 有效通话call_result=33，一律不入OCS表，防止重复外呼
			sql2.append("sum(case when call_result=33 or call_result is null then 1 else 0 end) as success_num,");//呼叫成功数量/待呼
			sql2.append("sum(case when call_result = 3 or call_result = 6 then 1 else 0 end) as nobody_num ");//无人接听数量
			//sql2.append("count(1) as all_num");//近30天全部数量
			sql2.append("from c_no_robot_call where 1=1");
			sql2.append(phone,"and contact_info = ?");
			sql2.append(lastThirtyDay,"and add1 >= ?");
			EasyRow row = query.queryForRow(sql2.getSQL(), sql2.getParams());
			if(StringUtils.isNotBlank(row.getColumnValue("SUCCESS_NUM"))) {
				successNum = Integer.valueOf(row.getColumnValue("SUCCESS_NUM"));
			}
			if(StringUtils.isNotBlank(row.getColumnValue("NOBODY_NUM"))) {
				noBodyNum = Integer.valueOf(row.getColumnValue("NOBODY_NUM"));
			}
//				if(StringUtils.isNotBlank(row.getColumnValue("ALL_NUM"))) {
//					allNum = Integer.valueOf(row.getColumnValue("ALL_NUM"));
//				}
		}


		/**
		 * 近30天
		 * 1.人工外呼/自动外呼成功1次，不再允许外呼
		 * 2.自动外呼未接通最多允许1次，不再允许外呼
		 */
		//外呼成功记录判断
		if(successNum >= 1) {//历史数据临时查询
			return false;
		}
		//外呼未接通记录判断");
//			if(successNum >0) {
//				return false;
//			}
		//机器人无人接听判断
		return noBodyNum< 1;//历史数据临时查询

	}


	/**
	 *  判断手机号是否允许外呼
	 * @param phone 带有前叉码的号码
	 * @param originalPhone 原始号码，不带前叉码
	 * @param isAutoCall 是否自动外呼表的数据 C_NO_AS_ROBOT_AUTO_CALL
	 * @return
	 */
	public static boolean isAllowToCallByStratgy2(String phone,String originalPhone,Boolean isAutoCall,String crowdPackId,int serialId,String serviceOrderNo) throws Exception {

		//手机号白名单,主要用于业务测试,免去外呼限制条件
		if(checkWhiteMobile(phone)){
			return true;
		}

		String today = DateUtil.getCurrentDateStr("yyyy-MM-dd");
		String lastThirtyDay = DateUtil.addDay("yyyy-MM-dd", today, -7);
		int todayCallNum = 0;
		int day7CallNum = 0;
		if(isAutoCall){//外呼机器人的
			String lastThirtyDateTime = today+" 00:00:00";
			//同一个人群包，同一个用户号码一天内最多外呼一次
			EasySQL sql1 = new EasySQL("select");
			sql1.append("count(1) as CALL_NUM ");//呼叫数量
			//sql2.append("count(1) as all_num");//近30天全部数量
			sql1.append("from c_no_robot_call where ");
			sql1.append(today,"add1 >= ?");
			sql1.append(phone,"and contact_info = ?");
			sql1.appendLike(crowdPackId,"and BUSI_DATA like ?");
			EasyRow row = query.queryForRow(sql1.getSQL(), sql1.getParams());
			if(StringUtils.isNotBlank(row.getColumnValue("CALL_NUM"))) {
				todayCallNum = Integer.valueOf(row.getColumnValue("CALL_NUM"));
			}

			//同一个服务单，7天内最多外呼两次
			EasySQL sql2 = new EasySQL("select");
			sql2.append("count(1) as CALL_NUM ");//呼叫数量
			//sql2.append("count(1) as all_num");//近30天全部数量
			sql2.append("from c_no_robot_call where ");
			sql2.append(lastThirtyDay,"add1 >= ?");
			sql2.appendLike(serviceOrderNo,"and BUSI_DATA like ?");
			EasyRow row2 = query.queryForRow(sql2.getSQL(), sql2.getParams());
			if(StringUtils.isNotBlank(row2.getColumnValue("CALL_NUM"))) {
				day7CallNum = Integer.valueOf(row2.getColumnValue("CALL_NUM"));
			}
			//打印day7CallNum
			autocallStartgy2Logger.info("[自动外呼"+serialId+"]呼叫情况：day7CallNum="+day7CallNum+"，todayCallNum="+todayCallNum);
		}else{
			autocallStartgy2Logger.info("人工发布机器人外呼暂时不支持此策略");
		}
		//外呼成功记录判断
		if(day7CallNum < 2&&todayCallNum<1) {
			return true;
		}else{
			return false;
		}
	}

	/**
	 *  判断手机号是否允许外呼
	 * @param phone
	 * @return
	 */
	public static boolean checkCallIn(String phone){
		try {
			String today = DateUtil.getCurrentDateStr("yyyy-MM-dd");
			String lastThirtyDay = DateUtil.addDay("yyyy-MM-dd", today, -30);
			//查询通话记录表近30天被呼叫次数
			String sql="select count(1) from c_pf_call_record where called = ? and RINGING_TIME >= ?";
			int count = query.queryForInt(sql, new Object[]{phone,lastThirtyDay+" 00:00:00"});

			return count <= 0;
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			logger.error("error:" + e.getMessage(), e);
		}
		return false;
	}
	

	/**
	 * 获取当前空闲坐席
	 * @param skillGroupId
	 * @return
	 */
	public static int getFreeAgentNum(String skillGroupId){
		String agentFreedStatus = Constants.AGENT_FREE_STATUS;
		String[] list = agentFreedStatus.split(",");
		String status = "";
		for(String str:list){
			status += "".equals(status) ? "'"+str+"'" : ",'"+str+"'";
		}
		String sql = "select  COUNT(1) from stat.cc_rpt_agent_monitor where "
				+ " agent_id in ( "
				+ " select user_acct from ycbusi.CC_SKILL_GROUP_USER left join ycuser.CC_USER on CC_SKILL_GROUP_USER.USER_ID=CC_USER.USER_ID "
				+ " where  skill_group_id = ? "
				+ " ) "
				+ "and AGENT_STATE in ("+status+") ";
		try {
			int num = query.queryForInt(sql, new Object[]{skillGroupId});
			return num;
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			logger.error("error:" + e.getMessage(), e);
		}
		return 0;
	}
	
	/**
	 * 当前机器人呼叫数
	 * @return
	 */
	public static int getCurrentRobotCallNum(){
		String getCallRecordStatus = ConfigUtil.getString("neworder","CALL_RECORD_STATUS", "1,2");
		String sql="select count(1) from C_NO_ROBOT_CALL  where  RECORD_STATUS in ("+getCallRecordStatus+")";
		try {
			//当前呼叫数
			int num = query.queryForInt(sql, new  Object[]{});
			return num;
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			logger.error("error:" + e.getMessage(), e);
		}
		return 0;
	}
	
	
	/**
	 * 获取商城短连接
	 * @param pagePath
	 * @param phone
	 * @return
	 * @throws ServiceException
	 */
	public static String getShopSmsResult(String pagePath,String phone){
		String shortScheme = "";
		JSONObject params = new JSONObject();
		JSONObject bizargs = new JSONObject();
		bizargs.put("page_path", pagePath);
		bizargs.put("seller_mobile", phone);
		bizargs.put("mtag", ConfigUtil.getString(Constants.APP_NAME,"MTAG"));
		
		try {
			IService service = ServiceContext.getService(ServiceID.MIXGW_ELEC_INTEFACE);
			params.put("command","genshortscheme");
			params.put("bizargs",bizargs);
			JSONObject response = service.invoke(params);
			JSONObject respData = response.getJSONObject("respData");
			String respCode = response.getString("respCode");
			if ("000".equals(respCode)) {// "000" 成功 
				if("000000".equals(respData.getString("code"))){
					shortScheme = respData.getJSONObject("data").getString("shortLinkUrl");
					shortScheme = " " + shortScheme.replace("https://", "") + " ";
				}
			}
		} catch (ServiceException e) {
			logger.error("error:" + e.getMessage(),e);
		}
		return shortScheme;
	}
	
	/**
	 * 获取机器人成功外呼标志
	 * @return
	 */
	public static String getRobotCallSuccessStatus(){
		String status = AppContext.getContext("neworder").getProperty("CALL_ERROR_STATUS_SUCCESS","33");
		String[] list= status.split(",");
		String newFreeStatus="";
		for(String str:list){
			newFreeStatus = "".equals(newFreeStatus)?"'"+str+"'":newFreeStatus+",'"+str+"'";
		}
		return newFreeStatus;
	}
	
	
	/**
	 * 机器人外呼数据回收
	 * @param tableName 表名
	 * @param data 业务数据
	 * @throws SQLException 
	 */
	public static String robotDataRecycle(String tableName,JSONObject data) throws SQLException {
		
		String callResult = data.getString("CALL_RESULT");
		String result="20";
		String resultContent = "";
		if(StringUtil.isStrInListStr(callResult,
				AppContext.getContext("neworder").getProperty("CALL_ERROR_STATUS_WRJT",""))){//无人接听
			result="2";
			resultContent = "无人接听";
		}else if(StringUtil.isStrInListStr(callResult,
				AppContext.getContext("neworder").getProperty("CALL_ERROR_STATUS_WFJT","3"))){//无法接通
			result="3";
			resultContent = "无法接通";
		}else if(StringUtil.isStrInListStr(callResult,
				AppContext.getContext("neworder").getProperty("CALL_ERROR_STATUS_YHJF",""))){//用户拒访
			result="4";
			resultContent = "用户拒访";
		}else if(StringUtil.isStrInListStr(callResult,
				AppContext.getContext("neworder").getProperty("CALL_ERROR_STATUS_HMCW",""))){//号码错误
			result="5";
			resultContent = "号码错误";
		}else if(StringUtil.isStrInListStr(callResult,
				AppContext.getContext("neworder").getProperty("CALL_ERROR_STATUS_DHKH",""))){//电话空号
			result="6";
			resultContent = "电话空号";
		}else if(StringUtil.isStrInListStr(callResult,
				AppContext.getContext("neworder").getProperty("CALL_ERROR_STATUS_TJ",""))){//停机
			result="9";
			resultContent = "停机";
		}
		data.put("RESULT_CONTENT",resultContent);
		AutoRobotCallService autoRobotCallService=new AutoRobotCallService();
		JSONObject robotStrategy = autoRobotCallService.getRobotStrategy(data.getString("CROWD_PACK_ID"));
		//修改明细表
		String currentDateStr=DateUtil.getCurrentDateStr();
		EasySQL sql = new EasySQL();
		sql.append("update "+tableName+" set ");
		sql.append(Constants.HAS_REVISITED,"SERVICE_STATUS=?");
		sql.append(result," , REVISIT_RESULT =?");
		sql.append(currentDateStr," , RESULT_TIME =?");
		if(Constants.MANUAL_ROBOT_CALL_TABLE.equals(tableName)) {
			sql.append(EasyDate.getCurrentDateString("yyyyMMdd")," , SERVICE_DATE =?");
			sql.append(Constants.CHANNEL_ROBOT," , RESULT_SOURCE =?");
		}
		if(Constants.AUTO_ROBOT_CALL_TABLE.equals(tableName)) {
			sql.append(resultContent," , RESULT_CONTENT =?");
		}
		sql.append("where 1=1");
		sql.append(data.getString("CROWD_ID")," and id=?",false);
		query.execute(sql.getSQL(), sql.getParams());

		recycleLogger.info("外呼表"+tableName+","+data.getString("CROWD_ID"));
		if(Constants.AUTO_ROBOT_CALL_TABLE.equals(tableName)&&StringUtils.isNotBlank(data.getString("CROWD_ID"))){//外呼表
			try {
//				EasySQL crowdRobotSql=new EasySQL();
//				crowdRobotSql.append(data.getColumnValue("CROWD_ID"),"select  *  from  c_no_as_crowd_robot where CROWD_ID=?");
//				JSONObject crowdRobotInfo = query.queryForRow(crowdRobotSql.getSQL(), crowdRobotSql.getParams(),new JSONMapperImpl());
//				未呼通的重呼
				EasySQL lastCallSql=new EasySQL();
				lastCallSql.append(data.getString("CROWD_ID"),"select  count(1) as COUNT  from  C_NO_AS_ROBOT_CALL_RESULT  where ROBOT_AUTO_CALL_ID=?");
				JSONObject lastCall = query.queryForRow(lastCallSql.getSQL(), lastCallSql.getParams(),new JSONMapperImpl());
				String lastCallFlag=Constants.SN_Y;
				if(StringUtils.isNotBlank(robotStrategy.getString("RECALL_TIMES"))&&Integer.valueOf(robotStrategy.getString("RECALL_TIMES"))>=(lastCall.getInteger("COUNT")+1)){
//				if(StringUtils.isNotBlank(data.getColumnValue("CALL_TIMES"))&&Integer.valueOf(data.getColumnValue("CALL_TIMES"))>=(lastCall.getInteger("COUNT")+1)){
					lastCallFlag=Constants.SN_N;
					recycleLogger.info(data.getString("CROWD_ID")+"非最后一次外呼");
				}else{
					recycleLogger.info(data.getString("CROWD_ID")+"最后一次外呼");
				}
				
				String outId=data.getString("OUT_ID");
				EasyRecord saveRecord = new EasyRecord("C_NO_AS_ROBOT_CALL_RESULT", "ID");
				saveRecord.set("ID",  RandomKit.uniqueStr());
				saveRecord.set("ROBOT_CALL_ID", "");//c_no_as_crowd_robot.id
				saveRecord.set("REVISIT_RESULT", result);
				saveRecord.set("RESULT_CONTENT", resultContent);
				saveRecord.set("RESULT_TIME", currentDateStr);
				saveRecord.set("CARD_ID", "");
				saveRecord.set("INTENTION_ID", "");
				saveRecord.set("INTENTION_NAME", "");
				saveRecord.set("INTENTION_TAG", "");
				saveRecord.set("INTENTION_RESULT", "");
				saveRecord.set("INTENTION_RESULT_TYPE", "");
				saveRecord.set("LAST_CALL_FLAG", lastCallFlag);//需要查询是否最后一次
				saveRecord.set("ROBOT_AUTO_CALL_ID",data.getString("CROWD_ID"));//c_no_as_robot_auto_call.id
				saveRecord.set("IS_RETURN_RESULT", Constants.SN_N);
				saveRecord.set("SORT_NUM", 0);
				query.save(saveRecord);
				recycleLogger.info(data.getString("CROWD_ID")+"外呼");
//				未呼通的重呼
				if(Constants.SN_N.equals(lastCallFlag)){
					//如果不是最后一次 ，且需要重呼
					int recallIntervalTime=0;//RECALL_INTERVAL_TIME
//					C_NO_AS_ROBOT_STRATEGY。RECALL_INTERVAL_TIME
					
					JSONObject strategy = AutoRobotCallService.getInstance().getRobotStrategy(data.getString("CROWD_PACK_ID"));
					if(strategy!=null){
						if(StringUtils.isNotBlank(strategy.getString("RECALL_INTERVAL_TIME"))){
							recallIntervalTime=strategy.getInteger("RECALL_INTERVAL_TIME");
						}
					}
					LocalDateTime now = LocalDateTime.now();
			        LocalDateTime futureTime = now.plusSeconds(recallIntervalTime*60);
			        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
			        String futureTimeString = futureTime.format(formatter);
			        
					EasyRecord updateRecord = new EasyRecord("C_NO_AS_ROBOT_AUTO_CALL", "ID");
					updateRecord.set("ID",  data.getString("CROWD_ID"));
					updateRecord.set("PUBLISH_TIME",  futureTimeString);
					updateRecord.set("SERVICE_STATUS",  Constants.HAS_PUBLISHED);
					query.update(updateRecord);
				}
			} catch (Exception e) {
				recycleLogger.info(data.getString("CROWD_ID")+"出错"+e.getMessage(),e);
			}
			
		}

		String phone = data.getString("CONTACT_INFO");
		String cacheId="cache_as_autoRobotCall_" + phone;//一小时内是否有在呼的号码外呼（当有外呼结果回收 或者同步时，清掉当前缓存）
		cache.delete(cacheId);
		recycleLogger.info("机器人外呼数据回收成功,清除缓存: " + cacheId);

		return result;
	}
	
	public static List<JSONObject> getRobotIntentList(EasyQuery  query,String crowdPackId,JSONArray arr) throws SQLException {
		List<JSONObject> list = new ArrayList<JSONObject>();

		EasySQL sql = new EasySQL("select * from C_NO_AS_ROBOT_SMS t1");
		sql.append("left join C_NO_AS_ROBOT_STRATEGY t2 on t1.strategy_id = t2.id");
		sql.append("where 1=1");
		sql.append(crowdPackId,"and t2.crowd_pack_id = ?");
		sql.append("Y","and t1.is_open = ?");

		logger.info("getRobotIntentList----" + sql.getSQL() + ",param:" + JSON.toJSONString(sql.getParams()));
		list = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
		return list;
	}
	
	/**
	 * 解析机器人意图发短信
	 * @param phone
	 * @param arr
	 * @param callInfo C_NO_AS_ROBOT_AUTO_CALL表数据
	 * @return 发送成功的数量
	 * 
	 */
	public static int sendRobotSMSByIntention(String crowdPackId,String phone,JSONArray arr,JSONObject callInfo) {
		int successNum = 0;
		JSONArray cardList = new JSONArray();//卡片
		JSONArray intentionNameList = new JSONArray();//意图
		for(int i=0;i<arr.size();i++) {
			String[] l = arr.getString(i).split("_");
			if(l.length < 1) {
				continue;
			}
			cardList.add(l[0]);
			intentionNameList.add(l[1]);
		}
		if(cardList.size()==0||intentionNameList.size()==0) {
			return 0;
		}
		EasySQL sql = new EasySQL("select * from C_NO_AS_ROBOT_SMS t1");
		sql.append("left join C_NO_AS_ROBOT_STRATEGY t2 on t1.strategy_id = t2.id");
		sql.append("where 1=1");
		sql.append(crowdPackId,"and t2.crowd_pack_id = ?");
		sql.append("Y","and t1.is_open = ?");
		sql.append("Y","and t1.SMS_OPEN = ?");
		sql.append("and ( ");
		String str = "";
		for(int i=0;i<cardList.size();i++) {
			if(i!=0) {
				str = "or";
			}
			sql.append(cardList.get(i), str + " ( ROBOT_CARD_ID = ? ");
			sql.append(intentionNameList.get(i),"and ROBOT_INTENTION_NAME = ?)");
		}
		sql.append(" ) ");

//		
		try {
			IService service = ServiceContext.getService("SMSGW_INTEFACE");
			JSONObject smsParams = new JSONObject();
			smsParams.put("command",ServiceCommand.SENDMESSAGE);
			smsParams.put("sender", Constants.APP_NAME);
			smsParams.put("serialId", IDGenerator.getDefaultNUMID());
			smsParams.put("password", "YQ_85521717");
			smsParams.put("source", "05");
			smsParams.put("busiId", IDGenerator.getIDByCurrentTime(20));
			smsParams.put("sendTime", EasyCalendar.newInstance().getDateTime("-")); 
			smsParams.put("userAcc", "system");
			logger.info("----log:" + sql.getSQL() + ",param:" + JSON.toJSONString(sql.getParams()));
			List<EasyRow> list = query.queryForList(sql.getSQL(), sql.getParams());
			for(EasyRow r:list) {
				String content = r.getColumnValue("CONTENT");
				String expandData = callInfo.getString("EXPAND_DATA");//拓展参数
				String uniqueIntentFlag = r.getColumnValue("UNIQUE_INTENT_FLAG");

				//当前意图短信在命中的意图短信数量大于1时不允许发送
				if("Y".equals(uniqueIntentFlag) && list.size() > 1){
					continue;
				}

				logger.info("----log:拓展字段数据处理前"+expandData+"-->"+content);

				try {
					if(StringUtils.isNotBlank(expandData)&&StringUtils.isNotBlank(content)){
						JSONObject expandDataJson=JSON.parseObject(expandData);
						String[] contents = content.split("\\{");
						for(int i=1;i<contents.length;i++ ){
							String key = contents[i].substring(0,contents[i].indexOf("}"));
							if(expandDataJson.getString(key) != null&&StringUtils.isNotBlank(expandDataJson.getString(key))){
								String value=expandDataJson.getString(key);
								content=content.replace("${"+key+"}",value);//从工单中获取信息
								content=content.replace("{"+key+"}",value);//从工单中获取信息
							}else{
								content=content.replace("${"+key+"}","");
								content=content.replace("{"+key+"}","");
							}
						}
						logger.info("----log:拓展字段数据生成"+expandData+"-->"+content);
						smsParams.put("model", r.getColumnValue("ORG_CODE"));
						smsParams.put("category", r.getColumnValue("BRAND_CODE"));
						smsParams.put("receivers", StringUtil.spiltByRegex(phone,";",content));
						JSONObject resp = service.invoke(smsParams);
						if(GWConstants.RET_CODE_SUCCESS.equals(resp.getString("respCode"))){
							successNum++;
						}
					}
				} catch (Exception e) {
					logger.error("----log:拓展字段数据处理异常"+expandData+"-->"+e.getMessage(),e);
				}
			}
		} catch (Exception e) {
			logger.error("调用短信接口error:" + e.getMessage(),e);
		}
		return successNum;
	}
	
	public static void main(String[] args) {

		String content="尊敬的用户您好，我是{brandName}官方客服，感谢您使用{brandName}产品，已给您发送了{brandName} {productName}调研问卷，您点开短信链接填写即可参与，谢谢！{url}";
		JSONObject expandDataJson=new JSONObject();
		expandDataJson.put("brandName", "1");
		String[] contents = content.split("\\{");
		for(int i=1;i<contents.length;i++ ){
			String key = contents[i].substring(0,contents[i].indexOf("}"));
			if(expandDataJson.getString(key) != null&&StringUtils.isNotBlank(expandDataJson.getString(key))){
				String value=expandDataJson.getString(key);
				content=content.replace("${"+key+"}",value);//从工单中获取信息
				content=content.replace("{"+key+"}",value);//从工单中获取信息
			}else{
				content=content.replace("${"+key+"}","");
				content=content.replace("{"+key+"}","");
			}
		}
		System.out.println(content);
	}
	private static String encodeURI(String string) {
		// TODO Auto-generated method stub
		return null;
	}



	/**
	 * 自动外呼结果表
	 * @param crowdPackId
	 * @param phone
	 * @param arr
	 * @return
	 */
	public static int saveCallResult(String crowRobotId,String robotAutoCallId,String crowdPackId,String phone,JSONArray arr,String currentDateStr,String revisitResult,String resultContent) {
		logger.info("----自动外呼结果表log:" + crowRobotId+";"+robotAutoCallId);
		int successNum = 0;
		try {
			JSONArray cardList = new JSONArray();//卡片
			JSONArray intentionNameList = new JSONArray();//意图
			Map<String, String> intentionNameMap = new LinkedHashMap<>();
			for(int i=0;i<arr.size();i++) {
				String[] l = arr.getString(i).split("_");
				if(l.length < 1) {
					continue;
				}
				//去重，重复的卡片ID，取后者(同一个卡片有多个意图，取最新)
				intentionNameMap.put(l[0], l[1]);
//				cardList.add(l[0]);
//				intentionNameList.add(l[1]);
			}
			//将intentionNameMap的key和value分别放入cardList和intentionNameList
			for (Map.Entry<String, String> entry : intentionNameMap.entrySet()) {
				cardList.add(entry.getKey());
				intentionNameList.add(entry.getValue());
			}

			if(cardList.size()==0||intentionNameList.size()==0) {
				return 0;
			}
			EasySQL sql = new EasySQL("select * from C_NO_AS_ROBOT_SMS t1");//策略表
			sql.append("left join C_NO_AS_ROBOT_STRATEGY t2 on t1.strategy_id = t2.id");
			sql.append("where 1=1");
			sql.append(crowdPackId,"and t2.crowd_pack_id = ?");
			sql.append("Y","and t1.is_open = ?");
			sql.append("and ( ");
			String str = "";
			for(int i=0;i<cardList.size();i++){
				if(i!=0) {
					str = "or";
				}
				sql.append(cardList.get(i), str + " ( ROBOT_CARD_ID = ? ");
				sql.append(intentionNameList.get(i),"and ROBOT_INTENTION_NAME = ?)");
			}
			sql.append(" ) ");

			logger.info("----log:" + sql.getSQL() + ",param:" + JSON.toJSONString(sql.getParams()));
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			logger.info("----log:" + list.size());
			//是否需要重呼
			
			EasySQL lastCallSql=new EasySQL();
			lastCallSql.append(robotAutoCallId,"select  count(1) as COUNT  from  C_NO_AS_ROBOT_CALL_RESULT  where ROBOT_AUTO_CALL_ID=?");
			
			JSONObject lastCall = query.queryForRow(lastCallSql.getSQL(), lastCallSql.getParams(),new JSONMapperImpl());
			String lastCallFlag=Constants.SN_Y;
			//C_NO_AS_ROBOT_AUTO_CALL  
			int recallIntervalTime=0;//RECALL_INTERVAL_TIME
			if(list.size()>0&&StringUtils.isNotBlank(list.get(0).getString("RECALL_TIMES"))&&Integer.valueOf(list.get(0).getString("RECALL_TIMES"))>=(lastCall.getInteger("COUNT")+1)){
				lastCallFlag=Constants.SN_N;
				recycleLogger.info(robotAutoCallId+"非最后一次外呼");
			}else{
				recycleLogger.info(robotAutoCallId+"最后一次外呼");
			}
			Boolean isRecall=false;//是否需要重呼
			for(JSONObject r:list) {
				if(Constants.SN_N.equals(lastCallFlag)&&StringUtils.isNotBlank(r.getString("RECALL_FLAG"))&&"1".equals(r.getString("RECALL_FLAG"))){
					//有命中需要重呼
					isRecall=true;
				}
				if(StringUtils.isNotBlank(r.getString("RECALL_INTERVAL_TIME"))){
					recallIntervalTime=r.getInteger("RECALL_INTERVAL_TIME");
				}
			}
			List<JSONObject> newList=new ArrayList<JSONObject>();
			for (int i = 0; i < cardList.size(); i++) {
				for (int y = 0; y < list.size(); y++) {
					recycleLogger.info("ROBOT_CARD_ID="+list.get(y).get("ROBOT_CARD_ID")+",ROBOT_INTENTION_NAME="+list.get(y).get("ROBOT_INTENTION_NAME"));
					recycleLogger.info("cardList="+cardList.get(i)+",intentionNameList="+intentionNameList.get(i));
					if (cardList.get(i).equals(list.get(y).get("ROBOT_CARD_ID"))&&intentionNameList.get(i).equals(list.get(y).get("ROBOT_INTENTION_NAME"))) {
						newList.add(list.get(y));
					}
				}
			}
			
			for (int i = 0; i < newList.size(); i++) {
				JSONObject r=newList.get(i);
				EasyRecord saveRecord = new EasyRecord("C_NO_AS_ROBOT_CALL_RESULT", "ID");
				saveRecord.set("ID",  RandomKit.uniqueStr());
				saveRecord.set("ROBOT_CALL_ID", crowRobotId);
				saveRecord.set("REVISIT_RESULT", revisitResult);
				saveRecord.set("RESULT_CONTENT", resultContent);
				saveRecord.set("RESULT_TIME", currentDateStr);
				saveRecord.set("CARD_ID", r.getString("ROBOT_CARD_ID"));
				saveRecord.set("INTENTION_ID", "");//无当前数据
				saveRecord.set("INTENTION_NAME", r.getString("INTENTION"));
				saveRecord.set("INTENTION_TAG", r.getString("INTENTION_TAG"));
				saveRecord.set("INTENTION_TAG_CODE", r.getString("INTENTION_TAG_CODE"));
				saveRecord.set("INTENTION_RESULT", r.getString("INTENTION_RESULT"));
				saveRecord.set("INTENTION_RESULT_TYPE", r.getString("INTENTION_RESULT_TYPE"));
				if(isRecall){
					//不需要重呼
					saveRecord.set("LAST_CALL_FLAG", Constants.SN_N);
				}else{
					saveRecord.set("LAST_CALL_FLAG", Constants.SN_Y);
				}
				saveRecord.set("ROBOT_AUTO_CALL_ID", robotAutoCallId);
				saveRecord.set("IS_RETURN_RESULT", Constants.SN_N);
				saveRecord.set("SORT_NUM", i);
				query.save(saveRecord);
				logger.info("----log:"+saveRecord.toJSONString());
			}
			if(isRecall){//
				//需要重呼
				
				LocalDateTime now = LocalDateTime.now();
		        LocalDateTime futureTime = now.plusSeconds(recallIntervalTime*60);
		        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		        String futureTimeString = futureTime.format(formatter);
		        
				EasyRecord updateRecord = new EasyRecord("C_NO_AS_ROBOT_AUTO_CALL", "ID");
				updateRecord.set("ID",  robotAutoCallId);
				updateRecord.set("PUBLISH_TIME",  futureTimeString);
				updateRecord.set("SERVICE_STATUS",  Constants.HAS_PUBLISHED);
				query.update(updateRecord);
			}
		} catch (Exception e) {
			logger.error("自动外呼结果表error:" + e.getMessage(),e);
		}
		return successNum;
	}
	
	/**
	 * 机器人通过未接听意图发送短信
	 * @param crowdPackId
	 * @param phone
	 * @return
	 */
	public static int sendRobotSMSByFailCall(String robotCallId,String crowdPackId,String phone) {
		int successNum = 0;
		EasySQL sql = new EasySQL("select * from C_NO_AS_ROBOT_SMS t1");
		sql.append("left join C_NO_AS_ROBOT_STRATEGY t2 on t1.strategy_id = t2.id");
		sql.append("where 1=1");
		sql.append(crowdPackId,"and t2.crowd_pack_id = ?");
		sql.append("Y","and t1.is_open = ?");
		sql.append("1","and t1.type = ?");
		try {
			IService service = ServiceContext.getService("SMSGW_INTEFACE");
			JSONObject smsParams = new JSONObject();
			smsParams.put("command",ServiceCommand.SENDMESSAGE);
			smsParams.put("sender", Constants.APP_NAME);
			smsParams.put("serialId", IDGenerator.getDefaultNUMID());
			smsParams.put("password", "YQ_85521717");
			smsParams.put("source", "05");
			smsParams.put("busiId", IDGenerator.getIDByCurrentTime(20));
			smsParams.put("sendTime", EasyCalendar.newInstance().getDateTime("-")); 
			smsParams.put("userAcc", "system");
			EasyRow r = query.queryForRow(sql.getSQL(), sql.getParams());
			if(r==null) {
				return successNum;
			}
			smsParams.put("model", r.getColumnValue("ORG_CODE"));
			smsParams.put("category", r.getColumnValue("BRAND_CODE"));
			EasySQL expandDataSql = new EasySQL();
			expandDataSql.append("select EXPAND_DATA from C_NO_AS_ROBOT_AUTO_CALL where 1 = 1"); // 查询expand_data
			expandDataSql.append(robotCallId, "and ID = ?");
			String content = r.getColumnValue("CONTENT");
			logger.info("content1=" + content);
			String expandData = query.queryForString(expandDataSql.getSQL(), expandDataSql.getParams());
			if (StringUtils.isNotBlank(expandData)) {
				JSONObject expandDataJson = JSON.parseObject(expandData); // 获取expand_data
				String[] contents = content.split("\\{");
				for(int i=1;i<contents.length;i++ ){
					String key = contents[i].substring(0,contents[i].indexOf("}"));
					logger.info("key" + i + "=" + key);
					if(expandDataJson.getString(key) != null&&StringUtils.isNotBlank(expandDataJson.getString(key))){
						String value=expandDataJson.getString(key);
						logger.info("value" + i + "=" + value);
						content=content.replace("${"+key+"}",value);//从工单中获取信息
						content=content.replace("{"+key+"}",value);//从工单中获取信息
					}else{
						content=content.replace("${"+key+"}","");
						content=content.replace("{"+key+"}","");
					}
				}
			}
			logger.info("content2=" + content);
			smsParams.put("receivers", StringUtil.spiltByRegex(phone,";", content));
			JSONObject resp = service.invoke(smsParams);
			if(GWConstants.RET_CODE_SUCCESS.equals(resp.getString("respCode"))){
				successNum = successNum + 1;
			}
			
		} catch (Exception e) {
			logger.error("调用短信接口error:" + e.getMessage(),e);
		}
		return successNum;
	}
	
	
	public static List<JSONObject> getRobotSMSList(String strategyId){
		EasySQL sql = new EasySQL("select INTENTION,CODE,TYPE from C_NO_AS_ROBOT_SMS");
		sql.append("where 1=1");
		sql.append(strategyId,"and strategy_id = ?",false);
		sql.append("Y","and is_open = ?");
		List<JSONObject> list = new ArrayList<JSONObject>();
		try {
			list = query.queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			for(JSONObject obj:list) {
				obj.put("s_intention",obj.getString("INTENTION"));
				obj.put("s_code",obj.getString("CODE"));
				obj.put("s_type",obj.getString("TYPE"));
				obj.remove("INTENTION");
				obj.remove("CODE");
				obj.remove("TYPE");
			}
		} catch (SQLException e) { 
			logger.error("error:" + e.getMessage(),e);
		}
		return list;
	}
	
}
