package com.yunqu.cc.activeService.servlet;


import java.io.File;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;

import com.yq.busi.common.servlet.model.GWConstants;
import com.yunqu.cc.activeService.dao.CrowdPackSql;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.activeService.base.AppBaseServlet;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.base.Constants;
import com.yunqu.cc.activeService.inf.RevisitService;
import com.yunqu.cc.activeService.utils.PhoneEncryptUtil;
import com.yunqu.cc.activeService.utils.StringUtil;

@WebServlet("/servlet/revisit")
public class RevisitServlet extends AppBaseServlet {
	
	private static final long serialVersionUID = 1L;
	
	private final Logger logger = CommLogger.logger;
	private final Logger strategyLogger = CommLogger.getCommLogger("strategy");
	
	/**
	 * 发布
	 * @return
	 */
	public JSONObject actionForPublish() {
		JSONObject data = getJSONObject();
		EasyQuery query = getQuery();
		int taskNum = 0;
		try {
			int userNum = 0;//人工外呼时此数据为坐席数，机器人外呼时此数据为发布量
			int averageNum = data.getIntValue("USER_AVE_NUM") == 0 ? 50:data.getIntValue("USER_AVE_NUM");
			String currentTime = EasyCalendar.newInstance().getDateTime("-");//统一时间
			String publishId = RandomKit.uniqueStr();//发布流水ID
			if(Constants.PUBLISH_PART.equals(data.getString("PUBLISH_TYPE"))) {//部分发布
				String publishUserNum = data.getString("PUB_USER_NUM");
				if(StringUtil.getInt(publishUserNum)==0) {
					return EasyResult.error(500,"分配人数不正确");
				}
				userNum = StringUtil.getInt(publishUserNum);
			}
			//判断选中的人群包是否配置相应策略
			if("1".equals(data.getString("CALLOUT_TYPE"))) {//人工外呼
				boolean flag = query.queryForExist("select count(1) from c_no_as_crowdpack_priority where crowd_pack_id = ? and (channel_type = ? or channel_type = ?)", 
						new Object[] {data.getString("CROWD_PACK_ID"),Constants.CHANNEL_MARKET,Constants.CHANNEL_OUTBOUND});
				if(!flag) {
					return EasyResult.error(500,"该人群包未配置主动营销或主动外呼策略，请先配置后再发布");
				}
			}else if("2".equals(data.getString("CALLOUT_TYPE"))) {//机器人外呼
				boolean flag = query.queryForExist("select count(1) from c_no_as_crowdpack_priority where crowd_pack_id = ? and channel_type = ?", 
						new Object[] {data.getString("CROWD_PACK_ID"),Constants.CHANNEL_ROBOT});
				if(!flag) {
					return EasyResult.error(500,"该人群包未配置机器人外呼策略，请先配置后再发布");
				}
				//判断发布时间是否满足条件
				EasySQL robotStrategySQL = new EasySQL("select t1.* from " + Constants.strategyTable.get(Constants.CHANNEL_ROBOT) + " t1");
				robotStrategySQL.append("left join C_NO_AS_CROWDPACK_PRIORITY t2 on t2.strategy_id = t1.id");
				robotStrategySQL.append("where 1=1");
				robotStrategySQL.append(data.getString("CROWD_PACK_ID"),"and t2.crowd_pack_id = ?");
				robotStrategySQL.append("1","and t1.is_open = ?");
				robotStrategySQL.append("order by t2.strategy_level");
				List<JSONObject> list = query.queryForList(robotStrategySQL.getSQL(), robotStrategySQL.getParams(),new JSONMapperImpl());
				JSONObject strategyData = list.get(0);
				//判断当前时间是否在外呼时间内
				boolean timeFlag = checkTime(strategyData.getString("PUBLISH_BEGIN_TIME"),strategyData.getString("PUBLISH_END_TIME"),"HH:mm:ss");
				if(!timeFlag) {
					return EasyResult.error(500,"该人群包未在配置的发布时间内，无法发布");
				}
			}else {
				return EasyResult.error();
			}
			//将表单状态改为已发布
			EasySQL updateSql = new EasySQL("UPDATE C_NO_AS_CROWD SET");
			updateSql.append(publishId,"PUBLISH_ID = ?,");
			updateSql.append(currentTime,"PUBLISH_TIME = ?,");
			updateSql.append(UserUtil.getUser(getRequest()).getUserAcc(),"PUBLISH_ACC = ?,");
			updateSql.append(data.getString("CONVERSION_TYPE"),"CONVERSION_TYPE = ?,");
			updateSql.append(data.getString("SERVICE_TYPE"),"SERVICE_TYPE = ?,");
			updateSql.append(Constants.HAS_PUBLISHED,"SERVICE_STATUS = ?");
			updateSql.append("WHERE 1=1");
			updateSql.append(Constants.IS_DISTURB_OFF,"AND IS_DISTURB = ?");//非免打扰
			updateSql.append(data.getString("CROWD_PACK_ID"),"AND CROWD_PACK_ID = ?",false);//人群包
			updateSql.append(Constants.NO_PUBLISH,"AND SERVICE_STATUS = ?");//回访状态：未发布
			if(Constants.PUBLISH_PART.equals(data.getString("PUBLISH_TYPE"))) {
				//部分发布时，判断是人工还是机器人
				//人工发布数量：userNum * averageNum(默认50)
				//机器人发布数量：userNum
				if("1".equals(data.getString("CALLOUT_TYPE"))) {//人工
					updateSql.append(userNum*averageNum,"AND ROWNUM <= ?");
				}else if("2".equals(data.getString("CALLOUT_TYPE"))) {//机器人
					updateSql.append(userNum,"AND ROWNUM <= ?");
				}
			}
			//任务数量
			taskNum = query.executeUpdate(updateSql.getSQL(), updateSql.getParams());
			if(taskNum==0) {
				return EasyResult.error(500,"任务数为0，无资料发布");
			}
			int min = ConfigUtil.getInt(Constants.APP_NAME, "DISTRIBUTE_TIME",3);
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
			Date date = new Date();
	        Calendar calendar = Calendar.getInstance();
	        calendar.setTime(date); //设置为当前时间
	        calendar.set(Calendar.MINUTE, calendar.get(Calendar.MINUTE) + min);
	        date = calendar.getTime();
	        String deadline = dateFormat.format(date);
			//写入发布记录
			EasyRecord record = new EasyRecord("C_NO_AS_CROWD_PUBLISH");
			record.set("ID", publishId);
			record.set("PUBLISH_ACC",UserUtil.getUser(getRequest()).getUserAcc());
			record.set("PUBLISH_TIME",currentTime);
			record.set("PUBLISH_TYPE",data.getString("PUBLISH_TYPE"));
			record.set("PUBLISH_NUM",taskNum);
			record.set("DEADLINE",deadline);
			record.set("CALLOUT_TYPE",data.getString("CALLOUT_TYPE"));
			record.set("CONVERSION_TYPE",data.getString("CONVERSION_TYPE"));
			record.set("PUBLISH_STATE",Constants.HAS_PUBLISHED);
			record.set("CROWD_PACK_ID",data.getString("CROWD_PACK_ID"));
			query.save(record);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "ERROR:" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok(null,"成功发布"+taskNum+"条数据");
	}
	
	/**
	 * 回收
	 * @return
	 */
	public JSONObject actionForRecycle() {
		JSONObject data = getJSONObject();
		int taskNum = 0;
		//判断呼叫是否存在未结束的数据，存在则不允许进行回收
		EasySQL easySQL = new EasySQL("select count(1) from C_NO_AS_CROWD t1");
		easySQL.append(data.getString("CROWD_PACK_ID")," where t1.CROWD_PACK_ID=?", false);
		easySQL.append("and exists(select 1 from C_NO_ROBOT_CALL t2 where (call.RECORD_STATUS=1 OR call.RECORD_STATUS=2) and t1.ID=t2.ADD2)");
		try {
			int count = getQuery().queryForInt(easySQL.getSQL(), easySQL.getParams());
			logger.info("进行回收除据：" + count);
			if (count>0) {
				return EasyResult.fail("存在未呼叫结束的数据");
			}
		}catch (Exception e){
		}
		
		EasySQL sql = new EasySQL("UPDATE C_NO_AS_CROWD SET");
		sql.append("PUBLISH_TIME = NULL,PUBLISH_ACC = NULL,CONVERSION_TYPE = NULL,PUBLISH_ID = NULL,RECEIVE_TIME = NULL,REVISIT_RESULT = NULL,RESULT_TIME = NULL,");
		sql.append("AGENT_ACC = NULL,AGENT_NAME = NULL,AGENT_DEPT_CODE = NULL,AGENT_DEPT_NAME = NULL,AGENT_AREA_CODE = NULL,AGENT_AREA_NAME = NULL,");
		sql.append(Constants.NO_PUBLISH,"SERVICE_STATUS = ?");
		sql.append("WHERE 1=1");
		sql.append(data.getString("CONVERSION_TYPE"),"AND CONVERSION_TYPE = ?");//折算类型
		sql.append(data.getString("REVISIT_RESULT"),"AND REVISIT_RESULT = ?");//回访结果
		sql.append(data.getString("CROWD_PACK_ID"),"AND CROWD_PACK_ID = ?",false);//人群包id
		if(StringUtils.isNotBlank(data.getString("REVISIT_RESULT"))) {
			sql.append(Constants.HAS_REVISITED,"AND SERVICE_STATUS = ?");//选定了回访结果，回访状态写死已回访;
		}else if("".equals(data.getString("STATUS"))) {
			String status = "'2','3'";
			sql.append("AND SERVICE_STATUS IN ( " + status + " )");//回访状态
		}else {
			sql.append(data.getString("STATUS"),"AND SERVICE_STATUS = ?");//回访状态
		}
		if(!"".equals(data.getString("USER_ACC"))) {
			String[] userAcc = data.getString("USER_ACC").split(",");
			sql.append("AND AGENT_ACC IN (" + StringUtil.joinSqlStr(userAcc) + ")");
		}
		sql.append(data.getString("PUBLISH_NUM"),"AND ROWNUM <= ?");
		try {
			taskNum = getQuery().executeUpdate(sql.getSQL(), sql.getParams());
			if(taskNum==0) {
				return EasyResult.error(500,"任务数为0，无资料回收");
			}
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "回收出错：" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok(null,"成功回收"+taskNum+"条数据");
	}
	
	/**
	 * 申请
	 * @return
	 */
	public JSONObject actionForApply() {
		JSONObject data = getJSONObject();
		UserModel user = UserUtil.getUser(getRequest());//当前坐席
		String currentTime = EasyCalendar.newInstance().getDateTime("-");//统一时间
		EasyQuery query = getQuery();
		try {
			//判断待处理量
			EasySQL checkSql = new EasySQL("SELECT COUNT(*) FROM C_NO_AS_CROWD WHERE AGENT_ACC = ? AND SERVICE_STATUS = ?");
			int waitToRevisitNum = query.queryForInt(checkSql.getSQL(), new Object[] {user.getUserAcc(),Constants.HAS_RECEIVED,});
			if(waitToRevisitNum>20) {
				return EasyResult.error(500,"待处理数量大于20，无法申请");
			}
			EasySQL sql = new EasySQL("SELECT DISTINCT T1.PUBLISH_ID FROM C_NO_AS_CROWD T1");
			sql.append("JOIN C_NO_AS_CROWD_PUBLISH T2 ON T1.PUBLISH_ID = T2.ID WHERE 1=1");
			sql.append(data.getString("SERVICE_TYPE"),"AND T1.SERVICE_TYPE = ?");
			sql.append(Constants.REVISIT_CALLOUT_ROBOT,"AND T2.CALLOUT_TYPE != ?");
			//sql.append(data.getString("CONVERSION_TYPE"),"AND T1.CONVERSION_TYPE = ?");
			sql.append(currentTime,"AND T2.DEADLINE >= ?").append(Constants.HAS_PUBLISHED,"AND T1.SERVICE_STATUS = ?");
			List<JSONObject> dataList = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			if(dataList!=null&&dataList.size()>0) {
				EasyRecord record = new EasyRecord("C_NO_AS_APPLY_USER");
				for(JSONObject obj:dataList) {
					if(query.queryForInt("SELECT COUNT(1) FROM C_NO_AS_APPLY_USER WHERE USER_ACC = ? AND PUBLISH_ID = ?",new Object[] {user.getUserAcc(),obj.getString("PUBLISH_ID")})>0) {
						continue;
					}
					record.set("ID",RandomKit.uniqueStr());
					record.set("PUBLISH_ID",obj.getString("PUBLISH_ID"));
					record.set("USER_ACC",user.getUserAcc());
					record.set("USER_NAME",user.getUserName());
					record.set("CREATE_TIME",currentTime);
					record.set("ALLOCATION","0");
					query.save(record);
				}
			}else {
				return EasyResult.error(500,"无资料发布，申请失败");
			}
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "资料申请失败：" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok(null,"申请成功，请等待系统分配");
	}
	
	/**
	 * 判断是否免打扰
	 * @param mobile
	 * @param channelType
	 * @return true免打扰/false非免打扰
	 */
	private boolean checkMobileDisturb(String mobile,String channelType) {
		try {
			return getQuery().queryForExist("select count(1) from C_NO_AS_NOT_DISTURB where CUST_PHONE = ? and channel_id = ? and is_open = ?", 
					new Object[] {mobile,channelType,"1"});
		} catch (SQLException e) {
			logger.error(e.getMessage());
		}
		return false;
	}
	/**
	 * 判断是否免打扰
	 * @param mobile
	 * @param channelType
	 * @return true免打扰/false非免打扰
	 */
	private boolean checkMobileDisturbCrowdInfo(String mobile,String id) {
		try {
			EasyRow row = getQuery().queryForRow("select is_disturb from C_NO_AS_CROWD where id = ?", 
					new Object[] {id});
			if(row==null) {
				return false;
			}else {
				return Integer.toString(Constants.IS_DISTURB_ON).equals(row.getColumnValue("IS_DISTURB"));
			}
		} catch (SQLException e) {
			logger.error(e.getMessage());
		}
		return false;
	}
	
	
	/**
	 * 检查回访单状态，已回访则无法提交
	 * @return true已回访/false其他
	 */
	private boolean checkIsRevisited(String id) {
		try {
			EasyRow row = getQuery().queryForRow("select service_status from C_NO_AS_CROWD where id = ?", new Object[] {id});
			if(row==null) {
				return false;
			}else {
				return Constants.HAS_REVISITED.equals(row.getColumnValue("SERVICE_STATUS"));
			}
		} catch (SQLException e) {
			logger.error(e.getMessage(),e);
		}
		return false;
	}
	
	/**
	 * 营销处理
	 * @return
	 */
	public JSONObject actionForHandleMarket() {
		JSONObject data = getJSONObject();
		String currentTime = DateUtil.getCurrentDateStr();
		String resultType = data.getString("resultType");
		String realMobile = data.getString("realMobile");
		String serviceType = data.getString("SERVICE_TYPE");
		String resultName = "";
		try {
			String crowdId = data.getString("crowdId");
			String serviceId = RandomKit.uniqueStr();
			if(StringUtils.isBlank(crowdId)) {
				return EasyResult.error();
			}
			if(checkIsRevisited(crowdId)) {
				return EasyResult.error(500,"已回访数据无法再次提交");
			}
			if("0".equals(resultType)) {
				resultName = "有效回访";
			}else if("1".equals(resultType)) {
				resultName = "无效回访";
			}
			//写入结果表
			EasyRecord record = new EasyRecord("C_NO_AS_RESULT");
			record.put("ID", RandomKit.uniqueStr());
			record.put("CROWD_ID", data.getString("crowdId"));
			record.put("RESULT", "1".equals(resultType) ? "":data.getString("RESULT"));
			record.put("RESULT_NAME", resultName);
			record.put("RESULT_CLASS", data.getString("REVISIT_RESULT"));
			record.put("RESULT_ACC", UserUtil.getUser(getRequest()).getUserAcc());
			record.put("RESULT_TIME", currentTime);
			record.put("RESULT_CHANNEL", Constants.CHANNEL_MARKET);
			record.put("RESULT_CHANNEL_NAME", "人工营销");
			record.put("SESSION_ID", data.getString("sessionId"));
			record.put("RESULT_CONTENT", data.getString("RESULT_CONTENT"));//备注
			record.put("CONTACT_ORDER_CODE", data.getString("contactOrderCode"));
			record.put("CONVERSION_TYPE", data.getString("conversionType"));
			getQuery().save(record);
			//更新明细表
			EasyRecord detailRecord = new EasyRecord("C_NO_AS_CROWD","ID");
			detailRecord.setPrimaryValues(data.getString("crowdId"));
			detailRecord.set("SERVICE_STATUS", Constants.HAS_REVISITED);
			detailRecord.set("RESULT_CONTENT", data.getString("RESULT_CONTENT"));//备注
			detailRecord.set("RESULT", "1".equals(resultType) ? "":data.getString("RESULT"));
			detailRecord.set("RESULT_CONTENT", data.getString("RESULT_NAME"));
			detailRecord.set("RESULT_TIME", currentTime);
			detailRecord.set("SERVICE_DATE", EasyDate.getCurrentDateString("yyyyMMdd"));
			detailRecord.set("RESULT_SOURCE", Constants.CHANNEL_MARKET);
			detailRecord.set("SERVICE_ID", serviceId);
			detailRecord.set("PRIORITY", data.getString("PRIORITY"));
			detailRecord.set("RESULT_SERVICE_TYPE", serviceType);//服务类型
			//1有效回访、其他无效回访
			detailRecord.set("REVISIT_RESULT", "0".equals(resultType) ? "1":data.getString("RESULT"));
			getQuery().update(detailRecord);
			//洗悦家营销还需要记录
			if("3".equals(serviceType)) {
				handleMarket(realMobile, serviceId);
			}
			logger.info("用户：" + UserUtil.getUser(getRequest()).getUserAcc() + "提交了营销处理单，人群包id：" + data.getString("crowdId") + "，时间：" + DateUtil.getCurrentDateStr());
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "error:" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok();
	}
	
	/**
	 * 外呼处理
	 * @return
	 */
	public JSONObject actionForHandleCallOut() {
		JSONObject data = getJSONObject();
		JSONObject questionnaire = data.getJSONObject("questionnaire");
		String serviceType = data.getString("SERVICE_TYPE");
		UserModel user = UserUtil.getUser(getRequest());
		String currentTime = DateUtil.getCurrentDateStr();
		String resultType = data.getString("resultType");
		String resultName = "";
		try {
			String crowdId = data.getString("crowdId");
			if(StringUtils.isBlank(crowdId)) {
				return EasyResult.error();
			}
			if(checkIsRevisited(crowdId)) {
				return EasyResult.error(500,"已回访数据无法再次提交");
			}
			if("0".equals(resultType)) {
				resultName = "有效回访";
			}else if("1".equals(resultType)) {
				resultName = "无效回访";
			}
			//写入结果表
			EasyRecord record = new EasyRecord("C_NO_AS_RESULT");
			String resultId = RandomKit.uniqueStr();
			record.put("ID", resultId);
			record.put("CROWD_ID", crowdId);
			record.put("RESULT", "1".equals(resultType) ? "":data.getString("RESULT"));
			record.put("RESULT_NAME", resultName);
			record.put("RESULT_CLASS", data.getString("REVISIT_RESULT"));
			record.put("RESULT_ACC", user.getUserAcc());
			record.put("RESULT_TIME", currentTime);
			record.put("RESULT_CHANNEL", Constants.CHANNEL_OUTBOUND);
			record.put("RESULT_CHANNEL_NAME", "人工外呼");
			record.put("SESSION_ID", data.getString("sessionId"));
			record.put("RESULT_CONTENT", data.getString("RESULT_CONTENT"));
			record.put("CONTACT_ORDER_CODE", data.getString("contactOrderCode"));
			record.put("CONVERSION_TYPE", data.getString("conversionType"));
			record.put("QUESTIONNAIRE_ID", questionnaire.getString("id"));
			getQuery().save(record);
			//更新明细表
			EasyRecord detailRecord = new EasyRecord("C_NO_AS_CROWD","ID");
			detailRecord.setPrimaryValues(crowdId);
			detailRecord.set("SERVICE_STATUS", Constants.HAS_REVISITED);
			detailRecord.set("RESULT_CONTENT", data.getString("RESULT_CONTENT"));
			detailRecord.set("RESULT", "1".equals(resultType) ? "":data.getString("RESULT"));
			detailRecord.set("RESULT_CONTENT", data.getString("RESULT_NAME"));
			detailRecord.set("RESULT_TIME", currentTime);
			detailRecord.set("SERVICE_DATE", EasyDate.getCurrentDateString("yyyyMMdd"));
			detailRecord.set("RESULT_SOURCE", Constants.CHANNEL_OUTBOUND);
			detailRecord.set("PRIORITY", data.getString("PRIORITY"));
			detailRecord.set("RESULT_SERVICE_TYPE", serviceType);//服务类型
			//1有效回访、其他无效回访
			detailRecord.set("REVISIT_RESULT", "0".equals(resultType) ? "1":data.getString("RESULT"));
			getQuery().update(detailRecord);
			//处理问卷
			handleQuestionnaire(questionnaire,resultId,data.getString("crowdId"),user,false);
			logger.info("用户：" + UserUtil.getUser(getRequest()).getUserAcc() + "提交了主动外呼单，人群包id：" + data.getString("crowdId") + "，时间：" + DateUtil.getCurrentDateStr());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "error:" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok();
	}
	
	/**
	 * 处理填写的问卷信息
	 * @param data 问卷
	 * @param resultId
	 * @param crowdId
	 * @throws SQLException 
	 */
	private void handleQuestionnaire(JSONObject data,String resultId,String crowdId,UserModel user,boolean updateFlag) throws SQLException{
		//如果是修改且问卷没有改动，找出当前问卷结果记录，修改id
		if(updateFlag&&(data==null||data.size()==0)) {
			getQuery().execute("update C_NO_AS_QUESTION_RESULT set result_id = ? where id in("
					+ "select id from C_NO_AS_QUESTION_RESULT where crowd_id = ?)", resultId,crowdId);
			logger.info("更新问卷对应的结果id成功，resultId：" + resultId + ",crowdId：" + crowdId);
			return;
		}
		JSONArray questionList = data.getJSONArray("list");
		String currentTime = DateUtil.getCurrentDateStr();
		EasyRecord record = new EasyRecord("C_NO_AS_QUESTION_RESULT");
		for(int i=0;i<questionList.size();i++) {
			JSONObject question = questionList.getJSONObject(i);
			//答案为空，直接跳出
			if(StringUtils.isBlank(question.getString("answer"))) {
				continue;
			}
			JSONArray answerList = question.getJSONArray("answerList");
			record.put("ID", RandomKit.uniqueStr());
			record.put("QUESTIONNAIRE_ID", data.getString("id"));
			record.put("QUESTION_ID", question.getString("id"));
			record.put("QUESTION_NAME", question.getString("name"));
			record.put("RESULT_ID", resultId);
			record.put("CROWD_ID", crowdId);
			record.put("CREATE_ACC", user.getUserAcc());
			record.put("CREATE_TIME", currentTime);
			if(question.get("answer") instanceof String) {
				if(answerList!=null&&answerList.size()>0&&question.getIntValue("answer")!=0) {
					record.put("ANSWER_NAME", answerList.getJSONObject(question.getIntValue("answer")-1).getString("name"));
				}
				record.put("ANSWER_CODE", question.getString("answer"));
				getQuery().save(record);
			}else if(question.get("answer") instanceof JSONArray) {//多选，写入多条信息
				JSONArray array = question.getJSONArray("answer");
				for(int j=0;j<array.size();j++) {
					record.put("ID", RandomKit.uniqueStr());
					record.put("ANSWER_NAME", answerList.getJSONObject(array.getIntValue(j)-1).getString("name"));
					record.put("ANSWER_CODE", array.getString(j));
					getQuery().save(record);
				}
			}
			record.clear();
		}
		
	}
	
	/**
	 * 获取任务数量
	 * @return
	 */
	public JSONObject actionForGetRevisitCount() {
		JSONObject data = getJSONObject();
		EasySQL sql = new EasySQL("SELECT COUNT(1) AS NUM FROM C_NO_AS_CROWD T1");
		sql.append("WHERE 1=1");
		//回收
		if("recycle".equals(data.getString("HANDLE_TYPE"))) {
			//坐席账号
			if(StringUtils.isNotBlank(data.getString("USER_ACC"))) {
				String[] userAcc = data.getString("USER_ACC").split(",");
				sql.append("AND T1.AGENT_ACC IN (" + StringUtil.joinSqlStr(userAcc) + ")");
			}
			if(StringUtils.isBlank(data.getString("STATUS"))&&StringUtils.isBlank(data.getString("REVISIT_RESULT"))) {
				sql.append(Constants.NO_PUBLISH,"AND T1.SERVICE_STATUS != ?");
				sql.append(Constants.HAS_REVISITED,"AND T1.SERVICE_STATUS != ?");
			}else if(StringUtils.isNotBlank(data.getString("REVISIT_RESULT"))) {
				sql.append(Constants.HAS_REVISITED,"AND SERVICE_STATUS = ?");//选定了回访结果，回访状态写死已回访;
			}
			//回访状态
			sql.append(data.getString("STATUS"),"AND T1.SERVICE_STATUS = ?");
			//回访结果
			sql.append(data.getString("REVISIT_RESULT"),"AND T1.REVISIT_RESULT = ?");
			//折算类型
			sql.append(data.getString("CONVERSION_TYPE"),"AND T1.CONVERSION_TYPE = ?");
		}else {
			sql.append(Constants.NO_PUBLISH,"AND T1.SERVICE_STATUS = ?");//未发布状态
		}
		sql.append(data.getString("CROWD_PACK_ID"),"AND T1.CROWD_PACK_ID = ?");//人群包ID
		sql.append(Constants.IS_DISTURB_OFF,"AND IS_DISTURB = ?");//非免打扰
		JSONObject obj = new JSONObject();
		try {
			obj = getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		} catch (SQLException e) {
			logger.error("查询数量出错:" + e.getMessage(),e);
		}
		return EasyResult.ok(obj);
	}
	
	/**
	 * 导出
	 */
	public void actionForExportRevisitData() {
		EasySQL sql = new EasySQL("select t1.*,t2.name as pack_name from C_NO_AS_CROWD t1");
		sql.append("left join C_NO_AS_CROWD_PACK t2 on t2.ID = t1.CROWD_PACK_ID");
		sql.append("left join C_NO_AS_CROWD_PUBLISH t3 on t3.ID = t1.PUBLISH_ID");
		sql.append("where 1=1");
		sql.append(Constants.NO_PUBLISH,"and service_status != ?");
		sql.append(getRequest().getParameter("SERVICE_TYPE"),"and t1.service_type = ?");
		sql.append(getRequest().getParameter("RESULT"),"and t1.result = ?");
		sql.append(getRequest().getParameter("REVISIT_RESULT"),"and t1.revisit_result = ?");
		sql.append(getRequest().getParameter("CUST_PHONE"),"and t1.cust_phone = ?");
		sql.append(getRequest().getParameter("AGENT_ACC"),"and t1.agent_acc = ?");
		sql.append(getRequest().getParameter("SERVICE_ORDER_TYPE"),"and t1.service_order_type = ?");
		sql.append(getRequest().getParameter("SERVICE_ORDER_CODE"),"and t1.service_order_code = ?");
		sql.append(getRequest().getParameter("SERVICE_STATUS"),"and t1.service_status = ?");
		sql.append(StringUtil.urlDecode(getRequest().getParameter("RESULT_START_TIME")),"and t1.result_time >= ?");
		sql.append(StringUtil.urlDecode(getRequest().getParameter("RESULT_END_TIME")),"and t1.result_time <= ?");
		sql.append(StringUtil.urlDecode(getRequest().getParameter("PUBLISH_START_TIME")),"and t3.publish_time >= ?");
		sql.append(StringUtil.urlDecode(getRequest().getParameter("PUBLISH_END_TIME")),"and t3.publish_time <= ?");
		sql.appendLike(getRequest().getParameter("CROWD_PACK_NAME"),"and t2.name like ?");
		//判断权限
		boolean flag = checkRole(UserUtil.getUser(this.getRequest()).getUserAcc(), "as_revisit_list_monitor");
		if(flag) {
			sql.append(getRequest().getParameter("AGENT_ACC"),"and t1.agent_acc = ?");
		}else {
			sql.append(UserUtil.getUser(this.getRequest()).getUserAcc(),"and t1.agent_acc = ?");
		}
		sql.append("order by t3.publish_time,t1.cust_phone,t3.id desc");
		
		EasyQuery query = getQuery();
		query.setMaxRow(10000);
		List<Map<String, String>> data = null;
		try {
			data = query.queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl() {
				@Override
				public Map<String, String> mapRow(ResultSet rs, int rowNum) {
					try {
						ResultSetMetaData meta = rs.getMetaData();
						int columnCount = meta.getColumnCount();
						String[] column = new String[columnCount];
						for (int i = 0; i < columnCount; i++)
							column[i] = meta.getColumnLabel(i + 1).toUpperCase();
						Map<String, String> row = new HashMap<String, String>();
						for (int j = 0; j < columnCount; j++) {
							String value = rs.getString(j + 1);
							if (value == null || "".equals("null"))
								value = "";
							if("CUST_PHONE".equals(column[j])) {
								value = PhoneEncryptUtil.getPhone(value);
							}
							row.put(column[j], value);
						}
						return row;
					} catch (Exception ex) {
						logger.error("EasyMapMapperImpl.mapRow() exception , cause:" + ex.getMessage());
						return null;
					}
				}
			});
		} catch (SQLException e1) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "查询数据出错：" + e1.getMessage(),e1);
			return;
		}
		// 组装表头
		File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
		List<String> headers = new ArrayList<String>();
		headers.add(" 序号 ");
		headers.add(" 姓名 ");
		headers.add(" 电话区号 ");
		headers.add(" 电话号码");
		headers.add(" 区域");
		headers.add(" 地址");
		headers.add(" 品类");
		headers.add(" 单据编码");
		headers.add(" 单据类型");
		headers.add(" 人群包名称");
		headers.add(" 发布时间");
		headers.add(" 服务类型");
		headers.add(" 服务时间");
		headers.add(" 外呼结果");
		headers.add(" 坐席名称");
		headers.add(" 班级");
		headers.add(" 区域");
		headers.add(" 状态");
		List<ExcelHeaderStyle> styles = new ArrayList<ExcelHeaderStyle>();
		for (String header : headers) {
			ExcelHeaderStyle style = new ExcelHeaderStyle();
			style.setData(header);
			style.setWidth(3600);
			styles.add(style);
		}
		List<List<String>> excelData = new ArrayList<List<String>>();
		int i = 0;
		List<String> list = null;
		JSONObject serviceType = new JSONObject() {{
			put("1", "营销外呼");
			put("2", "主动外呼");
		}};
		String depCode = UserUtil.getUser(this.getRequest()).getEpCode();
		JSONObject revisitState = DictCache.getJsonAllDictListByGroupCode(depCode, "CC_AS_REVISIT_STATUS").getJSONObject("data");// 回访状态
		JSONObject revisitResult = DictCache.getJsonAllDictListByGroupCode(depCode, "CC_AS_REVISIT_RESULT").getJSONObject("data");// 回访状态
		JSONObject serviceOrderType = DictCache.getJsonAllDictListByGroupCode(depCode, "CC_AS_ORDER_TYPE").getJSONObject("data");// 单据类型
		//Map<String, Object> CALLBACK_REGION = DictCache.getMapAllDictListByGroupCode(depCode, "CALLBACK_REGION");// 回访结果
		//Map ORG_CODE = commMap.getMapSysCode("ORG_CODE","");//主体
		for (Map<String, String> map : data) {
			list = new ArrayList<String>();
			list.add(String.valueOf(++i));
			list.add(map.get("CUST_NAME"));
			list.add(map.get("AREA_CODE"));
			list.add(map.get("CUST_PHONE"));
			list.add(map.get("AREA_NAME"));
			list.add(map.get("CUST_ADDRESS"));
			list.add(map.get("PROD_NAME"));
			list.add(map.get("SERVICE_ORDER_CODE"));
			list.add(serviceOrderType.getString(map.get("SERVICE_ORDER_TYPE")) == null ? "":serviceOrderType.getString(map.get("SERVICE_ORDER_TYPE")));
			list.add(map.get("PACK_NAME"));
			list.add(map.get("PUBLISH_TIME"));
			list.add(serviceType.getString(map.get("SERVICE_TYPE")) == null ? "":serviceType.getString(map.get("SERVICE_TYPE")));
			list.add(map.get("RESULT_TIME"));
			if(StringUtils.isBlank(map.get("RESULT_CONTENT"))) {
				String val = revisitResult.getString(map.get("REVISIT_RESULT")) == null ? "":revisitResult.getString(map.get("REVISIT_RESULT"));
				list.add(val);
			}else {
				list.add(map.get("RESULT_CONTENT"));
			}
			list.add(map.get("AGENT_NAME"));
			list.add(map.get("AGENT_DEPT_NAME"));
			list.add(map.get("AGENT_AREA_NAME"));
			list.add(revisitState.get(map.get("SERVICE_STATUS")) == null ? "" : revisitState.getString(map.get("SERVICE_STATUS")));
			excelData.add(list);
		}
		try {
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			renderFile(file, "主动外呼处理列表.xlsx");
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "数据导出失败：" + e.getMessage(),e);
		}
	}
	
	/**
	 * 导出机器人回访记录
	 */
	public void actionForExportRobotRevisitData() {
		HttpServletRequest request = getRequest();
		String crowdPackId = request.getParameter("crowdPackId");
		JSONObject param = new JSONObject();
		param.put("pulish_start_time",request.getParameter("pulish_start_time"));
		param.put("pulish_end_time",request.getParameter("pulish_end_time"));
		param.put("crowd_begin_time",request.getParameter("crowd_begin_time"));
		param.put("crowd_end_time",request.getParameter("crowd_end_time"));
		param.put("phone",request.getParameter("phone"));
		param.put("crowdPackId",request.getParameter("crowdPackId"));
		param.put("manual",request.getParameter("manual"));
		param.put("SERVICE_STATUS",request.getParameter("SERVICE_STATUS"));
		param.put("REVISIT_RESULT",request.getParameter("REVISIT_RESULT"));
		param.put("b_end_time",request.getParameter("b_end_time"));
		param.put("e_end_time",request.getParameter("e_end_time"));
		param.put("SMS_INF_FLAG",request.getParameter("SMS_INF_FLAG"));
		String crowdPackName = "";
		boolean autoCallUnionFlag = true;
		boolean manualCallUnionFlag = true;
		if(org.apache.commons.lang3.StringUtils.isNotBlank(crowdPackId)){
			autoCallUnionFlag = false;
			manualCallUnionFlag = false;
			try {
				List<EasyRow> packList = this.getQuery().queryForList("select category,name from c_no_as_crowd_pack where id = ?", new Object[]{crowdPackId});
				for (EasyRow easyRow : packList) {
					String category = easyRow.getColumnValue("category");
					crowdPackName = easyRow.getColumnValue("name");
					if("1".equals(category)){
						autoCallUnionFlag=true;
					}else{
						manualCallUnionFlag=true;
					}
				}
			} catch (SQLException e) {
				logger.error("机器人查询回访记录，失败");
			}
		}
		EasySQL sql = new EasySQL();
		if(manualCallUnionFlag){
			CrowdPackSql.getManualCallSql(sql,param,crowdPackName);
		}
		if(manualCallUnionFlag&&autoCallUnionFlag){
			sql.append("union all");
		}
		if(autoCallUnionFlag){
			CrowdPackSql.getAutoCallSql(sql,param,crowdPackName);
		}
		logger.info("sql:" + sql.getSQL() + " , param:" + JSON.toJSONString(sql.getParams()));
		String finalCrowdPackName = crowdPackName;
		if(StringUtils.isBlank(crowdPackName)){
			sql.append(" order by publish_time desc ");
		}

		EasyQuery query = getQuery();
		query.setMaxRow(10000);
		List<Map<String, String>> data = null;
		try {
			data = query.queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl() {
				@Override
				public Map<String, String> mapRow(ResultSet rs, int rowNum) {
					try {
						ResultSetMetaData meta = rs.getMetaData();
						int columnCount = meta.getColumnCount();
						String[] column = new String[columnCount];
						for (int i = 0; i < columnCount; i++)
							column[i] = meta.getColumnLabel(i + 1).toUpperCase();
						Map<String, String> row = new HashMap<String, String>();
						for (int j = 0; j < columnCount; j++) {
							String value = rs.getString(j + 1);
							if (value == null || "".equals("null"))
								value = "";
							if("CUST_PHONE".equals(column[j])) {
								value = PhoneEncryptUtil.getPhone(value);
							}
							row.put(column[j], value);
						}
						if(StringUtils.isNotBlank(finalCrowdPackName) ) {
							row.put("CROWD_PACK_NAME", finalCrowdPackName);
						}
						return row;
					} catch (Exception ex) {
						logger.error("EasyMapMapperImpl.mapRow() exception , cause:" + ex.getMessage());
						return null;
					}
				}
			});
		} catch (SQLException e1) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "查询数据出错：" + e1.getMessage(),e1);
			return;
		}
		// 组装表头
		File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
		List<String> headers = new ArrayList<String>();
		headers.add(" 序号 ");
		headers.add(" 姓名 ");
		headers.add(" 电话区号 ");
		headers.add(" 人群包");
		headers.add(" 品类");
		headers.add(" 回访状态");
		headers.add(" 回访结束时间");
		headers.add(" 回访结果");
		headers.add(" 是否转人工");
		headers.add(" 是否发送短信");
		headers.add(" 描述");
		List<ExcelHeaderStyle> styles = new ArrayList<ExcelHeaderStyle>();
		for (String header : headers) {
			ExcelHeaderStyle style = new ExcelHeaderStyle();
			style.setData(header);
			style.setWidth(3600);
			styles.add(style);
		}
		List<List<String>> excelData = new ArrayList<List<String>>();
		int i = 0;
		List<String> list = null;
		String depCode = UserUtil.getUser(this.getRequest()).getEpCode();
		JSONObject revisitStatus = DictCache.getJsonAllDictListByGroupCode(depCode, "CC_AS_REVISIT_STATUS").getJSONObject("data");// 回访状态
		JSONObject revisitResult = DictCache.getJsonAllDictListByGroupCode(depCode, "CC_AS_REVISIT_RESULT").getJSONObject("data");// 回访结果
		for (Map<String, String> map : data) {
			list = new ArrayList<String>();
			list.add(String.valueOf(++i));
			list.add(map.get("CUST_NAME"));
			list.add(map.get("CUST_PHONE"));
			list.add(map.get("CROWD_PACK_NAME"));
			list.add(map.get("PROD_NAME"));
			list.add(revisitStatus.getString(map.get("SERVICE_STATUS")) == null ? "":revisitStatus.getString(map.get("SERVICE_STATUS")));
			list.add(StringUtils.isBlank(map.get("END_TIME")) ? map.get("RESULT_TIME"):map.get("END_TIME"));
			if(StringUtils.isBlank(map.get("RESULT_CONTENT"))) {
				list.add(revisitResult.getString(map.get("REVISIT_RESULT")) == null ? "":revisitResult.getString(map.get("REVISIT_RESULT")));
			}else {
				list.add(map.get("RESULT_CONTENT"));
			}
			list.add("Y".equals(map.get("MANUAL")) ? "是":"否");
			list.add("1".equals(map.get("SMS_INF_FLAG")) ? "是":"否");
			list.add(StringUtils.isBlank(map.get("RESULT_DESCRIBE")) ? "":map.get("RESULT_DESCRIBE"));
			excelData.add(list);
		}
		try {
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			renderFile(file, "机器人回访列表.xlsx");
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "数据导出失败：" + e.getMessage(),e);
		}
	}

	/**
	 * 外呼前检查
	 * @return
	 */
	public JSONObject actionForBeforeCallCheck() {
		
		JSONObject data = getJSONObject();
		String mobile = data.getString("mobile");
		String id = data.getString("crowdId");
		String channelType = data.getString("channelType");
		
		//免打扰名单校验
		boolean disturbFlag1 = checkMobileDisturb(mobile, channelType);
		if(disturbFlag1) {
			return EasyResult.error(500,"该号码在当前渠道为免打扰状态，无法外呼");
		}
		
		//人群包免打扰状态校验
		boolean disturbFlag2 = checkMobileDisturbCrowdInfo(mobile,id);
		if(disturbFlag2) {
			return EasyResult.error(500,"该号码在当前渠道为免打扰状态，无法外呼");
		}
		
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForGetAnswer() {
		JSONObject entInfo = this.getJSONObject();
		EasyRecord setColumns = new EasyRecord("c_no_as_crowd_robot", "ID").setColumns(entInfo);
		try {
			return EasyResult.ok(this.getQuery().findById(setColumns));
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			
		}
		return EasyResult.error(500,"无法获取答案");
		
	}
	
	private boolean checkTime(String startTime,String endTime,String format) {
		if(DateUtil.compareDate(startTime, endTime, format)==1) {
			return false;
		}
		String currentTime = DateUtil.getCurrentDateStr(format);
		int flag1 = DateUtil.compareDate(startTime, currentTime, format);
		int flag2 = DateUtil.compareDate(endTime, currentTime, format);
		return flag1 == -1 && flag2 == 1;
	}
	
	/**
	 * 处理洗悦家营销数据
	 */
	private void handleMarket(String mobile,String serviceId) {
		EasyCalendar calendar = EasyCalendar.newInstance();
		calendar.add(EasyCalendar.HOUR, -1);
		String dateTime = calendar.getDateTime("-");
		try {
			getQuery().executeUpdate("UPDATE C_TELEMARKET SET AS_CROWDS_SERVICE_ID =? ,CHANNEL_SOURCE =? WHERE OPERATE_TIME >=? and CUST_PHONE =?",
					serviceId,Constants.CHANNEL_MARKET,dateTime,mobile);
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "error:" + e.getMessage());
		}
	}
	
	
	public JSONObject actionForUpdateCallOutSecond() {
		UserModel user = UserUtil.getUser(getRequest());
		String currentTime = DateUtil.getCurrentDateStr();
		JSONObject data = getJSONObject();
		JSONObject questionnaire = data.getJSONObject("questionnaire");
		String serviceType = data.getString("SERVICE_TYPE");
		String resultType = data.getString("resultType");
		String resultName = "";
		try {
			String crowdId = data.getString("crowdId");
			if(StringUtils.isBlank(crowdId)) {
				return EasyResult.error();
			}
			if("0".equals(resultType)) {
				resultName = "有效回访";
			}else if("1".equals(resultType)) {
				resultName = "无效回访";
			}
			//写入结果表
			EasyRecord record = new EasyRecord("C_NO_AS_RESULT");
			String resultId = RandomKit.uniqueStr();
			record.put("ID", resultId);
			record.put("CROWD_ID", crowdId);
			record.put("RESULT", "1".equals(resultType) ? "":data.getString("RESULT"));
			record.put("RESULT_NAME", resultName);
			record.put("RESULT_CLASS", data.getString("REVISIT_RESULT"));
			record.put("RESULT_ACC", user.getUserAcc());
			record.put("RESULT_TIME", currentTime);
			record.put("RESULT_CHANNEL", Constants.CHANNEL_OUTBOUND);
			record.put("RESULT_CHANNEL_NAME", "人工外呼");
			record.put("SESSION_ID", data.getString("sessionId"));
			record.put("RESULT_CONTENT", data.getString("RESULT_CONTENT"));
			record.put("CONTACT_ORDER_CODE", data.getString("contactOrderCode"));
			record.put("CONVERSION_TYPE", data.getString("conversionType"));
			record.put("QUESTIONNAIRE_ID", questionnaire.getString("id"));
			getQuery().save(record);
			//更新明细表
			EasyRecord detailRecord = new EasyRecord("C_NO_AS_CROWD","ID");
			detailRecord.setPrimaryValues(crowdId);
			detailRecord.set("SERVICE_STATUS", Constants.HAS_REVISITED);
			detailRecord.set("RESULT_CONTENT", data.getString("RESULT_CONTENT"));
			detailRecord.set("RESULT", "1".equals(resultType) ? "":data.getString("RESULT"));
			detailRecord.set("RESULT_CONTENT", data.getString("RESULT_NAME"));
			//1有效回访、其他无效回访
			detailRecord.set("REVISIT_RESULT", "0".equals(resultType) ? "1":data.getString("RESULT"));
			getQuery().update(detailRecord);
			//处理问卷
			handleQuestionnaire(questionnaire,resultId,data.getString("crowdId"),user,true);
			logger.info("用户：" + UserUtil.getUser(getRequest()).getUserAcc() + "修改了主动外呼单，人群包id：" + data.getString("crowdId") + "，时间：" + DateUtil.getCurrentDateStr());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "error:" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok();
	}
	
	
	public JSONObject actionForUpdateMarketSecond() {
		JSONObject data = getJSONObject();
		String currentTime = DateUtil.getCurrentDateStr();
		String resultType = data.getString("resultType");
		String resultName = "";
		try {
			String crowdId = data.getString("crowdId");
			if(StringUtils.isBlank(crowdId)) {
				return EasyResult.error();
			}
			if("0".equals(resultType)) {
				resultName = "有效回访";
			}else if("1".equals(resultType)) {
				resultName = "无效回访";
			}
			//写入结果表
			EasyRecord record = new EasyRecord("C_NO_AS_RESULT");
			record.put("ID", RandomKit.uniqueStr());
			record.put("CROWD_ID", data.getString("crowdId"));
			record.put("RESULT", "1".equals(resultType) ? "":data.getString("RESULT"));
			record.put("RESULT_NAME", resultName);
			record.put("RESULT_CLASS", data.getString("REVISIT_RESULT"));
			record.put("RESULT_ACC", UserUtil.getUser(getRequest()).getUserAcc());
			record.put("RESULT_TIME", currentTime);
			record.put("RESULT_CHANNEL", Constants.CHANNEL_MARKET);
			record.put("RESULT_CHANNEL_NAME", "人工营销");
			record.put("SESSION_ID", data.getString("sessionId"));
			record.put("RESULT_CONTENT", data.getString("RESULT_CONTENT"));//备注
			record.put("CONTACT_ORDER_CODE", data.getString("contactOrderCode"));
			record.put("CONVERSION_TYPE", data.getString("conversionType"));
			getQuery().save(record);
			//更新明细表
			EasyRecord detailRecord = new EasyRecord("C_NO_AS_CROWD","ID");
			detailRecord.setPrimaryValues(data.getString("crowdId"));
			detailRecord.set("SERVICE_STATUS", Constants.HAS_REVISITED);
			detailRecord.set("RESULT_CONTENT", data.getString("RESULT_CONTENT"));//备注
			detailRecord.set("RESULT", "1".equals(resultType) ? "":data.getString("RESULT"));
			detailRecord.set("RESULT_CONTENT", data.getString("RESULT_NAME"));
			//1有效回访、其他无效回访
			detailRecord.set("REVISIT_RESULT", "0".equals(resultType) ? "1":data.getString("RESULT"));
			getQuery().update(detailRecord);
			logger.info("用户：" + UserUtil.getUser(getRequest()).getUserAcc() + "修改了营销处理单，人群包id：" + data.getString("crowdId") + "，时间：" + DateUtil.getCurrentDateStr());
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "error:" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok();
	}
	
	/**
	 * 权限校验
	 * @param userAcc 账号
	 * @param resId 资源id
	 * @return
	 */
	private boolean checkRole(String userAcc,String resId) {
		EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.FRAME_DS);
		boolean flag = false;
		EasySQL checkSql = new EasySQL("SELECT COUNT(1) FROM EASI_ROLE_USER T1,EASI_ROLE_RES T2 WHERE 1=1");
		checkSql.append("AND T1.ROLE_ID = T2.ROLE_ID");
		checkSql.append(resId,"AND RES_ID = ?",false);//权限id
		checkSql.append(userAcc,"AND T1.USER_ID = (SELECT USER_ID FROM EASI_USER_LOGIN WHERE USER_ACCT = ?)");
		try {
			int k = query.queryForInt(checkSql.getSQL(), checkSql.getParams());
			if(k!=0) {
				flag = true;
			}
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "查询权限出错：" + e.getMessage(),e);
			return false;
		}
		return flag;
	}

	public EasyResult actionForCheckNumber() {
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		JSONObject params = new JSONObject();
		JSONObject param = getJSONObject();
		obj.put("command", "getStrategyIdByPhoneAndChannelId");
		params.put("phone", param.getString("phone"));
		params.put("channelId", "4");
		params.put("type", "0");
		obj.put("params", params);
		try {
			strategyLogger.info("检查号码是否在免打扰人群中，参数：" + obj.toJSONString());
			IService service = ServiceContext.getService("ACTIVE-SERVICE-CROWD-INTEFACE");
			JSONObject results = service.invoke(obj);
			strategyLogger.info("检查号码是否在免打扰人群中，结果：" + results.toJSONString());
			if (results.getString("respCode").equals(GWConstants.RET_CODE_SUCCESS) && results.getJSONObject("respData").getBoolean("isExist")) {
				EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.YW_DS);
				JSONArray data = results.getJSONObject("respData").getJSONArray("data");
				EasySQL sql = new EasySQL("SELECT SCRIPT_CONTENT FROM C_NO_AS_DND_STRATEGY WHERE 1 = 1 ");
				sql.append(" AND STRATEGY_ID IN (");
				for (int i = 0; i < data.size(); i++) {
					if (i != 0) {
						sql.append(",");
					}
					sql.append(data.getJSONObject(i).getString("STRATEGY_ID"), "?");
				}
				sql.append(") ");
				strategyLogger.info("检查号码是否在免打扰人群中，sql：" + sql.getSQL() + " 参数：" + Arrays.toString(sql.getParams()));
				List<EasyRow> easyRows = query.queryForList(sql.getSQL(), sql.getParams());
				strategyLogger.info("检查号码是否在免打扰人群中，结果：" + JSON.toJSONString(easyRows));
				JSONArray jsonArray = new JSONArray();
				for (EasyRow easyRow : easyRows) {
					jsonArray.add(easyRow.getColumnValue("SCRIPT_CONTENT"));
				}
				result.put("result", jsonArray);
				obj.put("command", "saveRemindRecord");
				params.put("CHANNEL_NAME", "主动服务平台-主动外呼处理列表呼出");
				params.put("STRATEGY_ID", data.getJSONObject(0).getString("STRATEGY_ID"));
				params.put("STRATEGY_NAME", data.getJSONObject(0).getString("STRATEGY_NAME"));
				params.put("CROWD_PACK_ID", data.getJSONObject(0).getString("CROWD_PACK_ID"));
				params.put("CROWD_NAME", data.getJSONObject(0).getString("CROWD_NAME"));
				params.put("CREATE_ACC", UserUtil.getUser(getRequest()).getUserAcc());
				obj.put("params", params);
				strategyLogger.info("记录免打扰提醒表，参数：" + obj.toJSONString());
				IService service1 = ServiceContext.getService("ACTIVE-SERVICE-CROWD-INTEFACE");
				JSONObject results1 = service1.invoke(obj);
				strategyLogger.info("记录免打扰提醒表，结果：" + results1.toJSONString());
			} else {
				return EasyResult.fail();
			}
		} catch (Exception e) {
			strategyLogger.error(e);
		}
		return EasyResult.ok(result);
	}
}
