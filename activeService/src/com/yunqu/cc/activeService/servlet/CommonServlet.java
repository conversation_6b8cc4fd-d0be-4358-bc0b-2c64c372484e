package com.yunqu.cc.activeService.servlet;

import javax.servlet.annotation.WebServlet;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.calendar.EasyCalendar;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.OrderCommand;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.activeService.base.AppBaseServlet;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.base.Constants;
import com.yunqu.cc.activeService.utils.CsUtil;
import com.yunqu.cc.activeService.utils.StringUtil;

@WebServlet("/servlet/common")
public class CommonServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;
	
	private final Logger logger = CommLogger.logger;
	
	public JSONArray queryForProCode2Level(){
		JSONObject result = new JSONObject();
		JSONObject pcObj = this.getJSONObject();
		JSONObject request = new JSONObject();
		request.put("command", OrderCommand.COMM_PRODUCT_CODE_TREE);
		if(pcObj!=null&&pcObj.getInteger("level")!=null){
			pcObj.put("pageIndex", 1);
			pcObj.put("pageSize", 1000);
		}else{
			pcObj = new JSONObject();
			String level = StringUtil.formatNull(this.getRequest().getParameter("level"),"2");
			pcObj.put("level",Integer.parseInt(level)+2);
			pcObj.put("orgCode",StringUtil.formatNull(this.getRequest().getParameter("orgCode")));
			pcObj.put("pageIndex",StringUtil.formatNull(this.getRequest().getParameter("pageIndex"),"1"));
			pcObj.put("pageSize",StringUtil.formatNull(this.getRequest().getParameter("pageSize"),"1000"));
			pcObj.put("parentProdCode",StringUtil.formatNull(this.getRequest().getParameter("id")));
			request.put("params", pcObj);
			try {
				IService service = ServiceContext.getService(Constants.CSSGW_COMM);
				result = service.invoke(request);
			} catch (ServiceException e) {
				logger.error("IService请求失败,请求参数"+JSON.toJSONString(request)+",原因"+e.getMessage());
			}
		}
		return CsUtil.getTreeData(result);
	}
	

	public EasyResult actionForSendSmsMessage(){
		JSONObject entInfo = this.getJSONObject();
		try {
			//发送短信号码
			String smsPhone = entInfo.getString("smsPhone");
			//短信内容
			String content=entInfo.getString("smsContent");
			String orgCode=entInfo.getString("orgCode");
			String brandCode=entInfo.getString("brandCode");
			if(StringUtils.isBlank(orgCode)) {
				return EasyResult.fail("短信发送失败，策略未配置主体");
			}
			
			if(StringUtils.isBlank(brandCode)) {
				return EasyResult.fail("短信发送失败，策略未配置品牌");
			}
			
			String id=IDGenerator.getDefaultNUMID();//短信编号
			JSONObject smsParams = new JSONObject();
			smsParams.put("command",ServiceCommand.SENDMESSAGE);
			smsParams.put("sender", Constants.APP_NAME);
			smsParams.put("serialId", id);
			smsParams.put("password", "YQ_85521717");
			smsParams.put("source", "05");
			smsParams.put("busiId", IDGenerator.getIDByCurrentTime(20));
			smsParams.put("sendTime", EasyCalendar.newInstance().getDateTime("-")); 
			smsParams.put("model", orgCode); //事业部标识
			smsParams.put("category", brandCode); ///品牌标识
			smsParams.put("receivers", StringUtil.spiltByRegex( smsPhone,";",content));
			smsParams.put("userAcc", UserUtil.getUser(this.getRequest()).getUserAcc());
			logger.info("--"+JSON.toJSONString(smsParams));
			IService service = ServiceContext.getService(ServiceID.SMSGW_INTEFACE);
			JSONObject result = service.invoke(smsParams);
			String respCode = result.getString("respCode");
			//短信发送成功
			if(!GWConstants.RET_CODE_SUCCESS.equals(respCode)){
				return EasyResult.fail("短信发送失败");
			}
		} catch (Exception e) {
			this.error("actionForSendSmsAndSaveRecord处理失败,",e);
			return EasyResult.error(500,"发送短信成功，但保存记录失败");
		}
		return EasyResult.ok("发送成功");
	}
}
