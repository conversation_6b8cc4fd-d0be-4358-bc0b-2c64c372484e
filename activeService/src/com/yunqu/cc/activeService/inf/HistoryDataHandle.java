package com.yunqu.cc.activeService.inf;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.JsonUtil;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.base.Constants;
import org.apache.log4j.Logger;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @title:HistoryDataHandle
 * @date:2023/4/2110:54
 */
public class HistoryDataHandle extends IService {

    private final Logger logger = CommLogger.getCommLogger("hisClear");
    
    protected EasyQuery getQuery()
    {
        return EasyQuery.getQuery(Constants.APP_NAME, Constants.YW_DS);
    }

    @Override
    public JSONObject invoke(JSONObject json) throws ServiceException {
        String command = json.getString("command");
        if("clearAutoRobotData".equals(command)){//AI自动外呼数据老化-每天
            return dataHandle();
        }else {
            JSONObject result = JsonUtil.createInfRespJson(json);
            result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
            result.put("respDesc", "error：不存在的command！");
            logger.error(CommonUtil.getClassNameAndMethod(this)+" 不存在的command,请检查:" + command);
            return result;
        }
    }

    //AI外呼数据处理
    private JSONObject dataHandle(){
        //统计报表
        boolean success = statForReport();
        if(success){//数据统计成功之后才进行老化
            //主表已回访数据迁移
            clearAutoRobotData();
            //自动打标回访超时数据结果（发布时间超过3天未外呼），第二天凌晨再进行老化
            clearOvertimeRevisitData();
        }
        return null;
    }

    private void clearOvertimeRevisitData() {
         try {
             //获取query
             EasyQuery query = getQuery();
             String nowDate = DateUtil.getCurrentDateStr("yyyy-MM-dd");
             //获取
             String publishDate = DateUtil.addDay("yyyy-MM-dd", nowDate, -4);
             String publishStartTime = publishDate+" 00:00:00";
             String publishEndTime = publishDate+" 23:59:59";

             //查询前四天发布，但未回访的数据（回访超时），默认结果为已超时
             List<EasyRow> overtimeList = query.queryForList("select ID from C_NO_AS_ROBOT_AUTO_CALL where PUBLISH_TIME>=? AND PUBLISH_TIME<=? AND SERVICE_STATUS = ? ", new Object[]{publishStartTime, publishEndTime, Constants.HAS_PUBLISHED});
             for (EasyRow easyRow : overtimeList) {
                 String updateSQL = "update C_NO_AS_ROBOT_AUTO_CALL set service_status = ?,result = ?,result_time = ? where ID = ?";
                 String id = easyRow.getColumnValue("ID");
                 //回访时间默认为昨天，方便老化
                 query.execute(updateSQL, Constants.OTHER_STATUS, "93", EasyDate.getCurrentTimeStampString(), id);
                 //写入意图表--30天内重呼
                 EasyRecord saveRecord = new EasyRecord("C_NO_AS_ROBOT_CALL_RESULT", "ID");
                 saveRecord.set("ID", RandomKit.uniqueStr());
                 saveRecord.set("ROBOT_CALL_ID", "");//c_no_as_crowd_robot.id
                 saveRecord.set("REVISIT_RESULT", "98");
                 saveRecord.set("RESULT_CONTENT", "回访超时");
                 saveRecord.set("RESULT_TIME", DateUtil.getCurrentDateStr());
                 saveRecord.set("CARD_ID", "");
                 saveRecord.set("INTENTION_ID", "");
                 saveRecord.set("INTENTION_NAME", "");
                 saveRecord.set("INTENTION_TAG", "");
                 saveRecord.set("INTENTION_RESULT", "");
                 saveRecord.set("INTENTION_RESULT_TYPE", "");
                 saveRecord.set("LAST_CALL_FLAG", "Y");//需要查询是否最后一次
                 saveRecord.set("ROBOT_AUTO_CALL_ID", id);//c_no_as_robot_auto_call.id
                 saveRecord.set("IS_RETURN_RESULT", Constants.SN_N);
                 saveRecord.set("SORT_NUM", 0);
                 query.save(saveRecord);
             }

         } catch (Exception e) {
             logger.error("e:"+e.getMessage(),e);
        }

    }

    /**
     * 报表统计
     * @return
     */
    private boolean statForReport() {
        logger.info(CommonUtil.getClassNameAndMethod(this) + "处理开始..."+System.currentTimeMillis());
        EasyQuery query = getQuery();
        String today = DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD);
        String yesterday = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, today, -1);
        EasySQL sql = new EasySQL("select ");
        sql.append("t2.id as crowd_pack_id,t2.bg_crowd_code as crowd_pack_code,t2.name,");
        sql.append("SUM(SMS_TIMES) SMS_NUM,");//短信触发次数
        sql.append("SUM(CALL_TIMES) CALL_NUM,");//外呼总数(明细表只要写进亿迅外呼表就会+1)
        sql.append("SUM(CASE WHEN t1.CALL_TIMES >0 THEN 1 ELSE 0 END) CALL_USER_NUM,");//外呼用户数
        sql.append("SUM(CASE WHEN t1.CALL_TIMES >1 THEN 1 ELSE 0 end) RE_CALL_USER_NUM,");//重呼用户数
        sql.append("SUM(CASE WHEN SERVICE_STATUS = '4' and REVISIT_RESULT = '1' THEN 1 ELSE 0 END) SUCCESS_NUM,");//有效回访
        sql.append("SUM(CASE WHEN SERVICE_STATUS = '4' and REVISIT_RESULT = '2' THEN 1 ELSE 0 END) FAIL_NUM_WRJT,");//无人接听
        sql.append("SUM(CASE WHEN SERVICE_STATUS = '4' and REVISIT_RESULT = '4' THEN 1 ELSE 0 END) FAIL_NUM_YHJF,");//用户拒访
        sql.append("SUM(CASE WHEN SERVICE_STATUS = '99' and RESULT = '91' THEN 1 ELSE 0 END) FAIL_NUM_WHSX,");//近30天外呼上限
        sql.append("SUM(CASE WHEN SERVICE_STATUS = '99' and RESULT = '92' THEN 1 ELSE 0 END) FAIL_NUM_MAX");//人群包已达外呼上限
        sql.append("from C_NO_AS_ROBOT_AUTO_CALL t1");
        sql.append("join C_NO_AS_CROWD_PACK t2 on t1.crowd_pack_id = t2.id");
        sql.append("where 1=1");
        sql.append(yesterday + " 00:00:00","and result_time >= ?");
        sql.append(today + " 00:00:00","and result_time < ?");
        sql.append("group by t2.id,name,t2.bg_crowd_code");
        try {
        	List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
            if(list!=null&&list.size()>0) {
                Map<String, List<JSONObject>> hisCallMap = getHisCall(today, yesterday, query);
            	EasyRecord record = new EasyRecord("C_NO_AS_ROBOT_CALL_STAT");
            	for(JSONObject j:list) {
	                int smsNum = j.getIntValue("SMS_NUM");
	                int callNum = j.getIntValue("CALL_NUM");
	                int callSuccessNum = j.getIntValue("SUCCESS_NUM");
	                int nobodyAnswerNum = j.getIntValue("FAIL_NUM_WRJT");
	                int noAccessNum = j.getIntValue("FAIL_NUM_YHJF");
	                int maxCallNum = j.getIntValue("FAIL_NUM_WHSX");
	                int maxCrowdCallNum = j.getIntValue("FAIL_NUM_MAX");
	                int callFailNum = callNum - callSuccessNum;
	                record.put("ID", RandomKit.uniqueStr());
	                record.put("DATE_ID", yesterday.replace("-", ""));
	                record.put("SMS_COUNT", smsNum);
	                record.put("CALL_COUNT", callNum);
	                record.put("ANSWER_COUNT", callSuccessNum);
	                record.put("FAIL_COUNT", callFailNum);
	                record.put("CROWD_PACK_ID", j.getString("CROWD_PACK_ID"));
	                record.put("CROWD_PACK_CODE", j.getString("CROWD_PACK_CODE"));
	                record.put("CROWD_PACK_NAME", j.getString("NAME"));
	                record.put("CREATE_TIME", j.getString("CREATE_TIME"));
	                record.put("NOBODY_COUNT", nobodyAnswerNum);
	                record.put("REJECT_COUNT", noAccessNum);
                    record.put("CALL_USER_NUM", j.getIntValue("CALL_USER_NUM"));
                    record.put("RECALL_USER_NUM", j.getIntValue("RE_CALL_USER_NUM"));
                    String crowdPackId = j.getString("CROWD_PACK_ID");
                    if(hisCallMap.containsKey(crowdPackId) && CommonUtil.listIsNotNull(hisCallMap.get(crowdPackId))) {
                        record.put("HIS_RECALL_USER", hisCallMap.get(crowdPackId).get(0).getIntValue("HIS_RECALL_USER"));
                    } else {
                        record.put("HIS_RECALL_USER", 0);
                    }
	                query.save(record);
            	}
                logger.info(CommonUtil.getClassNameAndMethod(this) + "处理["+list.size()+"]完成..."+System.currentTimeMillis());
            	return true;
            }
        } catch (SQLException e) {
            logger.error(CommonUtil.getClassNameAndMethod(this) + "error:" + e.getMessage(),e);
        }
        logger.info(CommonUtil.getClassNameAndMethod(this) + "处理完成..."+System.currentTimeMillis());
        return false;
    }


    private JSONObject clearAutoRobotData() {
        JSONObject json = new JSONObject();

        String now = EasyDate.getCurrentTimeStampString();
        //C_NO_AS_ROBOT_AUTO_CALL 的数据迁移当天的数据到C_NO_AS_ROBOT_AUTO_CALL_HIS
        try {
            //获取query
            EasyQuery query = getQuery();
            //query里面写插入语句的sql语句
            EasySQL sql = new EasySQL();
            sql.append("insert into C_NO_AS_ROBOT_AUTO_CALL_HIS(ID,CUST_PHONE,CUST_NAME,ORG_CODE,BRAND_CODE,BRAND_NAME,PROD_CODE,PROD_NAME,PRODUCT_MODEL,CREATE_TIME,PUBLISH_TIME,RESULT_TIME,SERVICE_STATUS,RESULT,REVISIT_RESULT,RESULT_CONTENT,CROWD_PACK_ID,DATA_SOURCE,CALL_TIMES,SMS_TIMES,CREATE_DATE,MIGRATE_TIME,OUT_ID,RETURN_TYPE,OPERATE_ID,RETURN_MSG,ROBOT_CALL_ID) ");
            sql.append("select ID,CUST_PHONE,CUST_NAME,ORG_CODE,BRAND_CODE,BRAND_NAME,PROD_CODE,PROD_NAME,PRODUCT_MODEL,CREATE_TIME,PUBLISH_TIME,RESULT_TIME,SERVICE_STATUS,RESULT,REVISIT_RESULT,RESULT_CONTENT,CROWD_PACK_ID,DATA_SOURCE,CALL_TIMES,SMS_TIMES,CREATE_DATE,'"+now+"',OUT_ID,RETURN_TYPE,OPERATE_ID,RETURN_MSG,ROBOT_CALL_ID");
            sql.append("FROM C_NO_AS_ROBOT_AUTO_CALL");
            sql.append("where CREATE_DATE is not null");//分区字段，不允许空
            sql.append(Constants.HAS_REVISITED," and (SERVICE_STATUS = ?");
            sql.append(Constants.OTHER_STATUS," or SERVICE_STATUS = ?)");
            //写一个变量计算小于当天时间
            sql.append(getTodayBeginTime(),"and RESULT_TIME < ?");


            query.execute(sql.getSQL(),sql.getParams());
            //删除已经迁移的数据
            EasySQL delsql = new EasySQL();
            delsql.append("delete from C_NO_AS_ROBOT_AUTO_CALL");
            delsql.append("where CREATE_DATE is not null");
            delsql.append(Constants.HAS_REVISITED," and (SERVICE_STATUS = ?");
            delsql.append(Constants.OTHER_STATUS," or SERVICE_STATUS = ?)");
            //写一个变量计算小于当天时间
            delsql.append(getTodayBeginTime(),"and RESULT_TIME < ?");
            query.execute(delsql.getSQL(),delsql.getParams());
            //迁移成功
            logger.info("迁移成功");
        } catch (Exception e) {
            json.put("result", "fail");
            logger.error(e.getMessage(),e);
        }
        json.put("result", "success");
        return json;
    }

    //写一个方法获取昨天的时间
    private String getTodayBeginTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String dateStr = sdf.format(new Date());
        return dateStr+" 00:00:00";
    }

    /**
     * 获取各人群包跨天重呼用户数
     *
     * @param today
     * @param yesterday
     * @param query
     * @return
     */
    private Map<String, List<JSONObject>> getHisCall(String today, String yesterday, EasyQuery query) {
        Map<String, List<JSONObject>> result = new HashMap<>();
        logger.info(CommonUtil.getClassNameAndMethod(this) + "[" + yesterday + "]查询历史重呼开始..." + System.currentTimeMillis());
        //呼叫次数大于1表示存在重呼，服务时间为当天，但存在非当天呼叫记录
        String lastWeek = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, today, -8);
        EasySQL sql = new EasySQL("SELECT t1.CROWD_PACK_ID,count(DISTINCT t1.ID) HIS_RECALL_USER");
        sql.append("FROM c_no_robot_call t LEFT JOIN C_NO_AS_ROBOT_AUTO_CALL t1 ON t.ADD2 =t1.ID");
        sql.append("WHERE t.record_status =3 AND t1.REVISIT_RESULT !=99 AND t1.CALL_TIMES >1 ");
        sql.append(yesterday + " 00:00:00", "and result_time >= ?");
        sql.append(today + " 00:00:00", "and result_time < ?");
        sql.append(lastWeek, "AND t.add1>=?");
        sql.append(yesterday, "AND t.add1<?");
        sql.append("GROUP BY t1.CROWD_PACK_ID");
        try {
            List<JSONObject> hisCallList = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
            if (CommonUtil.listIsNotNull(hisCallList)) {
                logger.info(CommonUtil.getClassNameAndMethod(this) + "[" + yesterday + "]查询历史重呼结果：" + hisCallList.size() + "," + System.currentTimeMillis());
                result = hisCallList.stream().collect(Collectors.groupingBy(t -> t.getString("CROWD_PACK_ID")));
            }
        } catch (Exception e) {
            logger.error(CommonUtil.getClassNameAndMethod(this) + e.getMessage(), e);
        } finally {
            logger.info(CommonUtil.getClassNameAndMethod(this) + "[" + yesterday + "]查询历史重呼结束..." + System.currentTimeMillis());
            return result;
        }
    }
    
}
