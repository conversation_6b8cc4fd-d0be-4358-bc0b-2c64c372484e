package com.yunqu.cc.activeService.inf;

import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Base64.Decoder;

import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.activeService.service.UserDndService;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.JsonUtil;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;
import com.yq.busi.common.util.security.SecurityUtil;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.base.Constants;
import com.yunqu.cc.activeService.model.DataSource;
import com.yunqu.cc.activeService.service.TriggerUserSyncService;

public class ActiveService extends IService {

	private final Logger logger = CommLogger.getCommLogger();
	private final Logger handleLogger = CommLogger.getCommLogger("handle");
	private final Logger triggerLogger = CommLogger.getCommLogger("trigger");
	private final Logger outTrigger = CommLogger.getCommLogger("outTrigger");
	private final Logger statLogger = CommLogger.getCommLogger("stat");
	private final Logger strategyLogger = CommLogger.getCommLogger("strategy");
	protected EasyQuery getQuery()
	  {
		 return EasyQuery.getQuery(Constants.APP_NAME, Constants.YW_DS);
	  }
	
	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		String command = json.getString("command");
		if("as-PRODUCT-TIME-TO-SYNC".equals(json.getString("command"))){
			return ProductInfoSyncService.getInstance().invoke();
		}else if("as-PRODUCT-TIME-TO-GATHER".equals(json.getString("command"))){
			return DictGatherService.getInstance().invoke();
//		}else if("getCrowdPackListByCustPhone".equals(json.getString("command"))){
//			return getCrowdPackListByCustPhone(json);
//		}else if("submitServiceResult".equals(json.getString("command"))){
//			return submitServiceResult(json);
//		}else if("getStrategyEnableResultList".equals(json.getString("command"))){
//			return getStrategyEnableResultList(json);
		} else if("getH5CustPhoneCrowdPackList".equals(json.getString("command"))){//H5获取人群包列表
			return getH5CustPhoneCrowdPackList(json);
		} else if("getH5CrowdPackScript".equals(json.getString("command"))){//H5获取人群包策略脚本
			return getH5CrowdPackScript(json);
		} else if("triggerGroupUserSync".equals(json.getString("command"))){//触发器同步
			return triggerGroupUserSync(json);
		} else if("dayActiveServiceStat".equals(json.getString("command"))){//每天统计-主动服务统计
			return dayActiveServiceStat(json);
		} else if("updateTriggerSyncInfo".equals(command)){ //五分钟定时更新触发器推送到人群包的同步时间和用户数量
			return updateTriggerSyncInfo(json);
		} else if("aiTriggerGroupUserSync".equals(json.getString("command"))){//AI自动外呼触发器同步
			return aiTriggerGroupUserSync(json);
		}else if("myxTriggerGroupUserSync".equals(json.getString("command"))){//外部自动外呼触发器同步
			return outTriggerGroupUserSync(json,Constants.RETURN_TYPE_MYX,false,false);//01 美云销
		}else if("outATriggerGroupUserSync".equals(json.getString("command"))){//外部自动外呼触发器同步(换名字 autoAITriggerGroupUserSync 后续不对外提供当前接口)
			return outTriggerGroupUserSync(json,json.getString("returnType"),false,false);//
		}else if("autoAITriggerGroupUserSync".equals(json.getString("command"))){//外部自动外呼触发器同步
			return outTriggerGroupUserSync(json,json.getString("returnType"),true,false);//
		}else if("repeatTriggerGroupUserSync".equals(json.getString("command"))){//外部自动外呼触发器同步(换名字 autoAITriggerGroupUserSync 后续不对外提供当前接口)
			return outTriggerGroupUserSync(json,json.getString("returnType"),false,true);//允许重复推送OUTID
		}else if("getStrategyIdByPhoneAndChannelId".equals(json.getString("command"))){
			return getStrategyIdByPhoneAndChannelId(json);
		}else if("saveRemindRecord".equals(json.getString("command"))){
			return saveRemindRecord(json);
		}else {
			JSONObject result = JsonUtil.createInfRespJson(json);
			result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
			result.put("respDesc", "error：不存在的command！");
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 不存在的command,请检查:" + command);
			return result;
		}
	}
	
    private JSONObject updateTriggerSyncInfo(JSONObject json) {
    	
    	triggerLogger.info("开始更新触发器同步信息");
    	
    	//从缓存获取触发器人群包同步信息
    	TriggerUserSyncService instance = TriggerUserSyncService.getInstance();
    	String triggerUpdateSwitch = instance.getTriggerUpdateSwitch();
    	//防止重复执行定时器
    	if(triggerUpdateSwitch==null||"".equals(triggerUpdateSwitch)){
    		//最多执行30分钟
    		instance.setTriggerUpdateSwitch();
    		
	    	String update = instance.getTriggerUpdate();
			triggerLogger.info("触发器人群包是否有更新run="+update);
	    	//有人群包更新
	    	if("1".equals(update)) {
	        	try {
	        		EasyQuery query = getQuery();
					List<EasyRow> list = query.queryForList("SELECT ID FROM C_NO_AS_CROWD_PACK WHERE DATA_SOURCE = ?",new Object[] {DataSource.DATA_SOURCE_TRI.getCode()});
					triggerLogger.info("查询所有触发器人群包列表list="+list);
					if(list!=null&&list.size()>0) {
						for (int i = 0; i < list.size(); i++) {
							EasyRow row = list.get(i);
							String crowdPackId = row.getColumnValue("ID");
							JSONObject crowdPackObj = instance.getTriggerSyncJson(crowdPackId);
							triggerLogger.info("查询缓存中触发器人群包，ID="+crowdPackId+",缓存对象crowdPackObj="+crowdPackObj);
							if(crowdPackObj==null) {
								triggerLogger.info("无需更新人群包信息,ID="+crowdPackId);
								continue;
							}
							String updateFlag = crowdPackObj.getString("updateFlag");
							String syncTime = crowdPackObj.getString("syncTime");
							//人群包有更新
							if("1".equals(updateFlag)) {//普通触发器
								int custNum = query.queryForInt("select count(1) from C_NO_AS_CROWD where CROWD_PACK_ID = ? ",new Object[] {crowdPackId});
								query.executeUpdate("update C_NO_AS_CROWD_PACK set CUST_NUM = ?,SYNC_TIME= ? where ID = ? ", custNum,syncTime,crowdPackId);
								//更新企微推送状态--企微策略配置推送-待推送
								int executeUpdate = query.executeUpdate("update C_NO_AS_CROWDPACK_PRIORITY set SEND_STATUS = '1' where CROWD_PACK_ID=? AND CHANNEL_TYPE = ? ",crowdPackId,Constants.CHANNEL_WECHAT);
								triggerLogger.info("更新记录结果："+executeUpdate);
								//标记已更新
								instance.setTriggerUpdateFlag(crowdPackId, "0");
							}else if("2".equals(updateFlag)) {//自动外呼触发器
								int custNum = query.queryForInt("select count(1) from C_NO_AS_ROBOT_AUTO_CALL where CROWD_PACK_ID = ? ",new Object[] {crowdPackId});
								int custHisNum = query.queryForInt("select count(1) from C_NO_AS_ROBOT_AUTO_CALL_HIS where CROWD_PACK_ID =? ",new Object[] {crowdPackId});
								int custTotalNum = custHisNum+custNum;//总数
								query.executeUpdate("update C_NO_AS_CROWD_PACK set CUST_NUM = ?,SYNC_TIME= ? where ID = ? ", custTotalNum,syncTime,crowdPackId);
								//标记已更新
								instance.setTriggerUpdateFlag(crowdPackId, "0");
							}else if("3".equals(updateFlag)) {//免打扰人群包
								int custNum = query.queryForInt("select count(1) from C_NO_AS_SENSITIVE_CROWD where CROWD_PACK_ID = ? ",new Object[] {crowdPackId});
								query.executeUpdate("update C_NO_AS_CROWD_PACK set CUST_NUM = ?,SYNC_TIME= ? where ID = ? ", custNum,syncTime,crowdPackId);
								//标记已更新
								instance.setTriggerUpdateFlag(crowdPackId, "0");
							}else {
								//人群包上次已更新，无需再次更新
								triggerLogger.info("人群包上次已更新，无需再次更新,ID="+crowdPackId);
							}
						}
					}
				} catch (Exception e) {
					// TODO Auto-generated catch block
					triggerLogger.error("更新触发器人群包信息失败"+e.getMessage(),e);
				}
	    		//更新完成
	    		instance.setTriggerUpdate("0");
	    	}
	    	//允许下一次执行定时任务
	    	instance.deleteTriggerUpdateSwitch();
    	}
    	triggerLogger.info("结束更新触发器同步信息");
		return null;
	}

    /**
     * 原主动服务汇总报表使用，2023.01.16 改为直接查询C_NO_AS_SERVICE_RECORD埋点表（业务在主动服务微信群确认），只展示热线和在线坐席渠道的埋点
     * @param json
     * @return
     */
	private JSONObject dayActiveServiceStat(JSONObject json) {
    	//统计热线渠道，H5渠道，工单回访渠道每天服务情况
    	statLogger.info("开始统计主动服务埋点数");
    	EasyQuery query = getQuery();
    	try {
			String dateId = query.queryForString("SELECT MAX(DATE_ID) FROM C_NO_AS_SERVICE_SUMMARY", null);
			String currentDateId = DateUtil.getCurrentDateStr("yyyyMMdd");
			if(StringUtils.isBlank(dateId)){
				EasyCalendar  cal = EasyCalendar.newInstance();
				cal.add(EasyCalendar.MONTH, -12);
				dateId = cal.getDateInt()+"";
			}else{
				EasyCalendar  cal = EasyCalendar.newInstance(dateId,"yyyyMMdd");
				cal.add(EasyCalendar.DAY, 1);
				int  curDateId = cal.getDateInt();
				dateId = curDateId+"";//从最新统计的日期后一天开始统计  T+1统计
			}
	    	statLogger.info("执行统计【热线】，【在线】，【工单回访】埋点统计，dateId="+dateId);
			//查询埋点数据-热线渠道，H5渠道，工单回访
			String sql = "insert into c_no_as_service_summary(DATE_ID,CHANNEL_TYPE,CROWD_PACK_ID,NEED_SERVICE_NUM,REAL_SERVIE_NUM)"+
						 " select DATE_ID,CHANNEL_TYPE,CROWD_PACK_ID,COUNT(CASE WHEN DATA_TYPE = '1' THEN ID ELSE NULL END) NEED_SERVICE_NUM,COUNT(CASE WHEN DATA_TYPE = '2' THEN ID ELSE NULL END) SERVICE_NUM from C_NO_AS_SERVICE_RECORD WHERE 1=1 "+
						 " AND DATE_ID >= "+dateId+ " AND DATE_ID < "+ currentDateId+
						 " GROUP BY  DATE_ID,CHANNEL_TYPE,CROWD_PACK_ID";
	    	statLogger.info("执行统计【热线】，【在线】，【工单回访】埋点统计，sql="+sql);
			query.execute(sql);
	    	statLogger.info("结束【热线】，【在线】，【工单回访】埋点统计，dateId="+dateId);
	    	
	    	statLogger.info("开始执行【H5】埋点统计，dateId="+dateId);
//	    	String sql2 = "insert into c_no_as_service_summary(DATE_ID,CHANNEL_TYPE,CROWD_PACK_ID,NEED_SERVICE_NUM,REAL_SERVIE_NUM)"+
//					 "SELECT DATE_ID,'3' CHANNEL_TYPE ,CROWD_PACK_ID,SUM(NEED_SERVICE_NUM) NEED_SERVICE_NUM,SUM(REAL_SERVIE_NUM) REAL_SERVIE_NUM FROM ("
//					 + " SELECT PRE_SERVICE_DATE DATE_ID, CROWD_PACK_ID,COUNT(1) NEED_SERVICE_NUM,'0' REAL_SERVIE_NUM FROM C_NO_AS_CROWD WHERE 1=1 "+
//					 " AND CREATE_DATE >= "+dateId+
////					 " AND CUST_READ = '1' "+
////					 " AND RESULT_SOURCE = "+Constants.CHANNEL_ONLINE_CUST+
//					 " GROUP BY  PRE_SERVICE_DATE,CROWD_PACK_ID"+
//	    			 " UNION ALL "+
//			    	  " SELECT SERVICE_DATE DATE_ID, CROWD_PACK_ID,'0' NEED_SERVICE_NUM,COUNT(1) REAL_SERVIE_NUM FROM C_NO_AS_CROWD WHERE 1=1 "+
//					 " AND SERVICE_DATE >= "+dateId+
//					 " AND CUST_READ = '1' "+
//					 " AND RESULT_SOURCE = "+Constants.CHANNEL_ONLINE_CUST+
//					 " AND SERVICE_STATUS = "+ Constants.HAS_REVISITED+
//					 " GROUP BY SERVICE_DATE,CROWD_PACK_ID) GROUP BY DATE_ID,CROWD_PACK_ID";
//	    	query.execute(sql2, new Object[] {});
	    	statLogger.info("结束【H5】工单埋点统计，dateId="+dateId);
	    	
	    	statLogger.info("开始【主动外呼】【主动营销】埋点统计，dateId="+dateId);
	    	//基础表数量大，先查询外呼营销的人群包再汇总
	    	List<EasyRow> list = query.queryForList("SELECT distinct CROWD_PACK_ID,CHANNEL_TYPE FROM C_NO_AS_CROWDPACK_PRIORITY where CHANNEL_TYPE = ? OR CHANNEL_TYPE = ? or CHANNEL_TYPE = ?", new Object[] {Constants.CHANNEL_OUTBOUND,Constants.CHANNEL_MARKET,Constants.CHANNEL_ONLINE_CUST});
	    	String boundCrowPackIds = "";
	    	String marketCrowPackIds = "";
	    	String h5CrowPackIds = "";
	    	String robotCrowPackIds = "";
	    	String webcahtCrowPackIds = "";
	    	 for(int i =0 ;i<list.size();i++) {
	    		 EasyRow row = list.get(i);
	    		 String crowPackId = row.getColumnValue("CROWD_PACK_ID");
	    		 String channelType = row.getColumnValue("CHANNEL_TYPE");
	    		 if(Constants.CHANNEL_MARKET.equals(channelType)) {//营销人群包
	    			 if(StringUtils.isBlank(boundCrowPackIds)) {
	    				 boundCrowPackIds+="'"+crowPackId+"'";
	    			 }else {
	    				 boundCrowPackIds+=",'"+crowPackId+"'";
	    			 }
	    		 }else if(Constants.CHANNEL_OUTBOUND.equals(channelType)) {//外呼人群包
	    			 if(StringUtils.isBlank(marketCrowPackIds)) {
	    				 marketCrowPackIds+="'"+crowPackId+"'";
	    			 }else {
	    				 marketCrowPackIds+=",'"+crowPackId+"'";
	    			 }
	    		 }else if(Constants.CHANNEL_ONLINE_CUST.equals(channelType)) {//外呼人群包
	    			 if(StringUtils.isBlank(h5CrowPackIds)) {
	    				 h5CrowPackIds+="'"+crowPackId+"'";
	    			 }else {
	    				 h5CrowPackIds+=",'"+crowPackId+"'";
	    			 }
	    		 }else if(Constants.CHANNEL_ROBOT.equals(channelType)) {//外呼人群包
	    			 if(StringUtils.isBlank(robotCrowPackIds)) {
	    				 robotCrowPackIds+="'"+crowPackId+"'";
	    			 }else {
	    				 robotCrowPackIds+=",'"+crowPackId+"'";
	    			 }
	    		 }else if(Constants.CHANNEL_WECHAT.equals(channelType)) {//外呼人群包
	    			 if(StringUtils.isBlank(webcahtCrowPackIds)) {
	    				 webcahtCrowPackIds+="'"+crowPackId+"'";
	    			 }else {
	    				 webcahtCrowPackIds+=",'"+crowPackId+"'";
	    			 }
	    		 }
			 }
	    	 
	    	 statLogger.info("开始执行【H5渠道】需服务用户数统计，dateId="+dateId);
	    	 if(StringUtils.notBlank(h5CrowPackIds)) {
	    		 String[] ids = h5CrowPackIds.split(",");
	    		 if(ids!=null&&ids.length>0) {
	    			List<String> key = new ArrayList<String>();
	    			for (int i = 0; i < ids.length; i++) {
	    				String id = ids[i];
	    				boolean contains = key.contains(id);
	    				if(contains) {
	    					continue;
	    				}
	    				String statSql = "insert into c_no_as_service_summary(DATE_ID,CHANNEL_TYPE,CROWD_PACK_ID,NEED_SERVICE_NUM,REAL_SERVIE_NUM)"+
	    						"SELECT CREATE_DATE DATE_ID,'3' CHANNEL_TYPE ,CROWD_PACK_ID,COUNT(1) NEED_SERVICE_NUM,'0' REAL_SERVIE_NUM FROM C_NO_AS_CROWD WHERE 1=1 "+
	    						" AND CREATE_DATE >= '"+dateId+"' AND CREATE_DATE < '"+ currentDateId+"'"+
	    						" AND CROWD_PACK_ID = "+id+
	    						" GROUP BY CREATE_DATE,CROWD_PACK_ID";
	    				statLogger.info("执行统计【H5渠道】需服务用户数统计，sql="+statSql);
	    				query.execute(statSql);
	    				key.add(id);
	    			}
	    		 }
	    	 }else {
	 	    	statLogger.info("【H5渠道】未配置人群包，dateId="+dateId);
	    	 }
	    	 
	    	 statLogger.info("开始执行【机器人】需服务用户数统计，dateId="+dateId);
	    	 if(StringUtils.notBlank(robotCrowPackIds)) {
	    		 String[] ids = robotCrowPackIds.split(",");
	    		 if(ids!=null&&ids.length>0) {
	    			List<String> key = new ArrayList<String>();
	    			for (int i = 0; i < ids.length; i++) {
	    				String id = ids[i];
	    				boolean contains = key.contains(id);
	    				if(contains) {
	    					continue;
	    				}
			    		String statSql = "insert into c_no_as_service_summary(DATE_ID,CHANNEL_TYPE,CROWD_PACK_ID,NEED_SERVICE_NUM,REAL_SERVIE_NUM)"+
			    				 "SELECT CREATE_DATE DATE_ID,'7' CHANNEL_TYPE ,CROWD_PACK_ID,COUNT(1) NEED_SERVICE_NUM,'0' REAL_SERVIE_NUM FROM C_NO_AS_CROWD WHERE 1=1 "+
			    				 " AND CREATE_DATE >= '"+dateId+"' AND CREATE_DATE < '"+ currentDateId+"'"+
			    				 " AND CROWD_PACK_ID  = "+id+
			    				 " GROUP BY CREATE_DATE,CROWD_PACK_ID";
			 	    	statLogger.info("执行统计【机器人】需服务用户数统计，sql="+statSql);
			 	    	query.execute(statSql);
	    			}
	    		 }
	    	 }else {
	 	    	statLogger.info("【机器人】未配置人群包，dateId="+dateId);
	    	 }
	    	 
	    	 statLogger.info("开始执行【企微】需服务用户数统计，dateId="+dateId);
	    	 if(StringUtils.notBlank(webcahtCrowPackIds)) {
	    		 String[] ids = webcahtCrowPackIds.split(",");
	    		 if(ids!=null&&ids.length>0) {
	    			List<String> key = new ArrayList<String>();
	    			for (int i = 0; i < ids.length; i++) {
	    				String id = ids[i];
	    				boolean contains = key.contains(id);
	    				if(contains) {
	    					continue;
	    				}
			    		String statSql = "insert into c_no_as_service_summary(DATE_ID,CHANNEL_TYPE,CROWD_PACK_ID,NEED_SERVICE_NUM,REAL_SERVIE_NUM)"+
			    				 "SELECT CREATE_DATE DATE_ID,'8' CHANNEL_TYPE ,CROWD_PACK_ID,COUNT(1) NEED_SERVICE_NUM,'0' REAL_SERVIE_NUM FROM C_NO_AS_CROWD WHERE 1=1 "+
			    				 " AND CREATE_DATE >= '"+dateId+"' AND CREATE_DATE < '"+ currentDateId+"'"+
			    				 " AND CROWD_PACK_ID  = "+id+
			    				 " GROUP BY CREATE_DATE,CROWD_PACK_ID";
				 	    statLogger.info("执行统计【企微】需服务用户数统计，sql="+statSql);
			    		query.execute(statSql);
	    			}
	    		 }
	    	 }else {
	    		 statLogger.info("【企微】未配置人群包，dateId="+dateId);
	    	 }
	    	 
	    	 
	    	 statLogger.info("开始执行【主动外呼】需服务用户数统计，dateId="+dateId);
	    	 if(StringUtils.notBlank(boundCrowPackIds)) {
	    		 String[] ids = boundCrowPackIds.split(",");
	    		 if(ids!=null&&ids.length>0) {
	    			List<String> key = new ArrayList<String>();
	    			for (int i = 0; i < ids.length; i++) {
	    				String id = ids[i];
	    				boolean contains = key.contains(id);
	    				if(contains) {
	    					continue;
	    				}
			    		String statSql = "insert into c_no_as_service_summary(DATE_ID,CHANNEL_TYPE,CROWD_PACK_ID,NEED_SERVICE_NUM,REAL_SERVIE_NUM)"+
			    				 "SELECT CREATE_DATE DATE_ID,'5' CHANNEL_TYPE ,CROWD_PACK_ID,COUNT(1) NEED_SERVICE_NUM,'0' REAL_SERVIE_NUM FROM C_NO_AS_CROWD WHERE 1=1 "+
			    				 " AND CREATE_DATE >= '"+dateId+"' AND CREATE_DATE < '"+ currentDateId+"'"+
			    				 " AND CROWD_PACK_ID = "+id+
			    				 " GROUP BY CREATE_DATE,CROWD_PACK_ID";
				 	    statLogger.info("执行统计【主动外呼】需服务用户数统计，sql="+statSql);
			 	    	query.execute(statSql);
	    			}
	    		 }
	    	 }else {
	 	    	statLogger.info("【主动外呼】未配置人群包，dateId="+dateId);
	    	 }
	    	 statLogger.info("结束执行【主动外呼】需服务用户数统计，dateId="+dateId);
	    	 
	    	 statLogger.info("开始执行【主动营销】需服务用户数统计，dateId="+dateId);
	    	 if(StringUtils.notBlank(marketCrowPackIds)) {
	    		 String[] ids = marketCrowPackIds.split(",");
	    		 if(ids!=null&&ids.length>0) {
	    			List<String> key = new ArrayList<String>();
	    			for (int i = 0; i < ids.length; i++) {
	    				String id = ids[i];
	    				boolean contains = key.contains(id);
	    				if(contains) {
	    					continue;
	    				}
			    		String statSql = "insert into c_no_as_service_summary(DATE_ID,CHANNEL_TYPE,CROWD_PACK_ID,NEED_SERVICE_NUM,REAL_SERVIE_NUM)"+
			    				 "SELECT CREATE_DATE DATE_ID,'6' CHANNEL_TYPE ,CROWD_PACK_ID,COUNT(1) NEED_SERVICE_NUM,'0' REAL_SERVIE_NUM FROM C_NO_AS_CROWD WHERE 1=1 "+
			    				 " AND CREATE_DATE >= '"+dateId+"' AND CREATE_DATE < '"+ currentDateId+"'"+
			    				 " AND CROWD_PACK_ID = "+id+
			    				 " GROUP BY CREATE_DATE,CROWD_PACK_ID";
				 	    statLogger.info("执行统计【主动营销】需服务用户数统计，sql="+statSql);
			 	    	query.execute(statSql);
	    			}
	    		 }
	    	 }else {
	 	    	statLogger.info("【主动营销】未配置人群包，dateId="+dateId);
	    	 }
	    	 statLogger.info("结束执行【主动营销】需服务用户数统计，dateId="+dateId);
	    	 
	    	 statLogger.info("开始执行【主动营销】【主动外呼】【H5】【机器人】【企微】已服务用户数统计，dateId="+dateId);
	    	 //按照同步时间保存
	    	 String sql1 = " insert into c_no_as_service_summary(DATE_ID,CHANNEL_TYPE,CROWD_PACK_ID,NEED_SERVICE_NUM,REAL_SERVIE_NUM) "+
	    	 "SELECT CREATE_DATE DATE_ID, RESULT_SOURCE, CROWD_PACK_ID,'0' NEED_SERVICE_NUM,COUNT(1) REAL_SERVIE_NUM FROM C_NO_AS_CROWD WHERE 1=1 "+
			 " AND SERVICE_DATE >= '"+dateId+"' AND SERVICE_DATE < '"+currentDateId+"'"+
			 " AND (RESULT_SOURCE = "+Constants.CHANNEL_MARKET+" OR RESULT_SOURCE ="+Constants.CHANNEL_OUTBOUND+" OR RESULT_SOURCE ="+Constants.CHANNEL_ROBOT+" OR RESULT_SOURCE ="+Constants.CHANNEL_WECHAT+" OR RESULT_SOURCE = "+Constants.CHANNEL_ONLINE_CUST+")"+
			 " AND SERVICE_STATUS = "+ Constants.HAS_REVISITED+
			 " GROUP BY CREATE_DATE,CROWD_PACK_ID,RESULT_SOURCE";
		 	 statLogger.info("执行统计【主动营销】【主动外呼】【H5】【机器人】【企微】已服务用户数统计，sql="+sql1);
    		 query.execute(sql1);
    		 
	    	 statLogger.info("结束执行【主动营销】【主动外呼】【H5】需服务用户数统计，dateId="+dateId);

	     	 statLogger.info("结束统计主动服务埋点数");

		} catch (SQLException e) {
			statLogger.error(e.getMessage(),e);
		}
    	
		return null;
	}

	/**
	*@title AI自动外呼数据同步
	*<AUTHOR>
	*@date 2023/4/20
	*@version 1.0
	*/
	public JSONObject aiTriggerGroupUserSync(JSONObject json) {
		triggerLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">AI自动外呼数据同步>参数>"+json.toJSONString());
		JSONArray dataArr = json.getJSONArray("data");
		JSONObject result = new JSONObject();
		if(dataArr!=null) {
			EasyQuery query = getQuery();
			String currentDateString = EasyDate.getCurrentDateString();
			Timestamp currentTimestamp = new Timestamp(System.currentTimeMillis());
			int addCount = 0;
			int failCount = 0;
			for (int i = 0; i < dataArr.size(); i++) {
				JSONObject data = dataArr.getJSONObject(i);
				String sysId = data.getString("sysId");
				String groupId = data.getString("groupId");
				if(StringUtils.isNotBlank(groupId)){
					groupId = groupId.trim();
				}
				JSONArray userList = data.getJSONArray("list");
				String crowdPackId = sysId+groupId;
				if(userList!=null) {
					for (int j = 0; j < userList.size(); j++) {
						JSONObject userObj = userList.getJSONObject(j);
						String custName = userObj.getString("custName");
						String custPhone = userObj.getString("custPhone");
						String orgCode = userObj.getString("orgCode");
						String brandCode = userObj.getString("brandCode");
						String brandName = userObj.getString("brandName");
						String prodCode = userObj.getString("prodCode");
						String prodName = userObj.getString("prodName");
						String productModel = userObj.getString("productModel");
						String faultCode = userObj.getString("faultCode");
						String faultLevel = userObj.getString("faultLevel");
						String notes = userObj.getString("notes");
						if(StringUtils.isNotBlank(notes)){
							notes = notes.substring(0,20);
						}
						EasyRecord record = new EasyRecord("C_NO_AS_ROBOT_AUTO_CALL","ID");
						record.set("ID", RandomKit.randomStr());
						record.set("CUST_PHONE", custPhone);
						record.set("CUST_NAME", custName);
						record.set("ORG_CODE", orgCode);
						record.set("BRAND_NAME", brandName);
						record.set("BRAND_CODE", brandCode);
						record.set("PROD_NAME", prodName);
						record.set("PROD_CODE", prodCode);
						record.set("PRODUCT_MODEL", productModel);
						record.set("FAULT_CODE", faultCode);
						record.set("FAULT_LEVEL", faultLevel);
						record.set("SERVICE_STATUS", "2");//初始状态  --自动发布
						record.set("CREATE_TIME", currentDateString);
						record.set("PUBLISH_TIME", currentDateString);
						record.set("CREATE_DATE", currentTimestamp);
						record.set("CROWD_PACK_ID", crowdPackId);
						record.set("DATA_SOURCE", sysId);//触发器来源
						record.set("NOTES", notes);//简要描述
						boolean saveResult = false;
						try {
							saveResult = query.save(record);
						} catch (SQLException e) {
							failCount++;
							triggerLogger.error("保存人群明细失败异常，userObj="+userObj + e.getMessage(),e);
						}
						if(saveResult) {
							++addCount;
						}
					}
					triggerLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">AI自动外呼数据同步>同步完成，成功addCount="+addCount+",失败failCount="+failCount);
					result.put("respCode", GWConstants.RET_CODE_SUCCESS);
					result.put("respDesc", "查询成功");
					//同步次数太高，防止同步时出错，同步时间放到缓存，同步后再定时更新同步时间和人群包用户数
					TriggerUserSyncService.getInstance().setTriggerSyncTime(crowdPackId, currentDateString,"2");
				}else {
					triggerLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">AI自动外呼数据同步>同步失败，人群用户为空，userList="+userList);
					result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
					result.put("respDesc", "查询失败");
				}
			}
		}else {
			triggerLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">AI自动外呼数据同步>同步失败，人群用户为空，dataArr="+dataArr);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "查询失败");
		}
		return result;
	}
	/**
	 * 自动外呼接收
	 * @param json
	 * @param returnType
	 * @param d 号码是否加密
	 * @return
	 */
	public JSONObject outTriggerGroupUserSync(JSONObject json,String returnType,boolean d,boolean repeatFlag) {
		outTrigger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">外部数据自动外呼数据同步>参数>"+json.toJSONString());
		JSONArray dataArr = json.getJSONArray("data");
		String strId=RandomKit.randomStr();
		JSONObject result = new JSONObject();
		if(dataArr!=null) {
			EasyQuery query = getQuery();
			String currentDateString = EasyDate.getCurrentDateString();
			String opId = RandomKit.randomStr();
			Timestamp currentTimestamp = new Timestamp(System.currentTimeMillis());
			int addCount = 0;
			int failCount = 0;
			for (int i = 0; i < dataArr.size(); i++) {
				JSONObject data = dataArr.getJSONObject(i);
				String sysId = data.getString("sysId");
				CommLogger.getCommLogger("outTrigger_"+sysId).info(">>"+strId+">外部数据自动外呼数据同步>参数>"+data.toJSONString());
				String groupId = data.getString("groupId");
				if(StringUtils.isNotBlank(groupId)){
					groupId = groupId.trim();
				}
				JSONArray userList = data.getJSONArray("list");
				String crowdPackId = sysId+groupId;
				if(userList!=null) {
					Map<String, String> decryptPhoneMap =new HashMap<String, String>();
					if(d){
						JSONArray decrypts = new JSONArray();
						List<JSONArray> decryptPhoneGroup = new ArrayList<JSONArray>();
						for (int  j= 0; j < userList.size(); j++) {
							JSONObject userObj =userList.getJSONObject(j);
							String custPhone = userObj.getString("custPhone");
							JSONObject param = new JSONObject();
							param.put("action", "Encrypt");
							param.put("ciphertextBlob", custPhone);
							param.put("dataType", "mobile");
							decrypts.add(param);
							if(j==userList.size()-1||(j+1)%10000==0) {//分组解密，最多一次解密10000条
								decryptPhoneGroup.add(decrypts);
								decrypts =  new JSONArray();
							}
						}
						decryptPhoneMap = decryptPhone(decryptPhoneGroup);
					}
					
					for (int j = 0; j < userList.size(); j++) {
						JSONObject userObj =new JSONObject();
						try {
							userObj = userList.getJSONObject(j);
							String outId = userObj.getString("serviceOrderCode");//外部数据唯一标识
							String custName = userObj.getString("custName");
							String custPhone = userObj.getString("custPhone");
							if(d){
								custPhone=decryptPhoneMap.get(userObj.getString("custPhone"));
							}
							custPhone = StringUtils.trim(custPhone);
							String orgCode = userObj.getString("orgCode");
							String brandCode = userObj.getString("brandCode");
							String brandName = userObj.getString("brandName");
							String prodCode = userObj.getString("prodCode");
							String prodName = userObj.getString("prodName");
							String productModel = userObj.getString("productModel");
							String faultCode = userObj.getString("faultCode");
							String faultLevel = userObj.getString("faultLevel");
							String notes = userObj.getString("notes");
							if(StringUtils.isNotBlank(notes)){
								notes = notes.substring(0,20);
							}
	//						String serviceOrderCode = userObj.getString("serviceOrderCode");
	//						String serviceOrderType = userObj.getString("serviceOrderType");
							EasyRecord record = new EasyRecord("C_NO_AS_ROBOT_AUTO_CALL_TEMPOR","ID");
							
							
							record.set("ID", RandomKit.randomStr());
							record.set("CUST_PHONE", custPhone);
							record.set("CUST_NAME", custName);
							record.set("ORG_CODE", orgCode);
							record.set("BRAND_NAME", brandName);
							record.set("BRAND_CODE", brandCode);
							record.set("PROD_NAME", prodName);
							record.set("PROD_CODE", prodCode);
							record.set("PRODUCT_MODEL", productModel);
							record.set("FAULT_CODE", faultCode);
							record.set("FAULT_LEVEL", faultLevel);
							record.set("SERVICE_STATUS", "2");//初始状态  --自动发布
							record.set("CREATE_TIME", currentDateString);
							record.set("PUBLISH_TIME", currentDateString);
							record.set("CREATE_DATE", currentTimestamp);
							record.set("CROWD_PACK_ID", crowdPackId);
							record.set("DATA_SOURCE", sysId);//触发器来源
							record.set("NOTES", notes);//简要描述
	//						record.set("SERVICE_ORDER_CODE", serviceOrderCode);//
	//						record.set("SERVICE_ORDER_TYPE", serviceOrderType);//
							if(StringUtils.isBlank(returnType)){
								returnType=userObj.getString("returnType");
							}
							record.set("RETURN_TYPE", returnType);//
							record.set("OUT_ID", outId);//外部唯一标识
							record.set("OPERATE_ID", opId);//操作id
							record.set("EXPAND_DATA", userObj.getString("expandData"));//拓展字段
							boolean saveResult = false;
							saveResult = query.save(record);
							if(saveResult) {
								++addCount;
							}
						} catch (Exception e) {
							failCount++;
							outTrigger.error("保存人群明细失败异常，userObj="+userObj + e.getMessage(),e);
						}
					}
					outTrigger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">AI自动外呼数据同步>同步完成，成功  addCount="+addCount+",失败failCount="+failCount);
					result.put("respCode", GWConstants.RET_CODE_SUCCESS);
					result.put("respDesc", "操作成功");
					//同步次数太高，防止同步时出错，同步时间放到缓存，同步后再定时更新同步时间和人群包用户数
					TriggerUserSyncService.getInstance().setTriggerSyncTime(crowdPackId, currentDateString,"2");
				}else {
					outTrigger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">AI自动外呼数据同步>同步失败，人群用户为空，userList="+userList);
					result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
					result.put("respDesc", "操作失败");
				}
			}
			outTrigger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">>"+strId+">AI自动外呼数据同步结束循环");

			try {
				EasyCalendar calendar = EasyCalendar.newInstance();
				calendar.add(EasyCalendar.DAY, -3);
				String dateToString = EasyDate.dateToString( calendar.getCalendar().getTime(), "yyyy-MM-dd HH:mm:ss");
				EasySQL sql=new EasySQL();
//				sql.append("INSERT INTO C_NO_AS_ROBOT_AUTO_CALL ");
//				sql.append("(ID,CUST_PHONE,CUST_NAME,ORG_CODE,BRAND_NAME,BRAND_CODE,PROD_NAME,PROD_CODE,PRODUCT_MODEL,FAULT_CODE,FAULT_LEVEL, ");
//				sql.append(" SERVICE_STATUS,CREATE_TIME,PUBLISH_TIME,CREATE_DATE,CROWD_PACK_ID,DATA_SOURCE,NOTES,RETURN_TYPE,OUT_ID,OPERATE_ID,EXPAND_DATA) ");
//				sql.append("(  ");
				sql.append("SELECT  ");
				sql.append(" ID,CUST_PHONE,CUST_NAME,ORG_CODE,BRAND_NAME,BRAND_CODE,PROD_NAME,PROD_CODE,PRODUCT_MODEL,FAULT_CODE,FAULT_LEVEL,SERVICE_STATUS,CREATE_TIME,PUBLISH_TIME,CREATE_DATE,CROWD_PACK_ID,DATA_SOURCE,NOTES,RETURN_TYPE,OUT_ID,OPERATE_ID,EXPAND_DATA ");
				sql.append(" FROM C_NO_AS_ROBOT_AUTO_CALL_TEMPOR ");
				sql.append(opId,"where OPERATE_ID=? ");
				if(!repeatFlag){
					sql.append("and ( OUT_ID not in (select out_Id from C_NO_AS_ROBOT_AUTO_CALL  ");
					sql.append(dateToString," where CREATE_TIME >?  AND OUT_ID  IS  NOT NULL   ");
					sql.append(")  OR OUT_ID IS NULL )");
				}
				List<JSONObject> queryForList = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
				for(JSONObject callInfo:queryForList){
					try {
						EasyRecord record = new EasyRecord("C_NO_AS_ROBOT_AUTO_CALL","ID");
						record.set("ID", callInfo.getString("ID"));
						record.set("CUST_PHONE", callInfo.getString("CUST_PHONE"));
						record.set("CUST_NAME", callInfo.getString("CUST_NAME"));
						record.set("ORG_CODE", callInfo.getString("ORG_CODE"));
						record.set("BRAND_NAME", callInfo.getString("BRAND_NAME"));
						record.set("BRAND_CODE", callInfo.getString("BRAND_CODE"));
						record.set("PROD_NAME", callInfo.getString("PROD_NAME"));
						record.set("PROD_CODE", callInfo.getString("PROD_CODE"));
						record.set("PRODUCT_MODEL", callInfo.getString("PRODUCT_MODEL"));
						record.set("FAULT_CODE", callInfo.getString("FAULT_CODE"));
						record.set("FAULT_LEVEL", callInfo.getString("FAULT_LEVEL"));
						record.set("SERVICE_STATUS", callInfo.getString("SERVICE_STATUS"));
						record.set("CREATE_TIME", callInfo.getString("CREATE_TIME"));
						record.set("PUBLISH_TIME", callInfo.getString("PUBLISH_TIME"));
//						record.set("CREATE_DATE", callInfo.getString("CREATE_DATE"));//格式不同不适合string
//						Timestamp currentTimestamp = new Timestamp(System.currentTimeMillis());
						record.set("CREATE_DATE", currentTimestamp);
						record.set("CROWD_PACK_ID", callInfo.getString("CROWD_PACK_ID"));
						record.set("DATA_SOURCE", callInfo.getString("DATA_SOURCE"));
						record.set("NOTES", callInfo.getString("NOTES"));
						record.set("RETURN_TYPE", callInfo.getString("RETURN_TYPE"));
						record.set("OUT_ID", callInfo.getString("OUT_ID"));
						record.set("OPERATE_ID", callInfo.getString("OPERATE_ID"));
						record.set("EXPAND_DATA", callInfo.getString("EXPAND_DATA"));
						
						JSONObject json1=new JSONObject();
						json1.put("ID", callInfo.getString("ID"));
						EasyRecord delrecord = new EasyRecord("C_NO_AS_ROBOT_AUTO_CALL_TEMPOR", "ID").setColumns(json1);
						query.deleteById(delrecord);
						query.save(record);
					} catch (Exception e) {
						outTrigger.error(CommonUtil.getClassNameAndMethod(ActiveService.class)+">AI自动外呼数据同步>同步失败，"+e.getMessage(),e);
					}
				}
//				sql.append(") ");
				outTrigger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">AI自动外呼数据同步主表>，"+sql.getSQL());
				query.execute(sql.getSQL(), sql.getParams());
				JSONObject json1=new JSONObject();
				json1.put("OPERATE_ID", opId);
				 EasyRecord delrecord = new EasyRecord("C_NO_AS_ROBOT_AUTO_CALL_TEMPOR", "OPERATE_ID").setColumns(json1);
			//	 query.deleteById(delrecord);
			} catch (SQLException e) {
				outTrigger.error(CommonUtil.getClassNameAndMethod(ActiveService.class)+">AI自动外呼数据同步>同步失败，"+e.getMessage(),e);
				e.printStackTrace();
			}	
			
		}else {
			triggerLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">AI自动外呼数据同步>同步失败，人群用户为空，dataArr="+dataArr);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "查询失败");
		}
		outTrigger.error(CommonUtil.getClassNameAndMethod(ActiveService.class)+">>"+strId+">AI自动外呼数据同步>同步结果，"+result);
		return result;
	}

	/**
     * 同步触发器人群明细
     * 同步系统：CSS等系统
     * @param json
     * @return
     */
	public JSONObject triggerGroupUserSync(JSONObject json) {
		triggerLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">触发器用户同步>参数>"+json.toJSONString());
		JSONArray dataArr = json.getJSONArray("data");
		JSONObject result = new JSONObject();
		if(dataArr!=null) {
			EasyQuery query = getQuery();
			String currentDateString = EasyDate.getCurrentDateString();
			String currentDateString2 = EasyDate.getCurrentDateString("yyyyMMdd");
			int addCount = 0;
			int failCount = 0;
			for (int i = 0; i < dataArr.size(); i++) {
				JSONObject data = dataArr.getJSONObject(i);
				String sysId = data.getString("sysId");
				String groupId = data.getString("groupId");
				if(StringUtils.isNotBlank(groupId)){
					groupId = groupId.trim();
				}
				JSONArray userList = data.getJSONArray("list");
				String crowdPackId = sysId+groupId;
				if(userList!=null) {
					for (int j = 0; j < userList.size(); j++) {
						JSONObject userObj = userList.getJSONObject(j);
						String custName = userObj.getString("custName");
						String custPhone = userObj.getString("custPhone");
						String orgCode = userObj.getString("orgCode");
						String brandCode = userObj.getString("brandCode");
						String brandName = userObj.getString("brandName");
						String prodCode = userObj.getString("prodCode");
						String prodName = userObj.getString("prodName");
						String productModel = userObj.getString("productModel");
						String faultCode = userObj.getString("faultCode");
						String faultLevel = userObj.getString("faultLevel");
						String serviceOrderCode = userObj.getString("serviceOrderCode");
						String serviceOrderType = userObj.getString("serviceOrderType");
						String notes = userObj.getString("notes");
						if(StringUtils.isNotBlank(notes)){
							notes = notes.substring(0,20);
						}
						EasyRecord record = new EasyRecord("C_NO_AS_CROWD","ID");
						record.set("ID", RandomKit.randomStr());
						record.set("CUST_PHONE", custPhone);
						record.set("CUST_NAME", custName);
						record.set("ORG_CODE", orgCode);
						record.set("BRAND_NAME", brandName);
						record.set("BRAND_CODE", brandCode);
						record.set("PROD_NAME", prodName);
						record.set("PROD_CODE", prodCode);
						record.set("PRODUCT_MODEL", productModel);
						record.set("FAULT_CODE", faultCode);
						record.set("FAULT_LEVEL", faultLevel);
						record.set("SERVICE_STATUS", "1");//初始状态  --待发布/待服务
						record.set("CREATE_TIME", currentDateString);
						record.set("CREATE_DATE", currentDateString2);
						record.set("CROWD_PACK_ID", crowdPackId);
						record.set("IS_DISTURB", "0");//默认值
						record.set("CUST_READ", "0");//默认值
						record.set("DATA_SOURCE", sysId);//触发器来源
						record.set("NOTES", notes);//简要描述
						record.set("SERVICE_ORDER_CODE", serviceOrderCode);//单据编码
						record.set("SERVICE_ORDER_TYPE", serviceOrderType);//单据类型
						boolean saveResult = false;
						try {
							saveResult = query.save(record);
						} catch (SQLException e) {
							failCount++;
							triggerLogger.error("保存人群明细失败异常，userObj="+userObj + e.getMessage(),e);
						}
						if(saveResult) {
							++addCount;
						}
					}
					triggerLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">触发器用户同步>同步完成，成功  addCount="+addCount+",失败failCount="+failCount);
					result.put("respCode", GWConstants.RET_CODE_SUCCESS);
					result.put("respDesc", "查询成功");
					//同步次数太高，防止同步时出错，同步时间放到缓存，同步后再定时更新同步时间和人群包用户数
					TriggerUserSyncService.getInstance().setTriggerSyncTime(crowdPackId, currentDateString);
				}else {
					triggerLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">触发器用户同步>同步失败，人群用户为空，userList="+userList);
					result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
					result.put("respDesc", "查询失败");
				}
			}
		}else {
			triggerLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">触发器用户同步>同步失败，人群用户为空，dataArr="+dataArr);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "查询失败");
		}
		return result;
	}

//	/**
//	 * 热线、在线坐席接入、工单回访页提交主动服务结果
//	 * @param param
//	 * @return
//	 */
//	public JSONObject submitServiceResult(JSONObject param) {
//		handleLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">submitServiceResult>参数>"+param.toJSONString());
//		JSONArray crowdIds = param.getJSONArray("crowdIds");
//		JSONArray crowdPackId = param.getJSONArray("crowdPackId");
//		String serviceResult = param.getString("serviceResult");
//		String serviceResultName = param.getString("serviceResultName");
//		String channelType = param.getString("channelType");
//		String agentAcc = param.getString("agentAcc");
//		String deptCode = param.getString("deptCode");
//		String deptName = param.getString("deptName");
//		String areaCode = param.getString("areaCode");
//		String agentName = param.getString("agentName");
//		JSONObject result = new JSONObject();
//		if(StringUtils.isBlank(serviceResult)||crowdIds==null) {
//			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
//			result.put("respDesc", "参数异常");
//			handleLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">submitServiceResult>参数异常>"+param.toJSONString());
//			return result;
//		}
//		EasyQuery query = getQuery();
//		String serviceTime = EasyDate.getCurrentDateString();
//		try {
//			for(int i=0;i<crowdIds.size();i++) {
//				String crowdId = (String)crowdIds.get(i);
//				EasyRecord record = new EasyRecord("C_NO_AS_CROWD", "ID");
//				record.set("ID", crowdId);
//				record.set("SERVICE_STATUS", Constants.HAS_REVISITED);
//				record.set("RESULT_CONTENT", serviceResultName);
//				record.set("RESULT_TIME", serviceTime);//策略渠道
//				record.set("RESULT", serviceResult);
//				record.set("AGENT_ACC", agentAcc);
//				record.set("AGENT_NAME", agentName);
//				record.set("AGENT_DEPT_CODE", deptCode);
//				record.set("AGENT_DEPT_NAME", deptName);
//				record.set("AGENT_AREA_CODE", areaCode);
////				resultRecord.set("AGENT_AREA_NAME", );
//				record.set("RESULT_SOURCE", channelType);	
//				query.update(record);
//			}
//			
//			//埋点数据--只使用热线、在线
//			if(Constants.CHANNEL_HOTLINE.equals(channelType)&&Constants.CHANNEL_ONLINE.equals(channelType)) {
//				//埋点数据
//				EasyRecord summaryRecord = new EasyRecord("C_NO_AS_SERVICE_RECORD", "ID");
//				summaryRecord.set("ID", RandomKit.randomStr());
//				summaryRecord.set("CROWD_PACK_ID", crowdPackId);//人群包组id
//				summaryRecord.set("CHANNEL_TYPE", channelType);//策略渠道
//				summaryRecord.set("DATA_TYPE", Constants.SUMMARY_DATA_TYPE_SUBMIT);//埋点类型--1：查询  2；提交结果
//				summaryRecord.set("DATE_ID", EasyDate.getCurrentDateString("yyyyMMdd"));//日期
//				summaryRecord.put("CREATE_TIME", EasyDate.getCurrentDateString());//时间
////				summaryRecord.put("CUST_PHONE", custPhone);
//
//				query.save(summaryRecord);
//				
//			}
//			
//			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
//			result.put("respDesc", "操作成功");
//		} catch (Exception e) {
//			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
//			result.put("respDesc", "保存失败");
//			handleLogger.error(CommonUtil.getClassNameAndMethod(ActiveService.class)+">submitServiceResult>保存失败>"+JSON.toJSONString(param)+"原因："+e.getMessage());
//		}
//		return result;
//	}
//	
//	/**
//	 * 热线、在线坐席接入、工单回访页查询人群包接口
//	 * @param param
//	 * @return
//	 */
//	public JSONObject getCrowdPackListByCustPhone(JSONObject param) {
//		handleLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">getCrowdPackListByCustPhone>参数>"+param.toJSONString());
//		EasyQuery query = getQuery();
//		JSONObject result = new JSONObject();
//		String channelType = param.getString("channelType");
//		String strategyTable = Constants.strategyTable.get(channelType);
//		String custPhone = param.getString("customerPhone");
//		List<JSONObject> crowdPackList = new ArrayList<JSONObject>();
//		try {
//			if(StringUtils.isNotBlank(custPhone)) {
//			
//				//查询用户免打扰用户群
//				
//				//最近30天内热线或者在线已服务不查询
//				SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//				Date date=new Date();
//				Calendar calendar = Calendar.getInstance();
//				calendar.setTime(date);
//				calendar.add(Calendar.DATE, -30);
//				date = calendar.getTime();
//				String before30Day = dateFormat.format(date);
//				EasySQL sql1 = new EasySQL("select COUNT(1) from C_NO_AS_CROWD WHERE ");
//				sql1.append(custPhone," CUST_PHONE=? ",false);//用户号码
//				sql1.append(Constants.CHANNEL_HOTLINE," and (RESULT_SOURCE = ? ");//热线渠道
//				sql1.append(Constants.CHANNEL_ONLINE," or RESULT_SOURCE = ? )");//在线渠道
//				sql1.append(before30Day," and RESULT_TIME > ? ");//最近30天
//				boolean hasResult  = query.queryForExist(sql1.getSQL(), sql1.getParams());
//				if(hasResult) {
//					result.put("respCode", GWConstants.RET_CODE_SUCCESS);
//					result.put("respDesc", "查询成功");
//					return result;
//				}
//				
//				//查询人群明细
//				EasySQL sql = new EasySQL("select ID,CROWD_PACK_ID,CUST_PHONE,CUST_NAME,BRAND_NAME,PROD_NAME from C_NO_AS_CROWD WHERE ");
//				sql.append(custPhone," CUST_PHONE=? ",false);//来电号码
//				sql.append(Constants.NO_PUBLISH," and SERVICE_STATUS = ? ");//初始待服务状态
//				sql.append("0"," and IS_DISTURB = ? ");//可打扰用户
//				List<EasyRow> list = query.queryForList(sql.getSQL(), sql.getParams());
//				if(list!=null&&list.size()<=0) {
//					result.put("respCode", GWConstants.RET_CODE_SUCCESS);
//					result.put("respDesc", "查询成功");
//					return result;
//				}
//				HashMap<String, JSONArray> map = new HashMap<String,JSONArray>();
//				//查询人群包信息和策略信息-如果同一个人群包存在组策略和单个人群包策略，则优先查询人群包策略。
//				EasySQL crowdPackSql = new EasySQL("select t6.CROWD_PACK_ID,t6.CROWD_PACK_NAME,t6.STRATEGY_ID,t6.PRIORITY,t6.SMS_CONTENT,t6.SMS_OPEN,t6.SCRIPT_CONTENT,t6.SCRIPT_OPEN from ( ");
//				crowdPackSql.append(" SELECT t1.CROWD_PACK_ID CROWD_PACK_ID, t2.NAME CROWD_PACK_NAME,t1.STRATEGY_ID,t1.PRIORITY,t3.SMS_CONTENT,t3.SMS_OPEN,t3.SCRIPT_CONTENT,t3.SCRIPT_OPEN,t1.STRATEGY_LEVEL   ");
//				crowdPackSql.append(" FROM C_NO_AS_CROWDPACK_PRIORITY t1 LEFT JOIN C_NO_AS_CROWD_PACK t2 ON t1.CROWD_PACK_ID = t2.ID ");
//				crowdPackSql.append(channelType," LEFT JOIN "+strategyTable+" t3 ON t1.STRATEGY_ID = t3.ID WHERE t1.CHANNEL_TYPE = ? AND t3.IS_OPEN = '1' ) t6 ");
//				crowdPackSql.append(" JOIN ( ");
//				crowdPackSql.append("SELECT t4.CROWD_PACK_ID,MIN(t4.STRATEGY_LEVEL) STRATEGY_LEVEL FROM C_NO_AS_CROWDPACK_PRIORITY t4 LEFT JOIN "+strategyTable+" t5 ON t4.STRATEGY_ID = t5.ID WHERE");
//				crowdPackSql.append(channelType," t4.CHANNEL_TYPE = ? AND t5.IS_OPEN = '1'  ");
//				crowdPackSql.append(" AND t4.CROWD_PACK_ID in ( ");
//				for(int i=0;i<list.size();i++) {
//					EasyRow row = list.get(i);
//					JSONObject jsonObject = new JSONObject();
//					String crowdPackId = row.getColumnValue("CROWD_PACK_ID");
//					String crowdId = row.getColumnValue("ID");
//					jsonObject.put("CROWD_PACK_ID", crowdPackId);
//					jsonObject.put("CROWD_ID", crowdId);
//					jsonObject.put("CUST_PHONE", row.getColumnValue("CUST_PHONE"));
//					jsonObject.put("CUST_NAME", row.getColumnValue("CUST_NAME"));
//					jsonObject.put("BRAND_NAME", row.getColumnValue("BRAND_NAME"));
//					jsonObject.put("PROD_NAME", row.getColumnValue("PROD_NAME"));
//					JSONArray recordList = map.get(crowdPackId);
//					if(recordList!=null) {
//						recordList.add(jsonObject);
//					}else {
//						recordList = new JSONArray();
//						recordList.add(jsonObject);
//					}
//					map.put(crowdPackId, recordList);
//					if(i==0) {
//						crowdPackSql.append(crowdPackId,"?");
//					}else {
//						crowdPackSql.append(crowdPackId,",?");
//					}
//				}
//				crowdPackSql.append(" ) ");
//				crowdPackSql.append(" GROUP BY t4.CROWD_PACK_ID) t7 ON t6.CROWD_PACK_ID = t7.CROWD_PACK_ID AND t6.STRATEGY_LEVEL = t7.STRATEGY_LEVEL ");
//				crowdPackSql.append("  ORDER BY t6.PRIORITY,t6.STRATEGY_ID ");
//				handleLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">getCrowdPackListByCustPhone>获取来电用户人群包,sql>"+crowdPackSql.getSQL()+"{"+JSON.toJSONString(crowdPackSql.getParams())+"}");
//				//只查询前4条
//				query.setMaxRow(4);
//				crowdPackList = query.queryForList(crowdPackSql.getSQL(), crowdPackSql.getParams(),new JSONMapperImpl());
//				for (int i = 0; i < crowdPackList.size(); i++) {
//					JSONObject record = crowdPackList.get(i);
//					String crowdPackId = record.getString("CROWD_PACK_ID");
//					String strategyId = record.getString("STRATEGY_ID");
//					JSONArray recordList = map.get(crowdPackId);
//					record.put("custInfo",recordList);
//					if(Constants.CHANNEL_HOTLINE.equals(channelType)&&Constants.CHANNEL_ONLINE.equals(channelType)) {
//						//埋点数据-只使用热线、在线
//						EasyRecord summaryRecord = new EasyRecord("C_NO_AS_SERVICE_RECORD", "ID");
//						summaryRecord.set("ID", RandomKit.randomStr());
//						summaryRecord.set("CROWD_PACK_ID", crowdPackId);//人群包组id
//						summaryRecord.set("CHANNEL_TYPE", channelType);//策略渠道
//						summaryRecord.set("DATA_TYPE", Constants.SUMMARY_DATA_TYPE_QUERY);//埋点类型--1：查询  2；提交结果
//						summaryRecord.set("DATE_ID", EasyDate.getCurrentDateString("yyyyMMdd"));//日期
//						summaryRecord.put("CREATE_TIME", EasyDate.getCurrentDateString());
//						summaryRecord.put("CUST_PHONE", custPhone);
//						query.save(summaryRecord);
//					}
//					
//				}
//				
//				
//				
//				
//			}
//			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
//			result.put("respDesc", "查询成功");
//			result.put("respData", crowdPackList);
//		} catch (Exception e) {
//			handleLogger.error(CommonUtil.getClassNameAndMethod(ActiveService.class)+">getCrowdPackListByCustPhone>获取人群包请求失败,请求参数>"+JSON.toJSONString(param)+"原因："+e.getMessage());
//			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
//			result.put("respDesc", "查询失败");
//		}
//
//		return result;
//	
//	}
//	
//	/**
//	 * 获取策略配置的结果反馈列表
//	 * @param param
//	 * @return
//	 */
//	public JSONObject getStrategyEnableResultList(JSONObject param){
//		handleLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">getStrategyEnableResultList>查询参数>"+param.toJSONString());
//		Map<String, String> dict = new LinkedHashMap<>();
//		JSONObject result = new JSONObject();
//		JSONObject data = new JSONObject();
//		EasySQL sql = new EasySQL("select ID,CONTENT ");
//		sql.append(" from  C_NO_AS_STRATEGY_RESULT ");
//		sql.append(" where 1=1 ");
//		sql.append(param.getString("strategyId"), " and STRATEGY_ID = ? ",false);
//		sql.append(param.getString("channelType"), " and CHANNEL_TYPE = ? ",false);
//		sql.append( " and IS_OPEN = '1' ");
//		sql.append(" ORDER BY SORT ASC");
//		List<EasyRow> list;
//		try {
//			list = getQuery().queryForList(sql.getSQL(), sql.getParams());
//			for (EasyRow row : list) {
//				dict.put(row.getColumnValue(1), row.getColumnValue(2));
//			}
//			data.put("total", Integer.valueOf(dict.size()));
//			data.put("data", dict);
//			
//			handleLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">getStrategyEnableResultList>查询结果>"+dict.toString());
//			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
//			result.put("respDesc", "查询成功");
//			result.put("respData", data);
//		} catch (Exception e) {
//			handleLogger.error(CommonUtil.getClassNameAndMethod(ActiveService.class)+">getStrategyEnableResultList>获取人群包服务结果列表失败,请求参数>"+JSON.toJSONString(param)+"原因："+e.getMessage());
//			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
//			result.put("respDesc", "查询失败");
//		}
//		return result;
//	}
	
	/**
	 * 在线H5客户待办页获取人群包列表
	 * @param json
	 * @return
	 */
	public JSONObject getH5CustPhoneCrowdPackList(JSONObject json) {
		handleLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">getH5CustPhoneCrowdPackList>参数>"+json.toJSONString());
		JSONObject param = json.getJSONObject("param");
		EasyQuery query = getQuery();
		JSONObject result = new JSONObject();
		List<EasyRow> crowdPackList = null;
		List<JSONObject> resultList = new ArrayList<JSONObject>();
		try {
			String custPhone = param.getString("custPhone");
			//如果手机号码为空，则根据用户标识查询客户资料获取手机号码
//			if(StringUtils.isBlank(custPhone)) {
//				String accountType = param.getString("accountType");//账号标识 openid 代表微信号 UIN代表UID
//				String customerId = param.getString("customerId");//用户标识
//				String channelKey = param.getString("channelKey");//全媒体渠道标识
//				IService service = ServiceContext.getService(ServiceID.CUSTMGR_INTERFACE);
//				JSONObject custParam = new JSONObject();
//				custParam.put("accountType", accountType);
//				custParam.put("customerId", customerId);
//				custParam.put("phoneNum", custPhone);
//				custParam.put("channelKey", channelKey);
//				custParam.put("command", ServiceCommand.CUSTMGR_SRH_CUST);
//				
//				JSONObject custResult = service.invoke(custParam);
//				if(custResult!=null){
//					if("000".equals(custResult.get("respCode"))){//客户资料存在数据
//						@SuppressWarnings("unchecked")
//						Map<String,String> custInfo = (Map<String,String>)custResult.get("custInfo");
//						if(custInfo!=null){
//							custPhone=custInfo.get("phoneNum");
//						}
//					}
//					
//				}
//			}
			
			
			if(StringUtils.isNotBlank(custPhone)) {
				ArrayList<String> crowdPackIdsList = new ArrayList<String>();
				//查询用户免打扰用户群
//				EasySQL sql1 = new EasySQL("select COUNT(1) from C_NO_AS_NOT_DISTURB where");
//				sql1.append(custPhone,"  CUST_PHONE=? ");//用户号码
//				sql1.append(Constants.CHANNEL_ONLINE_CUST," and CHANNEL_ID = ? ");//所属渠道
//				sql1.append(" and IS_OPEN = '1' ");//生效
//				boolean hasNotDistribResult  = query.queryForExist(sql1.getSQL(), sql1.getParams());
//				if(hasNotDistribResult) {
//					handleLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">getH5CustPhoneCrowdPackList>用户免打扰群，custPhone="+custPhone);
//					//用户在对应渠道设置免打扰
//					result.put("respCode", GWConstants.RET_CODE_SUCCESS);
//					result.put("respDesc", "查询成功");
//					result.put("respData", resultList);
//					return result;
//				}
				EasySQL easySQL = new EasySQL();
				easySQL.append("SELECT CASE WHEN EXISTS(SELECT 1 FROM C_NO_AS_SENSITIVE_CROWD T2 " +
						"JOIN C_NO_AS_CROWD_PACK T4 ON T2.CROWD_PACK_ID = T4.ID WHERE 1 = 1 ");
				easySQL.append(custPhone, " AND T2.PHONE = ? ");
				easySQL.append("ZXH5KHD", " AND T4.BG_CROWD_CODE = ? ");
				easySQL.append(") THEN 'TRUE' ELSE 'FALSE' END AS IS_EXIST FROM DUAL");
				strategyLogger.info("通过查询敏感人群明细表获取策略ID列表SQL语句 : " + easySQL.getSQL() + " 参数 ： " + Arrays.toString(easySQL.getParams()));
				EasyRow easyRow = query.queryForRow(easySQL.getSQL(), easySQL.getParams());
				strategyLogger.info("通过查询敏感人群明细表获取策略ID列表返回值 : " + easyRow.toJSONObject().toJSONString());
				if (easyRow.toJSONObject().getString("IS_EXIST").equals("TRUE")) {
					handleLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">getH5CustPhoneCrowdPackList>用户免打扰群，custPhone="+custPhone);
					//用户在对应渠道设置免打扰
					result.put("respCode", GWConstants.RET_CODE_SUCCESS);
					result.put("respDesc", "查询成功");
					result.put("respData", resultList);
					return result;
				}
				
				//查询人群明细
				EasySQL sql = new EasySQL("select ID,CROWD_PACK_ID from C_NO_AS_CROWD WHERE ");
				sql.append(custPhone," CUST_PHONE=? ",false);//用户号码
				sql.append(Constants.NO_PUBLISH," and SERVICE_STATUS = ? ");//初始状态
				sql.append("0"," and IS_DISTURB = ? ");//可打扰用户
				List<EasyRow> list = query.queryForList(sql.getSQL(), sql.getParams());
				if(list!=null&&list.size()>0) {
					String currentDateString = EasyDate.getCurrentDateString();
					EasySQL crowdPackSql = new EasySQL("select t6.STRATEGY_ID,t6.SHOW_TITLE,t6.CROWD_PACK_ID from ( ");
					crowdPackSql.append(" SELECT t1.CROWD_PACK_ID ,t1.STRATEGY_ID,t1.STRATEGY_LEVEL,t1.PRIORITY,t2.SHOW_TITLE ");
					crowdPackSql.append(" FROM C_NO_AS_CROWDPACK_PRIORITY t1 ");
					crowdPackSql.append(Constants.CHANNEL_ONLINE_CUST," LEFT JOIN C_NO_AS_ONLINE_CUST_STRATEGY t2 ON t1.STRATEGY_ID = t2.ID WHERE t1.CHANNEL_TYPE = ? AND t2.IS_OPEN = '1' ) t6 ");
					crowdPackSql.append(" JOIN ( ");
					crowdPackSql.append("SELECT t3.CROWD_PACK_ID,MIN(t3.STRATEGY_LEVEL) STRATEGY_LEVEL FROM C_NO_AS_CROWDPACK_PRIORITY t3 LEFT JOIN C_NO_AS_ONLINE_CUST_STRATEGY t4 ON t3.STRATEGY_ID = t4.ID WHERE");
					crowdPackSql.append(Constants.CHANNEL_ONLINE_CUST," t3.CHANNEL_TYPE = ? AND t4.IS_OPEN = '1'  ");
					crowdPackSql.append(" AND t3.CROWD_PACK_ID in ( ");
					for(int i=0;i<list.size();i++) {
						EasyRow row = list.get(i);
						//数据量太大，不在sql去重，影响效率，在这里去重，把重复的人群包id去掉
						String crowdPackId = row.getColumnValue("CROWD_PACK_ID");
						if(crowdPackIdsList.contains(crowdPackId)) {
							continue;
						}else {
							crowdPackIdsList.add(crowdPackId);
						}
						if(i==0) {
							crowdPackSql.append(crowdPackId,"?");
						}else {
							crowdPackSql.append(crowdPackId,",?");
						}
					}
					crowdPackSql.append(" ) ");
					//策略有效期内
					crowdPackSql.append(currentDateString,"  AND  VALID_BEGIN_TIME <=?");
					crowdPackSql.append(currentDateString,"  AND  VALID_END_TIME >=?");
					crowdPackSql.append(" GROUP BY t3.CROWD_PACK_ID) t7 ON t6.CROWD_PACK_ID = t7.CROWD_PACK_ID AND t6.STRATEGY_LEVEL = t7.STRATEGY_LEVEL ");
					crowdPackSql.append("  ORDER BY t6.PRIORITY,t6.STRATEGY_ID ");
					handleLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">getH5CustPhoneCrowdPackList>获取在线H5用户人群包,sql>"+crowdPackSql.getSQL()+"{"+JSON.toJSONString(crowdPackSql.getParams())+"}");
					crowdPackList = query.queryForList(crowdPackSql.getSQL(), crowdPackSql.getParams());
				}else {
					handleLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">getH5CustPhoneCrowdPackList>用户免打扰，custPhone="+custPhone);
				}
			}
			if(crowdPackList!=null&&crowdPackList.size()>0) {
				String currentDateString = EasyDate.getCurrentDateString();
				for(int i=0;i<crowdPackList.size();i++) {
					EasyRow row = crowdPackList.get(i);
					JSONObject jsonObject = new JSONObject();
					String id = row.getColumnValue("STRATEGY_ID");
					String title = row.getColumnValue("SHOW_TITLE");
					String crowdPackId = row.getColumnValue("CROWD_PACK_ID");
					
					//策略id+用户号码作为唯一标识给到H5外网，安全起见加密下
					jsonObject.put("ID", SecurityUtil.encryptMsgBy3DesAndBase64(id+"_"+custPhone));
					jsonObject.put("TITLE", title);
					resultList.add(jsonObject);
					//记录用户已查询人群包，标记已读
					query.execute("update C_NO_AS_CROWD set CUST_READ = '1',PRE_SERVICE_DATE=? where CUST_PHONE = ? and CROWD_PACK_ID=? and SERVICE_STATUS = ? and CUST_READ='0' and IS_DISTURB = '0'", currentDateString,custPhone,crowdPackId,Constants.NO_PUBLISH);
				}
			}
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			result.put("respDesc", "查询成功");
			result.put("respData", resultList);
		} catch (Exception e) {
			handleLogger.error(CommonUtil.getClassNameAndMethod(ActiveService.class)+">getCrowdPackListByCustPhone>获取在线H5用户人群包,请求参数>"+JSON.toJSONString(param)+"原因："+e.getMessage());
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "查询失败");
		}

		return result;
	
	}
	/**
	 * 在线H5获取人群包策略配置的脚本内容
	 * @param json
	 * @return
	 */
	public JSONObject getH5CrowdPackScript(JSONObject json) {
		handleLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">getH5CrowdPackScript>参数>"+json.toJSONString());
		EasyQuery query = getQuery();
		JSONObject result = new JSONObject();
		JSONObject param = json.getJSONObject("param");
		String encryptMsg=param.getString("id");
		try {
			if(StringUtils.isBlank(encryptMsg)) {
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				result.put("respDesc", "参数异常");
				handleLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">getH5CrowdPackScript>参数异常>"+param.toJSONString());
				return result;
			}
			String decryptMsg = SecurityUtil.decryptMsgBy3DesAndBase64(encryptMsg);
			String[] split = decryptMsg.split("_");
			String id = split[0];
			String custPhone = split[1];
			if(StringUtils.isBlank(id)||StringUtils.isBlank(custPhone)) {
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				result.put("respDesc", "参数异常");
				handleLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">getH5CrowdPackScript>参数异常>"+param.toJSONString());
				return result;
			}
			EasySQL crowdPackSql = new EasySQL("select t1.SCRIPT_CONTENT,t1.CROWD_PACK_ID,t2.PRIORITY,t1.SERVICE_TYPE ");
			crowdPackSql.append(" FROM C_NO_AS_ONLINE_CUST_STRATEGY	t1 LEFT JOIN C_NO_AS_CROWDPACK_PRIORITY t2 ON t1.ID = t2.STRATEGY_ID WHERE 1=1");
			crowdPackSql.append(id," AND t1.ID = ? ");
			handleLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">getH5CrowdPackScript>获取在线H5用户人群包脚本,sql>"+crowdPackSql.getSQL()+"{"+JSON.toJSONString(crowdPackSql.getParams())+"}");
			JSONObject row = query.queryForRow(crowdPackSql.getSQL(), crowdPackSql.getParams(),new JSONMapperImpl());
			String scriptContent=null;
			String crowdPackId=null;
			String priority=null;
			String serviceType=null;
			if(row!=null&&StringUtils.isNotBlank(row.getString("SCRIPT_CONTENT"))) {
				scriptContent = row.getString("SCRIPT_CONTENT");
				crowdPackId = row.getString("CROWD_PACK_ID");
				priority = row.getString("PRIORITY");
				serviceType = row.getString("SERVICE_TYPE");
				//记录用户已读取脚本，修改服务结果
				query.execute("update C_NO_AS_CROWD set SERVICE_STATUS = ? ,RESULT_TIME=?,SERVICE_DATE=?,RESULT_SOURCE = ?,PRIORITY = ?,RESULT_SERVICE_TYPE = ? where CUST_PHONE = ? and CROWD_PACK_ID=? and SERVICE_STATUS = ? and CUST_READ='1' and IS_DISTURB = '0'", Constants.HAS_REVISITED,EasyDate.getCurrentDateString(),EasyDate.getCurrentDateString("yyyyMMdd"),Constants.CHANNEL_ONLINE_CUST,priority,serviceType,custPhone,crowdPackId,Constants.NO_PUBLISH);
			}else {
				handleLogger.info(CommonUtil.getClassNameAndMethod(ActiveService.class)+">getH5CrowdPackScript>人群包id查询为空>param="+param.toJSONString());
				scriptContent="";
			}
			
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			result.put("respDesc", "查询成功");
			result.put("respData", scriptContent);
		} catch (Exception e) {
			handleLogger.error(CommonUtil.getClassNameAndMethod(ActiveService.class)+">getH5CrowdPackScript>获取在线H5用户人群包脚本,请求参数>"+JSON.toJSONString(param)+"原因："+e.getMessage());
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "查询失败");
		}

		return result;
	
	}
	
	private Map<String,String> decryptPhone(List<JSONArray> encryptPhoneList) {
		HashMap<String, String> hashMap = new HashMap<String,String>();
		for(int i=0;i<encryptPhoneList.size();i++) {
			JSONArray jsonArray = encryptPhoneList.get(i);
			JSONObject resp = null;
			try {
				IService service = ServiceContext.getService("MIXGW_DSMP_INTEFACE");
				JSONObject queryParams = new JSONObject();
				queryParams.put("command","decrypt");
				queryParams.put("params",jsonArray);
				resp = service.invoke(queryParams);
			} catch (ServiceException e) {
				logger.error("查询手机号码接口异常="+jsonArray.toJSONString()+"e:"+e.getMessage(),e);
				continue;
			}
			String respCode = resp.getString("respCode");
			if ("000".equals(respCode)) {// "000" 成功，查询接口
				JSONObject respData = resp.getJSONObject("respData");
				JSONArray data = respData.getJSONArray("result");
				Decoder decoder = Base64.getDecoder();
				for(int j=0;j<data.size();j++) {
					JSONObject jsonObject = null;
					try {
						jsonObject = data.getJSONObject(j);
						String plaintext = jsonObject.getString("plaintext");
						String ciphertextBlob = jsonObject.getString("ciphertextBlob");
						String phone = new String(decoder.decode(plaintext), StandardCharsets.UTF_8);
						hashMap.put(ciphertextBlob, phone);
					} catch (Exception e) {
						//todo 保底逻辑(可以进一步优化)
						logger.error("此条数据无法解密 jsonObject:" + jsonObject);
					}
				}
			}else {
				logger.info("查询手机号码失败，resp="+resp);
			}
		}
		return hashMap;
	}

	/**
	 * 通过手机号和渠道ID获取策略ID列表，若为空则返回空数组；
	 * @param json 请求参数，{params : { phone:手机号，channelId:渠道ID, type:1 （0-提醒类，1-阻断类） } , command : getStrategyIdByPhoneAndChannelId}
	 * @return {respCode : 000, respDesc : 查询成功, respData : { isExist: true , data : [{ STRATEGY_ID:策略ID1, STRATEGY_NAME:策略名称1, SCRIPT_CONTENT:话术配置1, CROWD_PACK_ID:人群包ID1, CROWD_NAME:人群包名称1 },
	 * 																					{ STRATEGY_ID:策略ID2, STRATEGY_NAME:策略名称2, SCRIPT_CONTENT:话术配置2, CROWD_PACK_ID:人群包ID2, CROWD_NAME:人群包名称2 }]}}
	 * 		   {respCode : 000, respDesc : 查询成功, respData : { isExist: false, data : []}}
	 */
	public JSONObject getStrategyIdByPhoneAndChannelId(JSONObject json) {
		strategyLogger.info("开始通过手机号和渠道ID获取策略ID列表》》》   请求参数 ： " + json.toJSONString());
		JSONObject result = new JSONObject();
		try {
			UserDndService userDndService = UserDndService.getInstance();
			return userDndService.getStrategy(json);
		} catch (Exception e) {
			strategyLogger.error(e.getMessage());
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "查询异常");
			result.put("respData", new JSONArray());
		}
		return result;
	}

	/**
	 * 通过手机号和渠道ID获取策略ID列表，若为空则返回空数组；
	 * @param json 请求参数，{params : { phone:手机号，channelId:渠道ID, STRATEGY_ID:策略ID，STRATEGY_NAME:策略名称, CHANNEL_NAME:渠道名称, CROWD_PACK_ID:人群包ID, CROWD_NAME:人群包名称, CREATE_ACC:创建人} , command : saveRemindRecord}
	 * @return {respCode : 000, respDesc : 记录成功}
	 */
	public JSONObject saveRemindRecord(JSONObject json) {
		strategyLogger.info("开始记录提醒记录表》》》   请求参数 ： " + json.toJSONString());
		JSONObject result = new JSONObject();
		try {
			UserDndService userDndService = UserDndService.getInstance();
			return userDndService.saveRemindRecord(json);
		} catch (Exception e) {
			strategyLogger.error(e.getMessage());
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "记录异常");
		}
		return result;
	}

	public static void main(String[] args) throws ServiceException {
//		IService service = ServiceContext.getService("ACTIVE-SERVICE-CROWD-INTEFACE");
//		JSONObject queryParams = new JSONObject();
//		queryParams.put("command","getH5CustPhoneCrowdPackList");
//		queryParams.put("custPhone", "15889981172");
//		JSONObject syncResp = service.invoke(queryParams);
//		
//		String syncRespCode = syncResp.getString("respCode");
		
		//联调的测试环境
		String url = "http://10.16.157.143:8080/uinterface/Receive.do";
		//数据数组
		JSONArray data= new JSONArray ();
		JSONObject info= new JSONObject();
		info.put("sysId", "CSS");
		info.put("groupId", "230802170705");
		//明细列表
		JSONArray list= new JSONArray();
		JSONObject user= new JSONObject();
		user.put("custName", "朱先生3");
		user.put("custPhone", "13542831337");
		user.put("orgCode", "CS002");
		user.put("brandCode", "MIDEA");
		user.put("brandName", "美的");
		user.put("prodCode", "100");
		user.put("prodName", "家用空调");
		user.put("productModel", "400");
		user.put("faultCode", "E5");
		user.put("faultLevel", "1");
		user.put("faultReason", "");
		user.put("serviceOrderCode", "09060118");
		list.add(user);
		info.put("list",list);
		data.add(info);
		JSONObject json = new JSONObject();
		json.put("sender", "CSS");
		json.put("password", "YQ_85521717");
		json.put("serialId", "");
		json.put("command", "myxTriggerGroupUserSync");
		json.put("serviceId", "ACTIVE-SERVICE-CROWD-INTEFACE");
		json.put("data", data);
		json.put("timestamp", DateUtil.getCurrentDateStr());
		String sign = SecurityUtil
		.encryptMsgByMD5(json.getString("sender")
		+json.getString("password")
		+json.getString("timestamp")
		+json.getString("serialId"));
		json.put("signature", sign);
		System.out.println(json.toJSONString());
		HttpResp resp = HttpUtil.sendPost(url,json.toJSONString());
		System.out.println(resp.getCode());
		
	
	}
}
