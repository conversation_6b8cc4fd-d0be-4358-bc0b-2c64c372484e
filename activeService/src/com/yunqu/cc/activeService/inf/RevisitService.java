package com.yunqu.cc.activeService.inf;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.yunqu.cc.activeService.msg.MqBrokerControl;
import com.yunqu.cc.activeService.msg.TimelyDataConsumerThread;
import com.yunqu.cc.activeService.service.*;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.http.util.TextUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.JsonUtil;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.base.Constants;
import com.yunqu.cc.activeService.utils.RobotCallUtil;
import com.yunqu.cc.activeService.utils.StringUtil;

import javax.jms.MessageProducer;
import javax.jms.TextMessage;

/**
 * 回访相关接口
 */
public class RevisitService extends IService{

	private final Logger logger = CommLogger.getCommLogger("distribute");
	private final Logger robotLogger = CommLogger.getCommLogger("robot_distribute");
	private final Logger robotLoggerSyn = CommLogger.getCommLogger("robot_sync");
	private final Logger robotLoggerRec = CommLogger.getCommLogger("robot_recycle");
    private final Logger robotLoggerMq = CommLogger.getCommLogger("robot_mq");
	private final EasyCache cache = CacheManager.getMemcache();
	private final EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME,Constants.YW_DS);
	
	
	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		String command = json.getString("command");
		if("AS_REVISIT_MANUAL_DISTRIBUTE".equals(command)) {//人工数据分配
			String cacheId = "JOB_AS_REVISIT_MANUAL_DISTRIBUTE";
			String value = cache.get(cacheId);
			if(value==null||"".equals(value)){
				cache.put(cacheId,"1",60*10);
				logger.info(Thread.currentThread().getName()+"开始分配....");
				distribute();
				logger.info(Thread.currentThread().getName()+"结束分配....");
				logger.info("---------------------------");
				cache.delete(cacheId);
			}
		}else if("AS_REVISIT_ROBOT_DISTRIBUTE".equals(command)) {//机器人数据分配
			//1.人工机器人外呼数据
			String cacheId = "JOB_AS_REVISIT_ROBOT_DISTRIBUTE";
			String value = cache.get(cacheId);
			if(value==null||"".equals(value)){
				cache.put(cacheId,"1",60*60);
				robotLogger.info(Thread.currentThread().getName()+"机器人任务开始分配....");
				robotDistribute();
				robotLogger.info(Thread.currentThread().getName()+"机器人任务结束分配....");
				robotLogger.info("---------------------------");
				cache.delete(cacheId);
			}
			int serialId = RandomKit.random(10000, 99999);
			 cacheId = "JOB_AS_AUTO_CALL_ROBOT_1";
			 value = cache.get(cacheId);
			if(value==null||"".equals(value)){
				robotLogger.info(Thread.currentThread().getName()+"机器人外呼任务开始....");
				cache.put(cacheId,"1",60*60);
				try {
					AutoRobotCallService.getInstance(serialId).autoRobotCall();
				} catch (Exception e) {
					// TODO: handle exception
				}finally {
					cache.delete(cacheId);
					robotLogger.info(Thread.currentThread().getName()+"机器人外呼任务结束....");
				}
			}

		}else if("AS_REVISIT_ROBOT_DISTRIBUTE_STRATEGY_2".equals(command)) {//20250320新建策略,主要用于策略2的机器人外呼
			//1.人工机器人外呼数据
			String cacheId = "JOB_AS_REVISIT_ROBOT_DISTRIBUTE_STRATEGY_2";
			String value = cache.get(cacheId);
			int serialId = RandomKit.random(10000, 99999);
			if(value==null||"".equals(value)){
				robotLogger.info(Thread.currentThread().getName()+"机器人策略2外呼任务开始....");
				cache.put(cacheId,"1",60*60);
				try {
					new AutoRobotCallStra2Service(serialId).autoRobotCall();
				} catch (Exception e) {
					// TODO: handle exception
				}finally {
					cache.delete(cacheId);
					robotLogger.info(Thread.currentThread().getName()+"机器人策略2外呼任务结束....");
				}
			}

		}else if("AS_REVISIT_ROBOT_DISTRIBUTE_STRATEGY_3".equals(command)) {//20250421新建策略,主要用于策略3的机器人外呼（不限制重复外呼）
			//1.人工机器人外呼数据
			String cacheId = "JOB_AS_REVISIT_ROBOT_DISTRIBUTE_STRATEGY_3";
			String value = cache.get(cacheId);
			int serialId = RandomKit.random(10000, 99999);
			if(value==null||"".equals(value)){
				robotLogger.info(Thread.currentThread().getName()+"机器人策略3外呼任务开始....");
				cache.put(cacheId,"1",60*60);
				try {
					new AutoRobotCallStra3Service(serialId).autoRobotCall();
				} catch (Exception e) {
					// TODO: handle exception
				}finally {
					cache.delete(cacheId);
					robotLogger.info(Thread.currentThread().getName()+"机器人策略3外呼任务结束....");
				}
			}

		}else if("TIMELY_OUTBOUND_CALL_ROBOT_STRATEGY".equals(command)) {//20250805新建策略,主要用于机器人外呼配置中为实时推送类型,不区分校验策略，仅判断是否实时推送
			//1.人工机器人外呼数据
			String cacheId = "TIMELY_OUTBOUND_CALL_ROBOT_STRATEGY";
			String value = cache.get(cacheId);
			int serialId = RandomKit.random(10000, 99999);
			if(value==null||"".equals(value)){
				robotLogger.info(Thread.currentThread().getName()+"实时推送外呼机器人外呼任务开始....");
				cache.put(cacheId,"1",60*60);
				try {
					new AutoRobotCallTimelyService(serialId).autoRobotCall();
				} catch (Exception e) {
					// TODO: handle exception
				}finally {
					cache.delete(cacheId);
					robotLogger.info(Thread.currentThread().getName()+"实时推送外呼机器人外呼任务结束....");
				}
			}

		}else if("AS_AUTO_REVISIT_ROBOT_DISTRIBUTE".equals(command)) {//机器人数据分配
			
			//2.自动外呼数据发布
//			int serialId = RandomKit.random(10000, 99999);
//			String cacheId = "JOB_AS_AUTO_CALL_ROBOT_1";
//			String value = cache.get(cacheId);
//			if(value==null||"".equals(value)){
//				cache.put(cacheId,"1",60*10);
//				AutoRobotCallService.getInstance().autoRobotCall(serialId);
//				cache.delete(cacheId);
//			}
		}else if("AS_REVISIT_RECYCLE_ROBOTDATA".equals(command)){//回收机器人数据
			//1.人工发布机器人外呼数据
			String cacheId = "JOB_AS_REVISIT_ROBOT_RECYCLE";
			String value = cache.get(cacheId);
			if(value==null||"".equals(value)){
				cache.put(cacheId,"1",60*10);
				robotLoggerRec.info(Thread.currentThread().getName()+"机器人任务开始回收....");
				robotRecycle();
				robotLoggerRec.info(Thread.currentThread().getName()+"机器人任务结束回收....");
				robotLoggerRec.info("---------------------------");
				cache.delete(cacheId);
			}
			//2.自动外呼数据回收
			int serialId = RandomKit.random(10000, 99999);
			cacheId = "JOB_AS_AUTO_CALL_RECYCLE";
			value = cache.get(cacheId);
			if(value==null||"".equals(value)){
				cache.put(cacheId,"1",60*10);
				AutoRobotCallService.getInstance(serialId).autoRobotCallRecycle();
				cache.delete(cacheId);
			}
		}else if("SYN_AS_ROBOTREVISIT_RESULT".equals(command)) {//同步机器人回访结果
			return synRobotRvisitResult(json);
		}else {
			JSONObject result = JsonUtil.createInfRespJson(json);
			result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
			result.put("respDesc", "error：不存在的command！");
			return result;
		}
		return EasyResult.ok("执行完毕");
	}
	
	/**
	 * 人工定时分配
	 * @return
	 */
	private JSONObject distribute() {
		try {
			String currentTime = EasyCalendar.newInstance().getDateTime("-");//当前时间
			EasySQL selectSql = new EasySQL("SELECT * FROM C_NO_AS_CROWD_PUBLISH WHERE 1=1");
			selectSql.append(currentTime,"AND DEADLINE <= ?").append(Constants.HAS_PUBLISHED,"AND PUBLISH_STATE = ?");
			selectSql.append(Constants.REVISIT_CALLOUT_MANUAL,"AND CALLOUT_TYPE = ?");
			List<JSONObject> publishList = query.queryForList(selectSql.getSQL(), selectSql.getParams(),new JSONMapperImpl());
			if(publishList.size()==0) {
				logger.info("人工外呼-无分配资料");
				return EasyResult.error(500,"无分配资料");
			}
			
			for(JSONObject obj:publishList) {
				//成功分配的任务数量
				int successNum = 0;
				try {
					EasySQL userNumSql = applyUserSQL(obj);
					//申请人数量，根据回访数升序
					List<JSONObject> applyUserList = query.queryForList(userNumSql.getSQL(), userNumSql.getParams(),new JSONMapperImpl());
					//任务总量
					String getDisTaskNumSql="select count(distinct CUST_PHONE) from C_NO_AS_CROWD where PUBLISH_ID= ? ";
					int taskNum = query.queryForInt(getDisTaskNumSql, new  Object[]{obj.getString("ID")});//可分配任务数
					//分配总人数
					int disUserNum = 0;
					//人均分配数量
					int averageNum = obj.getIntValue("AVERAGE_NUM") == 0 ? 50:obj.getIntValue("AVERAGE_NUM");
					
					if(taskNum%averageNum == 0) {
						disUserNum = taskNum/averageNum;
					}else {
						disUserNum = taskNum/averageNum + 1;
					}
					if(disUserNum!=0&&applyUserList.size()<disUserNum) {
						disUserNum = applyUserList.size();
					}
					for(int i=0;disUserNum!=0&&i<disUserNum;i++) {
						//验证当前用户的待回访数量，如果大于100，则当前用户不分配，分配给下一位，没有则回收资料
						if(query.queryForInt("SELECT COUNT(*) FROM C_NO_AS_CROWD WHERE AGENT_ACC = ? AND SERVICE_STATUS = ?", new Object[] {applyUserList.get(i).getString("APPLY_ACC"),Constants.HAS_RECEIVED}) > 100) {
							if(i==(disUserNum-1)) {
								break;
							}
							logger.info("用户："+ applyUserList.get(i).getString("APPLY_ACC") +"待回访数量大于100，不分配");
							continue;
						}
						//更新人群明细表数据
						EasySQL updateSql = updateCrowdSQL(applyUserList.get(i),obj,averageNum);
						int disTaskNum = query.executeUpdate(updateSql.getSQL(), updateSql.getParams());
						successNum += disTaskNum;
						logger.info("用户："+ applyUserList.get(i).getString("APPLY_ACC") +"资料分配完成，成功分配数量：" + disTaskNum);
						//更新领取表数量
						EasyRecord record = new EasyRecord("C_NO_AS_APPLY_USER","ID");
						record.setPrimaryValues(applyUserList.get(i).getString("ID"));
						record.set("SUBMIT_NUM", disTaskNum);
						query.update(record);
						logger.info("用户：" + applyUserList.get(i).getString("APPLY_ACC") + "，更新数据写入任务申请表");
					}
					//回收未分配的数据
					EasySQL recycleSql = new EasySQL("UPDATE C_NO_AS_CROWD SET");
					recycleSql.append("AGENT_ACC=null,AGENT_NAME=null,AGENT_DEPT_CODE=null,AGENT_DEPT_NAME=null,AGENT_AREA_CODE=null,AGENT_AREA_NAME=null,");
					recycleSql.append("CONVERSION_TYPE=null,PUBLISH_ID = null,publish_acc=null,publish_time=null,receive_time=null,");
					recycleSql.append(Constants.NO_PUBLISH,"SERVICE_STATUS = ? WHERE 1=1");
					recycleSql.append(obj.getString("ID"),"AND PUBLISH_ID = ?");
					recycleSql.append(Constants.HAS_PUBLISHED,"AND SERVICE_STATUS = ?");
					int recycleNum = query.executeUpdate(recycleSql.getSQL(), recycleSql.getParams());
					logger.info("本次ID：" + obj.getString("ID") +"资料发布完成，开始回收未分配的资料，成功分配数量：" + successNum + ",回收数量：" + recycleNum);
					//将此发布记录状态改为已分配
					query.executeUpdate("UPDATE C_NO_AS_CROWD_PUBLISH SET PUBLISH_STATE = ? WHERE ID = ?", Constants.HAS_RECEIVED,obj.getString("ID"));
				} catch (Exception e) {
					//分配过程中遇到错误，直接回收数据
					EasySQL recycleSql = new EasySQL("UPDATE C_NO_AS_CROWD SET");
					recycleSql.append("AGENT_ACC=null,AGENT_NAME=null,AGENT_DEPT_CODE=null,AGENT_DEPT_NAME=null,AGENT_AREA_CODE=null,AGENT_AREA_NAME=null,");
					recycleSql.append("CONVERSION_TYPE=null,PUBLISH_ID = null,publish_acc=null,publish_time=null,");
					recycleSql.append(Constants.NO_PUBLISH,"SERVICE_STATUS = ? WHERE 1=1");
					recycleSql.append(obj.getString("ID"),"AND PUBLISH_ID = ?");
					query.executeUpdate(recycleSql.getSQL(), recycleSql.getParams());
					//将此发布记录状态改为已分配
					query.executeUpdate("UPDATE C_NO_AS_CROWD_PUBLISH SET PUBLISH_STATE = ? WHERE ID = ?", Constants.HAS_RECEIVED,obj.getString("ID"));
					logger.info("遇到错误回收本批数据，id:"+obj.getString("ID")+"错误原因：" + e.getMessage()+",开始分配下一批数据",e);
				}
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "分配出错：" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok();
	}
	
	/**
	 * 机器人定时分配
	 * @return
	 */
	private JSONObject robotDistribute() {
		try {
			String currentTime = EasyCalendar.newInstance().getDateTime("-");//当前时间
			String startTime = DateUtil.addDay(DateUtil.TIME_FORMAT, currentTime, -3);//扫描最近3天的发布记录
			EasySQL selectSql = new EasySQL("SELECT * FROM C_NO_AS_CROWD_PUBLISH WHERE 1=1");
			selectSql.append(startTime,"AND PUBLISH_TIME >= ?"); 
			selectSql.append(currentTime,"AND DEADLINE <= ?");
			selectSql.append(Constants.HAS_PUBLISHED,"AND PUBLISH_STATE = ?");
			selectSql.append(Constants.REVISIT_CALLOUT_ROBOT,"AND CALLOUT_TYPE = ?");
			List<JSONObject> publishList = query.queryForList(selectSql.getSQL(), selectSql.getParams(),new JSONMapperImpl());
			if(publishList.size()==0) {
				robotLogger.info("机器人外呼-无分配资料");
				return EasyResult.error(500,"无分配资料");
			}
			for(JSONObject obj:publishList) {
				try {
					RevisitRobotService.getInstance().robotDataCall(obj);
				} catch (Exception e) {
					logger.error(CommonUtil.getClassNameAndMethod(this) + "error:" + e.getMessage(),e);
				}
			}
		} catch (Exception e) {
			robotLogger.error(CommonUtil.getClassNameAndMethod(this) + "分配出错：" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok();
	}
	
	/**
	 * 机器人回收
	 * @return
	 */
	private JSONObject robotRecycle() {
		try {
			String today = DateUtil.getCurrentDateStr("yyyy-MM-dd");
			String yesterday = DateUtil.addDay("yyyy-MM-dd", today, -1);
			
			String callStatus = RobotCallUtil.getRobotCallSuccessStatus();
			//查询出有问题的数据 
			EasySQL sql = new EasySQL("select t1.*,t2.id as crowd_id,t2.CROWD_PACK_ID from c_no_robot_call t1");
			sql.append("join c_no_as_crowd t2 on t1.add2=t2.id");
			sql.append(Constants.HAS_RECEIVED,"where (t2.service_status = ?");//已分配
			sql.append(Constants.HAS_PUBLISHED,"or t2.service_status = ?)");//已发布
			sql.append("3","and t1.record_status = ?");//状态3是亿迅已处理
			sql.append("and t1.call_result is not null");
			sql.append("and t1.call_result not in (" + callStatus + ")");
			sql.append(yesterday,"and add1 >= ?");//昨天以来的数据
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			robotLoggerRec.info("[机器人]-查询出需要回收的数据量：" + list.size());
			if(list.size()==0) {
				robotLoggerRec.info("[机器人]-无数据需回收");
				return EasyResult.error();
			}
			for(JSONObject row:list) {
				try {
					robotLoggerRec.info("[机器人]-开始回收数据，发布ID："+row.getString("TASK_ID")+"，人群包明细id：" + row.getString("CROWD_ID"));
					RobotCallUtil.robotDataRecycle(Constants.MANUAL_ROBOT_CALL_TABLE,row);
				} catch (Exception e) {
					robotLoggerRec.error(CommonUtil.getClassNameAndMethod(this) + "error:" + e.getMessage(),e);
				}
			}
		} catch (Exception e) {
			robotLoggerRec.error(CommonUtil.getClassNameAndMethod(this) + "分配出错：" + e.getMessage(),e);
			return EasyResult.error();
		}
		return EasyResult.ok();
	}
	
	/**
	 * 同步机器人回访结果
	 * @param json
	 * @return
	 */
	private JSONObject synRobotRvisitResult(JSONObject json){
		JSONObject response = new JSONObject();
		try {
			robotLoggerSyn.info("[机器人结果同步] 入参：" + json.toJSONString());
			JSONObject jsonObject = json.getJSONObject("data");
			boolean flag = true;
			String id = jsonObject.getString("crowdId");
			if(StringUtils.isBlank(id)) {
				flag = false;
				id = jsonObject.getString("autoId");
			}

			JSONArray answerDetailArray = null;
			String answerDetail = URLDecoder.decode(String.valueOf(jsonObject.get("answerDetail")), "UTF-8");
			//判断answerDetail是否是jsonarray
			if(StringUtils.isNotBlank(answerDetail)&&answerDetail.startsWith("[")) {
				answerDetailArray = JSONArray.parseArray(answerDetail);
				JSONArray intentionList = jsonObject.getJSONArray("intentionList");
				for(int i=0;i<answerDetailArray.size();i++) {
					JSONObject answerDetailJson = answerDetailArray.getJSONObject(i);
					String[] l = intentionList.getString(i).split("_");
					answerDetailJson.put("intentionId",l[0]);
				}
			}

			if(answerDetailArray!=null&&answerDetailArray.size()>0){
				answerDetail = answerDetailArray.toJSONString();
			}

			//写入机器人回访详情
			String crowRobotId = RandomKit.uniqueStr();
			EasyRecord record = new EasyRecord("c_no_as_crowd_robot");
			record.put("ID",crowRobotId );
			record.put("RECORDING_NO", URLDecoder.decode(String.valueOf(jsonObject.get("qtNo")),"UTF-8"));
			record.put("RECORDING_PATH", URLDecoder.decode(String.valueOf(jsonObject.get("qtPath")),"UTF-8"));
			record.put("CALL_NO", URLDecoder.decode(String.valueOf(jsonObject.get("call_no")),"UTF-8"));
			record.put("MANUAL", URLDecoder.decode(String.valueOf(jsonObject.get("manual")),"UTF-8"));
			record.put("IVR_SESSION_ID", URLDecoder.decode(String.valueOf(jsonObject.get("ivrSessionId")),"UTF-8"));
			record.put("QUESTIONNAIRE_RESULT", answerDetail);
			record.put("RESULT_LABEL", URLDecoder.decode(String.valueOf(jsonObject.get("answerLabel")),"UTF-8"));
			record.put("RESULT_DESCRIBE", URLDecoder.decode(String.valueOf(jsonObject.get("answerDescribe")),"UTF-8"));
			record.put("END_TIME", DateUtil.getCurrentDateStr());
			record.put("SMS_INF_FLAG", jsonObject.getString("smsSuccessFlag"));
			try {
				//新增字段是否发送短信 如果返回的是Y是已该字段为准
				if(!StringUtils.isBlank(jsonObject.getString("smsFlag"))
						&&"Y".equals(jsonObject.getString("smsFlag"))){
					record.put("SMS_INF_FLAG", "1");
					//如果需要发送短信单独记录一个表
						EasyRecord smsRecord = new EasyRecord("C_NO_AS_CROWD_ROBOT_SMS");
						smsRecord.put("ID", RandomKit.uniqueStr());
						smsRecord.put("CROW_ROBOT_ID", crowRobotId);
						smsRecord.put("SMS_FLAG", "Y");
						smsRecord.put("SMS_SUCCESS_FLAG", jsonObject.getString("smsSuccessFlag"));
						smsRecord.put("CREATE_TIME", DateUtil.getCurrentDateStr());

				}
			} catch (Exception e) {
				robotLoggerSyn.error("[机器人结果同步短信记录失败] error：" + e.getMessage(),e);
			}
			if("Y".equals(jsonObject.getString("manual"))){//如果有转人工则需要记录是否
				record.put("IVR_MANUAL", "N");
			}

			record.put("CROWD_ID", id);
			query.save(record);

			//暂时用flag区分两个渠道 后续增加再优化
			JSONObject synResp = null;
			if(flag) {
				synResp =synManualRobotResult(jsonObject);
			}else {
				jsonObject.put("crowRobotId", crowRobotId);
				synResp = synAutoRobotReuslt(jsonObject);
			}

			//用户免打扰服务
			UserDndService.getInstance().execute(jsonObject);
			//发送至消息队列
			jsonObject.put("smsStatus", synResp.getString("SMS_STATUS"));
			jsonObject.put("resultTime", synResp.getString("RESULT_TIME"));
			jsonObject.put("resultContent",synResp.getString("RESULT_CONTENT"));
			JSONObject robotStrategy = AutoRobotCallService.getInstance().getRobotStrategy(synResp.getString("CROWD_PACK_ID"));
			if("Y".equals(robotStrategy.getString("IS_TIMELY"))){
				sendMessageToMq(jsonObject);
			}

			response.put("respCode",GWConstants.RET_CODE_SUCCESS);
			response.put("respDesc","请求成功");
		} catch (Exception e) {
			robotLoggerSyn.error("[机器人结果同步] error：" + e.getMessage(),e);
			response.put("respCode",GWConstants.RET_CODE_OTHER_EXCEPTION);		
			response.put("respDesc",e.getMessage());		
		}
		return response;
	}

	private JSONObject sendMessageToMq(JSONObject reqRobotInfo) throws Exception {
		robotLoggerMq.info("[机器人结果发送至消息队列] 入参：" + reqRobotInfo.toJSONString());

		// 查询获取crowdPackId和out_id
		String sql = "select CROWD_PACK_ID, OUT_ID from c_no_as_robot_auto_call where id = ?";
		JSONObject callInfo = query.queryForRow(sql, new Object[] {URLDecoder.decode(reqRobotInfo.getString("autoId"), "utf-8")}, new JSONMapperImpl());
		String crowdPackId = callInfo != null ? callInfo.getString("CROWD_PACK_ID") : "";
		String outId = callInfo != null ? callInfo.getString("OUT_ID") : "";


		// 构造callContent
		JSONArray callContent = new JSONArray();
		JSONArray answerDetailArray = null;
		String answerDetail = URLDecoder.decode(String.valueOf(reqRobotInfo.get("answerDetail")), "UTF-8");
		if(StringUtils.isNotBlank(answerDetail)&&answerDetail.startsWith("[")) {
			answerDetailArray = JSONArray.parseArray(answerDetail);
			JSONArray intentionList = reqRobotInfo.getJSONArray("intentionList");

			List<JSONObject> smsList = RobotCallUtil.getRobotIntentList(query,crowdPackId,intentionList);
			Map<String,String> smsMap = new HashMap<>();
			Set<String> smsKeys = new HashSet<>();
			for (JSONObject sms : smsList){
				String robotIntentionName = sms.getString("ROBOT_INTENTION_NAME");
				String robotIntentionTag = sms.getString("INTENTION_TAG");
				String robotCardId = sms.getString("ROBOT_CARD_ID");
				if (robotIntentionName != null && robotIntentionTag != null){
					smsMap.put(robotIntentionName,robotIntentionTag);
				}
				if (robotCardId != null){
					smsKeys.add(robotCardId);
				}
			}
			int orderNo = 1;
			for(int i=0;i<answerDetailArray.size();i++) {
				JSONObject answerDetailJson = answerDetailArray.getJSONObject(i);
				JSONObject jsonObject = new JSONObject();
				jsonObject.put("content",answerDetailJson.getString("content"));
				jsonObject.put("answer",answerDetailJson.getString("answer"));

				String[] l = intentionList.getString(i).split("_");
				if (l.length>1){
					String intentionStr = l[1];
					String cardId = l[0];
					if (NumberUtils.isParsable(intentionStr)){
                        //不返回数字,只返回意图配置中有的意图标签
                        jsonObject.put("intention", smsMap.getOrDefault(intentionStr, ""));
					}else {
						jsonObject.put("intention",l[1]);
					}
					String intention = jsonObject.getString("intention");
					if( intention != null && smsKeys.contains(cardId)){
						jsonObject.put("orderNo",orderNo+"");
						orderNo++;
						callContent.add(jsonObject);
					}
				}


			}
		}

		// 构造resultData
		JSONObject resultData = new JSONObject();
		resultData.put("recordNo", outId);
		resultData.put("custPhone", reqRobotInfo.getString("phone"));
		resultData.put("effectiveOutBound", "Y");
		resultData.put("invalidReason", reqRobotInfo.getString("resultContent"));
		resultData.put("smsStatus", reqRobotInfo.getString("smsStatus"));
		resultData.put("callTime", reqRobotInfo.getString("resultTime"));
		resultData.put("callContent", callContent);

		// 构造最终返回的JSON
		JSONObject response = new JSONObject();
		String autoId = reqRobotInfo.getString("autoId");
		response.put("callId", autoId);
		response.put("crowdPackId", crowdPackId);
		response.put("resultData", resultData);
		response.put("outId", outId);

		robotLoggerMq.info("[机器人结果发送至消息队列] 出参：" + response.toJSONString());

		// 发送到消息队列(保持原有逻辑)
		try{
			MqBrokerControl mqBrokerControl = MqBrokerControl.getInstance();
			TextMessage arrayTextMessage = mqBrokerControl.getSession("ACTIVE_SERVICE_TIMELY_DATA").createTextMessage(response.toJSONString());
			MessageProducer arrayProducer = mqBrokerControl.getProducer("ACTIVE_SERVICE_TIMELY_DATA");
			arrayProducer.send(arrayTextMessage);
			robotLoggerMq.info("[机器人结果发送至消息队列] 操作完成：ID=" + reqRobotInfo.getString("autoId"));
		}catch (Exception e){
			TimelyDataConsumerThread.saveRetryData(autoId,outId,crowdPackId,resultData);
			robotLoggerMq.info("[机器人结果发送至消息队列] 出现异常,储存至重试表");
			robotLoggerMq.error("[机器人结果发送至消息队列] 异常原因: " + e.getMessage(),e);
		}

		//还需将 C_NO_AS_ROBOT_CALL_RESULT 中的 IS_RETURN_RESULT 设为 Y
		String updateResultSql = "UPDATE C_NO_AS_ROBOT_CALL_RESULT SET IS_RETURN_RESULT = 'Y' WHERE ROBOT_AUTO_CALL_ID = ?";
		query.executeUpdate(updateResultSql, new Object[]{URLDecoder.decode(reqRobotInfo.getString("autoId"), "utf-8")});

		return response;
	}


	private JSONObject synManualRobotResult(JSONObject reqRobotInfo) throws UnsupportedEncodingException, SQLException {
		String result = reqRobotInfo.getString("result");
		String revisitResult = "10";
		String resultContent = "";
		if("有效回访".equals(result)) {
			revisitResult = "1";
			resultContent = "有效回访";
		}else if("用户拒访".equals(result)) {
			revisitResult = "4";
			resultContent = "用户拒访";
		}else {
			resultContent = result;
		}
		//修改明细表
		String resultTime = DateUtil.getCurrentDateStr();
		String updateSQL = "update c_no_as_crowd set service_status = ?,RESULT_SOURCE=?,RESULT_TIME=?,SERVICE_DATE=?,REVISIT_RESULT=?,RESULT_CONTENT=? where id = ?";
		query.execute(updateSQL, Constants.HAS_REVISITED,
				Constants.CHANNEL_ROBOT,
				resultTime,
				EasyDate.getCurrentDateString("yyyyMMdd"),
				revisitResult,
				resultContent,
				URLDecoder.decode(String.valueOf(reqRobotInfo.get("crowdId")), "utf-8"));
		robotLoggerSyn.info("[机器人结果同步] 修改明细表：" + updateSQL);
		JSONObject response = new JSONObject();
		response.put("RESULT_TIME", resultTime);
		response.put("SMS_STATUS", "N");
		return response;
	}
	
	private JSONObject synAutoRobotReuslt(JSONObject reqRobotInfo) throws UnsupportedEncodingException, SQLException {
		JSONObject response = new JSONObject();
		//意图列表
		JSONArray array = reqRobotInfo.getJSONArray("intentionList");
		int smsNum = 0;
		JSONObject callInfo=new JSONObject();
		if(array!=null) {
			String sql="select id,cust_phone,crowd_pack_id,EXPAND_DATA from c_no_as_robot_auto_call where id = ?";
			callInfo = query.queryForRow(sql, new Object[] {URLDecoder.decode(reqRobotInfo.getString("autoId"), "utf-8")},new JSONMapperImpl());
			robotLoggerSyn.info("[机器人结果同步] 修改明细表：" + sql+";"+URLDecoder.decode(reqRobotInfo.getString("autoId"), "utf-8")+"-->"+callInfo.toJSONString());
			//解析机器人意图信息发送短信
			smsNum = RobotCallUtil.sendRobotSMSByIntention(callInfo.getString("CROWD_PACK_ID"),callInfo.getString("CUST_PHONE"), array,callInfo);
		}
		//挂机短信
		if("1".equals(reqRobotInfo.getString("smsSuccessFlag"))) {
			smsNum += 1;
		}
		if(smsNum>0){
			try {
				EasyRecord record = new EasyRecord("c_no_as_crowd_robot","ID");
				record.put("ID",reqRobotInfo.get("crowRobotId") );
				record.put("SMS_INF_FLAG", "1");
				query.update(record);
			} catch (Exception e) {
				// TODO: handle exception
			}
			
		}

		if (smsNum>0){
			response.put("SMS_STATUS", "Y");
		}else {
			response.put("SMS_STATUS", "N");
		}
		
		String result = reqRobotInfo.getString("result");
		String revisitResult = "10";
		String resultContent = "";
		if("有效回访".equals(result)) {
			revisitResult = "1";
			resultContent = "有效回访";
		}else if("用户拒访".equals(result)) {
			revisitResult = "4";
			resultContent = "用户拒访";
		}else {
			resultContent = result;
		}
		String currentDateStr = DateUtil.getCurrentDateStr();
		//修改明细表
		String updateSQL = "update c_no_as_robot_auto_call set service_status=?,RESULT_TIME=?,REVISIT_RESULT=?,RESULT_CONTENT=?,SMS_TIMES=SMS_TIMES+"+smsNum+" where id = ?";
		query.execute(updateSQL, Constants.HAS_REVISITED,
				currentDateStr,
				revisitResult,
				resultContent,
				URLDecoder.decode(reqRobotInfo.getString("autoId"), "utf-8"));
		robotLoggerSyn.info("[机器人结果同步] 修改明细表：" + updateSQL);
		robotLoggerSyn.info("解析机器人意图保存记录：" +( array!=null)+";"+(callInfo!=null));

		if(array!=null&&callInfo!=null) {
			robotLoggerSyn.info("解析机器人意图保存记录："+reqRobotInfo.getString("crowRobotId") );
			//解析机器人意图保存记录
			smsNum = RobotCallUtil.saveCallResult(reqRobotInfo.getString("crowRobotId"),callInfo.getString("ID"),callInfo.getString("CROWD_PACK_ID"),callInfo.getString("CUST_PHONE"), array,currentDateStr,revisitResult,resultContent);
		}

		//释放一个小时的缓存
		JSONObject robotStrategy = AutoRobotCallService.getInstance().getRobotStrategy(callInfo.getString("CROWD_PACK_ID"));
		if(robotStrategy!=null){
			String custPhone = StringUtils.trim(callInfo.getString("CUST_PHONE"));
			String phone = RobotCallUtil.getFormatPhone(custPhone, "0757", robotStrategy.getString("PREFIX_CODE"));
			String cacheId="cache_as_autoRobotCall_" + phone;
			cache.delete(cacheId);
			robotLoggerSyn.info("[机器人结果同步] 成功删除缓存：" + cacheId);
		}
		response.put("CROWD_PACK_ID",callInfo.getString("CROWD_PACK_ID"));
		response.put("RESULT_TIME",currentDateStr);
		response.put("RESULT_CONTENT",resultContent);
       return response;
	}
	
	private EasySQL applyUserSQL(JSONObject publishObj) {
		int day = ConfigUtil.getInt(Constants.APP_NAME, "RECEIVE_DATE",7);
		EasySQL userNumSql = new EasySQL("SELECT T1.ID,T1.USER_ACC as APPLY_ACC,T2.USER_NAME as APPLY_NAME,(SELECT COUNT(*) FROM C_NO_AS_RESULT");
		userNumSql.append("WHERE C_NO_AS_RESULT.RESULT_ACC = T1.USER_ACC");
		userNumSql.append("AND RESULT_TIME>to_char(sysdate-" + day + ",'yyyy-mm-dd hh24:mi:ss')) AS REVISIT_NUM,");
		userNumSql.append("T2.AREACODE AS AREA_CODE,T2.DEPT_CODE,T2.AREA_NAME,T2.DEPT_NAME");
		userNumSql.append("FROM C_NO_AS_APPLY_USER T1 LEFT JOIN C_YG_EMPLOYEE T2 ON T1.USER_ACC = T2.USER_ACC");
		userNumSql.append("WHERE 1=1");
		userNumSql.append(publishObj.getString("ID"),"AND PUBLISH_ID = ? ORDER BY REVISIT_NUM");
		logger.info("查询申请坐席sql：" + userNumSql.getSQL() + "，参数：" + JSON.toJSONString(userNumSql.getParams()));
		return userNumSql;
	}
	
	/**
	 * 人群包明细更新sql
	 * @param applyUserObj
	 * @param publishObj
	 * @return
	 */
	private EasySQL updateCrowdSQL(JSONObject applyUserObj,JSONObject publishObj,int averageNum) {
		EasySQL updateSql = new EasySQL("UPDATE C_NO_AS_CROWD SET");
		updateSql.append(DateUtil.getCurrentDateStr(),"RECEIVE_TIME = ?,");
		updateSql.append(applyUserObj.getString("APPLY_ACC"),"AGENT_ACC = ?,");
		updateSql.append(applyUserObj.getString("APPLY_NAME"),"AGENT_NAME = ?,");
		updateSql.append(applyUserObj.getString("DEPT_CODE"),"AGENT_DEPT_CODE = ?,");
		updateSql.append(applyUserObj.getString("DEPT_NAME"),"AGENT_DEPT_NAME = ?,");
		updateSql.append(applyUserObj.getString("AREA_CODE"),"AGENT_AREA_CODE = ?,");
		updateSql.append(applyUserObj.getString("AREA_NAME"),"AGENT_AREA_NAME = ?,");
		updateSql.append(Constants.HAS_RECEIVED,"SERVICE_STATUS = ?");
		updateSql.append(publishObj.getString("ID"),"WHERE PUBLISH_ID = ?");
		updateSql.append(Constants.HAS_PUBLISHED,"AND SERVICE_STATUS = ?");
		
		updateSql.append("AND CUST_PHONE IN (");
		updateSql.append("select CUST_PHONE from ( ");
		updateSql.append("select distinct CUST_PHONE from C_NO_AS_CROWD ");
		updateSql.append(Constants.HAS_PUBLISHED,"where SERVICE_STATUS = ?");
		updateSql.append(publishObj.getString("ID"),"and PUBLISH_ID = ? ");
		updateSql.append(")");
		updateSql.append(averageNum,"where ROWNUM <=?");
		updateSql.append(")");
		logger.info("更新人群明细sql：" + updateSql.getSQL() + "，参数：" + JSON.toJSONString(updateSql.getParams()));
		return updateSql;
	}
	
	public static void main(String[] args) {
			ExecutorService executor = Executors.newFixedThreadPool(5); // 创建线程池，指定并发执行的线程数

			int totalTasks = 20; // 总共需要执行的任务数量

			// 创建一个 CompletableFuture 数组用于保存每个任务的结果
			List<CompletableFuture<Void>> futures = new ArrayList<>();

			for (int i = 0; i < totalTasks; i++) {
				final int taskNumber = i + 1;

				if(i%2==0){
					continue;
				}
				// 使用 CompletableFuture.supplyAsync 方法创建每个任务的 CompletableFuture
				CompletableFuture<Void> future = CompletableFuture.supplyAsync(() -> {
					// 任务的具体逻辑
					try {
						Thread.sleep(10000);
					} catch (InterruptedException e) {
						e.printStackTrace();
					}
					System.out.println("Executing task " + taskNumber);
					return null;
				}, executor);

				futures.add(future);
			}
		System.out.println("All tasks completed1");

			// 等待所有任务完成
		CompletableFuture<Void> voidCompletableFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
		voidCompletableFuture.join();

		System.out.println("All tasks completed");

			// 关闭线程池
			executor.shutdown();
		}

}
