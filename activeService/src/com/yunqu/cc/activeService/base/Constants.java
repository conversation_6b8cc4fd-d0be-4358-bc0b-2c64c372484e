package com.yunqu.cc.activeService.base;

import java.util.HashMap;
import java.util.Map;

import org.easitline.common.core.context.AppContext;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

/**
 * 常量
 *
 * <AUTHOR>
 */
public class Constants {
	public final static String APP_NAME = "activeService";     //应用名称

	public final static String DS_NAME = "default_ds";     //默认数据源名称
	public final static String YW_DS = "yw-ds";     //工单数据源名称
	public final static String FRAME_DS = "mars-ds";     //平台数据源名称
	public final static String YCBUSI_DS = "ycbusi-ds";     //平台数据源名称
	public final static String GENUSER_DS = "genuser-ds";     //亿讯数据源名称

	/**
	 * 售后接口 IService 服务名
	 */
	public final static String CSSGW_activeService = "MIXGW-COMM-productActiveService";

	//售后网关接口
	public final static String CSSGW_COMM = "CSSGW-COMM";

	//混合网关接口
	public final static String MIXGW_COMM = "MIXGW-COMM";

	//缓存失效时间,10分钟
	public final static int CACHE_FAIL_TIME = 60 * 10;
	//AI外呼有效发布时长，单位天
	public final static int AI_PUBLISH_DAY = 60 * 10;

	/**
	 * 回访状态1-未发布，2-已发布，3-已分配，4-已回访 5-推送中
	 */
	public final static String NO_PUBLISH = "1";
	public final static String HAS_PUBLISHED = "2";
	public final static String HAS_RECEIVED = "3";
	public final static String HAS_REVISITED = "4";
	public final static String SENDING = "5";
	public final static String OTHER_STATUS = "99";

	
	public final static String SN_Y = "Y";
	public final static String SN_N = "N";
	
	public final static String RETURN_TYPE_MYX = "01";//外呼类型美云销
	public final static String RETURN_TYPE_IN_IOT = "02";//外呼类型美云销
	public final static String RETURN_TYPE_IN_CSS_YS = "04";//外呼类型售后延时
	public final static String RETURN_TYPE_IN_CSS_CCI_1 = "05";//外呼类型05-安德物流_甲方
	public final static String RETURN_TYPE_IN_CSS_CCI_2 = "06";//外呼类型06-安德物流_收货方

	public final static String CROWD_PACK_CATE_1 = "1";//自动外呼人群包
	public final static String CROWD_PACK_CATE_2 = "2";//不文明用语人群包
	public final static String CROWD_PACK_CATE_3 = "3";//3s不足人群包
	public final static String CROWD_PACK_CATE_4 = "4";//普通免打扰人群包


	/**
	 * 坐席空闲状态
	 */
	public static final String AGENT_FREE_STATUS =  AppContext.getContext(APP_NAME).getProperty("AGENT_FREE_STATUS","空闲,签出");

	public static final String CALL_KEY =  AppContext.getContext(APP_NAME).getProperty("ROBOT_CALL_KEY","81");

	/**
	 * 是否免打扰 0-可以，1-免打扰
	 */
	public final static int IS_DISTURB_OFF = 0;
	public final static int IS_DISTURB_ON = 1;

	/**
	 * 全部发布
	 */
	public final static String PUBLISH_ALL = "0";
	/**
	 * 部分发布
	 */
	public final static String PUBLISH_PART = "1";
	/**
	 * ivr策略版本
	 */
	public final static String IVR_REFRESH_EDITION = "IVR_REFRESH_EDITION";


	/**
	 * 工作时间配置
	 * 一个时间段由-分割起始结束时间点，多个时间段由,分割
	 */
	public static final String WORK_TIME_CONFIG = AppContext.getContext(APP_NAME).getProperty("WORK_TIME_CONFIG", "");


	/**
	 * 1-热线渠道1，2-在线接入单，3-在线H5，4-工单回访，5-人工外呼，6-人工营销，7-机器人自动外呼，8-企微，9-ivr
	 */
	public final static String CHANNEL_HOTLINE = "1";
	public final static String CHANNEL_ONLINE = "2";
	public final static String CHANNEL_ONLINE_CUST = "3";
	public final static String CHANNEL_REVISIT = "4";
	public final static String CHANNEL_OUTBOUND = "5";
	public final static String CHANNEL_MARKET = "6";
	public final static String CHANNEL_ROBOT = "7";
	public final static String CHANNEL_WECHAT = "8";
	public final static String CHANNEL_IVR = "9";
	public final static String CHANNEL_5GVOICE = "10";

	/**
	 * 服务类型 1-服务，2-营销，3-回访
	 */
	public final static String SERVICE_TYPE_MARKET = "1";
	public final static String SERVICE_TYPE_OUTBOUND = "2";
	public final static String SERVICE_TYPE_REVISIT = "3";

	/**
	 * 系统来源 1-大数据，2-触发器，3-人工导入
	 */
	public final static String DATA_SOURCE_BD = "1";
	public final static String DATA_SOURCE_TRI = "2";
	public final static String DATA_SOURCE_IMPORT = "3";

	/**
	 * 回访发布 外呼类型 1-人工外呼 2-自动外呼
	 */
	public final static String REVISIT_CALLOUT_MANUAL = "1";
	public final static String REVISIT_CALLOUT_ROBOT = "2";

	/**
	 * 问卷的问题答案类型1单选，2多选，3填空
	 */
	public static final int QUESTION_ANSWER_RADIO = 1;
	public static final int QUESTION_ANSWER_CHECKBOX = 2;
	public static final int QUESTION_ANSWER_INPUT = 3;
	
	/**
	 * 策略等级1-人群包策略，2-人群包组策略
	 */
	public final static String STRATEGY_LEVEL_CROWDPACK = "1";
	public final static String STRATEGY_LEVEL_CROWDPACKGROUP = "2";

	/**
	 * 主动服务埋点类型
	 * 1-查询人群包，2-提交主动服务结果
	 */
	public final static String SUMMARY_DATA_TYPE_QUERY= "1";
	public final static String SUMMARY_DATA_TYPE_SUBMIT = "2";

	/**
	 * 渠道对应策略表名
	 * 通过strategyTable.get(channelType)获取
	 */
	public static final Map<String, String> strategyTable = new HashMap<String, String>(){
		private static final long serialVersionUID = 1L;
		{
			put(CHANNEL_HOTLINE, "C_NO_AS_HOTLINE_STRATEGY");
			put(CHANNEL_IVR,"C_NO_IVR_STRATEGY");
			put(CHANNEL_MARKET,"C_NO_AS_MARKET_STRATEGY");
			put(CHANNEL_ONLINE,"C_NO_AS_ONLINE_STRATEGY");
			put(CHANNEL_ONLINE_CUST,"C_NO_AS_ONLINE_CUST_STRATEGY");
			put(CHANNEL_OUTBOUND,"C_NO_AS_OUTBOUND_STRATEGY");
			put(CHANNEL_REVISIT,"C_NO_AS_REVISIT_STRATEGY");
			put(CHANNEL_ROBOT,"C_NO_AS_ROBOT_STRATEGY");
			put(CHANNEL_WECHAT,"C_NO_AS_WECHAT_STRATEGY");
			put(CHANNEL_5GVOICE,"C_NO_AS_5GVOICE_STRATEGY");
		}
	};

	/**
	 * 机器人外呼策略类型
	 */
	public final static String ROBOT_CALL_STRATEGY_1 = "1";
	public final static String ROBOT_CALL_STRATEGY_2 = "2";
	public final static String ROBOT_CALL_STRATEGY_3 = "3";



	public static final String SCHEDULER_ON_OFF = AppContext.getContext(APP_NAME).getProperty("SCHEDULER_ON_OFF", "false");

	//地动仪大数据人群包圈选地址
	public static final String DDY_CROWDPACK_PAGE_URL = AppContext.getContext(APP_NAME).getProperty("DDY_CROWDPACK_PAGE_URL", "");

	/**
	 * 微信网关接口上下文
	 * <AUTHOR>
	 * @date 2022/3/3 19:42
	 * @return java.lang.String
	 */
	public static String getWechatWorkBaseUrl() {
		return AppContext.getContext(APP_NAME).getProperty("WECHAT_WORK_BASE_URL","");
	}

	/**
	 * 系统外网地址
	 */
	public static String getSystemExtranetUrl() {
		return AppContext.getContext(APP_NAME).getProperty("SYSTEM_EXTRANET_URL","");
	}

	public static String getMqAddr() {
		return AppContext.getContext(APP_NAME).getProperty("MQ_ADDR","");
	}
	
	public static int getMaxAutoCallNum() {
		String num = AppContext.getContext(APP_NAME).getProperty("maxAutoCallNum","2");
		try{
			return Integer.parseInt(num);
		}catch (Exception e) {
			return 2;
		}
	}
	

	public static int getDataDealTime() {
		String time = AppContext.getContext(APP_NAME).getProperty("DataDealTime","7");
		try{
			return Integer.parseInt(time);
		}catch (Exception e) {
			return 7;
		}
	}
	
	public static int getWechatPushCrowUserNum(){
		try{
			String num = AppContext.getContext(APP_NAME).getProperty("WECHAT_PUSH_CROW_USERNUM", "500");
			return Integer.parseInt(num);
		}catch (Exception e) {
			return 500;
		}
	}
	
	public static String getRobotURL() {
		return AppContext.getContext(APP_NAME).getProperty("ROBOT_URL","http://10.16.123.135:9060");
	}

	public static String getEnableQueueConsumer() {
		return AppContext.getContext(APP_NAME).getProperty("ENABLE_MQ_QUEUE_CONSUMER","N");
	}
	
	/**
	 * 机器人策略缓存id
	 */
	public static final String ROBOT_STRATEGY_CACHEKEY = "robotStrategyCache_";
	
	/**
	 * 人工机器人外呼表
	 */
	public static final String MANUAL_ROBOT_CALL_TABLE = "C_NO_AS_CROWD";
	/**
	 * 自动机器人外呼表
	 */
	public static final String AUTO_ROBOT_CALL_TABLE = "C_NO_AS_ROBOT_AUTO_CALL";
	
}
