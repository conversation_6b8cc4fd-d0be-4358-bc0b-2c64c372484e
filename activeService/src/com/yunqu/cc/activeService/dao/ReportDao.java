package com.yunqu.cc.activeService.dao;

import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.activeService.base.CommLogger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.activeService.base.AppDaoContext;
import com.yunqu.cc.activeService.utils.StringUtil;

import java.util.Calendar;

/**
 * 报表相关查询
 * <AUTHOR>
 */
@WebObject(name = "report")
public class ReportDao extends AppDaoContext{

	@WebControl(name="autoRobotCallReport",type=Types.LIST)
	public JSONObject autoRobotCallReport() {
		EasySQL sql = new EasySQL("select * from C_NO_AS_ROBOT_CALL_STAT");
		sql.append("where 1=1");
		if(StringUtils.isNotBlank(param.getString("date_start"))) {
			sql.append(param.getString("date_start").replace("-", ""),"and date_id >= ?");
		}
		if(StringUtils.isNotBlank(param.getString("date_end"))) {
			sql.append(param.getString("date_end").replace("-", ""),"and date_id <= ?");
		}
		sql.appendLike(param.getString("crowdPackName"),"and crowd_pack_name like ?");
		sql.append("order by date_id desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	@WebControl(name = "autoRobotCallDetail", type = Types.LIST)
	public JSONObject autoRobotCallDetail() {
		EasySQL easySQL = new EasySQL("SELECT t.ADD1 ,t.CALL_TIME ,t.CALL_RESULT , t.RECORD_STATUS , t1.CUST_PHONE ,t1.CUST_NAME");
		easySQL.append(",t1.RESULT_TIME , t1.REVISIT_RESULT ,t1.RESULT_CONTENT ,t1.CALL_TIMES ,t1.SMS_TIMES ");
		easySQL.append("FROM c_no_robot_call t ");
		String date = param.getString("dateId");
		EasyCalendar calendar = EasyCalendar.newInstance(DateUtil.getDate(date + " 00:00:00"));
		if (StringUtils.equals(date, DateUtil.getCurrentDateStr("yyyy-MM-dd"))) {
			easySQL.append("LEFT JOIN C_NO_AS_ROBOT_AUTO_CALL t1 ON t.ADD2 =t1.ID ");
		} else {
			easySQL.append("LEFT JOIN C_NO_AS_ROBOT_AUTO_CALL_HIS t1 ON t.ADD2 =t1.ID ");
		}
		easySQL.append(param.getString("crowdPackId"), "WHERE t.RECORD_STATUS=3 AND t1.CROWD_PACK_ID =? AND t1.REVISIT_RESULT !=99", false);
		if (StringUtils.equals(param.getString("callType"), "recall")) {
			calendar.add(Calendar.DAY_OF_MONTH, -7);
			easySQL.append(calendar.getDateString("-"), "AND t.add1>=?");
			easySQL.append(date, "AND t.add1<?");
			easySQL.append(date + " 00:00:00", "AND t1.RESULT_TIME>=?");
			easySQL.append(date + " 23:59:59", "AND t1.RESULT_TIME<=?");
		} else {
			calendar.add(Calendar.DAY_OF_MONTH, 7);
			easySQL.append(date, "AND t.add1=?");
			easySQL.append(date + " 00:00:00", "AND t1.RESULT_TIME>=?");
			easySQL.append(calendar.getDateString("-") + " 23:59:59", "AND t1.RESULT_TIME<=?");
		}
		easySQL.append(param.getString("custPhone"), "AND t1.CUST_PHONE=?");
		easySQL.append(param.getString("SERVICE_STATUS"), "AND t1.SERVICE_STATUS=?");
		easySQL.append(param.getString("REVISIT_RESULT"), "AND t1.REVISIT_RESULT=?");
		if(StringUtils.equals(param.getString("CALL_RESULT"), "33")) {
			easySQL.append(param.getString("CALL_RESULT"), "AND t.CALL_RESULT=?");
		} else if(StringUtils.isNotBlank(param.getString("CALL_RESULT"))) {
			easySQL.append("33", "AND t.CALL_RESULT!=?");
		}
		if(StringUtils.equals(param.getString("CALL_TIMES"), "1")) {
			easySQL.append("AND t1.CALL_TIMES>1");
		} else if(StringUtils.isNotBlank(param.getString("CALL_TIMES"))) {
			easySQL.append("AND t1.CALL_TIMES<=1");
		}
		if (!StringUtils.equals(date, DateUtil.getCurrentDateStr("yyyy-MM-dd")) 
				&& !StringUtils.equals(param.getString("callType"), "recall")) {
			easySQL.append("union all");
			easySQL.append("SELECT t.ADD1 ,t.CALL_TIME ,t.CALL_RESULT , t.RECORD_STATUS , t1.CUST_PHONE ,t1.CUST_NAME");
			easySQL.append(",t1.RESULT_TIME , t1.REVISIT_RESULT ,t1.RESULT_CONTENT ,t1.CALL_TIMES ,t1.SMS_TIMES ");
			easySQL.append("FROM c_no_robot_call t ");
			easySQL.append("LEFT JOIN C_NO_AS_ROBOT_AUTO_CALL t1 ON t.ADD2 =t1.ID ");
			easySQL.append(param.getString("crowdPackId"), "WHERE t.RECORD_STATUS=3 AND t1.CROWD_PACK_ID =? AND t1.REVISIT_RESULT !=99", false);
			easySQL.append(date, "AND t.add1=?");
			easySQL.append(date + " 00:00:00", "AND t1.RESULT_TIME>=?");
			easySQL.append(calendar.getDateString("-") + " 23:59:59", "AND t1.RESULT_TIME<=?");
			easySQL.append(param.getString("custPhone"), "AND t1.CUST_PHONE=?");
			easySQL.append(param.getString("SERVICE_STATUS"), "AND t1.SERVICE_STATUS=?");
			easySQL.append(param.getString("REVISIT_RESULT"), "AND t1.REVISIT_RESULT=?");
			if(StringUtils.equals(param.getString("CALL_RESULT"), "33")) {
				easySQL.append(param.getString("CALL_RESULT"), "AND t.CALL_RESULT=?");
			} else if(StringUtils.isNotBlank(param.getString("CALL_RESULT"))) {
				easySQL.append("33", "AND t.CALL_RESULT!=?");
			}
			if(StringUtils.equals(param.getString("CALL_TIMES"), "1")) {
				easySQL.append("AND t1.CALL_TIMES>1");
			} else if(StringUtils.isNotBlank(param.getString("CALL_TIMES"))) {
				easySQL.append("AND t1.CALL_TIMES<=1");
			}
		}
		CommLogger.getCommLogger().info(easySQL.getSQL() + "-- " + JSONObject.toJSONString(easySQL.getParams()));
		return queryForPageList(easySQL.getSQL(), easySQL.getParams());
	}
}
