package com.yunqu.cc.activeService.dao;

import java.text.SimpleDateFormat;
import java.util.*;

import com.yunqu.openapi.utils.OpenApiUserUtil;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.DeptModel;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.user.DeptMgr;
import com.yunqu.cc.activeService.base.AppDaoContext;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.base.Constants;

@WebObject(name = "strategy")
public class StrategyDao extends AppDaoContext {

	private final Logger logger = CommLogger.logger;

	private final Logger strategyLogger = CommLogger.getCommLogger("strategy");
	
	@WebControl(name="getStrategyDetail",type=Types.RECORD)
	public  JSONObject getStrategyDetail(){
		
		JSONObject strategy = JsonKit.getJSONObject(param, "strategy");
		String channelType = param.getString("CHANNEL_TYPE");
		String strategyTable = Constants.strategyTable.get(channelType);
		EasySQL sql = this.getEasySQL("select  * ");
		sql.append(" from  "+strategyTable);
		sql.append(" where 1=1 ");
		sql.append(strategy.getString("CROWD_PACK_ID"), " and CROWD_PACK_ID = ? ");
		sql.append(strategy.getString("CROWD_PACK_GROUP_ID"), " and CROWD_PACK_GROUP_ID = ? ");
		return this.queryForRecord(sql.getSQL(), sql.getParams(),null);
	}
	
	
	/**
	 * 热线接入单页面，在线坐席接入客户页面，工单回访外呼页面查询人群包列表，修改时请注意，三个查询页面公用方法
	 * @return
	 */
	@WebControl(name="getCrowdPackListByCustPhone",type=Types.RECORD)
	public JSONObject getCrowdPackListByCustPhone() {
		EasyQuery query = this.getQuery();
		JSONObject result = new JSONObject();
		String channelType = this.param.getString("channelType");
		String strategyTable = Constants.strategyTable.get(channelType);
		String custPhone = this.param.getString("customerPhone");
		List<JSONObject> crowdPackList = new ArrayList<JSONObject>();
		logger.info("获取用户人群包,param="+this.param.toJSONString());
		try {
			if(StringUtils.isNotBlank(custPhone)) {
				
				//查询用户免打扰用户群
//				EasySQL sql1 = new EasySQL("select COUNT(1) from C_NO_AS_NOT_DISTURB where");
//				sql1.append(custPhone,"  CUST_PHONE=? ");//用户号码
//				sql1.append(channelType," and CHANNEL_ID = ? ");//所属渠道
//				sql1.append(" and IS_OPEN = '1' ");//生效
				String bgCrowdCode = "";
				switch (channelType) {
					case Constants.CHANNEL_HOTLINE :
						bgCrowdCode = "RXJRD";//热线接入单
						break;
					case Constants.CHANNEL_ONLINE_CUST:
						bgCrowdCode = "ZXH5KHD";//在线H5客户端
						break;
					case Constants.CHANNEL_ONLINE:
						bgCrowdCode = "ZXJRD";//在线接入单
						break;
					case Constants.CHANNEL_REVISIT:
						bgCrowdCode = "GDHF";//工单回访
						break;
				}
				if (!bgCrowdCode.isEmpty()) {
					EasySQL easySQL = new EasySQL();
					easySQL.append("SELECT CASE WHEN EXISTS(SELECT 1 FROM C_NO_AS_SENSITIVE_CROWD T2 " +
							"JOIN C_NO_AS_CROWD_PACK T4 ON T2.CROWD_PACK_ID = T4.ID WHERE 1 = 1 ");
					easySQL.append(custPhone, " AND T2.PHONE = ? ");
					easySQL.append(bgCrowdCode, " AND T4.BG_CROWD_CODE = ? ");
					easySQL.append(") THEN 'TRUE' ELSE 'FALSE' END AS IS_EXIST FROM DUAL");
					strategyLogger.info("通过查询敏感人群明细表获取策略ID列表SQL语句 : " + easySQL.getSQL() + " 参数 ： " + Arrays.toString(easySQL.getParams()));
					EasyRow easyRow = query.queryForRow(easySQL.getSQL(), easySQL.getParams());
					strategyLogger.info("通过查询敏感人群明细表获取策略ID列表返回值 : " + easyRow.toJSONObject().toJSONString());
					if (easyRow.toJSONObject().getString("IS_EXIST").equals("TRUE")) {
						//用户在对应渠道设置免打扰
						result.put("data", crowdPackList);
						result.put("state", 1);
						return result;
					}
				}
				
				
				//最近30天内热线或者在线渠道已服务不查询
				SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				Date date=new Date();
				Calendar calendar = Calendar.getInstance();
				calendar.setTime(date);
				calendar.add(Calendar.DATE, -30);
				date = calendar.getTime();
				String before30Day = dateFormat.format(date);
				EasySQL sql2 = new EasySQL("select COUNT(1) from C_NO_AS_CROWD where ");
				sql2.append(custPhone," CUST_PHONE=? ");//用户号码
				sql2.append(Constants.CHANNEL_HOTLINE," and (RESULT_SOURCE = ? ");//热线渠道
				sql2.append(Constants.CHANNEL_ONLINE," or RESULT_SOURCE = ? )");//在线渠道
				sql2.append(before30Day," and RESULT_TIME > ? ");//最近30天
				boolean hasServiceResult  = query.queryForExist(sql2.getSQL(), sql2.getParams());
				if(hasServiceResult) {
					//最近三十天已回访
					result.put("data", crowdPackList);
					result.put("state", 1);
					return result;
				}
				
				//查询人群明细
				EasySQL sql = this.getEasySQL("select ID,CROWD_PACK_ID,CUST_PHONE,CUST_NAME,BRAND_NAME,PROD_NAME from C_NO_AS_CROWD WHERE ");
				sql.append(custPhone," CUST_PHONE=? ",false);//来电号码
				sql.append(Constants.NO_PUBLISH," and SERVICE_STATUS = ? ");//初始状态
				sql.append("0"," and IS_DISTURB = ? ");//可打扰用户
				List<EasyRow> list = query.queryForList(sql.getSQL(), sql.getParams());
				if(list==null||list.size()<=0) {
					result.put("data", crowdPackList);
					result.put("state", 1);
					return result;
				}
				HashMap<String, JSONArray> map = new HashMap<String,JSONArray>();
				//查询人群包信息和策略信息-如果同一个人群包存在组策略和单个人群包策略，则优先查询人群包策略。
				EasySQL crowdPackSql = this.getEasySQL("select t6.CROWD_PACK_ID,t6.CROWD_PACK_NAME,t6.STRATEGY_ID,t6.PRIORITY,t6.SMS_CONTENT,t6.SMS_OPEN,t6.ORG_CODE,t6.BRAND_CODE,t6.SCRIPT_CONTENT,t6.SCRIPT_OPEN,t6.SERVICE_TYPE from ( ");
				crowdPackSql.append(" SELECT t1.CROWD_PACK_ID CROWD_PACK_ID, t2.NAME CROWD_PACK_NAME,t1.STRATEGY_ID,t1.PRIORITY,t3.SMS_CONTENT,t3.SMS_OPEN,t3.ORG_CODE,t3.BRAND_CODE,t3.SCRIPT_CONTENT,t3.SCRIPT_OPEN,t3.SERVICE_TYPE,t1.STRATEGY_LEVEL   ");
				crowdPackSql.append(" FROM C_NO_AS_CROWDPACK_PRIORITY t1 LEFT JOIN C_NO_AS_CROWD_PACK t2 ON t1.CROWD_PACK_ID = t2.ID ");
				crowdPackSql.append(channelType," LEFT JOIN "+strategyTable+" t3 ON t1.STRATEGY_ID = t3.ID WHERE t1.CHANNEL_TYPE = ? AND t3.IS_OPEN = '1' ) t6 ");
				crowdPackSql.append(" JOIN ( ");
				crowdPackSql.append("SELECT t4.CROWD_PACK_ID,MIN(t4.STRATEGY_LEVEL) STRATEGY_LEVEL FROM C_NO_AS_CROWDPACK_PRIORITY t4 LEFT JOIN "+strategyTable+" t5 ON t4.STRATEGY_ID = t5.ID WHERE");
				crowdPackSql.append(channelType," t4.CHANNEL_TYPE = ? AND t5.IS_OPEN = '1'  ");
				crowdPackSql.append(" AND t4.CROWD_PACK_ID in ( ");
				for(int i=0;i<list.size();i++) {
					EasyRow row = list.get(i);
					JSONObject jsonObject = new JSONObject();
					String crowdPackId = row.getColumnValue("CROWD_PACK_ID");
					String crowdId = row.getColumnValue("ID");
					jsonObject.put("CROWD_PACK_ID", crowdPackId);
					jsonObject.put("CROWD_ID", crowdId);
					jsonObject.put("CUST_PHONE", row.getColumnValue("CUST_PHONE"));
					jsonObject.put("CUST_NAME", row.getColumnValue("CUST_NAME"));
					jsonObject.put("BRAND_NAME", row.getColumnValue("BRAND_NAME"));
					jsonObject.put("PROD_NAME", row.getColumnValue("PROD_NAME"));
					JSONArray recordList = map.get(crowdPackId);
					if(recordList!=null) {
						recordList.add(jsonObject);
					}else {
						recordList = new JSONArray();
						recordList.add(jsonObject);
					}
					map.put(crowdPackId, recordList);
					if(i==0) {
						crowdPackSql.append(crowdPackId,"?");
					}else {
						crowdPackSql.append(crowdPackId,",?");
					}
				}
				crowdPackSql.append(" ) ");
				crowdPackSql.append(" GROUP BY t4.CROWD_PACK_ID) t7 ON t6.CROWD_PACK_ID = t7.CROWD_PACK_ID AND t6.STRATEGY_LEVEL = t7.STRATEGY_LEVEL ");
				crowdPackSql.append("  ORDER BY t6.PRIORITY,t6.STRATEGY_ID ");
				logger.info("获取来电用户人群包,sql="+crowdPackSql.getSQL()+"{"+JSON.toJSONString(crowdPackSql.getParams())+"}");
				//只查询前4条
				query.setMaxRow(4);
				crowdPackList = query.queryForList(crowdPackSql.getSQL(), crowdPackSql.getParams(),new JSONMapperImpl());
				for (int i = 0; i < crowdPackList.size(); i++) {
					JSONObject record = crowdPackList.get(i);
					String serviceId = RandomKit.uniqueStr();
					record.put("SERVICE_ID", serviceId);
					String crowdPackId = record.getString("CROWD_PACK_ID");
					String serviceType = record.getString("SERVICE_TYPE");
					JSONArray recordList = map.get(crowdPackId);
					record.put("custInfo",recordList);
					if(Constants.CHANNEL_HOTLINE.equals(channelType)||Constants.CHANNEL_ONLINE.equals(channelType)) {
						UserModel user = OpenApiUserUtil.getUser(request);
						String userAcc = user.getUserAcc();
						String userName = user.getUserName();
						String areaCode = user.getAreaCode();
						DeptModel dept = DeptMgr.getDeptByDeptCode(areaCode, false);
						String areaName = dept.getDeptName();
						String deptName = user.getDeptName();
						String deptCode = user.getDeptCode();
						String currTime = EasyDate.getCurrentDateString();
						//埋点数据-只使用热线、在线
						EasyRecord summaryRecord = new EasyRecord("C_NO_AS_SERVICE_RECORD", "ID");
						summaryRecord.set("ID", RandomKit.randomStr());
						summaryRecord.set("CROWD_PACK_ID", crowdPackId);//人群包组id
						summaryRecord.set("CHANNEL_TYPE", channelType);//策略渠道
						summaryRecord.set("SERVICE_TYPE", serviceType);//服务类型
						summaryRecord.set("DATA_TYPE", Constants.SUMMARY_DATA_TYPE_QUERY);//埋点类型--1：查询  2；提交结果
						summaryRecord.set("DATE_ID", EasyDate.getCurrentDateString("yyyyMMdd"));//日期
						summaryRecord.put("CREATE_TIME", EasyDate.getCurrentDateString());
						summaryRecord.put("CUST_PHONE", custPhone);
						summaryRecord.put("CREATE_ACC", user.getUserAcc());
						query.save(summaryRecord);
						
						//埋点明细数据-只使用热线、在线
						EasyRecord serviceLogRecord = new EasyRecord("C_NO_AS_SERVICE_LOG", "ID");
						serviceLogRecord.set("ID", serviceId);
						serviceLogRecord.set("CROWD_PACK_ID", crowdPackId);//人群包id
						serviceLogRecord.set("CHANNEL_TYPE", channelType);//策略渠道
						serviceLogRecord.set("SERVICE_TYPE", serviceType);//服务类型
						serviceLogRecord.set("PRE_SERVICE_DATE", EasyDate.getCurrentDateString("yyyyMMdd"));//日期
						serviceLogRecord.put("CREATE_TIME", currTime);
						serviceLogRecord.put("AGENT_ACC", userAcc);
						serviceLogRecord.put("AGENT_NAME", userName);
						serviceLogRecord.put("AGENT_DEPT_CODE", deptCode);
						serviceLogRecord.put("AGENT_DEPT_NAME", deptName);
						serviceLogRecord.put("AGENT_AREA_CODE", areaCode);
						serviceLogRecord.put("AGENT_AREA_NAME", areaName);
						serviceLogRecord.put("CUST_PHONE", custPhone);
						serviceLogRecord.put("IS_HANDLE", 0);
						serviceLogRecord.put("CUST_PHONE", custPhone);
						query.save(serviceLogRecord);
					}
				}
			}
			result.put("data", crowdPackList);
			result.put("state", 1);
			
		} catch (Exception e) {
			logger.error("获取人群包请求失败,请求参数"+JSON.toJSONString(this.param)+",原因"+e.getMessage());
			result.put("state", 0);
		}

		return result;
	}
	
	
	
	
	/**
	 * 获取机器人列表，返回机器人名字、机器人ID
	 * @return
	 */
	@WebControl(name = "getRobotList",type = Types.DICT)
	public JSONObject getRobotList() {
		return null;
	}
	
}
