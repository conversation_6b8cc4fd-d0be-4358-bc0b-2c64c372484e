package com.yunqu.cc.activeService.dao;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.activeService.base.AppDaoContext;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.base.Constants;
import com.yunqu.cc.activeService.utils.ConfigUtil;
import com.yunqu.cc.activeService.utils.PhoneEncryptUtil;

@WebObject(name = "revisit")
public class RevisitDao extends AppDaoContext{

	private final Logger logger = CommLogger.logger;
	private EasyQuery getmarQuery(){
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.FRAME_DS);
	} 
	
	/**
	 * 处理列表
	 * @return
	 */
	@WebControl(name = "list",type = Types.LIST)
	public JSONObject list() {
		boolean f = false;
		EasySQL sql = new EasySQL("select t1.*,t2.name as pack_name from C_NO_AS_CROWD t1");
		sql.append("left join C_NO_AS_CROWD_PACK t2 on t2.ID = t1.CROWD_PACK_ID");
		sql.append("left join C_NO_AS_CROWD_PUBLISH t3 on t3.ID = t1.PUBLISH_ID");
		sql.append("where 1=1");
		sql.append(Constants.NO_PUBLISH,"and service_status != ?");
		sql.append(param.getString("SERVICE_TYPE"),"and t1.service_type = ?");
		sql.append(param.getString("RESULT"),"and t1.result_content = ?");
		sql.append(param.getString("REVISIT_RESULT"),"and t1.revisit_result = ?");
		sql.append(param.getString("CUST_PHONE"),"and t1.cust_phone = ?");
		sql.append(param.getString("SERVICE_ORDER_TYPE"),"and t1.service_order_type = ?");
		sql.append(param.getString("SERVICE_ORDER_CODE"),"and t1.service_order_code = ?");
		sql.append(param.getString("AGENT_ACC"),"and t1.agent_acc = ?");
		sql.append(param.getString("SERVICE_STATUS"),"and t1.service_status = ?");
		sql.append(param.getString("RESULT_START_TIME"),"and t1.result_time >= ?");
		sql.append(param.getString("RESULT_END_TIME"),"and t1.result_time <= ?");
		sql.append(param.getString("PUBLISH_START_TIME"),"and t3.publish_time >= ?");
		sql.append(param.getString("PUBLISH_END_TIME"),"and t3.publish_time <= ?");
		sql.appendLike(param.getString("CROWD_PACK_NAME"),"and t2.name like ?");
		//判断权限
		boolean flag = checkRole(UserUtil.getUser(request).getUserAcc(), "as_revisit_list_monitor");
		if(flag) {
			f = true;
			sql.append(param.getString("AGENT_ACC"),"and t1.agent_acc = ?");
		}else {
			sql.append(UserUtil.getUser(request).getUserAcc(),"and t1.agent_acc = ?");
		}
		sql.append("order by t3.publish_time,t1.cust_phone,t3.id desc");
		JSONObject data = queryForPageList(sql.getSQL(),sql.getParams(),new JSONMapperImpl() {
			@Override
			public JSONObject mapRow(ResultSet rs, int rowNum) {
				try {
					ResultSetMetaData meta = rs.getMetaData();
					int columnCount = meta.getColumnCount();
					String[] column = new String[columnCount];
					for (int i = 0; i < columnCount; i++)
						column[i] = meta.getColumnLabel(i + 1).toUpperCase();
					JSONObject row = new JSONObject();
					for (int j = 0; j < columnCount; j++) {
						String value = rs.getString(j + 1);
						if (value == null || "".equals("null"))
							value = "";
						if("CUST_PHONE".equals(column[j])) {
							value = PhoneEncryptUtil.getPhone(value);
						}
						row.put(column[j], value);
					}
					return row;
				} catch (Exception ex) {
					logger.error("EasyMapMapperImpl.mapRow() exception , cause:" + ex.getMessage());
					return null;
				}
			}
		});
		if(!f) {
			data.put("acc", UserUtil.getUser(request).getUserAcc());
		}
		return data;
	}
	
	/**
	 * 权限校验
	 * @param userAcc 账号
	 * @param resId 资源id
	 * @return
	 */
	private boolean checkRole(String userAcc,String resId) {
		boolean flag = false;
		EasySQL checkSql = new EasySQL("SELECT COUNT(1) FROM EASI_ROLE_USER T1,EASI_ROLE_RES T2 WHERE 1=1");
		checkSql.append("AND T1.ROLE_ID = T2.ROLE_ID");
		checkSql.append(resId,"AND RES_ID = ?",false);//权限id
		checkSql.append(userAcc,"AND T1.USER_ID = (SELECT USER_ID FROM EASI_USER_LOGIN WHERE USER_ACCT = ?)");
		try {
			int k = getmarQuery().queryForInt(checkSql.getSQL(), checkSql.getParams());
			if(k!=0) {
				flag = true;
			}
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "查询权限出错：" + e.getMessage(),e);
			return false;
		}
		return flag;
	}
	
	/**
	 * 通过ID获取策略信息
	 * @return
	 */
	@WebControl(name = "getHandleRecordInfo",type = Types.RECORD)
	public JSONObject getHandleRecordInfo() {
		
		if(StringUtils.isBlank(param.getString("channelType"))||StringUtils.isBlank(param.getString("crowdPackId"))) {
			return EasyResult.error();
		}
		//回显
		if("2".equals(param.getString("handleType"))) {
			JSONObject data = getCrowdInfo(param.getString("crowdId"),param.getString("crowdPackId"),param.getString("channelType"));
			return data;
		}
		//策略表
		String tableName = Constants.strategyTable.get(param.getString("channelType"));
		if(tableName==null) {
			return EasyResult.error();
		}
		//String custPhone = param.getString("custPhone");
		EasyResult result = new EasyResult();
		JSONObject data = new JSONObject();
		try {
			//查询一些需要的数据、手机号折算系数等
			EasyRow row = getQuery().queryForRow("select t1.cust_phone,t2.coefficient from C_NO_AS_CROWD t1"
					+ " left join c_no_convert t2 on t1.conversion_type = t2.id"
					+ " where t1.id = ?", new Object[] {param.getString("crowdId")});
			//查询此手机号上一次服务信息
			EasySQL lastServiceSQL = new EasySQL("select result_time,result_content,revisit_result,result_source from C_NO_AS_CROWD");
			lastServiceSQL.append("where 1=1");
			lastServiceSQL.append(row.getColumnValue("CUST_PHONE"),"and cust_phone = ?",false);
			lastServiceSQL.append(Constants.HAS_REVISITED,"and service_status = ?");
			lastServiceSQL.append("and result_time is not null");
			lastServiceSQL.append("order by result_time desc");
			List<JSONObject> lastServiceList = getQuery().queryForList(lastServiceSQL.getSQL(), lastServiceSQL.getParams(),new JSONMapperImpl());
			if(lastServiceList!=null&&lastServiceList.size()>0) {
				JSONObject obj = lastServiceList.get(0);
				result.put("tipObj",obj);
			}
			//获取人群包策略信息
			EasySQL sql = new EasySQL("select t1.*,t2.crowd_pack_group_id,t2.strategy_level,t2.PRIORITY from " + tableName + " t1");
			sql.append("left join C_NO_AS_CROWDPACK_PRIORITY t2 on t2.strategy_id = t1.id");
			sql.append("where 1=1");
			sql.append(param.getString("crowdPackId"),"and t2.crowd_pack_id = ?");
			sql.append("1","and t1.is_open = ?");
			sql.append("order by t2.strategy_level");
			List<JSONObject> list = getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			if(list==null||list.size()==0) {
				return EasyResult.error(500,"请检查策略是否配置或开启");
			}
			data = list.get(0);
			//获取结果选项
			EasySQL rSql = new EasySQL("select id,content,sort from C_NO_AS_STRATEGY_RESULT where 1=1");
			if("1".equals(data.getString("STRATEGY_LEVEL"))) {
				rSql.append(param.getString("crowdPackId"),"and crowd_pack_id = ?");
			}else if("2".equals(data.getString("STRATEGY_LEVEL"))) {
				rSql.append(data.getString("CROWD_PACK_GROUP_ID"),"and crowd_pack_group_id = ?");
			}
			rSql.append(param.getString("channelType"),"and channel_type = ?");
			rSql.append(data.getString("ID"),"and strategy_id = ?");
			rSql.append("1","and is_open = ?");
			rSql.append("ORDER BY SORT");
			List<JSONObject> rList = getQuery().queryForList(rSql.getSQL(), rSql.getParams(),new JSONMapperImpl());
			JSONArray rJSON = new JSONArray();
			for(int i=0;i<rList.size();i++) {
				JSONObject obj = rList.get(i);
				rJSON.add(obj);
			}
			data.put("RESULT", rJSON);
			data.put("realMobile",row.getColumnValue("CUST_PHONE"));
			data.put("coefficient",row.getColumnValue("COEFFICIENT"));
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "错误:" + e.getMessage(),e);
		}
		result.put("data", data);
		result.put("type", "RECORD");
		return result;
	}
	
	/**
	 * 通过服务类型获取服务结果
	 * @return
	 */
	@WebControl(name = "getServiceResult",type = Types.DICT)
	public JSONObject getServiceResult() {
		String serviceType = (String)getMethodParam(0);
		if(StringUtils.isBlank(serviceType)) {
			return EasyResult.error(500,"参数不完整");
		}
		EasySQL sql = new EasySQL("select DISTINCT content,content as id from c_no_as_strategy_result");
		sql.append("where 1=1");
		sql.append("1","and is_open = ?");
		if("1".equals(serviceType)) {//营销
			//sql.append(Constants.SERVICE_TYPE_MARKET,"and service_type = ?");
			sql.append(Constants.CHANNEL_MARKET,"and channel_type = ?");
		}else if("2".equals(serviceType)) {//主动
			//sql.append(Constants.SERVICE_TYPE_OUTBOUND,"and service_type = ?");
			sql.append(Constants.CHANNEL_OUTBOUND,"and channel_type = ?");
		}
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "getRobotResultList",type = Types.LIST)
	public JSONObject getRobotResultList() {
		String crowdPackId = param.getString("crowdPackId");
		String queryTotalNum = param.getString("queryTotalNum");
		String crowdPackName = "";
		boolean autoCallUnionFlag = true;
		boolean manualCallUnionFlag = true;
		if(StringUtils.isNotBlank(crowdPackId)){
			autoCallUnionFlag = false;
			manualCallUnionFlag = false;
			try {
				List<EasyRow> packList = this.getQuery().queryForList("select category,name from c_no_as_crowd_pack where id = ?", new Object[]{crowdPackId});
				for (EasyRow easyRow : packList) {
					String category = easyRow.getColumnValue("category");
					crowdPackName = easyRow.getColumnValue("name");
					if("1".equals(category)){
						autoCallUnionFlag=true;
					}else{
						manualCallUnionFlag=true;
					}
				}
			} catch (SQLException e) {
				logger.error("机器人查询回访记录，失败");
			}
		}
		EasySQL sql = new EasySQL();
		if(StringUtils.isNotBlank(queryTotalNum)){
			sql.append("select count(1) TOTAL_NUM from ( ");
		}
		if(manualCallUnionFlag){
			CrowdPackSql.getManualCallSql(sql,param,crowdPackName);
		}
		if(manualCallUnionFlag&&autoCallUnionFlag){
			sql.append("union all");
		}
		if(autoCallUnionFlag){
			CrowdPackSql.getAutoCallSql(sql,param,crowdPackName);
		}
		logger.info("sql:" + sql.getSQL() + " , param:" + JSON.toJSONString(sql.getParams()));
		String finalCrowdPackName = crowdPackName;
		if(StringUtils.isBlank(queryTotalNum)){
			if(StringUtils.isBlank(crowdPackName)){
				sql.append(" order by publish_time desc ");
			}
			return queryForPageList(sql.getSQL(), sql.getParams(),new JSONMapperImpl() {
				@Override
				public JSONObject mapRow(ResultSet rs, int rowNum) {
					try {
						ResultSetMetaData meta = rs.getMetaData();
						int columnCount = meta.getColumnCount();
						String[] column = new String[columnCount];
						for (int i = 0; i < columnCount; i++)
							column[i] = meta.getColumnLabel(i + 1).toUpperCase();
						JSONObject row = new JSONObject();
						for (int j = 0; j < columnCount; j++) {
							String value = rs.getString(j + 1);
							if (value == null || "".equals("null"))
								value = "";
							if("CUST_PHONE".equals(column[j])) {
								value = PhoneEncryptUtil.getPhone(value);
							}
							row.put(column[j], value);
						}
						if(StringUtils.isNotBlank(finalCrowdPackName) ) {
							row.put("CROWD_PACK_NAME", finalCrowdPackName);
						}
						return row;
					} catch (Exception ex) {
						logger.error("EasyMapMapperImpl.mapRow() exception , cause:" + ex.getMessage());
						return null;
					}
				}
			});
		}else{
			sql.append(" ) temp");
			return this.queryForRecord(sql.getSQL(), sql.getParams(),null);
		}

	}

	/**
	 * 获取发布记录
	 * @return
	 */
	@WebControl(name = "getPublishList",type = Types.LIST)
	public JSONObject getPublishList() {
		EasySQL sql = new EasySQL("select t1.*,t2.name as CROWD_PACK_NAME from c_no_as_crowd_publish t1");
		sql.append("left join C_NO_AS_CROWD_PACK t2 on t1.CROWD_PACK_ID = t2.id");
		sql.append("where 1=1");
		sql.appendLike(param.getString("publish_acc"),"and publish_acc like ?");
		sql.appendLike(param.getString("name"),"and t2.name like ?");
		sql.append(param.getString("conversion_type"),"and t1.conversion_type = ?");
		sql.append(param.getString("publish_start_time"),"and t1.publish_time >= ?");
		sql.append(param.getString("publish_end_time"),"and t1.publish_time <= ?");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 查询申请列表
	 * @return
	 */
	@WebControl(name = "getApplyList",type = Types.LIST)
	public JSONObject getApplyList() {
		EasySQL sql = new EasySQL("select * from C_NO_AS_APPLY_USER");
		sql.append(param.getString("id1"),"where publish_id = ?",false);
		sql.append("order by RECEIVE_NUM,ID");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 结果回显
	 * @param id 人群包明细id
	 * @param crowdPackId 人群包id
	 * @param channelType 营销/主动
	 * @return
	 */
	private JSONObject getCrowdInfo(String id,String crowdPackId,String channelType) {
		JSONObject result = new JSONObject();
		try {
			String col = "t1.CUST_NAME,t1.CUST_PHONE,t1.CUST_ADDRESS,t1.AREA_CODE,"
					+ "t1.PROD_NAME,t1.AREA_NAME,t1.RESULT_CONTENT,t1.REVISIT_RESULT,"
					+ "t2.ID,t2.result_content as tips,t2.result_time";
			EasySQL sql = new EasySQL("select "+col+" from c_no_as_crowd t1");
			sql.append("left join c_no_as_result t2 on t1.id = t2.crowd_id");
			sql.append(id,"where t1.id = ?");
			sql.append("order by t2.result_time desc");
			List<JSONObject> list = getQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl() {
				@Override
				public JSONObject mapRow(ResultSet rs, int rowNum) {
					try {
						ResultSetMetaData meta = rs.getMetaData();
						int columnCount = meta.getColumnCount();
						String[] column = new String[columnCount];
						for (int i = 0; i < columnCount; i++)
							column[i] = meta.getColumnLabel(i + 1).toUpperCase();
						JSONObject row = new JSONObject();
						for (int j = 0; j < columnCount; j++) {
							String value = rs.getString(j + 1);
							if (value == null || "".equals("null"))
								value = "";
							//20230525 要求详情无需脱敏
//							if("CUST_PHONE".equals(column[j])) {
//								value = PhoneEncryptUtil.getPhone(value);
//							}
							row.put(column[j], value);
						}
						return row;
					} catch (Exception ex) {
						logger.error("EasyMapMapperImpl.mapRow() exception , cause:" + ex.getMessage());
						return null;
					}
				}
			});
			if(list==null||list.size()==0) {
				return null;
			}
			JSONObject crowdData = list.get(0);
			//如果是主动外呼 还要查询出问卷答案
			if(Constants.CHANNEL_OUTBOUND.equals(channelType)) {
				JSONArray answerList = getQuestionnaireAnswer(crowdData.getString("ID"));
				result.put("answerList", answerList);
			}
			result.put("data", crowdData);
	
			String tableName = Constants.strategyTable.get(channelType);
			JSONObject data = new JSONObject();
			//获取人群包策略信息
			sql = new EasySQL("select t1.*,t2.crowd_pack_group_id,t2.strategy_level,t2.PRIORITY from " + tableName + " t1");
			sql.append("left join C_NO_AS_CROWDPACK_PRIORITY t2 on t2.strategy_id = t1.id");
			sql.append("where 1=1");
			sql.append(param.getString("crowdPackId"),"and t2.crowd_pack_id = ?");
			sql.append("1","and t1.is_open = ?");
			sql.append("order by t2.strategy_level");
			list = getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			if(list==null||list.size()==0) {
				return EasyResult.error(500,"请检查策略是否配置或开启");
			}
			data = list.get(0);
			//获取结果选项
			EasySQL rSql = new EasySQL("select id,content,sort from C_NO_AS_STRATEGY_RESULT where 1=1");
			if("1".equals(data.getString("STRATEGY_LEVEL"))) {
				rSql.append(crowdPackId,"and crowd_pack_id = ?");
			}else if("2".equals(data.getString("STRATEGY_LEVEL"))) {
				rSql.append(data.getString("CROWD_PACK_GROUP_ID"),"and crowd_pack_group_id = ?");
			}
			rSql.append(channelType,"and channel_type = ?");
			rSql.append(data.getString("ID"),"and strategy_id = ?");
			rSql.append("1","and is_open = ?");
			rSql.append("ORDER BY SORT");
			List<JSONObject> rList = getQuery().queryForList(rSql.getSQL(), rSql.getParams(),new JSONMapperImpl());
			JSONArray rJSON = new JSONArray();
			for(int i=0;i<rList.size();i++) {
				JSONObject obj = rList.get(i);
				rJSON.add(obj);
			}
			result.put("RESULT", rJSON);
			
			//查询是否可以修改                                                                  
			int validityDay = ConfigUtil.getInt("REVISIT_UPDATE_VALIDITY_DAY",30);
			String resultTime = crowdData.getString("RESULT_TIME");
			String currentTime = DateUtil.getCurrentDateStr();
			boolean updateFlag = DateUtil.bwDays(resultTime, currentTime, DateUtil.TIME_FORMAT) <= validityDay;
			result.put("updateFlag", updateFlag);
		} catch (Exception e) {
			logger.info(CommonUtil.getClassNameAndMethod(this) + "error:" + e.getMessage(),e);
		}
		return result;
	}
	
	/**
	 * 查询问卷答案
	 * @param resultId
	 * @return
	 */
	private JSONArray getQuestionnaireAnswer(String resultId) {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONArray newList = new JSONArray();
		try {
			EasySQL sql = new EasySQL("select * from c_no_as_question_result");
			sql.append("where 1=1");
			sql.append(resultId,"and result_id = ?",false);
			list = getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			//数据整理
			JSONObject data = new JSONObject();
			for(JSONObject obj:list) {
				JSONArray arr = new JSONArray();
				if(data.containsKey(obj.getString("QUESTION_ID"))) {
					Object object = data.get(obj.getString("QUESTION_ID"));
					if(object instanceof String) {
						arr.add(String.valueOf(object));
					}else {
						arr = data.getJSONArray(obj.getString("QUESTION_ID"));
						arr.add(obj.getString("ANSWER_CODE"));
					}
					data.put(obj.getString("QUESTION_ID"), arr);
					continue;
				}
				data.put(obj.getString("QUESTION_ID"), obj.getString("ANSWER_CODE"));
			}
			for(String questionId:data.keySet()) {	
				for(JSONObject obj:list) {
					if(questionId.equals(obj.getString("QUESTION_ID"))) {
						obj.put("ANSWER_CODE", data.get(questionId));
						newList.add(obj);
						break;
					}
					
				}
			}
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "error:" + e.getMessage(),e);
		}
		return newList;
	}
	
	/**
	 * 获取结果列表
	 * @return
	 */
	@WebControl(name = "getResultSelect",type = Types.RECORD)
	public JSONObject getResultSelect() {
		
		String channelType = param.getString("channelType");
		String crowdPackId = param.getString("crowdPackId");
		String tableName = Constants.strategyTable.get(channelType);
		JSONObject data = new JSONObject();
		//获取人群包策略信息
		EasySQL sql = new EasySQL("select t1.*,t2.crowd_pack_group_id,t2.strategy_level,t2.PRIORITY from " + tableName + " t1");
		sql.append("left join C_NO_AS_CROWDPACK_PRIORITY t2 on t2.strategy_id = t1.id");
		sql.append("where 1=1");
		sql.append(crowdPackId,"and t2.crowd_pack_id = ?");
		sql.append("1","and t1.is_open = ?");
		sql.append("order by t2.strategy_level");
		List<JSONObject> list = new ArrayList<>();
		try {
			list = getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
		} catch (SQLException e1) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "error:" + e1.getMessage(),e1);
		}
		if(list==null||list.size()==0) {
			return EasyResult.error(500,"请检查策略是否配置或开启");
		}
		data = list.get(0);
		//获取结果选项
		EasySQL rSql = new EasySQL("select id,content,sort from C_NO_AS_STRATEGY_RESULT where 1=1");
		if("1".equals(data.getString("STRATEGY_LEVEL"))) {
			rSql.append(crowdPackId,"and crowd_pack_id = ?");
		}else if("2".equals(data.getString("STRATEGY_LEVEL"))) {
			rSql.append(data.getString("CROWD_PACK_GROUP_ID"),"and crowd_pack_group_id = ?");
		}
		rSql.append(channelType,"and channel_type = ?");
		rSql.append(data.getString("ID"),"and strategy_id = ?");
		rSql.append("1","and is_open = ?");
		rSql.append("ORDER BY SORT");
		List<JSONObject> rList = new ArrayList<>();
		try {
			rList = getQuery().queryForList(rSql.getSQL(), rSql.getParams(),new JSONMapperImpl());
		} catch (SQLException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "error:" + e.getMessage(),e);
		}
		JSONArray rJSON = new JSONArray();
		for(int i=0;i<rList.size();i++) {
			JSONObject obj = rList.get(i);
			rJSON.add(obj);
		}
		return EasyResult.ok(rJSON);
	}
}
