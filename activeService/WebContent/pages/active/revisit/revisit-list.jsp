<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>主动外呼处理列表</title>
	<style>
		.container-fluid{
			overflow: visible!important;
		}
		.layui-layer-page .layui-layer-content{
			overflow: visible!important;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="searchForm" autocomplete="off" name="searchForm" autocomplete="off" class="form-inline" >
		<select class="hidden" id="revisitResultList" data-mars="Dict.getDict('CC_AS_REVISIT_RESULT')"></select>
		<select class="hidden" id="channelList" data-mars="Dict.getDict('CC_AS_CHANNEL')"></select>
		<div class="ibox">
			<div class="ibox-title clearfix">
				<div class="form-group">
					<h5>
						<span class="glyphicon glyphicon-list"></span> 主动外呼处理列表
					</h5>
				</div>
				<hr style="margin: 5px -15px">
				<div class="form-group">
					<div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width: 80px;">服务类型</span>	
						<select class="form-control input-sm" name="SERVICE_TYPE" onchange="changeServiceType($(this).val())">
							<option value="">请选择</option>
							<option value="1">营销外呼</option>
							<option value="2">主动外呼</option>
                    	</select>
					</div>
					<div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width: 80px;">外呼结果</span>
						<select class="form-control input-sm" name="RESULT" id="resultSelectL">
							<option value="">请选择</option>
                    	</select>
					</div>
					<div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width:80px">电话号码</span>	
						<input type="text" class="form-control input-sm" name="CUST_PHONE">
				    </div>
					<div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width:80px">人群包名称</span>	
						<input type="text" class="form-control input-sm" name="CROWD_PACK_NAME">
				    </div>
					<div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width:80px">坐席账号</span>	
                        <input type="text" class="form-control input-sm" name="AGENT_ACC" id="agentAcc">
				    </div>
					<div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width:80px">状态</span>
						<select class="form-control input-sm" name="SERVICE_STATUS" data-mars="Dict.getDict('CC_AS_REVISIT_STATUS')">
							<option value="">请选择</option>
                    	</select>
				    </div>
				    <div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width:80px">单据编码</span>	
                        <input type="text" class="form-control input-sm" name="SERVICE_ORDER_CODE">
				    </div>
				    <div class="input-group input-group-sm" style="width: 200px;">
						<span class="input-group-addon" style="width:80px">单据类型</span>
						<select class="form-control input-sm" id="serviceOrderType" name="SERVICE_ORDER_TYPE" data-mars="Dict.getDict('CC_AS_ORDER_TYPE')">
							<option value="">请选择</option>
                    	</select>
				    </div>
				    <div class="input-group input-group-sm">
						<span class="input-group-addon" style="width: 80px;">发布时间</span>	
						<input type="text" name="PUBLISH_START_TIME" id="select_syn_start_time" class="form-control input-sm Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})">
						<span class="input-group-addon" >-</span>	
						<input type="text" name="PUBLISH_END_TIME" id="select_syn_end_time" class="form-control input-sm Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})">
					</div>
				    <div class="input-group input-group-sm">
						<span class="input-group-addon" style="width: 80px;">服务时间</span>	
						<input type="text" name="RESULT_START_TIME" class="form-control input-sm Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})">
						<span class="input-group-addon" >-</span>	
						<input type="text" name="RESULT_END_TIME" class="form-control input-sm Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})">
					</div>
				    <div class="input-group input-group-sm">
				    	<button type="button" class="btn btn-sm btn-default ml-10" onclick="reload()">搜索</button>
				    </div>
					
				</div>

				<div class="form-group">
					<div class="input-group input-group-sm pull-left mr-10">
						<button type="button" class="btn btn-sm btn-success" onclick="revisit.apply()"><span class="glyphicon glyphicon-pawn"></span>申请</button>
					</div>
					<EasyTag:res resId="as_revisit_list_publish"> 
						<div class="input-group input-group-sm pull-left mr-10">
							<button type="button" class="btn btn-sm btn-success" onclick="revisit.publish()"><span class="glyphicon glyphicon-send"></span>发布</button>
						</div>
					 </EasyTag:res> 
					 <EasyTag:res resId="as_revisit_list_recycle"> 
						<div class="input-group input-group-sm pull-left mr-10">
							<button type="button" class="btn btn-sm btn-success" onclick="revisit.recycle()"><span class="glyphicon glyphicon-refresh"></span>回收</button>
						</div>
					 </EasyTag:res> 
					 <EasyTag:res resId="as_revisit_list_export"> 
						<div class="input-group input-group-sm pull-left mr-10">
							<button type="button" class="btn btn-sm btn-success" onclick="revisit.exportData()"><span class="glyphicon glyphicon-export"></span>导出</button>
						</div>
					 </EasyTag:res> 
					<!-- <div class="input-group input-group-sm pull-right">
						<button type="button" class="btn btn-sm btn-default" onclick="resetForm()">重置</button>
						
					</div> -->
				</div>
			</div>
			<div class="ibox-content">
				<div class="row table-responsive">
					<table class="table table-auto table-bordered table-hover table-condensed text-c" id="tableHead" data-mars="revisit.list">
						<thead>
							<tr>
								<th class="text-c">操作</th>
								<th class="text-c">姓名</th>
								<th class="text-c">电话区号</th>
								<th class="text-c">电话号码</th>
								<th class="text-c">区域</th>
								<th class="text-c">地址</th>
								<th class="text-c">品类</th>
								<th class="text-c">单据编码</th>
								<th class="text-c">单据类型</th>
								<th class="text-c">人群包名称</th>
								<th class="text-c">发布时间</th>
								<th class="text-c">服务类型</th>
								<th class="text-c">服务时间</th>
								<th class="text-c">外呼结果</th>
								<th class="text-c">回访结果</th>
								<th class="text-c">坐席名称</th>
								<th class="text-c">坐席账号</th>
								<th class="text-c">所属班级</th>
								<th class="text-c">所属区域</th>
								<th class="text-c">状态</th>
							</tr>
						</thead>
						<tbody id="dataList"></tbody>
					</table>
					<script id="list-template" type="text/x-jsrender">
						{{for list}}
							<tr>
								<td>
									{{if SERVICE_STATUS=="4"}}
										<a href="javascript:revisit.searchInfo('{{:ID}}','{{:SERVICE_TYPE}}','{{:CROWD_PACK_ID}}')">查看</a>
									{{else}}
										<a href="javascript:revisit.handle('{{:ID}}','{{:CROWD_PACK_ID}}','{{:SERVICE_TYPE}}','{{:CUST_NAME}}','{{:CUST_PHONE}}','{{:CUST_ADDRESS}}','{{:PROD_NAME}}','{{:CONVERSION_TYPE}}','{{:SERVICE_ORDER_CODE}}','{{:SERVICE_ORDER_TYPE}}')">处理</a>
									{{/if}}
								</td>
								<td>{{:CUST_NAME}}</td>
								<td>{{:AREA_CODE}}</td>
								<td>{{:CUST_PHONE}}</td>
								<td>{{:AREA_NAME}}</td>
								<td>{{:CUST_ADDRESS}}</td>
								<td>{{:PROD_NAME}}</td>
								<td>{{:SERVICE_ORDER_CODE}}</td>
								<td>{{dictFUN:SERVICE_ORDER_TYPE 'CC_AS_ORDER_TYPE'}}</td>
								<td>{{:PACK_NAME}}</td>
								<td>{{:PUBLISH_TIME}}</td>
								<td>{{service_type:SERVICE_TYPE}}</td>
								<td>{{:RESULT_TIME}}</td>
								<td>{{:RESULT_CONTENT}}</td>
								<td>{{dictFUN:REVISIT_RESULT 'CC_AS_REVISIT_RESULT'}}</td>
								<td>{{:AGENT_NAME}}</td>
								<td>{{:AGENT_ACC}}</td>
								<td>{{:AGENT_DEPT_NAME}}</td>
								<td>{{:AGENT_AREA_NAME}}</td>
								<td>{{dictFUN:SERVICE_STATUS 'CC_AS_REVISIT_STATUS'}}</td>
							</tr>
						{{/for}}					         
					</script>
				</div>
				<div class="row paginate" id="page">
					<jsp:include page="/pages/common/pagination.jsp">
						<jsp:param value="10" name="pageSize" />
					</jsp:include>
				</div>
			</div>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
	<script type="text/javascript">
		jQuery.namespace("revisit");
		requreLib.setplugs('wdate');//加载时间控件
		var revisitStatusJSON = {};
		var revisitResultJSON = {};
		var channelJSON = {};
		function reload() {
			$("#searchForm").searchData();
		}
		
		$(function() {
			var startDate = getTodayDate(-7);
			var endDate = getTodayDate();
			$("#select_syn_start_time").val(startDate + " 00:00:00");
			$("#select_syn_end_time").val(endDate + " 23:59:59");
			$("#searchForm").render({success : function(result) {
				var acc = result["revisit.list"].acc;
				$("#agentAcc").val(acc);
				revisitStatusJSON = result["Dict.getDict('CC_AS_REVISIT_STATUS')"].data;
				revisitResultJSON = result["Dict.getDict('CC_AS_REVISIT_RESULT')"].data;
				channelJSON = result["Dict.getDict('CC_AS_CHANNEL')"].data;
			}});
			
		});

		//发布
		revisit.publish = function() {
			popup.layerShow({
				type : 1,
				title : '发布',
				area : [ '550px', '450px' ],
			}, "${ctxPath}/pages/active/revisit/revisit-publish.jsp");
		}
		
		//查看详情
		revisit.searchInfo = function(id,serviceType,crowdPackId){
			if(serviceType==1){//营销
				popup.layerShow({
					type : 1,
					title : '查看',
					area : [ '750px', '680px' ],
					shadeClose:false
				}, "${ctxPath}/pages/active/revisit/revisit-handle-market.jsp",{crowdId:id,type:2,crowdPackId:crowdPackId});
			}else if(serviceType==2){//外呼 
				popup.layerShow({
					type : 1,
					title : '查看',
					area : [ '900px', '800px' ],
					shadeClose:false
				}, "${ctxPath}/pages/active/revisit/revisit-handle-callout.jsp",{crowdId:id,type:2,crowdPackId:crowdPackId});
 			}else{
				layer.msg("数据错误!",{icon:5});
			} 
		}
		
		//回收
		revisit.recycle = function() {
			popup.layerShow({
				type : 1,
				title : '回收',
				area : [ '550px', '400px' ],
			}, "${ctxPath}/pages/active/revisit/revisit-recycle.jsp");
		}
		
		//处理
		revisit.handle = function(id,crowdPackId,serviceType,custName,custPhone,custAddress,prodName,conversionType,serviceOrderCode,serviceOrderTypeCode) {
			var serviceOrderType = getText(serviceOrderTypeCode,"#serviceOrderType");
 			if(serviceType==1){//营销
				popup.layerShow({
					type : 1,
					title : '处理',
					area : [ '750px', '680px' ],
					shadeClose:false
				}, "${ctxPath}/pages/active/revisit/revisit-handle-market.jsp",{
					crowdId:id,
					crowdPackId:crowdPackId,
					custName:custName,
					custPhone:custPhone,
					custAddress:custAddress,
					prodName:prodName,
					conversionType:conversionType,
					serviceOrderCode:serviceOrderCode,
					serviceOrderType:serviceOrderType
				});
			}else if(serviceType==2){//外呼 
				popup.layerShow({
					type : 1,
					title : '处理',
					area : [ '900px', '800px' ],
					shadeClose:false
				}, "${ctxPath}/pages/active/revisit/revisit-handle-callout.jsp",{
					crowdId:id,
					crowdPackId:crowdPackId,
					custName:custName,
					custPhone:custPhone,
					custAddress:custAddress,
					prodName:prodName,
					conversionType:conversionType,
					serviceOrderCode:serviceOrderCode,
					serviceOrderType:serviceOrderType
				});
 			}else{
				layer.msg("数据错误!",{icon:5});
			} 
		}
		
		//数据导出
		revisit.exportData = function() {
			location.href = "${ctxPath}/servlet/revisit?action=exportRevisitData&"+ $("#searchForm").serialize();
		}
		
		
		//申请
		revisit.apply = function(){
			var data = form.getJSONObject("searchForm");
			ajax.remoteCall("${ctxPath}/servlet/revisit?action=apply",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		
		function resetForm() {
			document.searchForm.reset();
			var startDate = getTodayDate(-7);
			var endDate = getTodayDate();
			$("#select_syn_start_time").val(startDate + " 00:00:00");
			$("#select_syn_end_time").val(endDate + " 23:59:59");
			$("#searchForm").searchData();
		}
		
		function changeServiceType(val){
			$("#resultSelectL").data("mars","revisit.getServiceResult('"+val+"')");
			$("#resultSelectL").render();
		}
		
		$.views.converters("service_type", function(val) {
			if(val==1){
				return "营销外呼";
			}else if(val==2){
				return "主动外呼";
			}else{
				return "未知";
			}
		});
		
		function call(value,data, index, elem){
			if(value){
	   			//外呼
		  		parent.top.myCCbar.call(value,data,function(){
				    compUpgrade.changeParentStatus('call');
				    layer.msg("正在接通···",{icon:1,time:1000});
				    layer.close(index); 
			  	}
				,function(){
					   layer.msg("外呼失败，请重新拨打！",{icon:5,time:3000});
				});
	   		}
		}
		
		function cleanQuestionnaire(){
			
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>