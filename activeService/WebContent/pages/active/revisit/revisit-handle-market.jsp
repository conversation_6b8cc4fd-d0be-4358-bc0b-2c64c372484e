<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>营销处理</title>
	<style>
		.form-label.flb{
			margin-right:10px;
			min-width: 100px;
			text-align: right;
			line-height: 30px;
		}
		.page-header{
			margin: 0 0 10px 0;
		}
		.form-group.fg{
			margin-bottom: 5px!important;
			display: flex;
		}
		.form-group-d{
			line-height: 30px;
			color: #12a9d6;
		}
		.form-group-d span{
			cursor:pointer;
			margin-right: 10px;
		}
		.form-label-radio{
			margin-right: 30px;
			line-height:30px;
			cursor: pointer;
		}
		.input-group-addon{
			background: none;
			border: none;
			cursor: pointer;
		}
		.glyphicon.ge{
			font-size: 14px;
			color: #17a6f0;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="marketForm"  method="post"  autocomplete="off" data-mars="revisit.getHandleRecordInfo" >
		<input type="hidden" name="crowdId" value="${param.crowdId}">
		<input type="hidden" name="crowdPackId" value="${param.crowdPackId}">
		<input type="hidden" name="custPhone" value="${param.custPhone}">
		<input type="hidden" name="conversionType" value="${param.conversionType}">
		<input type="hidden" name="handleType" value="${param.type}">
		<input type="hidden" name="channelType" id="channelType" value="6">
		<input type="hidden" name="PRIORITY">
		<!-- 策略服务类型 -->
		<input type="hidden" name="SERVICE_TYPE">
		<div class="container-fluid">
			<div class="page-header">
				<h6>
					<span class="label label-info">客户信息</span>
					<span id="tips" style="float: right;color: red"></span>
				</h6>
			</div>
			<div class="col-xs-6">
				<div class="form-group fg">
					<label class="form-label flb">用户姓名</label>
					<input type="text" class="form-control input-sm" id="custName" readonly="readonly" value="${param.custName}">
				</div>
			</div>
			<div class="col-xs-6">
				<div class="form-group fg">
					<label class="form-label flb ">用户号码</label>
					<input type="text" class="form-control input-sm" id="realMobile" name="realMobile" readonly="readonly">
			        <span class="input-group-addon input-sm" style="padding-left: 15px;padding-right: 15px" onclick="beforeCallCheck()"><i class="glyphicon glyphicon-earphone ge"></i></span>
				</div>
			</div>
			<div class="col-xs-6">
				<div class="form-group fg">
					<label class="form-label flb">产品品类</label>
					<input type="hidden" class="form-control input-sm" id="prodCode">
					<input type="text" class="form-control input-sm" id="prodName" readonly="readonly" value="${param.prodName}">
				</div>
			</div>
			<div class="col-xs-6">
				<div class="form-group fg">
					<label class="form-label flb">区域</label>
					<input type="text" class="form-control input-sm" id="areaCode" readonly="readonly" value="${param.areaName}">
				</div>
			</div>
			<div class="col-xs-12">
				<div class="form-group fg">
					<label class="form-label flb">用户地址</label>
					<input type="text" class="form-control input-sm" id="custAddress" readonly="readonly" value="${param.custAddress}">
				</div>
			</div>
			<div class="page-header">
				<h6><span class="label label-info">营销内容</span></h6>
			</div>
			<div class="col-xs-12">
				<div class="form-group fg">
					<label class="form-label flb">脚本内容</label>
					<textarea name="SCRIPT_CONTENT"  rows="4" class="form-control input-sm" readonly="readonly"></textarea>
				</div>
			</div>
			<div class="col-xs-12">
				<div class="form-group fg">
					<label class="form-label flb">营销下单入口</label>
					<div class="form-group-d">
						<span onclick="market.openMarketForm()">洗悦家营销</span>
						<span onclick="market.openProdForm()">产品营销</span>
					</div>
				</div>
			</div>
			<div class="page-header">
				<h6><span class="label label-info">结果反馈</span></h6>
			</div>
			<div class="col-xs-12">
				<div class="form-group fg">
					<label class="form-label flb">备注</label>
					<textarea name="RESULT_CONTENT"  rows="3" class="form-control input-sm"></textarea>
				</div>
			</div>
			<div class="col-xs-12">
				<div class="form-group fg">
					<label class="form-label flb required">结果类型</label>
					<label class="radio-inline" style="line-height: 30px">
						<input type="radio" style="height: 20px;" name="resultType" value="0" onchange="changeResultType($(this).val())" checked> 有效回访
					</label>
					<label class="radio-inline" style="line-height: 30px">
						<input type="radio" style="height: 20px;" name="resultType" value="1" onchange="changeResultType($(this).val())"> 无效回访
					</label>
				</div>
			</div>
			<div class="col-xs-12">
				<div class="form-group fg">
					<label class="form-label flb required">结果选项</label>
					<select class="form-control input-sm" name="RESULT" id="marketResult" data-rules="required" onchange="changeResult()">
						<option value="">请选择</option>
					</select>
					<input type="hidden" name="RESULT_NAME" id="resultName">
				</div>
			</div>
		</div>
		<div class="layer-foot text-c">
			<button class="btn btn-sm btn-primary" id="confirmBtn" type="button" onclick="market.ajaxSubmitForm();">确认</button>
			<EasyTag:res resId="as_revisit_list_monitor">
				<button class="btn btn-sm btn-primary" id="updateBtn" style="display: none" type="button" onclick="market.update();">修改</button>
			</EasyTag:res>
			<button class="btn btn-sm btn-outline ml-20" type="button" id="smsSendBtn" onclick="openSmsPage()">发送短信</button>
			<button class="btn btn-sm btn-default ml-20" type="button" onclick="layer.closeAll();">关闭</button>
	    </div>
	</form>		
</EasyTag:override>
<EasyTag:override name="script">
	<script type = "text/javascript" src="${ctxPath}/static/js/common.js" ></script>
	<script type="text/javascript">
		jQuery.namespace("market");
		
		var _orgCode = "";
		var _coefficient = "";
		var marketingData = {};
		var smsOpen;
		var smsContent;
		var orgCode;
		var brandCode;
		$(function(){
    		$("#marketForm").render({success : function(result){
    			var data = result["revisit.getHandleRecordInfo"].data;
    			if($.isEmptyObject(data)){
    				layer.alert(result["revisit.getHandleRecordInfo"].msg,{icon:5},function(){
    					layer.closeAll();
    				});
    				return;
    			}
    			var tipObj = result["revisit.getHandleRecordInfo"].tipObj;
    			if(typeof(tipObj)!="undefined"){
    				var result = tipObj.RESULT_CONTENT;
    				if(result==""){
    					result = revisitResultJSON[tipObj.REVISIT_RESULT];
    				}
    				$("#tips").text("该号码最近一次服务时间："+tipObj.RESULT_TIME+"，渠道：" + channelJSON[tipObj.RESULT_SOURCE] + "，结果：" + result);
    			}
    			
    			if(data){
    				smsOpen = data.SMS_OPEN;
    				smsContent = data.SMS_CONTENT;
    				orgCode = data.ORG_CODE;
    				brandCode = data.BRAND_CODE;
    				if(smsOpen=='1'&&smsContent!=''){
    					$('#smsSendBtn').addClass("btn-info");
    				}else{
    					$('#smsSendBtn').attr("disabled",true);
    					$('#smsSendBtn').addClass("btn-default");
    				}
    	    		marketingData.customerMobilephone1 = data.realMobile;

    			}
    			
    			//回显走这部分
    			if($("input[name='handleType']").val()=="2"){
    				setCrowdInfo(result["revisit.getHandleRecordInfo"]);
    				return;
    			}

    			if(typeof(data.coefficient)!="undefined"){
    				_coefficient = data.coefficient;
    			}
    			//渲染营销结果
    			renderResultSelect(data);
    		}});
    		marketingData.customerName = $("#custName").val();
    		marketingData.customerAddress = $("#custAddress").val();
    		marketingData.areaCode = $("#areaCode").val();//区域
    		marketingData.prodName = $("#prodName").val();//品类
		})

		market.ajaxSubmitForm = function(){
			if(!form.validate("#marketForm")){
				return;
			}
			var data = form.getJSONObject("marketForm");
			ajax.remoteCall("${ctxPath}/servlet/revisit?action=handleMarket",data,function(result) { 
				if(result.state == 1){
					layer.closeAll();
					layer.msg(result.msg,{icon: 1});
					reload();
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		
		market.update = function(){
			if(!form.validate("#marketForm")){
				return;
			}
			var data = form.getJSONObject("marketForm");
			ajax.remoteCall("${ctxPath}/servlet/revisit?action=updateMarketSecond",data,function(result) { 
				if(result.state == 1){
					layer.closeAll();
					layer.msg(result.msg,{icon: 1});
					reload();
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		//打开洗悦家营销
		market.openMarketForm = function(){
			popup.layerShow({
		   		type:2,
		   		title:'营销',
		   		area:['1200px','900px'],
		   		shade:0,
		   		moveOut:true,
		   		maxmin:2,
		   		shadeClose:false
		   	},"/marketing/servlet/marketing?action=editResource",
		   	{mid:'',CUST_PHONE:$("#realMobile").val(),module:"1",channelSource:"3"});
		}
		//打开产品营销
		market.openProdForm = function(){
			var phone=$("#realMobile").val();
	 		popup.layerShow({
	 			type:2,
	 			title:'营销',
	 			area:['1020px','900px'],
	 			offset:'20px',
	 			shade:0,
	 			moveOut:true,
	 			maxmin:2,
	 			shadeClose:false
	 		},"/marketing/pages/productMarketing/product_marketing.jsp",
	 		{phone:phone,prodCode:""});	 
	 	}
		
		//修改结果选项时赋值name
		function changeResult(){
			var name = $("#marketResult option:checked").text();
			if(name=="请选择"){
				$("#resultName").val("");
				return;
			}
			if($("input[name='resultType']:checked").val()=="1"){
				return;
			}
			$("#resultName").val(name);
		}
		
		function changeResultType(val){
			$("#resultName").val("");
			var el = $("#marketResult");
			el.empty();
			if(val==0){//有效
				getResultSelect(function(result){
					var option = result;
	    			if(!$.isEmptyObject(option)){
		    			var tempHtml = '<option value="">请选择</option>';
		    			for(var i in option){
		    				tempHtml += "<option value='"+option[i].ID+"'>"+option[i].CONTENT+"</option>";
		    			}
		    			el.append(tempHtml);
	    			}
				});
			}else{//无效
				el.append('<option value="">请选择</option>');
				el.data("mars","Dict.getDict('CC_AS_REVISIT_RESULT')");
				el.render({success:function(result){
					$("#marketResult option").each(function(){
	    				if($(this).text()=="有效回访"){
	    					$(this).remove();

	    				}
	    			})
				}});
			}
		}
		
		//回显
		function setCrowdInfo(data){
			var record = data.data;
			$("#custName").val(record.CUST_NAME);
			$("#realMobile").val(record.CUST_PHONE);
			$("#prodName").val(record.PROD_NAME);
			$("#areaCode").val(record.AREA_CODE);
			$("#custAddress").val(record.CUST_ADDRESS);
			$("textarea[name='RESULT_CONTENT']").val(record.TIPS);
			$("#confirmBtn").remove();
			$("#smsSendBtn").remove();
			var selectList = data.RESULT;
			var result = record.RESULT_CONTENT;
			if(result==""){
				$("input[name='resultType']:last").attr("checked",true)
				result = revisitResultJSON[record.REVISIT_RESULT];
			}
			var select = $("#marketResult");
			select.empty();
			var tempHtml = '<option value="">请选择</option>';
			for(var i in selectList){
				if(result == selectList[i].CONTENT){
					tempHtml += "<option value='"+selectList[i].ID+"' selected = 'selected'>"+selectList[i].CONTENT+"</option>";
				}else{
					tempHtml += "<option value='"+selectList[i].ID+"'>"+selectList[i].CONTENT+"</option>";
				}
			}
			select.append(tempHtml);
			var flag = data.updateFlag;
			if(flag){
				$("#updateBtn").show();
			}
		}
		
		//接收洗悦家营销单号
		function setOrderId(orderId){
			console.log(orderId);
		}
		
		//结果渲染
		function renderResultSelect(data){
			var select = $("#marketResult")
			var option = data.RESULT;
			if(!$.isEmptyObject(option)){
				select.empty();//清空现有子元素
    			var tempHtml = '<option value="">请选择</option>';
    			for(var i in option){
    				tempHtml += "<option value='"+option[i].ID+"'>"+option[i].CONTENT+"</option>";
    			}
    			select.append(tempHtml);
			}
		}
		
		//结果获取
		function getResultSelect(cb){
			var param = {
				channelType:$("#channelType").val(),
				crowdPackId:$("input[name='crowdPackId']").val()
			}
			ajax.daoCall({controls: ['revisit.getResultSelect'],params:param},function(result){
				var data = result["revisit.getResultSelect"].data;
				if(data){
					cb&&cb(data);
				}else{
					layer.alert("服务结果列表获取失败，请联系管理员",{icon:5});
				}
			});
		}
		
		
		//外呼前校验
		function beforeCallCheck(){
			if($("input[name='handleType']").val()=="2"){
				layer.msg("已回访单无法再次拨打电话",{icon:5});
				return;
			}
			//根据用户手机号和渠道ID判断用户是否为敏感用户
			ajax.remoteCall("${ctxPath}/servlet/revisit?action=checkNumber",{"phone":$("#realMobile").val()},function(result){
				if (result.state === 1 && result.data.result.length > 1) {
					layer.confirm(result.data.result[0], {
						title: "提示",
						icon: 0,
						btn: ['继续回访', '取消回访', '更多信息']
						, btn3: function () {
							layer.confirm(result.data.result.join('<br/>'), {
								title: "更多详细信息",
								btn: ['继续回访', '取消回访'],
								btn1: function(index) {
									var mobile = $("#realMobile").val();
									var channelType = $("#channelType").val();
									var crowdId = $("input[name='crowdId']").val();
									ajax.remoteCall("${ctxPath}/servlet/revisit?action=beforeCallCheck",{mobile:mobile,channelType:channelType,crowdId:crowdId},function(result) {
										if(result.state == 1){
											toCall();
										}else{
											layer.alert(result.msg,{icon: 5});
										}
									});
									layer.close(index);
								}, btn2: function(index) {
									layer.close(index);
								}
							});
						}, btn1: function(index) {
							var mobile = $("#realMobile").val();
							var channelType = $("#channelType").val();
							var crowdId = $("input[name='crowdId']").val();
							ajax.remoteCall("${ctxPath}/servlet/revisit?action=beforeCallCheck",{mobile:mobile,channelType:channelType,crowdId:crowdId},function(result) {
								if(result.state == 1){
									toCall();
								}else{
									layer.alert(result.msg,{icon: 5});
								}
							});
							layer.close(index);
						}, btn2: function(index) {
							layer.close(index);
						}
					});
				} else if (result.state === 1 && result.data.result.length === 1) {
					layer.confirm(result.data.result[0], {
						title: "提示",
						icon: 0,
						btn: ['继续回访', '取消回访']
						, btn1: function(index) {
							var mobile = $("#realMobile").val();
							var channelType = $("#channelType").val();
							var crowdId = $("input[name='crowdId']").val();
							ajax.remoteCall("${ctxPath}/servlet/revisit?action=beforeCallCheck",{mobile:mobile,channelType:channelType,crowdId:crowdId},function(result) {
								if(result.state == 1){
									toCall();
								}else{
									layer.alert(result.msg,{icon: 5});
								}
							});
							layer.close(index);
						}, btn2: function(index) {
							layer.close(index);
						}
					});
				} else {
					var mobile = $("#realMobile").val();
					var channelType = $("#channelType").val();
					var crowdId = $("input[name='crowdId']").val();
					ajax.remoteCall("${ctxPath}/servlet/revisit?action=beforeCallCheck",{mobile:mobile,channelType:channelType,crowdId:crowdId},function(result) {
						if(result.state == 1){
							toCall();
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				}
			});

		}
		
		
		function toCall(){
			var _custPhone = $("#realMobile").val();
	    	if(_custPhone.substring(0,1) != "1"&&_custPhone.length>11){
	    		layer.confirm('该手机号码可能出错，是否继续', {
					 btn : [ '确定', '取消' ]//按钮
			        }, function(index) {
			        	layer.closeAll();
				    	telephoneTemp_callPhone(_custPhone);
			        });
	    	}else{
		    	telephoneTemp_callPhone(_custPhone);
	    	}
		}
		function telephoneTemp_callPhone(phone){
			layer.prompt({
				formType: 0, 
				shade : false ,
				title: ['请确认号码','color:red'],
				value:phone,
				area: ['500px', '300px'] ,
				offset: '150px',btn:['拨打','取消']
			},function(value,userData, index, elem){
				Isexistunitmanagertel(value,userData, index, elem);
			});
		}
		var uids = "";
		function Isexistunitmanagertel(value,userData, index, elem){
			var uid = guid();
			uids = uids=="" ? uid:uid+";"+uids;
			_orgCode = _orgCode=="" ? "CS006":_orgCode
			var userData={ICC_BUSI_TYPE:'09', ICC_BUSI_ID:uid,ICC_COEFFICIENT:_coefficient,ICC_DIVISION_ID:_orgCode};
			/*  var areaNum=$("#areaNum").val();
	    	if(areaNum!=null&&areaNum!=""&&value.length <= 8){
	    		value=areaNum+value;
	    	} */
	    	value = $("#realMobile").val();
			call(value,userData, index, elem);
			layer.close(index);
		}
		function guid() {//生成uuid
	        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
	            var r = Math.random()*16|0, v = c == 'x' ? r : (r&0x3|0x8);
	            return v.toString(16);
	        });
	    }
		
		function openSmsPage() {
	   		parent.popup.layerShow({type:2,title:'发送短信',area:['860px','400px'],offset:'100px',shade:0,maxmin:2,moveOut:true,shadeClose:false,url:"${ctxPath}/pages/crowdPack/send-message.jsp",data:{orgCode:orgCode,brandCode:brandCode,custPhone:$("#realMobile").val(),content:smsContent}});
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>