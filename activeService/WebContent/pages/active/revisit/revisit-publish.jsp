<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>发布</title>
	<style></style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="publishForm"  method="post" autocomplete="off" data-mars="common.getDeadline">
		<table class="table table-edit table-vzebra mt-10" style="padding: 0px 30px 0px 30px;">
	    	<tbody>
	        	<tr>
                	<td width="100px">发布类型</td>
                    <td>
                        <select name="PUBLISH_TYPE" id="publish_type"  class="form-control input-sm"   onchange="publish.changePublishType(this);">
                        	<option value="0">全部发布</option>
                        	<option value="1">部分发布</option>
                        </select>
                    </td>
				</tr>
                <tr>
                    <td>分配人数</td>
                	<td><input type="text" name="PUB_USER_NUM" id="pulish_num" class="form-control input-sm" placeholder="人工外呼：坐席人数，自动外呼：需发布的任务数" readonly="readonly" onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" onblur="this.v();"></td>
                </tr>
                <tr>
					<td class="required">外呼方式</td>
            		<td>
            			<select class="form-control input-sm" name="CALLOUT_TYPE" id="callOutType" onchange="changeCrowdData()" data-rules="required">
                           <option value="1">人工外呼</option>
                           <option value="2">自动外呼</option>
                       </select>
					</td>
				</tr>
				<tr id="averageTR">
                    <td>人均任务数</td>
                	<td><input type="text" name="USER_AVE_NUM" id="averageNum" class="form-control input-sm" placeholder="人均外呼任务数，不填则默认为50" value="100" onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" onblur="this.v();"></td>
                </tr>
				<tr>
					<td class="required">服务类型</td>
            		<td>
            			<select class="form-control input-sm" name="SERVICE_TYPE" id="serviceTypeP" onchange="changeCrowdData()" data-rules="required">
                           <option value="1">营销外呼</option>
                           <option value="2">主动外呼</option>
                       </select>
					</td>
				</tr>
				<tr>
					<td class="required">人群包<span class="glyphicon glyphicon-question-sign" aria-hidden="true" data-toggle="tooltip" data-placement="top" title="展示配置了人工外呼/回访策略或机器人外呼策略的人群包"></span></td>
            		<td>
            			<div id="crowdSelect"></div>
            			<input type="hidden" id="crowdPackId" name="CROWD_PACK_ID" onchange="publish.getRevisitCount()">
					</td>
				</tr>
            	<tr>
                  	<td class="required">折算类型</td>
	                <td>
	                	<select class="form-control input-sm" name="CONVERSION_TYPE" data-rules="required" data-mars="common.getConvert">
	                        <option value="">请选择</option>
						</select>
	                </td>
                </tr>
				<tr>
	            	<!-- 统计数 -->
					<th colspan="4" >
						<div>
		                 	<div style="float: left;padding-left: 10px">截止时间：<span id="deadline" style="color: blue"></span>(分钟)</div>
		                	<div style="padding-right: 10px" >任务数：<span style="color: red"  id="countContent">0</span></div>
						</div>
		        	</th>
				</tr>
			</tbody>
		</table>  
		<div class="layer-foot text-c">
			<button class="btn btn-sm btn-primary"  type="button" onclick="publish.ajaxSubmitForm()">确认</button>
			<button class="btn btn-sm btn-default ml-20"  type="button" onclick="layer.closeAll();">关闭</button>
	    </div>
	</form>		
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/js/xm-select.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/common.js" ></script>
	<script type="text/javascript">
		jQuery.namespace("publish");
		$(function(){
    		$("#publishForm").render({success : function(result){
    			if(result["common.getDeadline"]){
	    			$("#deadline").html(result["common.getDeadline"].data.TIME)
	    		}
    			
    		}});
    		$('[data-toggle="tooltip"]').tooltip();
    		changeCrowdData();
		});
		
		//人群包下拉框
		var crowdSelect = xmSelect.render({
			el: '#crowdSelect',
			size: 'small',
			radio:true,
			clickClose:true,
			filterable: true,
			paging: true,
			pageSize: 5,
			data: [],
			model: {
				label: {
					type: 'block',
					block: {
						//最大显示数量, 0:不限制
						showCount: 1,
						//是否显示删除图标
						showIcon: true,
					}
				}
			},
			on: function(data){//监听选择框
				if(data.change.length!=0){
					$("#crowdPackId").val(data.change[0].value).change();
				}
			}
		})
		
		
		$("#publishForm").on("change","input[class='form-control input-sm'],select[class='form-control input-sm']",function(){
			 publish.getRevisitCount();
		});
		
	    publish.changePublishType = function(data){
			if(data.value=="1"){//部分发布
				$("#pulish_num").removeAttr('readonly')
	 		}else{
	 			$("#pulish_num").val('');
	 			$("#pulish_num").attr('readonly','readonly')
			}
		}

		//获取任务数量
	    publish.getRevisitCount = function(){
			var data = form.getJSONObject("publishForm");
			if(!$("#crowdPackId").val()){
				return;
			}
			ajax.remoteCall("${ctxPath}/servlet/revisit?action=getRevisitCount",data,function(result) { 
				if(result.state == 1){
					var count = result.data.NUM || 0;
					$("#countContent").html(count);
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
	    
		publish.ajaxSubmitForm = function(){
			if($("#publish_type").val()=="1"){//部分发布
				if($("#pulish_num").val()==""){
					layer.alert("可分配人数不为空",{icon: 5});
			 		return;	
				}
			}
			if(!form.validate("#publishForm")){
				return;
			}
			var data = form.getJSONObject("publishForm");
			ajax.remoteCall("${ctxPath}/servlet/revisit?action=publish",data,function(result) { 
				if(result.state == 1){
					layer.closeAll();
					layer.msg(result.msg,{icon: 1});
					reload();
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		
		function changeCrowdData(){
			$("#crowdPackId").val("");
			var serviceType = $("#serviceTypeP").val();
			var callOutType = $("#callOutType").val();
			if(callOutType == 1){
				$("#averageNum").val("100");
				$("#averageTR").show();
			}else{
				$("#averageNum").val("");
				$("#averageTR").hide();
			}
			ajax.remoteCall("${ctxPath}/webcall?action=crowdPackDao.getCrowdPackListByRevisit",{
    			pageIndex:1,pageSize:5,pageType:3,callOutType:callOutType,serviceType:serviceType},function(result) {
    			var crowdList = [];
    			if(result.data&&result.data.length>0){
    				var totalPage = 0;
					var json = result.data;
					for(var key in json){
						var imp = {
							name:json[key].NAME,
							value:json[key].ID,
						}
						crowdList.push(imp);
					}
    				if(result.totalPage>0){
    					totalPage = result.totalPage;
    				}
   				}
    			crowdSelect.update({
					data: crowdList,
				});
   			});
		}

	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>