<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>档案回访主动服务</title>
	<style>
		.container-fluid{
			padding: 0px 15px;
			background-color: #fff;
		}
		.info-title p{
			width: 100px;
		}
		.float-right{
			float:right;
		}
		.gray-bg{
			background-color: #fff;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<div class="crowdPackList" id="crowdPackList" style="margin:-15px">
		<script id="crowd_pack_list_template" type="text/x-jsrender">
		{{for}}
		<div class="crowdPack">
		  <div style="border-bottom: thin #e8eaec solid;cursor:pointer;height:30px" onclick="toggleMore(this)" >
	  		   <h3 style="font-size: 20px;">
				<span class="label label-info">{{:CROWD_PACK_NAME}}</span>
			    <a type="button" class="moreBtn btn btn-sm btn-link float-right" title="展开"> <span class="glyphicon glyphicon-menu-down glyphicon glyphicon-menu-up"></span>&nbsp;</a>
			   </h3>
	      </div>
	      <div class="info">
		   <form id="crowdPackForm-{{:CROWD_PACK_ID}}" name="crowdPackForm-{{:CROWD_PACK_ID}}" method="post"  autocomplete="off" data-mars-prefix="" >
			   <input type="hidden" name ="channelType" value="4">
			   <input type="hidden" id ="smsContent-{{:CROWD_PACK_ID}}" value="{{:SMS_CONTENT}}">
			   <input type="hidden" id ="scriptContent-{{:CROWD_PACK_ID}}" value="{{:SCRIPT_CONTENT}}">
 			   <input type="hidden" name ="crowdPackId" value="{{:CROWD_PACK_ID}}"/>
			   <input type="hidden" name ="priority" value="{{:PRIORITY}}"/>
			   <input type="hidden" name ="serviceType" value="{{:SERVICE_TYPE}}">
			   {{for custInfo}}
					<input type="hidden" name ="crowdIds" value="{{:CROWD_ID}}">
			   {{/for}}
			   <table class="table table-edit" style="margin-bottom: 0px;">
	                   <tbody>
	                   	<tr>
	                  		 <td width="80px">客户姓名</td>
						 	 <td width="250px"><input type="text" class="form-control input-sm" readonly="readonly" value="{{:custInfo[0].CUST_NAME}}"></td>
							 <td width="80px"><a href="#" onmouseout="revisitService.hideAllText()" onmouseover="revisitService.showAllText(this,{{if SCRIPT_OPEN=='1'&&SCRIPT_CONTENT!=''}}'#scriptContent-{{:CROWD_PACK_ID}}'{{/if}})" >话术脚本<i class="glyphicon glyphicon-search"></i></a></td>
			                 <td rowspan="2" clospan="4"><textarea class="form-control input-sm" rows="4" cols="">{{if SCRIPT_OPEN=='1'}}{{:SCRIPT_CONTENT}}{{/if}}</textarea></td>
	                   	</tr>
	                   	<tr>
	                         <td width="80px">电话号码</td>
						 	 <td width="250px"><input type="text" name="custPhone" class="form-control input-sm" readonly="readonly" value="{{:custInfo[0].CUST_PHONE}}"></td>
	                   	</tr>
	                   	<tr>
	                  		 <td width="80px">产品品类</td>
	           				 <td width="250px"><input type="text" class="form-control input-sm" readonly="readonly" value="{{prodNames:custInfo}}"></td>
							{{if SMS_OPEN=='1'&&SMS_CONTENT!=''}}
							 <td width="80px"><a href="#" onmouseout="revisitService.hideAllText()" onmouseover="revisitService.showAllText(this,{{if SMS_OPEN=='1'&&SMS_CONTENT!=''}}'#smsContent-{{:CROWD_PACK_ID}}'{{/if}})" >短信内容<i class="glyphicon glyphicon-search"></i></a></td>
			              	 <td rowspan="2" clospan="4"><textarea class="form-control input-sm" rows="4" cols="" >{{if SMS_OPEN=='1'}}{{:SMS_CONTENT}}{{/if}}</textarea></td>
							{{/if}}
	                   	</tr>
	                   	<tr>
	                  		 <td width="80px">服务结果</td>
	                         <td width="250px">
						 		<select id="serviceResult-{{:CROWD_PACK_ID}}" name="serviceResult"  data-rules="required" class="form-control input-sm" >
		                           	<option value="">请选择</option>
	                            </select>
	                         </td>
	                   	</tr>
						<tr>
						     <td></td>
	                  		 <td align="center">
								<button class="btn btn-info btn-sm btn-outline"  type="button" id="submitBtn_{{:CROWD_PACK_ID}}" onclick="revisitService.ajaxSubmitForm('{{:CROWD_PACK_ID}}')">保存</button>
							 </td>
							{{if SMS_OPEN=='1'&&SMS_CONTENT!=''}}
							 <td></td>
	                         <td align="center">
						 		<button class="btn {{if SMS_OPEN=='1'&&SMS_CONTENT!=''}}btn-info{{else}}btn-default{{/if}}  btn-sm btn-outline" {{if SMS_OPEN!='1'||SMS_CONTENT==''}}disabled{{/if}} type="button" onclick="revisitService.openSmsPage('{{:ORG_CODE}}','{{:BRAND_CODE}}','{{:custInfo[0].CUST_PHONE}}','{{:CROWD_PACK_ID}}')">发送短信</button>
	                         </td>
							{{/if}}
	                   	</tr>
	                   </tbody>
	             </table>
	        	<p class=" text-c">
		   			
		   			
	   	 		</p>
			</form>		
			</div>
	   	 </div>
		{{/for}}
		</script>
   	 </div>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	    jQuery.namespace("revisitService")	
	    $(function(){
	    	if(crowdPackList&&crowdPackList.length>0){
	    		itemontentHtml = $.templates("#crowd_pack_list_template").render(crowdPackList);
	    		$("#crowdPackList").html(itemontentHtml);
	    		for(var i in crowdPackList){
	    			revisitService.getStrategyResultList(crowdPackList[i].STRATEGY_ID,crowdPackList[i].CROWD_PACK_ID);
	    		}
	    		//默认展示第一个
	    		var infoObjList = $('.crowdPackList').find(".info");
	    		for(var i=0;i<infoObjList.length;i++){
	    			if(i>0){
	    				$(infoObjList[i]).slideToggle('fast');
	    			}
	    		}
	    	}else{
	    		$("#crowdPackList").append('<div class="nodata text-center" id="nodata" ><img src="/easitline-static/images/nodata.png"/></div>');
	    	}
	    })
	    function toggleMore(el){
			var btn = $(el).find(".glyphicon");
			$(el).parents(".crowdPack").find(".info").slideToggle('fast');
			btn.toggleClass("glyphicon glyphicon glyphicon-menu-up")
		}
	    revisitService.showAllText = function(obj,textId){
	    	var text= $(textId).val();
	    	if(!text){
	    		text = "无"
	    	}
			layer.tips(text,$(obj),{tips:[1, '#0FA6D8'],time:0,area:['250px','auto']});
	    }
	    revisitService.hideAllText = function(obj) { 
	 		layer.closeAll('tips');
	 	}
	    
	    $.views.converters("prodNames", function(array) {
	    	var prodNames = "";
	    	for(var i in array){
	    		var info = array[i];
	    		var prodName = info.PROD_NAME;
	    		if(prodName){
		    		if(i==0){
			    		prodNames += prodName;
		    		}else{
			    		prodNames += "|"+prodName;
		    		}
	    		}
	    	}
			return prodNames;
		}); 
	    
	    revisitService.ajaxSubmitForm = function(crowdPackId){
	    	var formId = "crowdPackForm-"+crowdPackId;
			if(!form.validate('#'+formId)){
				return;
			}
			var crowdIds = $('#'+formId+" input[name='crowdIds']").map(function () {
			     return $(this).val();
			}).get();
			var data = form.getJSONObject(formId);
			data['crowdIds']=crowdIds;
			data['serviceResultName']=$('#'+formId+" select[name='serviceResult']").find("option:selected").text();
			ajax.remoteCall("/activeService/servlet/strategy?action=submitServiceResult",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1});
					$("#submitBtn_"+crowdPackId).attr("disabled",true);
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}

	    revisitService.openSmsPage= function(orgCode,brandCode,custPhone,crowdPackId) {
	    	var content = $("#smsContent-"+crowdPackId).val();
	   		parent.popup.layerShow({type:2,title:'发送短信',area:['860px','400px'],offset:'100px',shade:0,maxmin:2,moveOut:true,shadeClose:false,url:"/activeService/pages/crowdPack/send-message.jsp",data:{orgCode:orgCode,brandCode:brandCode,custPhone:custPhone,content:content}});
		}
	    
	    revisitService.getStrategyResultList= function(strategyId,crowdPackId){
		    ajax.daoCall({
					params:{'param[0]':strategyId,'param[1]':'4'},
					controls: ['strategyResult.getStrategyEnableResultList'],
				},function(result) {
					var result = result['strategyResult.getStrategyEnableResultList'];
					var data = result.data;
					if(data){
						var tempHtml = '';
						for(var o in data){  
							var optionText = data[o];
							tempHtml+=("<option data-fetch value='"+o+"'>"+optionText+"</option>");
						}
						$("#serviceResult-"+crowdPackId).append(tempHtml);
					}else{
						console.log("策略反馈结果列表为空");
					}
				},{contextPath: '/activeService'});
	    }
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>