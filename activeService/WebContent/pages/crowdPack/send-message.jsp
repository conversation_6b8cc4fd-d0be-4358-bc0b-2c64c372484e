<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>短信发送</title>
	<style>
	div{
	    margin-bottom: 10px;
   		 font-size: 16px;
	}
	fieldset div p{
	float:right;
	}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form id="messageForm" data-mars="" method="post"  autocomplete="off" data-mars-prefix="" >
			  <input type="hidden" name="orgCode" value="${param.orgCode }">
			  <input type="hidden" name="brandCode" value="${param.brandCode }">
			  <table class="table  table-edit mt-10" >
                    <tbody>
	                     <tr>
	                        <td width="20px">用户号码</td>
                        	<td width="220px">
                        		<input data-rules="required"  name="smsPhone" class="form-control input-sm" value="${param.custPhone}">
                        	</td>
	                     </tr>
	                     <tr>
                            <td>短信内容</td>
	                        <td colspan="3">
	                            <textarea data-rules="required"  name="smsContent" id="smsContent" class="form-control input-sm" rows="6">${param.content}</textarea>
	                        </td>
	                     </tr>
                   </tbody>
              </table>
			  <div class="layer-foot text-c">
			   		<button class="btn btn-sm btn-primary"  type="button" onclick="messageForm.sendRemindSms()">发送</button>
			   		<button class="btn btn-sm btn-default ml-20"  type="button"  onclick="messageForm.close()">取消</button>
			  </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("messageForm");
		$(function() {
			$("#messageForm").render({success:function(result){
		 		
			}});
		 })
		messageForm.sendRemindSms=function(){
	    	if(!form.validate("#messageForm")){
				return;
			}
		 	var data = form.getJSONObject("messageForm");
		 	if (data) {
		 		ajax.remoteCall("${ctxPath}/servlet/common?action=sendSmsMessage",data,function(result) { 
					//数据校验
					if(result.state == 1){
						 layer.msg('发送成功',{icon:1,time:1200},function(){
						 parent.layer.closeAll();
						 layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});	
			}
	    }
		messageForm.close = function() {
			var index = parent.layer.getFrameIndex(window.name);
			parent.layer.close(index);
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>