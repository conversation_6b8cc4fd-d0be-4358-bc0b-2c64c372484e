<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>热线来电主动服务</title>
	<style>
		.container-fluid{
			padding: 0px 15px;
			background-color: #fff;
		}
		.info-title p{
			width: 100px;
		}
		.float-right{
			float:right;
		}
		.gray-bg{
			background-color: #fff;
		}
		.info-title p {
    		width: auto;
    		white-spacing:nowrap;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<div class="crowdPackList" id="crowdPackList" style="margin:-5px">
		<script id="crowd_pack_list_template" type="text/x-jsrender">
		{{for}}
		<div class="crowdPack">
		  <div style="border-bottom: thin #e8eaec solid;cursor:pointer;height:30px;margin-top:5px;" onclick="toggleMore(this)" >
	  		   <h3 style="font-size: 20px;">
				<span class="label label-info">{{:CROWD_PACK_NAME}}</span>
			    <a type="button" class="moreBtn btn btn-sm btn-link float-right" title="展开"> <span class="glyphicon glyphicon-menu-down glyphicon glyphicon-menu-up"></span>&nbsp;</a>
			   </h3>
	      </div>
	      <div class="info">
		   <form id="crowdPackForm-{{:CROWD_PACK_ID}}" name="crowdPackForm-{{:CROWD_PACK_ID}}" method="post"  autocomplete="off" data-mars-prefix="" >
			   <input type="hidden" name ="channelType" value="1"/>
			   <input type="hidden" name ="serviceId" value="{{:SERVICE_ID}}"/>
			   <input type="hidden" name ="serviceType" value="{{:SERVICE_TYPE}}"/>
			   <input type="hidden" name ="crowdPackId" value="{{:CROWD_PACK_ID}}"/>
			   <input type="hidden" name ="priority" value="{{:PRIORITY}}"/>
 			   <input type="hidden" id ="smsContent-{{:CROWD_PACK_ID}}" value="{{:SMS_CONTENT}}"/>
			   <input type="hidden" id ="scriptContent-{{:CROWD_PACK_ID}}" value="{{:SCRIPT_CONTENT}}"/>
			   <input type="hidden" name="custPhone" value="{{:custInfo[0].CUST_PHONE}}"/>
			   {{for custInfo}}
					<input type="hidden" name ="crowdIds" value="{{:CROWD_ID}}">
			   {{/for}}
			   <table class="table table-edit" style="margin-bottom: 0px;">
	                   <tbody>
	                   	<tr>
	                  		 <td>客户姓名</td>
						 	 <td><input type="text" class="form-control input-sm" readonly="readonly" value="{{:custInfo[0].CUST_NAME}}"></td>
	                   	</tr>
	                   	<!--<tr>
	                         <td>电话号码</td>
						 	 <td><input type="text" class="form-control input-sm" readonly="readonly" value="{{:custInfo[0].CUST_PHONE}}"></td>
	                   	</tr>-->
	                   	<tr>
	                  		 <td>产品品类</td>
	           				 <td><input type="text" class="form-control input-sm" readonly="readonly" value="{{prodNames:custInfo}}"></td>
	                   	</tr>
	                    <tr>
	                      <td><a href="#" onmouseout="hotlineService.hideAllText()" onmouseover="hotlineService.showAllText(this,{{if SCRIPT_OPEN=='1'&&SCRIPT_CONTENT!=''}}'#scriptContent-{{:CROWD_PACK_ID}}'{{/if}})" >话术脚本<i class="glyphicon glyphicon-search"></i></a></td>
			              <td><textarea class="form-control input-sm" rows="5"  cols="">{{if SCRIPT_OPEN=='1'}}{{:SCRIPT_CONTENT}}{{/if}}</textarea></td>
	                    </tr>
						{{if SMS_OPEN=='1'&&SMS_CONTENT!=''}}
	                    <tr>
	                      <td><a href="#" onmouseout="hotlineService.hideAllText()" onmouseover="hotlineService.showAllText(this,{{if SMS_OPEN=='1'&&SMS_CONTENT!=''}}'#smsContent-{{:CROWD_PACK_ID}}'{{/if}})" >短信内容<i class="glyphicon glyphicon-search"></i></a></td>
			              <td><textarea class="form-control input-sm" rows="5"  cols="">{{if SMS_OPEN=='1'}}{{:SMS_CONTENT}}{{/if}}</textarea></td>
	                    </tr>
						{{/if}}
	                   	<tr>
	                  		 <td>服务结果</td>
	                         <td>
						 		<select name="serviceResult"  data-rules="required" class="form-control input-sm" data-mars="strategyResult.getStrategyEnableResultList('{{:STRATEGY_ID}}','1')">
		                           	<option value="">请选择</option>
	                            </select>
	                         </td>
	                   	</tr>
	                   </tbody>
	             </table>
	        	<p class=" text-c">
				{{if SMS_OPEN=='1'&&SMS_CONTENT!=''}}
					<button class="btn btn-info  btn-sm btn-outline" type="button" onclick="hotlineService.openSmsPage('{{:ORG_CODE}}','{{:BRAND_CODE}}','{{:custInfo[0].CUST_PHONE}}','{{:CROWD_PACK_ID}}')">发送短信</button>
				{{/if}}
		   			<button class="btn btn-info btn-sm"  type="button" id="submitBtn_{{:CROWD_PACK_ID}}" onclick="hotlineService.ajaxSubmitForm('{{:CROWD_PACK_ID}}');helper_proactive_bury('保存')">保存</button>
	   	 		</p>
			</form>		
			</div>
	   	 </div>
		{{/for}}
		</script>
   	 </div>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	    jQuery.namespace("hotlineService")	
	    $(function(){
	    	var crowdPackList = parent.ordercons.crowdPackList;
	    	if(crowdPackList&&crowdPackList.length>0){
	    		itemontentHtml = $.templates("#crowd_pack_list_template").render(crowdPackList);
	    		$("#crowdPackList").html(itemontentHtml);
	    		for(var i in crowdPackList){
	    			var id = crowdPackList[i].CROWD_PACK_ID;
	    			$('#crowdPackForm-'+id).render();
	    		}
	    		//默认展示第一个
	    		var infoObjList = $('.crowdPackList').find(".info");
	    		for(var i=0;i<infoObjList.length;i++){
	    			if(i>0){
	    				$(infoObjList[i]).slideToggle('fast');
	    			}
	    		}
	    	}else{
	    		$("#crowdPackList").append('<div class="nodata text-center" id="nodata" ><img src="/easitline-static/images/nodata.png"/></div>');
	    		console.log("无人群包数据...");
	    	}
	    })
	    function toggleMore(el){
			var btn = $(el).find(".glyphicon");
			$(el).parents(".crowdPack").find(".info").slideToggle('fast');
			btn.toggleClass("glyphicon glyphicon glyphicon-menu-up")
		}
	    hotlineService.showAllText = function(obj,textId){
	    	var text= $(textId).val();
	    	if(!text){
	    		text = "无"
	    	}
			layer.tips(text,$(obj),{tips:[1, '#0FA6D8'],time:0,area:['250px','auto']});
	    }
	    hotlineService.hideAllText = function(obj) { 
	 		layer.closeAll('tips');
	 	}
	    
	    $.views.converters("prodNames", function(array) {
	    	var prodNames = "";
	    	for(var i in array){
	    		var info = array[i];
	    		var prodName = info.PROD_NAME;
	    		if(prodName){
		    		if(i==0){
			    		prodNames += prodName;
		    		}else{
			    		prodNames += "|"+prodName;
		    		}
	    		}
	    	}
			return prodNames;
		}); 
	    
	    hotlineService.ajaxSubmitForm = function(crowPackId){
	    	var formId = "crowdPackForm-"+crowPackId;
			if(!form.validate('#'+formId)){
				return;
			}
			var crowdIds = $('#'+formId+" input[name='crowdIds']").map(function () {
			     return $(this).val();
			}).get();
			var data = form.getJSONObject(formId);
			data['crowdIds']=crowdIds;
			data['serviceResultName']=$('#'+formId+" select[name='serviceResult']").find("option:selected").text();
			ajax.remoteCall("${ctxPath}/servlet/strategy?action=submitServiceResult",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1});
					$("#submitBtn_"+crowPackId).attr("disabled",true);
					if($("#li_activeService",parent.document).hasClass("highlight")){
                        $("#li_activeService",parent.document).removeClass("highlight");
					}
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}

	    hotlineService.openSmsPage= function(orgCode,brandCode,custPhone,crowdPackId) {
	    		var content = $("#smsContent-"+crowdPackId).val();
		   		parent.popup.layerShow({type:2,title:'发送短信',area:['860px','400px'],offset:'100px',shade:0,maxmin:2,moveOut:true,shadeClose:false,url:"${ctxPath}/pages/crowdPack/send-message.jsp",data:{orgCode:orgCode,brandCode:brandCode,custPhone:custPhone,content:content}});
		}
        // 保存按钮埋点        
        function helper_proactive_bury(ele) {
            console.log(ele, "ele1");
            if (top.collectEvent) {
            console.log(ele, "ele2");
                top.collectEvent("click_voice_helper_proactive", {
                    element: ele,
                });
            }
        }
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>