<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>策略配置主页面</title>
	<style>
	#menuContent {display:none;position: absolute;border:1px solid rgb(170,170,170);max-width: 220px; max-height: 350px;z-index:10;overflow: auto;background-color: #f4f4f4}
	.nav-pills>li {
    	 float: none; 
	}
	.nav-pills>li>a {
    	 color:#000;
	}
	.chirld-level>li>a {
    	 display: block;
    	 padding-left:35px;
	}
	.son-level>li>a {
    	 display: block;
    	 padding-left:70px;
	}
	.chirld-level>li.active>a, .chirld-level>li.active>a:focus, .chirld-level>li.active>a:hover {
	    background-color: #218868;
	    display: block;
    	padding-left:35px;
	}
	.parent-level>li.active>a, .parent-level>li.active>a:focus, .parent-level>li.active>a:hover {
	    background-color: #218868;
	    display: block;
	}
	.son-level>li.active>a, .son-level>li.active>a:focus, .son-level>li.active>a:hover {
	    background-color: #218868;
	    display: block;
    	padding-left:70px;
	}
	.gray-bg{
		background-color: #FFF;
	}
	.navbar-default {
		background-color:#FFF;
	}
	.ibox-panel-content{
   		height:100%;
	}
	.sidebar{
		position: fixed;
		width:215px;
	}
	.container-fluid{
		padding: 0px 15px;
	}
	.info-title p{
		width: 100px;
	}
	hr{
		margin:0px 0px;
	}
	.table-vzebra tbody > tr > td:nth-child(odd){
	   min-width: 80px;
	 }
	 .logic-btn{
	   width: 70px;
	  }
	  a:hover {
	      color: #20a0ff;
	  }
	</style>
</EasyTag:override>
<EasyTag:override name="content">
 <div class="row">
      <div class="col-md-2" style="padding:0px">
         <div class="sidebar" style="height:800px;overflow-x: hidden;overflow-y: auto;padding-bottom: 150px;">
	        <div class="navbar navbar-default" id="mycollapse">
	            <!-- <ul class="nav navbar-nav nav-pills nav-stacked"> -->
	            <ul class="nav nav-pills nav-stacked parent-level" id="first-level">
	                <li>
	                    <a href="#second-level-1"  class="second-level accordion-toggle"
	                       data-toggle="collapse"><span>热线接入单页面</span><i class="fa fa-angle-down pull-right"></i>
	                    </a>
	                    <ul class="nav nav-pills collapse in chirld-level sitenav" data-row = "hotline" data-url="/activeService/pages/crowdPack/strategy/strategy-hotline-config.jsp" aria-expanded="true" id="second-level-1">
	                        <li id="defaultRow" onclick="jumpTo(this)"><a href="#hotline-base">基础配置</a></li>
	                        <li onclick="jumpTo(this)"><a href="#hotline-script">脚本配置</a></li>
	                        <li onclick="jumpTo(this)"><a href="#hotline-sms">短信配置</a></li>
	                        <li onclick="jumpTo(this)"><a href="#hotline-result">结果配置</a></li>
	                    </ul>
	                </li>
	
	                <li>
	                    <a href="#second-level-2" class="second-level accordion-toggle" 
	                       data-toggle="collapse" onclick="itemToggle(this)"><span>在线接入单页面</span><i class="fa fa-angle-down pull-right"></i>
	                    </a>
	                    <ul class="nav nav-pills collapse chirld-level" id="second-level-2">
	                        <li>
	                         	<a href="#second-level-2-1" class="second-level accordion-toggle2"
			                       data-toggle="collapse"
			                       data-parent="#second-collapse" onclick="itemToggle(this)"><span>坐席在线页面</span><i class="fa fa-angle-down pull-right"></i>
			                    </a>
		                        <ul class="nav nav-pills collapse son-level" data-row = "online" id="second-level-2-1" data-url="/activeService/pages/crowdPack/strategy/strategy-online-config.jsp">
                        			<li onclick="jumpTo(this)"><a href="#online-base">基础配置</a></li>
                        			<li onclick="jumpTo(this)"><a href="#online-script">脚本配置</a></li>
<!--                         			<li onclick="javascript:jumpTo(this)"><a href="#online-sms">短信配置</a></li>
 -->                        			<li onclick="jumpTo(this)"><a href="#online-result">结果配置</a></li>
		                        </ul>
		                    </li>
		                    <li>
	                         	<a href="#second-level-2-2" class="second-level accordion-toggle"
			                       data-toggle="collapse"
			                       data-parent="#second-collapse" onclick="itemToggle(this)"><span>客户在线页面</span><i class="fa fa-angle-down pull-right"></i>
			                    </a>
		                        <ul class="nav nav-pills collapse son-level" id="second-level-2-2" data-row = "onlinecust" data-url="/activeService/pages/crowdPack/strategy/strategy-online-cust-config.jsp">
		                        	<li onclick="jumpTo(this)"><a href="#onlinecust-base">基础配置</a></li>
                        			<li onclick="jumpTo(this)"><a href="#onlinecust-script">脚本配置</a></li>
		                        </ul>
		                    </li>
	                    </ul>
	                </li>
	                <li>
	                    <a href="#second-level-3" class="second-level accordion-toggle"
	                       data-toggle="collapse" onclick="itemToggle(this)"><span>工单回访页面</span><i class="fa fa-angle-down pull-right"></i>
	                    </a>
	                    <ul class="nav nav-pills collapse chirld-level" id="second-level-3" data-row = "revisit" data-url="/activeService/pages/crowdPack/strategy/strategy-revisit-config.jsp">
	                        <li onclick="jumpTo(this)"><a href="#revisit-base">基础配置</a></li>
                   			<li onclick="jumpTo(this)"><a href="#revisit-script">脚本配置</a></li>
                   			<li onclick="jumpTo(this)"><a href="#revisit-sms">短信配置</a></li>
                   			<li onclick="jumpTo(this)"><a href="#revisit-result">结果配置</a></li>
	                    </ul>
	                </li>
	                <li>
	                	<a href="#second-level-4" class="second-level accordion-toggle" 
	                       data-toggle="collapse" onclick="itemToggle(this)"><span>回访及营销策略</span><i class="fa fa-angle-down pull-right"></i>
	                    </a>
	                    <ul class="nav nav-pills collapse chirld-level" id="second-level-4">
		                    <li onclick="jumpTo(this,2)" data-row ="robot" data-url="/activeService/pages/crowdPack/strategy/strategy-robot-config.jsp"><a href="javascript:">机器人外呼策略</a></li>
	                        <li>
	                         	<a href="#second-level-4-2" class="second-level accordion-toggle"
			                       data-toggle="collapse"
			                       data-parent="#second-collapse" onclick="itemToggle(this)"><span>主动营销策略</span><i class="fa fa-angle-down pull-right"></i>
			                    </a>
		                        <ul class="nav nav-pills collapse son-level" data-row="market" id="second-level-4-2" data-url="/activeService/pages/crowdPack/strategy/strategy-market-config.jsp">
                        			<li onclick="jumpTo(this)"><a href="#market-base">基础配置</a></li>
                        			<li onclick="jumpTo(this)"><a href="#market-script">脚本配置</a></li>
                        			<li onclick="jumpTo(this)"><a href="#market-sms">短信配置</a></li>
                        			<li onclick="jumpTo(this)"><a href="#market-result">结果配置</a></li>
		                        </ul>
		                    </li>
	                        <li>
	                         	<a href="#second-level-4-3" class="second-level accordion-toggle"
			                       data-toggle="collapse"
			                       data-parent="#second-collapse" onclick="itemToggle(this)"><span>调查回访策略</span><i class="fa fa-angle-down pull-right"></i>
			                    </a>
		                        <ul class="nav nav-pills collapse son-level" data-row ="manual" id="second-level-4-3" data-url="/activeService/pages/crowdPack/strategy/strategy-manual-config.jsp">
                        			<li onclick="jumpTo(this)"><a href="#manual-base">基础配置</a></li>
                        			<li onclick="jumpTo(this)"><a href="#manual-questionnaire">问卷配置</a></li>
                        			<li onclick="jumpTo(this)"><a href="#manual-sms">短信配置</a></li>
                        			<li onclick="jumpTo(this)"><a href="#manual-result">结果配置</a></li>
		                        </ul>
		                    </li>

	                    </ul>
	                </li>
	                <li data-row ="webchat" data-url="/activeService/pages/config/wechatWorkStrategy/strategy-list.jsp" onclick="jumpTo(this,2)">
	                    <a href="javascript:">企业微信策略</a>
	                </li>
					<EasyTag:res resId="voice_strategy">
						<li>
							<a href="#second-level-5"  class="second-level accordion-toggle"
							   data-toggle="collapse"  onclick="itemToggle(this)"><span>5G策略配置页面</span><i class="fa fa-angle-down pull-right"></i>
							</a>
							<ul class="nav nav-pills collapse chirld-level" data-row = "5GVoice" data-url="/activeService/pages/crowdPack/strategy/strategy-5g-voice-config.jsp" id="second-level-5">
								<li onclick="jumpTo(this)"><a href="#5GVoice-base">基础配置</a></li>
							</ul>
						</li>
					</EasyTag:res>
	            </ul>
	        </div>
	    </div>
      </div>
      <div class="col-md-10" style="padding:0px;">
      		<div class="ibox-panel-content" id="ibox-panel-content">
      			<div id="defaultDiv" style="text-align:center;line-height:200px;"><i class="glyphicon glyphicon-hand-left"> 请选择左侧菜单</i></div>
      		</div>
      </div>
 </div>
 <script id="result-list-tpl" type="text/x-jsrender">
			{{for data}}
				<tr>
					<td>
					<span class="DATA1 xm-select-nri"><input type="text" {{if SERVICE_TYPE=='3'}} readonly {{/if}} data-rules="required" name="result.RESULT_CONTENT" class="form-control input-sm resultReadOnlyFlag" style="width:390px" value="{{:CONTENT}}" /></span>
					</td>
					<td>
					<select class="form-control input-sm mr-5 resultReadOnlyFlag" {{if SERVICE_TYPE=='3'}} readonly {{/if}} data-rules="required" name="result.RESULT_OPEN" data-rules="required" onchange="">
						<option value="1" {{if IS_OPEN == '1'}}selected='selected'{{/if}}>是</option>
						<option value="0" {{if IS_OPEN == '0'}}selected = 'selected'{{/if}}>否</option>
					</select>
					</td>
					<td>
					<span class="DATA1 xm-select-nri"><input type="text" {{if SERVICE_TYPE=='3'}} readonly {{/if}}  data-rules="digits|required" name="result.RESULT_SORT" class="form-control input-sm resultReadOnlyFlag" style="width:100px" value="{{:SORT}}" /></span>
					</td>
					<td>
					<span {{if SERVICE_TYPE=='3'}}style="display:none"{{/if}} class="resultReadOnlyFlag" >
					<span class="glyphicon glyphicon-minus"  onclick="strategyIndex.removeRef(this)"></span>
					<span class="glyphicon glyphicon-plus" onclick="strategyIndex.addItem2(this)"></span>
					</span>
					</td>
				</tr>
			{{/for}}
		</script>
</EasyTag:override>

<EasyTag:override name="script">
	<script type = "text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	<script type = "text/javascript" src = "${ctxPath}/static/js/jquery.nav.js" ></script>
	<script type="text/javascript">
    jQuery.namespace("strategyIndex")
	var crowdPackId="${param.crowdPackId}";
	var crowdNum="${param.crowdNum}";
	var crowdPackGroupId="${param.crowdPackGroupId}";
	var display="${param.display}";
	var category="${param.category}";
	$(function () {
		//var height = window.screen.availHeight;
		//$("#ibox-panel-content").css("height",height-70);
		$('.chirld-level').onePageNav({scrollSpeed:100,scrollThreshold:0.02});
		//$('#defaultRow').click();
	})
	function itemToggle(obj){
	    $(obj).find("i").toggleClass("fa fa-angle-up");
	    $(obj).find("i").toggleClass("fa fa-angle-down");
	}
	function reloadPanelContent(){
	    $(obj).find("i").toggleClass("fa fa-angle-up");
	    $(obj).find("i").toggleClass("fa fa-angle-down");
	}
    function jumpTo(obj,type){
    	$("#defaultDiv").hide();
       	$(".nav li").removeClass("active");
       	var $this = $(obj);
       	$this.addClass("active");
    	var $parent = $this.parent();
       	var target = type == 2 ? $this.data("url"):$parent.data("url");
       	var page_href = addParams($parent,target);
       	var rowId = type == 2 ? $this.data("row"):$parent.data("row");
       	if(!$("#ibox-panel-content-"+rowId).html()){//不存在加载界面
           	$('div[id^="ibox-panel-content-"]').hide();
           	$('#ibox-panel-content').append('<div id="ibox-panel-content-'+rowId+'"><div>')
            $("#ibox-panel-content-"+rowId).load(page_href);
            $("#ibox-panel-content-"+rowId).show();
       	}else{
           	$('div[id^="ibox-panel-content-"]').hide();
            $("#ibox-panel-content-"+rowId).show();
       	}
       	itemToggle(obj);
    }
	function addParams($this,url){
		url+="?crowdPackId="+crowdPackId+"&crowdPackGroupId="+crowdPackGroupId+"&crowdNum="+crowdNum+"&display="+display+"&category="+category
		return url;
	}
	
	strategyIndex.addItem= function(el){
    	var container = $(el).data("container");
    	$(""+container).find(".nodata").remove();
		var resultOptionTpl = $.templates('#result-list-tpl').render({data:[{}]});
		$(""+container).append(resultOptionTpl); 
	}
	
	strategyIndex.addItem2= function(el){
		var resultOptionTpl = $.templates('#result-list-tpl').render({data:[{}]});
		$(el).closest('tr').after(resultOptionTpl); 
	}
	strategyIndex.removeRef = function(obj){
		if($(obj).closest('tr').find('input').length>=1){
			$(obj).closest('tr').remove();
		}
	}
	function setLength(obj,maxlength,id){
	    var num=obj.value.length;
	    var leng=id;
	   /*  if(num<0){
	        num=0;
	    } */
	    document.getElementById(leng).innerHTML=num+"/"+maxlength;
	}
	
	function closeWin(){
		var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
		parent.layer.close(index);  // 关闭layer
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>