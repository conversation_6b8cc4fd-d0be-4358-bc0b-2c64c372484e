<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>机器人策略配置</title>
	<link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css" />
	<style>
		#prodMenuContent {display:none;position: absolute;border:1px solid rgb(170,170,170);max-width: 220px; max-height: 350px;z-index:10;overflow: auto;background-color: #f4f4f4}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="robotStrategyForm"autocomplete="off" data-mars="strategy.getStrategyDetail" method="post"  autocomplete="off" data-mars-prefix="strategy." >
		<input type="hidden" name="strategy.CROWD_PACK_ID" value="${param.crowdPackId }" >
		<input type="hidden" name="strategy.CROWD_PACK_GROUP_ID" value="${param.crowdPackGroupId }" >
		<input type="hidden" name="strategy.ID"  >
		<input type="hidden" name="CHANNEL_TYPE" value="7">
		<div class="panel panel-default">
     		<div class="panel-heading">
      			<i class="glyphicon glyphicon-edit"></i> 机器人外呼策略配置
   			</div>
			<div class="panel-body">
				<table class="table table-edit table-vzebra mt-10" >
					<tbody>
	                   	<tr>
	                  		 <td>是否启用</td>
	                         <td>
						 		 <label class="radio-inline">
					                <input type="radio" value="1" data-rules="required" name="strategy.IS_OPEN" checked="checked" > 是
					             </label>
					             <label class="radio-inline">
					                <input type="radio" value="0" data-rules="required" name="strategy.IS_OPEN"> 否
					             </label>
	                         </td>
	                      	<td>是否实时推送</td>
	                         <td>
						 		 <label class="radio-inline">
					                <input type="radio" value="Y" data-rules="required" name="strategy.IS_TIMELY"> 是
					             </label>
					             <label class="radio-inline">
					                <input type="radio" value="N" data-rules="required" name="strategy.IS_TIMELY" checked="checked"> 否
					             </label>
	                         </td>
	                   	</tr>
	                   	<tr>
	                    	<td>机器人名称</td>
                        	<td>
                        		<input type="text"  name="strategy.ROBOT_NAME" id="robotName" class="form-control input-sm" value="">
	                      	</td>
	                      	<td class="required" width="162px">机器人流程</td>
							<td>
								<select name="strategy.ROBOT_ID" id="robotSelect" class="form-control input-sm" data-rules="required" style="width: 60%;display: inherit;" onchange="setRobotName()">
                           			<option value="">请选择</option>
                           		</select>
                           		<button type="button" class="btn btn-sm btn-success" onclick="getRobotSelectInfo()">刷新</button>
                           		<button type="button" class="btn btn-sm btn-success" onclick="openRobotConfigWebsite()">新增</button>
							</td>
	                    </tr>
						<tr>
							<td class="required">外呼线路</td>
							<td>
								<select name="strategy.TASK_ID" id="activitySelect" class="form-control input-sm" data-mars="Dict.getDict('ACTIVITY_TYPE')" data-rules="required">
									<option value="">请选择</option>
								</select>
							</td>
							<td class="required">校验策略</td>
							<td>
								<select name="strategy.VALIDATION_STRATEGY" id="validationSelect" class="form-control input-sm" data-mars="Dict.getDict('VALIDATION_STRATEGY')" data-rules="required">
									<option value="">请选择</option>
								</select>
							</td>
						</tr>
	                    <tr>
	                    	<td>品牌</td>
                        	<td>
                        		<select name="strategy.BRAND_CODE_R" id="brandCodeR" class="form-control input-sm" data-mars="common.getBrandlist" onchange="$('#brandNameR').val($(this).find('option:selected').text())">
                           			<option value="">请选择</option>
                           		</select>
                        		<input type="hidden"  name="strategy.BRAND_NAME_R" id="brandNameR">
                        	</td>
	                      	<td>品类</td>
							<td>
								<input type="text"  name="strategy.PROD_NAME_R" id="prodNameR" class="form-control input-sm" onclick="showMenu(this,'prodMenuContent')">
								<input type="hidden"  name="strategy.PROD_CODE_R" id="prodCodeR">
								<div id="prodMenuContent" class="menuContent">
						        	<input type="hidden" id = "proCodeTreeHidden" data-mars="common.productCode" />
						         	<ul id="proCodeTree" class="ztree" style="margin-top:0; width:100%; height:auto;"></ul>
						    	</div>
							</td>
	                    </tr>
	                    <tr>
	                    	<td class="required">发布开始时间</td>
                        	<td><input type="text"  name="strategy.PUBLISH_BEGIN_TIME" data-rules="required" class="form-control input-sm" id="startDate" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'endDate\')}',dateFmt:'HH:mm:ss'})"></td>
	                      	<td class="required">发布结束时间</td>
							<td><input type="text"  name="strategy.PUBLISH_END_TIME" data-rules="required" class="form-control input-sm" id="endDate" onclick="WdatePicker({minDate:'#F{$dp.$D(\'startDate\')}',dateFmt:'HH:mm:ss'})"></td>
	                    </tr>
	                    <tr>
	                    	<td class="required">外呼开始时间1</td>
                        	<td><input type="text"  name="strategy.OUTBOUND_BEGIN_TIME1" data-rules="required" class="form-control input-sm" id="startDate1" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'endDate1\')}',dateFmt:'HH:mm:ss'})"></td>
	                      	<td class="required">外呼结束时间1</td>
							<td><input type="text"  name="strategy.OUTBOUND_END_TIME1" data-rules="required" class="form-control input-sm" id="endDate1" onclick="WdatePicker({minDate:'#F{$dp.$D(\'startDate1\')}',dateFmt:'HH:mm:ss'})"></td>
	                    </tr>
	                    <tr>
	                    	<td >外呼开始时间2</td>
                        	<td><input type="text"  name="strategy.OUTBOUND_BEGIN_TIME2" class="form-control input-sm" id="startDate2" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'endDate2\')}',dateFmt:'HH:mm:ss'})"></td>
	                      	<td >外呼结束时间2</td>
							<td><input type="text"  name="strategy.OUTBOUND_END_TIME2" class="form-control input-sm" id="endDate2" onclick="WdatePicker({minDate:'#F{$dp.$D(\'startDate2\')}',dateFmt:'HH:mm:ss'})"></td>
	                    </tr>
	                    <tr>
	                    	<td class="required">呼叫时间间隔</td>
                        	<td><input type="text"  name="strategy.INTERVAL_TIME" data-rules="required" class="form-control input-sm number" style="width: 20%;display: inherit;margin-right: 10px;">秒</td>
	                      	<td class="required">人工空闲外呼比</td>
							<td><input type="text"  name="strategy.IDLE_RATIO" data-rules="required" class="form-control input-sm number" style="width: 20%;display: inherit;margin-right: 10px;"></td>
	                    </tr>
						<c:if test="${param.category=='1'}">
						<tr>
							<td>最小间隔重呼时间</td>
							<td><input type="text" name="strategy.RECALL_INTERVAL_TIME" class="form-control input-sm number"></td>
							<td>最大重呼次数</td>
							<td><input type="text" name="strategy.RECALL_TIMES" class="form-control input-sm"></td>
						</tr>
	                    <tr>
	                    	<td>意图配置</td>
	                        <td>
						 		<a href="javascript:" onclick="openRobotSMSConfig()">进入配置</a>
	                        </td>
							<td class="required">当日最大外呼量</td>
							<td>
								<input type="text"  name="strategy.DAY_MAX_CALL_NUM" data-rules="required" class="form-control input-sm" style="width: 20%;display: inherit;margin-right: 10px;">
							</td>
	                    </tr>
						</c:if>
	                    <tr>
							<td>活动</td>
							<td>
								<select name="strategy.PREFIX_CODE" class="form-control input-sm" data-mars="Dict.getDict('AS_CALL_PREFIX_CODE')" >
									<option value="">请选择</option>
								</select>
							</td>
							<td class="required">转人工技能组ID</td>
							<td><input type="text"  name="strategy.SKILL_GROUP_ID" class="form-control input-sm" data-rules="required"></td>
						</tr>
						<tr>
	                    	<td class="required" width="138px;">短信所属事业部</td>
                        	<td>
                        		<select name="strategy.ORG_CODE" id="orgSelect" class="form-control input-sm" data-mars="Dict.getDict('SMS_MODEL')" data-rules="required">
                           			<option value="">请选择</option>
                           		</select>
	                      	</td>
	                      	<td class="required">短信所属品牌</td>
							<td>
								<select name="strategy.BRAND_CODE" id="brandSelect" class="form-control input-sm" data-mars="Dict.getDict('SMS_CATEGORY')" data-rules="required" onchange="changeBrand()">
                           			<option value="">请选择</option>
                           		</select>
                           		<input type="hidden" name="strategy.BRAND_NAME">
							</td>
	                    </tr>
	                    <tr>
	                    	<td>启用挂机短信</td>
	                        <td>
						 		<label class="radio-inline">
									<input type="radio" value="1" data-rules="required" name="strategy.IS_SMS_OPEN" checked="checked" onchange="changeSMSFlag()"> 是
					            </label>
					             <label class="radio-inline">
					                <input type="radio" value="0" data-rules="required" name="strategy.IS_SMS_OPEN" onchange="changeSMSFlag()"> 否
					             </label>
	                        </td>
	                    	<td>挂机短信类型</td>
	                        <td>
						 		<label class="radio-inline">
									<input type="radio" value="0" data-rules="required" name="strategy.SMS_TYPE" checked="checked" onchange="changeSMSTypeFlag(this)"> 普通短信
					            </label>
					             <label class="radio-inline">
					                <input type="radio" value="1" data-rules="required" name="strategy.SMS_TYPE" onchange="changeSMSTypeFlag(this)"> 商城短信
					             </label>
	                        </td>
	                    </tr>
	                    <tr id="shopSmsDiv">
	                    	<td id="partnerPhoneTd">合伙人手机号码</td>
                        	<td><input type="text" id="partnerPhone" name="strategy.PARTNER_PHONE" class="form-control input-sm number"></td>
	                      	<td id="shopMiniProgramUrlTd" >商品购买小程序路径</td>
							<td><input type="text" id="shopMiniProgramUrl" name="strategy.SHOP_MINI_PROGRAM_URL" class="form-control input-sm"></td>
	                    </tr>
	                    <tr>
	                    	<td class="required sms">挂机短信文本</td>
			              	<td colspan="3"><textarea id="smsContent" data-rules="required" class="form-control input-sm" rows="4" name="strategy.SMS_CONTENT" cols=""></textarea>
			              		<span style="color: red">* 商城短链配置  {shopMiniProgramUrl}</span>
			              	</td>
	                    </tr>
					</tbody>
				</table>
			</div>
		</div>
	</form>		
    <div class="text-c">
		<button class="btn btn-sm btn-primary"  type="button" style="display:${param.display}" onclick="robotStrategy.ajaxSubmitForm()">保存</button>
		<button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="closeWin()">关闭</button>
 	</div>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
	<script type="text/javascript" src="/neworder/static/js/tree/productTree.js"></script>
	<script type="text/javascript">
	    jQuery.namespace("robotStrategy");
	    
	    var currentRobotId = "";
	    $(function(){
	    	$("#robotStrategyForm").render({success:function(result){
	    		var data = result["strategy.getStrategyDetail"].data;
	    		if(!$.isEmptyObject(data)){
	    			currentRobotId = data.ROBOT_ID;
	    			if(data.IS_SMS_OPEN == 0){
	    				$(".sms").removeClass("required");
	    	    		$("#smsContent").removeAttr("data-rules");
	    			}
	    			// 回显所属活动和校验策略
	    			if(data.TASK_ID) {
	    				$("#activitySelect").val(data.TASK_ID);
	    			}
	    			if(data.VALIDATION_STRATEGY) {
	    				$("#validationSelect").val(data.VALIDATION_STRATEGY);
	    			}
	    		}
	    		if(result["common.productCode"]){
    				pcTree.nodes = result["common.productCode"].data;
    			}
	    		$.fn.zTree.init($("#proCodeTree"), pcTree.setting,pcTree.nodes);
	    		//获取机器人信息
	    		getRobotSelectInfo();
	    		changeSMSTypeFlag();
	    	}});
	    	$(".number").keydown(function(event){
				if(!(event.keyCode==46)&&!(event.keyCode==8)&&!(event.keyCode==37)&&!(event.keyCode==39)){
	           		if(!((event.keyCode>=48&&event.keyCode<=57)||(event.keyCode>=96&&event.keyCode<=105))){
	           			return false;   	    
	           		}
				}
			}).keyup(function(e){
	           	this.value=this.value.replace(/^0+/,'')
			});
	    })
		
	    
	    //保存
		robotStrategy.ajaxSubmitForm = function(){
			if(!form.validate("#robotStrategyForm")){
				return;
			}
			var smsContent = $("textarea[name='strategy.SMS_CONTENT']").val();
			//判断短信内容是否包含%，如果包含，则提示用户不允许保存
			if(smsContent&&smsContent.indexOf("%")>-1){
				layer.alert("短信内容不允许包含%，请修改后保存！",{icon: 5});
				return;
			}
			var id = $("input[name='strategy.ID']").val();
			if(id){
				layer.confirm("确认修改配置？", {
		            btn : [ '确定', '取消' ]//按钮
		        }, function(index) {
		        	robotStrategy.saveConfig();
		        });
			}else{
					robotStrategy.saveConfig();
			}
		}
	    
	    robotStrategy.saveConfig = function(){
	    	var data = form.getJSONObject("robotStrategyForm");
			ajax.remoteCall("${ctxPath}/servlet/strategy?action=configSave",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1},function(){
						closeWin();
					});
					//$("#robotStrategyForm").render();
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
	    
	    //获取机器人信息、渲染机器人流程选择框
	    function getRobotSelectInfo(){
	    	ajax.remoteCall("${ctxPath}/servlet/strategy?action=getRobotConfigUrl",{},function(result){
	    		if(result.state == 1){
	    			var option = result.data;
	    			if(!$.isEmptyObject(option)){
	    				var select = $("select[name='strategy.ROBOT_ID']");
	    				select.empty();//清空现有子元素
		    			var tempHtml = '<option value="">请选择</option>';
		    			for(var i in option){
		    				tempHtml += '<option value="'+i+'">'+option[i]+'</option>';
		    			}
		    			select.append(tempHtml);
		    			if(currentRobotId!=""){
		    				select.val(currentRobotId);
		    			}
	    			}else{
	    				layer.alert("未获取到机器人列表!",{icon:5});
	    			}
	    		}
			});
	    }
	    
	    //打开机器人配置页面
	    function openRobotConfigWebsite(){
	    	ajax.remoteCall("${ctxPath}/servlet/strategy?action=getRobotListUrl",{},function(result){
	    		if(result.state == 1){
	    			window.open(result.data,"机器人配置");
	    		}else{
	    			layer.alert("未获取到机器人服务器地址!",{icon:5});
	    		}
			});
	    }
	    
	    //打开机器人短信模板配置
	    function openRobotSMSConfig(){
	    	var strategyId = $("input[name='strategy.ID']").val();
	    	var robotId = $("#robotSelect").val();
	    	if(!strategyId){
	    		layer.msg("请先配置机器人策略，保存之后再配置意图短信模板",{icon:0});
	    		return;
	    	}
	    	parent.popup.openTab({url:"${ctxPath}/pages/config/sms/robot-sms-list.jsp",title: "机器人意图短信模板配置",data:{strategyId,robotId}});
	    }
	    
	    function changeSMSFlag(){
	    	var val = $("input[name='strategy.IS_SMS_OPEN']:checked").val();
	    	if(val==1){//开启
	    		$(".sms").addClass("required");
	    		$("#smsContent").attr("data-rules","required");
	    	}else{//关闭
	    		$(".sms").removeClass("required");
	    		$("#smsContent").removeAttr("data-rules");
	    	}
	    }
	    
	    function changeSMSTypeFlag(){
	    	var val = $("input[name='strategy.SMS_TYPE']:checked").val();
	    	if(val==1){//商城短链接
	    		$("#shopSmsDiv").show();
	    		$("#partnerPhoneTd").addClass("required");
	    		$("#shopMiniProgramUrlTd").addClass("required");
	    		$("#partnerPhone").attr("data-rules","required");
	    		$("#shopMiniProgramUrl").attr("data-rules","required");
	    	}else{//普通链接
	    		$("#shopSmsDiv").hide();
	    		$("#partnerPhoneTd").removeClass("required");
	    		$("#shopMiniProgramUrlTd").removeClass("required");
	    		$("#partnerPhone").removeAttr("data-rules");
	    		$("#shopMiniProgramUrl").removeAttr("data-rules");
	    	}
	    }
	    
	    //set robotName
	    function setRobotName(){
	    	var robotName = $("#robotSelect option:selected").text();
	    	var startIdx = robotName.indexOf("】") + 1;
	    	$("#robotName").val(robotName.substring(startIdx,robotName.length));
	    }
	    
	    //set brandName
	    function changeBrand(){
	    	var brandName = $("#brandSelect").find("option:selected").text();
	    	$("input[name='strategy.BRAND_NAME']").val(brandName);
	    }
	    
	  //显示菜单
		function showMenu(obj,treeId) {
			var leftPx = $(obj).offset().left;
			var topPx = $(obj).offset().top;
			var heightPx = $(obj).height()+$(obj).innerHeight()/2;
		    $("#"+treeId).slideDown("fast");
		    $("body").bind("mousedown", onBodyDownPc);
		}
		pcTree.setting ={
    		data : pcTree.pc_data,
    		callback :{
    			onClick : zTreePcOnclick
    		},
    		async : pcTree.pc_async
	    }
		//品类下拉树的点击事件
		function zTreePcOnclick(event,treeId,treeNode){
			if(treeNode.isParent){ 
				var ztreeObj = $.fn.zTree.getZTreeObj(treeId); 
				ztreeObj.expandNode(treeNode);
		    }else{ 
		    	$("#prodCodeR").val(treeNode.id);
		    	$("#prodNameR").val(treeNode.name);
		    	pcTree.hideMenu('prodMenuContent');
		    }
		}
		function onBodyDownPc(event) {
    	    if (!( event.target.id == "prodMenuContent" || event.target.id == "prodName" || $(event.target).parents("#prodMenuContent").length > 0)) {
    	        hideMenu('prodMenuContent');
    	  	}
    	}
		function hideMenu(divId) {
		    $("#"+divId).fadeOut("fast");
		    $("body").unbind("mousedown", onBodyDownPc);
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>