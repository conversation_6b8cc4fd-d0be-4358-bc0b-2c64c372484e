<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>回访渠道配置</title>
</EasyTag:override>
<EasyTag:override name="content">
	   <form id="revisitStrategyForm" name="revisitStrategyForm" data-mars="strategy.getStrategyDetail" method="post"  autocomplete="off" data-mars-prefix="strategy." >
		   <input  class="hidden" name="strategy.CROWD_PACK_ID" value="${param.crowdPackId }" >
		   <input  class="hidden" name="strategy.CROWD_PACK_GROUP_ID" value="${param.crowdPackGroupId }" >
		   <input  class="hidden" name="strategy.ID"  >
		   <input  class="hidden" name="CHANNEL_TYPE"  id="revisitChannelType" value="4" >
	       <div class="panel panel-default">
	       		<div class="panel-heading">
		       		<i class="glyphicon glyphicon-edit"></i> 工单回访页面
		    	</div>
			    <div class="panel-body">
			       <table class="table table-edit table-vzebra mt-10" >
	                   <tbody>
	                    <td colspan="4" style="text-align: left" id="revisit-base">
	   						<font color="#218868"><i class="glyphicon glyphicon-cog"></i> 基础配置</font>
	   						<hr>
     					</td>
	                   	<tr>
	                  		 <td>是否启用</td>
	                         <td colspan="3">
						 		 <label class="radio-inline">
					                <input type="radio" value="1" data-rules="required" name="strategy.IS_OPEN" checked="checked" > 是
					             </label>
					             <label class="radio-inline">
					                <input type="radio" value="0" data-rules="required" name="strategy.IS_OPEN"> 否
					             </label>
	                         </td>
	                   	</tr>
	                    <tr>
	                      <td>服务类型</td>
	                         <td>
	                           <select name="strategy.SERVICE_TYPE" onchange = "revisitStrategy.changeServiceType(this);" data-rules="required" class="form-control input-sm"  data-mars="Dict.getDict('CC_AS_SERVICE_TYPE')" >
	                           	<option value="">请选择</option>
	                           </select>
	                      </td>
	                      <td>人群数量</td>
	                      <td><input type="text"  name="CROWD_NUN" class="form-control input-sm" readonly="readonly" value="${param.crowdNum }"></td>
	                    </tr>
	                    <tr>
	                    	<td colspan="4" style="text-align: left" id="revisit-script">
		   						<font color="#218868"><i class="glyphicon glyphicon-comment"></i> 脚本配置</font>
		   						<hr>
     						</td>
	                    </tr>
	                   	<tr>
	                  		 <td>是否启用</td>
	                         <td>
						 		 <label class="radio-inline">
					                <input type="radio" value="1" name="strategy.SCRIPT_OPEN" checked="checked" > 是
					             </label>
					             <label class="radio-inline">
					                <input type="radio" value="0" name="strategy.SCRIPT_OPEN"> 否
					             </label>
	                         </td>
	                   	</tr>
	                    <tr>
	                      <td>脚本话术内容<br>
			              <span id="scriptRevisitLength" style="right:5px; bottom:3vh;font-size:12px;  color:#BDCADA">0/500</span>
	                      </td>
			              <td colspan="3">
			              <textarea maxlength="500" placeholder="" onkeyup="setLength(this,500,'scriptRevisitLength');" class="form-control input-sm" rows="10" name="strategy.SCRIPT_CONTENT" cols=""></textarea>
			              </td>
	                    </tr>
	                    <tr>
		                    <td colspan="4" style="text-align: left" id="revisit-sms">
		   						<font color="#218868"><i class="glyphicon glyphicon-envelope"></i> 短信配置</font>
		   						<hr>
	     					</td>
     					</tr>
	                    <tr>
	                  		 <td>是否启用</td>
	                         <td>
						 		 <label class="radio-inline">
					                <input type="radio" value="1" name="strategy.SMS_OPEN" checked="checked" > 是
					             </label>
					             <label class="radio-inline">
					                <input type="radio" value="0" name="strategy.SMS_OPEN"> 否
					             </label>
	                         </td>
	                   	</tr>
	                   	<tr>
	                   		<td>
	                   			主体
	                   		</td>
	                   		<td>
	                           <select class="form-control input-sm"  
	                            name="strategy.ORG_CODE" 
	                             data-mars="Dict.getDict('SMS_MODEL')">
                                        <option value="">请选择</option>
                               </select>
	                        </td>
	                        <td>品牌</td>
	                        <td>
		                       <select name="strategy.BRAND_CODE" onchange="" class="form-control input-sm" data-mars="Dict.getDict('SMS_CATEGORY')">
		                                <option value="">请选择</option>
		                       </select>
	                        </td>
	                   	</tr>
	                    <tr>
	                      <td>短信模板内容<br>
	                      <span id="smsRevisitLength" style="right:5px; bottom:3vh;font-size:12px;  color:#BDCADA">0/500</span>
	                      </td>
			              <td colspan="3"><textarea maxlength="500" placeholder="" onkeyup="setLength(this,500,'smsRevisitLength');" class="form-control input-sm" rows="10"  name="strategy.SMS_CONTENT" cols=""></textarea></td>
	                    </tr>
	                   </tbody>
	             </table>
	             
	             <table class="table table-edit mt-10" id="revisit-result-table" data-container="#revisit-result-el" data-template="result-list-tpl">
		             <tbody>
	                   	<tr>
		                    <td colspan="4" style="text-align: left" id="revisit-result">
		   						<font color="#218868"><i class="glyphicon glyphicon-tags"></i> 服务结果</font>
		   						<hr>
	     					</td>
	    				</tr>
	                   	<tr>
	                  		 <th>反馈按钮内容</th>
	                  		 <th>是否启用</th>
	                  		 <th>排序</th>
	                  		 <th style="width:80px;" id="revisit-result-add">
	                  		 	<span class="logic-btn" onclick="strategyIndex.addItem(this)" data-container="#revisit-result-el">
									<a href="JavaScript:false"><i class="fa fa-plus-circle" style="font-size: 18px;"></i>&nbsp;添加</a>
								</span>
	                  		 </th>
	                   	</tr>
	                  </tbody>
	                  <tbody id="revisit-result-el" style="border: none">
	                  </tbody>
	             </table>
			    </div>
		   </div>
		</form>		
        <div class=" text-c">
	   		<button class="btn btn-sm btn-primary"  type="button" style="display:${param.display}" onclick="revisitStrategy.ajaxSubmitForm()">确认</button>
	   		<button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="closeWin()">关闭</button>
   	 	</div>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	    jQuery.namespace("revisitStrategy")
	    revisitStrategy.renderForm = function(){
	    	$("#revisitStrategyForm").render({success:function(result){
	    		if(result["strategy.getStrategyDetail"]){
	    			var data = result["strategy.getStrategyDetail"].data;
	    			if(data){
		    			$("#revisit-result-table").data({
		    				"mars":"strategyResult.getStrategyResultList",
		    			});
		    	    	$("#revisit-result-table").render({data:{strategyId:data.ID,channelType:$("#revisitChannelType").val()}});
		    	    	if(data["SERVICE_TYPE"]=='3'){
		    	    		//洗悦家类型，不允许添加反馈结果
		    	    		$("#revisit-result-add").hide();
		    	    	}
	    			}
	    		}
	    	}});
	    }
	    $(function(){
	    	revisitStrategy.renderForm();
	    })
		revisitStrategy.ajaxSubmitForm = function(){
			if(!form.validate("#revisitStrategyForm")){
				return;
			}
			var smsContent = $("textarea[name='strategy.SMS_CONTENT']").val();
			//判断短信内容是否包含%，如果包含，则提示用户不允许保存
			if(smsContent&&smsContent.indexOf("%")>-1){
				layer.alert("短信内容不允许包含%，请修改后保存！",{icon: 5});
				return;
			}
			var id = $("input[name='strategy.ID']").val();
			if(id){
				layer.confirm("确认修改配置？", {
		            btn : [ '确定', '取消' ]//按钮
		        }, function(index) {
		        	revisitStrategy.saveConfig();
		        });
			}else{
					revisitStrategy.saveConfig();
			}
		}
	    
	    revisitStrategy.saveConfig = function(){
	    	var data = form.getJSONObject("revisitStrategyForm");
			ajax.remoteCall("${ctxPath}/servlet/strategy?action=configSave",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1},function(){
						closeWin();
					});
					//revisitStrategy.renderForm();
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
	    
	    revisitStrategy.changeServiceType = function(obj){
			var serviceType = $(obj).val();
			//洗悦家营销类型，默认指定的反馈列表，不允许修改
			if(serviceType=="3"){
				ajax.remoteCall("${ctxPath}/webcall?action=Dict.getDict",{'param[0]':'AS_MARKET_RESULT'},function(result) { 
					var array= [];
					if(result.data){
						var index = 0;
						for(var key in result.data){
							var item = {};
							item["CONTENT"] = result.data[key];
							item["SORT"] = index+1;
							item["SERVICE_TYPE"] = serviceType;
							array[index] = item;
							index++;
						}
					}
					var html = $.templates('#result-list-tpl').render({data:array});
			    	$("#revisit-result-el").empty();
			        $("#revisit-result-el").append(html);
			        $("#revisit-result-add").hide();
				});
			}else{
				//选择其他类型，则重新渲染
		    	$("#revisit-result-el").empty();
		        $("#revisit-result-add").show();
    	    	$("#revisit-result-table").render({data:{strategyId:$("#revisitStrategyForm input[name='strategy.ID']").val(),channelType:$("#revisitChannelType").val()},success:function(result){
	    	    	$("#revisitStrategyForm").find(".resultReadOnlyFlag").css("display","inline-block");
	    	    	$("#revisitStrategyForm").find(".resultReadOnlyFlag").removeAttr("readonly");
    	    	}});
			}
		}

	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>