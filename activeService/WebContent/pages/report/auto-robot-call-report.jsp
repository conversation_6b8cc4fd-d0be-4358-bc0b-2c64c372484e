<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>智能自动外呼情况报表</title>
	<style></style>
</EasyTag:override>
<EasyTag:override name="content">
       <form  autocomplete="off" method="post" name="searchForm" class="form-inline" id="searchForm" >
       		<div class="ibox">
				<div class="ibox-title clearfix">
					<div class="form-group">
            			<h5><span class="glyphicon glyphicon-list">智能自动外呼情况报表</span></h5>
					</div>
					<div class="form-group">
						<div class="input-group input-group-sm">
							<span class="input-group-addon">人群包</span>
							<input type="text" name="crowdPackName" class="form-control input-sm" style="width: 140px;">
						</div>
						<div class="input-group input-group-sm">
							<span class="input-group-addon">时间范围</span>
							<input type="text" name="date_start" id="startTime" class="form-control input-sm Wdate" style="width: 140px;" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})">
							<span class="input-group-addon">-</span>	
							<input type="text" name="date_end" id="endTime" class="form-control input-sm Wdate" style="width: 140px;" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})">
						</div>
						<div class="input-group input-group-sm">
							<button type="button" class="btn btn-sm btn-default" onclick="reload()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						</div>
				 	</div>
           	    </div>  
           	  	<div class="ibox-content">
             		<div class="table-responsive">
           	     		<table class="table table-auto table-bordered table-hover table-condensed text-c" id="tableHead" data-mars="report.autoRobotCallReport" >
							<thead>
                        		<tr>
	                         	    <th class="text-c">序号</th>
	                         	    <th class="text-c">人群包</th>
	                         	    <th class="text-c">人群包编码</th>
	                         	    <th class="text-c">日期</th>
	                         	    <th class="text-c">外呼数量</th>
									<th class="text-c">外呼用户数</th>
									<th class="text-c">重呼用户数</th>
									<th class="text-c">跨天外呼用户数</th>
	                         	    <th class="text-c">接通数量</th>
	                         	    <th class="text-c">未接通数量</th>
	                         	    <th class="text-c">短信触发数量</th>
									<th class="text-c">操作</th>
	   						 	</tr>
							</thead>
							<tbody id="dataList"></tbody>
						</table>
						<script id="list-template" type="text/x-jsrender">
							{{for list}}
								<tr>
									<td>{{:#index+1}}</td>
									<td>{{:CROWD_PACK_NAME}}</td>
									<td>{{:CROWD_PACK_CODE}}</td>
									<td>{{date_op:DATE_ID}}</td>
									<td>{{:CALL_COUNT}}</td>
									<td>{{:CALL_USER_NUM}}</td>
									<td>{{:RECALL_USER_NUM}}</td>
									<td>{{:HIS_RECALL_USER}}</td>
									<td>{{numPlus:ANSWER_COUNT REJECT_COUNT}}</td>
									<td>{{failCount:CALL_COUNT ANSWER_COUNT REJECT_COUNT}}</td>
									<td>{{:SMS_COUNT}}</td>
									<td><a href="javascript:loadDetail('{{:CROWD_PACK_ID}}','{{:DATE_ID}}')">详情</a></td>
								</tr>
							{{/for}}
						 </script>
             		 </div> 
             		 <div class="row paginate" id="page">
                 		<jsp:include page="/pages/common/pagination.jsp">
                     		<jsp:param value="10" name="pageSize"/>
                     	</jsp:include>
					</div>
                 </div>
            </div>
		   <div>
			   <p>备注:</p>
			   <p>外呼数量：服务时间范围内，非其他状态的总数 </p>
			   <p>外呼用户数：服务时间范围内，非其他状态的客户总数 </p>
			   <p>重呼用户数：服务时间范围内，非其他状态的重复呼叫客户总数 </p>
			   <p>跨天重呼用户数：服务时间范围内，非其他状态的跨天重呼客户总数 </p>
			   <p>接通数量：服务时间范围内，已回访且有效回访+已回访且用户拒访的数量</p>
			   <p>未接通数量：外呼总数-接通数量</p>
			   <p>短信触发数量：服务时间范围内，非其他状态且发送短信的数量 </p>
		   </div>
        </form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/neworder/static/js/common.js"></script>
	<script type="text/javascript">
		jQuery.namespace("list");
		$(function(){
			var startDate = getTodayDate(-30);
   	    	var endDate = getTodayDate();
			$("#startTime").val(startDate);
			$("#endTime").val(endDate);
	    	$("#searchForm").render({success : function(result){
	    		
    		}});
	    });
		
		function reload(){
			if(!$("#startTime").val()||!$("#endTime").val()){
				layer.msg("请选择时间范围",{icon:0});
				return;
			}
			$("#searchForm").searchData({success : function(result){
	    		
    		}});
		}

		$.views.converters("date_op", function(var1) {
			if(var1){
				var1 = var1.substring(0,4) + "-" + var1.substring(4,6) + "-" + var1.substring(6,8);
			}
			return var1;
		});

		$.views.converters("numPlus", function(var1,var2) {
			return checkInt(var1)+checkInt(var2);
		});
		
		function checkInt(val){
			if(!val){
				return 0;
			}
			return parseInt(val);
		}
		$.views.converters("failCount", function(callCount,answerCount,rejectCount) {
			return checkInt(callCount)-checkInt(answerCount)-checkInt(rejectCount);
		});
		
		function loadDetail(crowdPackId, dateId){
			if(dateId){
				dateId = dateId.substring(0,4) + "-" + dateId.substring(4,6) + "-" + dateId.substring(6,8);
			}
			popup.layerShow({
				type : 2,
				title : '查看',
				area : [ '1100px', '800px' ],
				shadeClose:false
			}, "${ctxPath}/pages/report/auto-robot-call-detail.jsp",{crowdPackId:crowdPackId,dateId:dateId});
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
